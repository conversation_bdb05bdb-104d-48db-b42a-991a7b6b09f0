CREATE OR ALTER VIEW GETCPPPRICE(
    ID_ORDER_ITEM,
    PRICE)
AS
select oi.id id_order_item,
       cast(cp.coupon_template_price /
        (select coalesce(sum(oi1.quantity * oi1.original_price),1)
            from coupon_payments cp1
            join order_item oi1 on cp1.id_order_item = oi1.id
            where cp1.id_option_parent = cp.id_option_parent and oi1.rowbeg = 0
            having sum(oi1.quantity * oi1.original_price)<>0
        )
        as decimal(17,6))
    from coupon_payments cp
    join order_item oi on cp.id_order_item = oi.id
    join coupon_items cpi on cpi.id = cp.id_coupon_items
    join coupons cpn on cpn.id = cpi.id_coupons
    where (cast(CURRENT_Date as timestamp) + (CURRENT_time - cast(CURRENT_TIMESTAMP - f_getsystime() as time)) /3600/24 > cpn.end_dt)
         or (cpi.used_dt is not null) or (cpn.single_barcode=0)
;
grant all on GETCPPPRICE to untilluser;
commit;
