create table cancel_order_item (
        id U_ID,
        id_articles 	bigint,
        datatime    	timestamp,	
        id_untill_users bigint,
        table_no    	integer,
        table_part  	char(3),
        price  			decimal(17,4),
        id_prices  		bigint,
        constraint coi_pk primary key (id),
        constraint coi_fk1 foreign key (id_articles) references articles (id),
        constraint coi_fk2 foreign key (id_untill_users) references untill_users (id),
        constraint coi_fk3 foreign key (id_prices) references prices (id)
);
commit;
grant all on cancel_order_item to untilluser;
commit;
execute procedure register_sync_table_ex('cancel_order_item', 'p', 1);
commit;

