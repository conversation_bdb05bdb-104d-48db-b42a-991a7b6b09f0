create table order_item_bookp (
    id u_id,
    id_order_item bigint,
    id_bookkeeping_turnover bigint,
    id_bookkeeping_vat bigint,
    constraint order_item_bookp_pk primary key (id),
    constraint order_item_bookp_fk1 foreign key (id_order_item) references order_item(id),
    constraint order_item_bookp_fk2 foreign key (id_bookkeeping_turnover) references BOOKKEEPING(id),
    constraint order_item_bookp_fk3 foreign key (id_bookkeeping_vat) references BOOKKEEPING(id)
);
commit;
grant all on order_item_bookp to untilluser;
commit;
execute procedure register_sync_table_ex('order_item_bookp', 'p', 1);
commit;


create table pbill_payments_bookp (
    id u_id,
    id_pbill_payments bigint,
    id_bookkeeping bigint,
    constraint pbill_payments_bookp_pk primary key (id),
    constraint pbill_payments_bookp_fk1 foreign key (id_pbill_payments) references pbill_payments(id),
    constraint pbill_payments_bookp_fk2 foreign key (id_bookkeeping) references BOOKKEEPING(id)
);
commit;
grant all on pbill_payments_bookp to untilluser;
commit;
execute procedure register_sync_table_ex('pbill_payments_bookp', 'p', 1);
commit;

 