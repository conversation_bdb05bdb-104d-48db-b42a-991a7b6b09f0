{ Invokable interface ISOAPIntf1 }

unit OrdermanAPIU;

interface

uses InvokeRegistry, Types, XSBuiltIns, SysUtils, Classes, VObjU, Diagnostics, NamedVarsU, Windows;


const
  ERR_OMANAPI_INTERNAL_ERROR        = 1;
  ERR_OMANAPI_DEVICE_NOT_REGISTERED = 2;
  ERR_OMANAPI_NEED_REBUILD_RESOURCES= 5;

type
  TOmanApiRequest = class(TRemotable)
  private
    FSerialNumber: Integer;
    procedure SetSerialNumber(const Value: Integer);
  published
    property SerialNumber: Integer read FSerialNumber write SetSerialNumber;
  end;

  TOmanApiResponse = class(TRemotable)
  private
    FErrCode: Integer;
    FErrMsg: String;
    procedure SetErrCode(const Value: Integer);
    procedure SetErrMsg(const Value: String);
  published
    property ErrCode: Integer read FErrCode write SetErrCode;
    property ErrMsg: String read FErrMsg write SetErrMsg;
  end;

  TOmanConnectedRequest = class(TOmanApiRequest)
  end;

  TOmanConnectedRespone = class(TOmanApiResponse)
  private
    FDataDir: String;
    FResourcesDir: String;
    FDllDir: String;
    FMapsDir: String;
    FNfcEnabled: Boolean;
    procedure SetDataDir(const Value: String);
    procedure SetDllDir(const Value: String);
    procedure SetResourcesDir(const Value: String);
    procedure SetMapsDir(const Value: String);
    procedure SetNfcEnabled(const Value: Boolean);
  published
    property ResourcesDir: String read FResourcesDir write SetResourcesDir;
    property DataDir: String read FDataDir write SetDataDir;
    property DllDir: String read FDllDir write SetDllDir;
    property MapsDir: String read FMapsDir write SetMapsDir;
    property NfcEnabled: Boolean read FNfcEnabled write SetNfcEnabled;
  end;

  TOmanReadyRequest = class(TOmanApiRequest)
  private
    FTcpPort: Integer;
    procedure SetTcpPort(const Value: Integer);
  published
    property TcpPort: Integer read FTcpPort write SetTcpPort;
  end;

  TOmanReadyRespone = class(TOmanApiResponse);

  TOmanClickRequest = class(TOmanApiRequest)
  private
    FWidgetName: String;
    procedure SetWidgetName(const Value: String);
  published
    property WidgetName: String read FWidgetName write SetWidgetName;
  end;

  TOmanClickRespone = class(TOmanApiResponse);

  TOmanListPositionRequest = class(TOmanApiRequest)
  private
    FPosition: Integer;
    FListName: String;
    procedure SetListName(const Value: String);
    procedure SetPosition(const Value: Integer);
  published
    property ListName: String read FListName write SetListName;
    property Position: Integer read FPosition write SetPosition;
  end;
  TOmanListPositionRespone = class(TOmanApiResponse);

  { Invokable interfaces must derive from IInvokable }
  IOrdermanAPI = interface(IInvokable)
  ['{52B1533E-F100-4A28-93B9-C45A25138AC9}']
    function OmanConnected(const Request: TOmanConnectedRequest): TOmanConnectedRespone; stdcall;
    function OmanReady(const Request: TOmanReadyRequest): TOmanReadyRespone; stdcall;
    function OmanPing(const Request: TOmanApiRequest): TOmanApiResponse; stdcall;
    function OmanRestartPos(const Request: TOmanApiRequest): TOmanApiResponse; stdcall;
  end;

  TOrdermanAPI= class(TInvokableClass, IOrdermanAPI)
  private
  public
    function OmanConnected(const Request: TOmanConnectedRequest): TOmanConnectedRespone; stdcall;
    function OmanReady(const Request: TOmanReadyRequest): TOmanReadyRespone; stdcall;
    function OmanPing(const Request: TOmanApiRequest): TOmanApiResponse; stdcall;
    function OmanRestartPos(const Request: TOmanApiRequest): TOmanApiResponse; stdcall;
  end;

function InvokeCached(SerialNumber: Integer; MethodName: String; vin: INamedVars): INamedVars;

implementation

uses VOTCP, ClassesU, Forms, UntillLogsU, Generics.Collections, SyncObjs;

var ocache: TDictionary<Integer, IVObjectProxy>;
    ocachelock: TCriticalSection;

function _GetVOClient(SerialNr: Integer): IVOClient;
var
  lTransport: IVOTransportTcpIp;
  UntillFullPath: string;
  wsConnectStr : String;
begin
  lTransport := TVOTransportTcpIp.Create;
  UntillFullPath := ExtractFilePath(Application.ExeName)  + 'Untill.exe';
  wsConnectStr := WideFormat('vo:%s/.OrdermanAPI%d', [UpperCase(UntillFullPath), SerialNr]);
  lTransport.ConnectLocal(wsConnectStr, 5);
  Result := TVOClient.Create(lTransport);
end;


function InvokeCached(SerialNumber: Integer; MethodName: String; vin: INamedVars): INamedVars;
var
  c: IVOClient;
  o : IVObjectProxy;
  bCached: boolean;
begin
  ocachelock.Enter;
  try
    if not ocache.TryGetValue(SerialNumber, o) then begin
      c := _GetVOClient(SerialNumber);
      o := c.CreateObject('TOrdermanAPIObject');
      ocache.Add(SerialNumber, o);
      bCached := false;
    end else
      bCached := true;
  finally
    ocachelock.Leave;
  end;

  try
    result := o.InvokeMethod(MethodName, vin);
  except
    on e: exception do begin
      if bCached then begin
        // remove from cache and try again
        ocachelock.Enter;
        try
          ocache.Remove(SerialNumber);
        finally
          ocachelock.Leave;
        end;
        result := InvokeCached(SerialNumber, MethodName, vin);
      end else
        raise;
    end;
  end;

end;

{ TGetArticlesResponse }


{ TOmanConnectedRequest }

procedure TOmanApiRequest.SetSerialNumber(const Value: Integer);
begin
  FSerialNumber := Value;
end;

{ TOmanConnectedRespone }

procedure TOmanConnectedRespone.SetDataDir(const Value: String);
begin
  FDataDir := Value;
end;

procedure TOmanConnectedRespone.SetDllDir(const Value: String);
begin
  FDllDir := Value;
end;

procedure TOmanConnectedRespone.SetMapsDir(const Value: String);
begin
  FMapsDir := Value;
end;

procedure TOmanConnectedRespone.SetNfcEnabled(const Value: Boolean);
begin
  FNfcEnabled := Value;
end;

procedure TOmanConnectedRespone.SetResourcesDir(const Value: String);
begin
  FResourcesDir := Value;
end;

{ TOmanClickRequest }

procedure TOmanClickRequest.SetWidgetName(const Value: String);
begin
  FWidgetName := Value;
end;

{ TOmanListPositionRequest }

procedure TOmanListPositionRequest.SetListName(const Value: String);
begin
  FListName := Value;
end;

procedure TOmanListPositionRequest.SetPosition(const Value: Integer);
begin
  FPosition := Value;
end;

function TOrdermanAPI.OmanConnected(
  const Request: TOmanConnectedRequest): TOmanConnectedRespone;
var
  AppPath: string;
  vin, vout: INamedVars;
begin
  //OrdermanLog.WriteString('TOrdermanAPI.OmanConnected');
  result := TOmanConnectedRespone.Create;
  result.ErrCode := 0;
  result.ErrMsg := '';

  // 1. Check oman is registered in database

  try
    AppPath := ExtractFilePath(Application.ExeName);
    vin := TNamedVars.Create;
    vout := InvokeCached(Request.SerialNumber, 'OmanConnected', vin);

    result.DataDir := AppPath + 'terminals\Orderman\Sol\data';
    result.ResourcesDir := AppPath + 'terminals\Orderman\Sol\resources';
    result.DllDir := AppPath + 'terminals\Orderman\Sol\formsdll';
    result.MapsDir := AppPath + 'terminals\Orderman\Sol\maps';

    if vout.GetIndexOf('nfc')>-1 then
      result.NfcEnabled := vout.ValByName['nfc'];

    if vout.GetIndexOf('needRebuild')>-1 then begin
      result.ErrCode := ERR_OMANAPI_NEED_REBUILD_RESOURCES;
      result.ErrMsg := 'Screens changed! Rebuild Orderman Resources at '+GetComputerNamePas();
    end;

  except
    on e: exception do begin
      MiscLog.WriteExceptionInfo(e, 'TOrdermanAPIImpl.OmanConnected');
      result.ErrCode := ERR_OMANAPI_DEVICE_NOT_REGISTERED;
      result.ErrMsg := e.ClassName + ': '+ e.Message;
    end;
  end;
end;

function TOrdermanAPI.OmanPing(
  const Request: TOmanApiRequest): TOmanApiResponse;
begin
  result := TOmanApiResponse.Create;
  result.ErrCode := 0;
  result.ErrMsg := '';
end;

function TOrdermanAPI.OmanReady(
  const Request: TOmanReadyRequest): TOmanReadyRespone;
var
  vin: INamedVars;
begin
  result := TOmanReadyRespone.Create;
  result.ErrCode := 0;
  result.ErrMsg := '';
  try
    vin := TNamedVars.Create;
    vin.Add('port', Request.TcpPort);
    InvokeCached(Request.SerialNumber, 'OmanReady', vin);
  except
    on e: exception do begin
      MiscLog.WriteExceptionInfo(e, 'TOrdermanAPIImpl.OmanReady');
      result.ErrCode := ERR_OMANAPI_DEVICE_NOT_REGISTERED;
      result.ErrMsg := e.ClassName + ': '+ e.Message;
    end;
  end;
end;

function TOrdermanAPI.OmanRestartPos(
  const Request: TOmanApiRequest): TOmanApiResponse;
var
  vin: INamedVars;
begin
  result := TOmanApiResponse.Create;
  result.ErrCode := 0;
  result.ErrMsg := '';
  try
    vin := TNamedVars.Create;
    InvokeCached(Request.SerialNumber, 'OmanRestartPos', vin);
  except
    on e: exception do begin
      MiscLog.WriteExceptionInfo(e, 'TOrdermanAPIImpl.OmanRestartPos');
      result.ErrCode := ERR_OMANAPI_DEVICE_NOT_REGISTERED;
      result.ErrMsg := e.ClassName + ': '+ e.Message;
    end;
  end;
end;

{ TOmanApiResponse }

procedure TOmanApiResponse.SetErrCode(const Value: Integer);
begin
  FErrCode := Value;
end;

procedure TOmanApiResponse.SetErrMsg(const Value: String);
begin
  FErrMsg := Value;
end;

{ TOmanReadyRequest }

procedure TOmanReadyRequest.SetTcpPort(const Value: Integer);
begin
  FTcpPort := Value;
end;

initialization
  InvRegistry.RegisterInterface(TypeInfo(IOrdermanAPI));
  InvRegistry.RegisterInvokableClass(TOrdermanAPI);
  ocache:=TDictionary<Integer, IVObjectProxy>.Create;
  ocachelock:=TCriticalSection.Create;

finalization
  ocache.Free;
  ocachelock.Free;

end.
