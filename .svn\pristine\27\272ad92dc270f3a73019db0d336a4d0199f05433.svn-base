unit BLRBillUtilsU;

interface
uses UntillDBU, ClassesU, CommonU, DB, MemTableU, NamedVarsU, UrlNotifierU, BLAlgU;

procedure RollbackPspTipsDirect(wt : IWTran; aid_oldbill, aid_payments, aid_newbill, aid_newpayments: Int64);
function ExcludeRecNoFromOsig(str: String): String;
function BillNextGroupID: string;
function GetFirstOrderTableArea(db : TUntillDB; aid_bill: Int64): Int64;
function GetGBCAlwaysPay(db : TUntillDB; aid_aricles: Int64): Integer;
function GetOrdDateTime(DB: TUntillDB; id_order_item: Int64): TDatetime;
function GetCardPaymentData(f: IPreparedSQLFactory; PBillPaymentId: Int64; fields: array of string; RaiseIfNotFound: Boolean = True; SkipEmpty: Boolean = False): INamedVars; overload;
procedure ChangeActiveBillPrice(PriceId: Int64;
  out TotalAmountInMainCur: Currency; out TotalAmountInCustomerCur: Currency;
  out PBillPaymentId: Int64);
procedure SendRefreshArticleQty(aid_articles : Int64 = 0);
procedure SendRefreshArticleBG;
procedure SendDataChanged;
function GetPBillNr(APBillId: Int64): String;
function GetBillNumStr(ABillId: Int64): string;
function ListIndexSort(Item1, Item2: TItemSelect): Integer;
function IsBillPrinterConfigured: boolean;

var
  iBillNextGroupID: Integer;

implementation
uses IBSQL, UntillPOSU, SysUtils, BlrBillTypesU, UntillAppU, FalconMsgFrm, OrdermanFrm;

function ListIndexSort(Item1, Item2: TItemSelect): Integer;
begin
  if Item1.tpos > Item2.tpos then
    result := 1
  else if Item1.tpos < Item2.tpos then
    result := -1
  else
    result := 0;
end;

function ExcludeRecNoFromOsig(str: String): String;
var
  p0, p1: Integer;
  c: String;
begin
  result := str;
  p0 := Pos('/recno^', str);
  while p0 > 0 do
  begin
    p1 := Pos('/recno^', str) + 7;
    c := str[p1];
    while IsNumeric(c) do
    begin
      Inc(p1);
      c := str[p1];
    end;
    Delete(str, p0, p1 - p0);
    p0 := Pos('/recno^', str);
  end;
  result := str;
end;

procedure Field2Param(param: TParam; field: TMTField);
begin
  if field.asString = '' then
  begin
    param.Clear;
    exit;
  end
  else
  begin
    param.asString := field.asString;
    exit;
  end;
end;

function BillNextGroupID: string;
begin
  Inc(iBillNextGroupID);
  result := IntToStr(iBillNextGroupID);
end;

procedure RollbackPspTipsDirect(wt : IWTran; aid_oldbill, aid_payments, aid_newbill, aid_newpayments: Int64);
var qSel, qIns: IIBSQL;
    amount : Currency;
begin
  if not assigned(wt) then exit;

  qSel := upos.Untilldb.GetPreparedIIBSQL('select sum(amount) from psp_tips '
    + ' where id_pbill=:id_pbill and id_pbill_payments=:id_pbill_payments');
  qSel.q.ParamByName('id_pbill').asInt64 := aid_oldbill;
  qSel.q.ParamByName('id_pbill_payments').asInt64 := aid_payments;
  qSel.ExecQuery;
  if qSel.fields[0].asCurrency = 0 then exit;

  amount := qSel.fields[0].asCurrency;
  qIns := wt.GetPreparedIIBSQL('insert into psp_tips(id_pbill, id_pbill_payments, amount) '
    + ' values (:id_pbill, :id_pbill_payments, :amount)');
  qIns.q.ParamByName('id_pbill').asInt64 := aid_newbill;
  qIns.q.ParamByName('id_pbill_payments').asInt64 := aid_newpayments;
  qIns.q.ParamByName('amount').asCurrency := -amount;
  qIns.ExecQuery;

end;

function GetFirstOrderTableArea(db : TUntillDB; aid_bill: Int64): Int64;
var iq: IIBSQL;
begin
  result := 0;
  iq := db.GetPreparedIIBSQL('select first 1 ID_TABLE_AREA from orders where id_bill=:id_bill order by ord_datetime');
  iq.q.Params[0].asInt64 := aid_bill;
  iq.ExecQuery;
  if not iq.eof then
    result := StrToInt64Def(iq.q.Fields[0].AsString,0);
end;

function GetGBCAlwaysPay(db : TUntillDB; aid_aricles: Int64): Integer;
var iq: IIBSQL;
begin
  result := 0;

  iq := db.GetPreparedIIBSQL('select always_pay from ARTICLE_GARBAGECOL d  where id_articles=:id_articles');
  iq.q.Params[0].asInt64 := aid_aricles;
  iq.ExecQuery;
  if not iq.eof then
    result := iq.q.Fields[0].AsInteger;
end;

function GetOrdDateTime(DB: TUntillDB; id_order_item: Int64): TDatetime;
var
  iq: IIBSQL;
begin
  result := 0;
  iq := DB.GetPreparedIIBSQL
    ('select orders.ord_datetime from orders join order_item on order_item.id_orders=orders.id where order_item.id=:id');
  iq.q.Params[0].asInt64 := id_order_item;
  iq.ExecQuery;
  if not iq.eof then
    result := iq.q.Fields[0].AsDatetime;
end;

function GetCardPaymentData(f: IPreparedSQLFactory; PBillPaymentId: Int64; fields: array of string; RaiseIfNotFound: Boolean = True; SkipEmpty: Boolean = False): INamedVars;
var
  field: String;
  iq: IIBSQL;
begin
  result := nil;
	iq := f.GetPreparedIIbSql('select first 1 * from pbill_card_payments_info where id_pbill_payments=:id_pbill_payments and coalesce(tip_approvement, 0)=1 order by id desc');
  iq.q.ParamByName('id_pbill_payments').asString := IntToStr(PBillPaymentId);
  iq.ExecQuery;
  if iq.Eof then begin // No EFT tips has been added
  	iq := f.GetPreparedIIbSql('select * from pbill_card_payments_info where id_pbill_payments=:id_pbill_payments and coalesce(tip_approvement, 0)=0');
    iq.q.ParamByName('id_pbill_payments').asString := IntToStr(PBillPaymentId);
    iq.ExecQuery;
    if iq.Eof then begin
    	if RaiseIfNotFound then
    		assert(not iq.eof)
	    else begin
  	  	result := newNv;
	      exit;
	    end;
  	end;
  end;
  result := newNv;
  for field in fields do begin
  	if SkipEmpty and (iq.fieldByName(field).IsNull or (iq.fieldByName(field).AsTrimString='')) then
    	continue;
    result.Add(field, iq.fieldByName(field).asString);
  end;
end;

procedure ChangeActiveBillPrice(PriceId: Int64;
  out TotalAmountInMainCur: Currency; out TotalAmountInCustomerCur: Currency;
  out PBillPaymentId: Int64);
begin
end;

procedure SendRefreshArticleBG;
var e: TOrderedEvent;
begin
  e:=TOrderedEvent.Create(0, '', uetUpdate);
  try
    upos.un.SendEvent(URL_ART_BG, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure SendRefreshArticleQty(aid_articles : Int64 = 0);
var e: TOrderedEvent;
begin
  e:=TOrderedEvent.Create(aid_articles, '', uetUpdate);
  try
    upos.un.SendEvent(URL_ART_QTY, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure SendDataChanged;
var
  dce:TPMDataChangedEvent;
begin
  dce := TPMDataChangedEvent.Create(0, '', uetUpdate);
  try
    UPos.Un.SendEvent(URL_BILL_CHANGED, dce);
  finally
    FreeAndNil(dce);
  end;
end;

function GetPBillNr(APBillId: Int64): String;
var iq: IIBSQL;
begin
  iq := upos.UntillDB.GetPreparedIIbSql('select number, suffix from pbill where id=:id');
  iq.q.Params[0].AsInt64 := APBillId;
  iq.ExecQuery;
  if not iq.Eof then begin
    result := iq.q.Fields[0].AsString;
    if not iq.q.Fields[1].IsNull then
      result := result + iq.q.Fields[1].AsTrimString;
  end else
    result := '';
end;

function IsBillPrinterConfigured: boolean;
var
  Qr: IIBSQL;
  Sql: string;
  Key: string;
begin
  Sql :=
    'select first 1 1' +
    '  from bill_printer_sel_avail a,' +
    '       bill_printer_selections s,' +
    '       bill_printer_sel_items i,' +
    '       printers p' +
    ' where i.id_bill_printer_selections = s.id' +
    '   and a.id_bill_printer_selections = s.id' +
    '   and p.id = i.id_printers' +
    '   and i.is_active = 1' +
    '   and a.is_active = 1' +
    '   and s.is_active = 1' +
    '   and p.is_active = 1';
  case RunMode of
    armFalcon: begin
      Sql := Sql + ' and a.id_falcon_terminals = (select first 1 id from falcon_terminals where is_active = 1 and upper(ip) = upper(:key))';
      Key := FalconMsgForm.FalconComm.Address;
    end;
    armOrderman: begin
      Sql := Sql + ' and a.id_ordermans = (select first 1 id from ordermans where is_active = 1 and serial = :key)';
      Key := IntToStr(OrdermanForm.SerialNumber);
    end;
    else begin
      Sql := Sql + ' and a.id_computers = (select first 1 id from computers where comp_active = 1 and upper(name) = upper(:key))';
      Key := upos.POSComputerName;
    end;
  end;
  Qr := upos.UntillDB.GetPreparedIIbSql(Sql);
  Qr.q.ParamByName('key').AsString := Key;

  Qr.ExecQuery;
  Result := not Qr.Eof;
end;

function GetBillNumStr(ABillId: Int64): string;
var
  Qr: IIBSQL;
begin
  Qr := upos.UntillDB.GetPreparedIIbSql(
    'select number' +
    '  from bill' +
    ' where id = :id'
  );
  Qr.q.Params[0].AsInt64 := ABillId;
  Qr.ExecQuery;
  if not Qr.Eof then
    result := Qr.q.Fields[0].AsString
  else
    result := '';
end;

end.
