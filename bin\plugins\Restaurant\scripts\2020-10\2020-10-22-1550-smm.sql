set term !! ;
create or alter procedure fix_discount_reason_guids
as 
  declare variable server_no integer;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;
  update discount_reasons set guid=uuid_to_char(gen_uuid());
  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_discount_reason_guids to untilluser
!!
commit
!!
execute procedure fix_discount_reason_guids
!!
commit
!!
set term ; !!
