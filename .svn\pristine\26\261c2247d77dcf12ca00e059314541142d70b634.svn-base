unit BLRAlgArticleManageU;

interface

uses
  Classes, BLAlgU, BLRAlgU, UntillDBU, PosAlgU, DB,UrlNotifierU,
  RestaurantPluginU, BLRMainU, SysUtils, UntillPosU, UntillAppU, DateUtils,
  BLSysU, RestaurantCashDatasetsU, SystemDataProviderU, StockPurchaseOrderU,
  DBTableU, UntillDatasetsU;

const
  EVENT_INFO_UPDATE_EDIT_ART_LIST    : string = 'UpdateEditArtList';
  EVENT_INFO_UPDATE_EDIT_ART_LIST_EX : string = 'UpdateEditArtListEx';

  EVENT_INFO_UPDATE_EDIT_SA_LIST     : string = 'UpdateEditSAList';
  EVENT_INFO_UPDATE_EDIT_SA_LIST_EX  : string = 'UpdateEditSAListEx';

  EVENT_INFO_UPDATE_EDIT_DEP_LIST    : string = 'UpdateEditDepList';
  EVENT_INFO_UPDATE_EDIT_DEP_LIST_EX : string = 'UpdateEditDepListEx';

  EVENT_INFO_CLEAR_DEP_LIST          : string = 'ClearDepList';
  EVENT_INFO_CLEAR_ART_LIST          : string = 'ClearArtList';
  EVENT_INFO_CLEAR_SA_SELECTION      : string = 'ClearSASelection';

  URL_ART_PRICE                      : string = 'URL_ART_PRICE';

type
  TEditArticleState = (easNone, easDisable, easReorder, easPrice, easSalesUm);
  TReorderMode = (rmRelative, rmAbsolute, rmExchange);
  TSalesUMStep = (sumsSup, sumsIng, sumsRec, sumsUM);

  TBLRMsgStockCreatePO = class (TBLMessage);
  TBLRMsgNeedDisableArticles  = class(TBLMessage);
  TBLRMsgNeedArticlePrice     = class(TBLMessage);
  TBLRMsgNeedArticlesReorder  = class(TBLMessage);
  TBLRMsgSwitchReorderMode    = class(TBLMessage);
  TBLRMsgResetSalesArea       = class(TBLMessage);
  TBLRMsgNeedArticlesSalesUM  = class(TBLMessage);
  TBLRMsgSwitchArtIngRec      = class(TBLMessage);

  TBLRMsgIngCat              = class (TBLMessage)
  private
    fid: int64;
  published
    property id: int64 read Fid write Fid;
  end;

  TBLRMsgSendUM               = class(TBLMessage)
  private
    fid : Int64;
    procedure Setid(const Value: Int64);
  published
    property id : Int64 read Fid write Setid;
  end;

  TBLRMsgRecipe   = class(TBLMessage)
  private
    fid : Int64;
  published
    property id : Int64 read Fid write fid;
  end;

  TBLRMsgResetItemOrder    = class(TBLMessage)
  private
    FResetType : Integer;
  published
    property ResetType : Integer read FResetType write FResetType;
  end;
  TBLRMsgResetArticleOrder    = class(TBLRMsgResetItemOrder);

  TBLRMsgArtOpt    = class(TBLMessage)
  private
    Fid_option : Int64;
    Fopt_type  : Integer;
    procedure Setid_option(const Value: Int64);
    procedure Setopt_type(const Value: Integer);
  published
    property id_option : Int64 read Fid_option write Setid_option;
    property opt_type  : Integer read Fopt_type write Setopt_type;
  end;

  TPMReorderArticleEvent = class(TUrlEvent)
  private
    FSrcId: Int64;
    FDestId: Int64;
    FOffset: Integer;
  public
    property SrcId: Int64 read FSrcId write FSrcId;
    property DestId: Int64 read FDestId write FDestId;
    property Offset: Integer read FOffset write FOffset;
  end;

  TBLRAlgArticleManage   = class (TBLAlgStep)
  private
    FStrFilter   : String;
    FFilter      : boolean;
    FReorderMode : TReorderMode;
    FCurState : TEditArticleState;
    procedure DoEnableArticle(id_article : Int64);
    procedure ResetDatasets;
    procedure RefreshDataset(id_article : Int64);
    procedure SetCurState(const Value: TEditArticleState);
    procedure ResetArticleOrder;
    procedure ResetArticleOrderAll;
    function GetFirstPriceID: Int64;
    procedure SendReorderArticle(ASrcId, ADestId: Int64; AOffset: Integer);
  public
    property   StrFilter   : String read FStrFilter;
    property   Filter      : boolean read FFilter;
    property   ReorderMode : TReorderMode read FReorderMode;
    property   CurState : TEditArticleState read FCurState write SetCurState;
    procedure  OnChildFinished(child: TBLAlgStep); override;
    class function GetStaticGuid: string; override;
    class procedure AddArticleAbsentSA(wt: IWTran; aId_art: Int64; aId_dep: Int64; aDisabled: Integer);
    class procedure DelArticleUnavailableSA(wt: IWTran; aId_art: Int64);
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep); override;
    function    GetHint: widestring; override;
    function    CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure   ProcessMessage(msg: TBLMessage); override;
    procedure   OnCancel; override;
    procedure   OnOK; override;
  end;

  TBLRAlgDepManage   = class (TBLAlgStep)
  private
    FReorderMode : TReorderMode;
    procedure RefreshDataset(id_dep : Int64);
    procedure ResetDepOrder;
    procedure ResetDepOrderAll;
  public
    property   ReorderMode : TReorderMode read FReorderMode;
    procedure  OnChildFinished(child: TBLAlgStep); override;
    class function GetStaticGuid: string; override;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep); override;
    function    GetHint: widestring; override;
    function    CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure   ProcessMessage(msg: TBLMessage); override;
    procedure   OnCancel; override;
    procedure   OnOK; override;
  end;

  TBLRChangeArticlePrice = class(TBLSGetNumberValue)
  private
    fid_articles : Int64;
    fid_prices   : Int64;
    Fid_currency : Int64;
    fprice       : Currency;
    fid_option   : Int64;
    fopt_type    : Integer;
    procedure Setid_articles(const Value: Int64);
    procedure Setprice(const Value: Currency);
    procedure Setid_currency(const Value: Int64);
    procedure Setid_prices(const Value: Int64);
    procedure UpdatePrices;
    function GetBOArticlePrice(id_article, id_price, id_option: Int64; opt_type : Integer; var price: Currency;
      var valuta: Int64): boolean;
    procedure SendUpdateOptions;
    procedure Setid_option(const Value: Int64);
    procedure Setopt_type(const Value: Integer);
    procedure RefreshArtPrice(id_article: Int64);
  public
    procedure InitStep(_id_articles, _id_prices, _id_option : Int64; _opt_type : Integer);
    property id_option   : Int64 read Fid_option write Setid_option;
    property opt_type    : Integer read Fopt_type write Setopt_type;
    property id_prices : Int64 read Fid_prices write Setid_prices;
    property id_currency : Int64 read Fid_currency write Setid_currency;
    property price : Currency read Fprice write Setprice;
    property id_articles : Int64 read Fid_articles write Setid_articles;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    procedure OnOK; override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep); reintroduce;
    function   CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure  ProcessMessage(msg: TBLMessage); override;
  end;

  TBLRChangeArticleSalesUM = class(TBLAlgStep)
  private
    Fid_ingcat: Int64;
    fid_supp  : Int64;
    fid_inv   : Int64;
    fid_rec   : Int64;
    fid_articles : Int64;
    Fid_um: Int64;
    FumStep: TSalesUMStep;
    procedure Setid_articles(const Value: Int64);
    function  GetArticleSalesUM(id_articles : Int64) : Int64;
    procedure SendUpdateUM;
    procedure SendUpdateStock;
    procedure SaveNewUM;
    procedure InitStep;
    procedure SaveCleanUM;
    procedure Setid_inv(const Value: Int64);
    procedure Setid_rec(const Value: Int64);
    procedure Setid_supp(const Value: Int64);
  public
    property  id_ingcat: Int64 read fid_ingcat;
    property  id_supp : Int64 read Fid_supp write Setid_supp;
    property  id_inv  : Int64 read Fid_inv write Setid_inv;
    property  id_rec  : Int64 read Fid_rec write Setid_rec;
    property umStep: TSalesUMStep read FumStep write fumStep;
    property id_um : Int64 read Fid_um write fid_um;
    property id_articles : Int64 read Fid_articles write Setid_articles;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    procedure OnOK; override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep; _id_articles : Int64); reintroduce;
    function   CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure  ProcessMessage(msg: TBLMessage); override;
  end;

  TBLRSelectArticle = class(TBLRSelectRestItem)
  protected
    class function DatasetParamClass : TUntillDbItemsListDatasetParamClass; override;
  public
    class function GetStaticGuid: string; override;
    function GetItemName(aid_art : Int64): String; override;
    function   GetHint: widestring; override;
    function   CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep); override;
  end;

  TBLRSelectDepartment = class(TBLRSelectRestItem)
  protected
    class function DatasetParamClass : TUntillDbItemsListDatasetParamClass; override;
  public
    class function GetStaticGuid: string; override;
    function GetItemName(aid_art : Int64): String; override;
    function   GetHint: widestring; override;
    function   CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure  ProcessMessage(msg: TBLMessage); override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep); override;
  end;

  TBLRSelectSalesArea = class(TBLRSelectRestItem)
  protected
    class function DatasetParamClass : TUntillDbItemsListDatasetParamClass; override;
  public
    class function GetStaticGuid: string; override;
    function   GetHint: widestring; override;
    function   CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure  ProcessMessage(msg: TBLMessage); override;
    function GetItemName(aid_art : Int64): String; override;
  end;

  procedure DummyUpdateSalesArea;

implementation

uses
  BLRAlgStockBalanceU, StockManagerU, StockEntityU, BLRReservationU, PosAsyncU,
  RestaurantDataSetsU, Generics.Collections, ClassesU, RestaurantDatasetParamsU,
  BlrMainAsyncOpsU, BLRBillUtilsU, UBLProxyU, UBLU, CacheSalesAreaU;

{ TBLRAlgArticleManage }

procedure SendUpdateSa;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, EVENT_INFO_UPDATE_EDIT_SA_LIST, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
  UntillApp.AnalyseChangeDSFlags;
end;

// Procedure executes update of all records in table Sales area
procedure DummyUpdateSalesArea;
var uTable : DBTableU.TDBTableUpdater;
begin
  // Prepare table data for update
  uTable := DBTableU.TDBTableUpdater.Create('sales_area', 'id');
  try
    // Execute update procedure from common unit "DBTablePOSU"
    uTable.ExecuteDummyUpdate(upos.UntillDB);
  finally
  // Kill table data
    FreeAndNil(uTable);
  end;
end;

procedure SendClearSASelection;
var
  e: TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, EVENT_INFO_CLEAR_SA_SELECTION, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure SendClearDepList;
var
  e: TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, EVENT_INFO_CLEAR_DEP_LIST, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure SendUpdateEditArt;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, EVENT_INFO_UPDATE_EDIT_ART_LIST, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure SendClearArtList;
var
  e: TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, EVENT_INFO_CLEAR_ART_LIST, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

function TBLRAlgArticleManage.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if FFilter then begin
    if msg = TBLMessageInput then Exit;
    if msg = TBLMessageBackSpace then Exit;
    if msg = TBLMessageClear then Exit;
  end;
  if msg = TBLRMsgStockCreatePO then exit;
  if msg = TBLRMsgNeedSearchArticle then Exit;
  if msg = TBLRMsgResetArticleOrder then Exit;
  if msg = TBLRMsgNeedArticlesSalesUM then Exit;
  if msg = TBLRMsgNeedArticlesReorder then Exit;
  if msg = TBLRMsgSwitchReorderMode then Exit;
  if msg = TBLRMsgSalesArea then Exit;
  if msg = TBLRMsgNeedArticlePrice then Exit;
  if msg = TBLRMsgDepartmentMessage then Exit;
  if msg = TBLMessageCancel then Exit;
  if msg = TBLMessageOK then Exit;
  if msg = TBLRMsgArticle then Exit;
  if msg = TBLRMsgNeedDisableArticles then Exit;
  if msg = TBLRMsgResetSalesArea then Exit;
  if msg = TBLMsgReorderButtonPosition then Exit;
  Result := false;
end;

constructor TBLRAlgArticleManage.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  FStrFilter   := '';
  FFilter      := false;
  FCurState    := easNone;
  FReorderMode := rmRelative;
  ResetDatasets;
end;

procedure TBLRAlgArticleManage.DoEnableArticle(id_article : Int64);
var iq, iqi: IIBSQL;
    wt : IWtran;
    sqlstr : String;
    newval : integer;
    bNeedDelete : boolean;
begin
  if id_article<=0 then exit;
  if (RestaurantProps.CurrentDepartment=0) then exit;

  newval := 0;
  wt := upos.UntillDB.getWTran;
  sqlstr :=
    'select min(coalesce(disabled, 0)), count(*)' +
    '  from article_position_ex' +
    ' where id_articles = :id_articles';
  if RestaurantProps.CurrentSalesArea>0 then
    sqlstr := sqlstr + ' and id_sales_area = :id_sales_area ';
  iq := wt.GetPreparedIIbSql(sqlstr);
  iq.q.ParamByName('id_articles').asInt64   := id_article;
  if (RestaurantProps.CurrentSalesArea > 0) then
    iq.q.ParamByName('id_sales_area').asInt64 := RestaurantProps.CurrentSalesArea;
  iq.ExecQuery;
  if (iq.q.fields[1].AsInteger = 0) and (RestaurantProps.CurrentSalesArea > 0) then begin
    sqlstr :=
      'insert' +
      '  into article_position_ex(id_sales_area, id_department, id_articles, pos, disabled)' +
      'values (:id_sales_area, :id_department, :id_articles, 0, 0)';
    iqi := wt.GetPreparedIIbSql(sqlstr);
    iqi.q.ParamByName('id_sales_area').asInt64 := RestaurantProps.CurrentSalesArea;
    iqi.q.ParamByName('id_department').asInt64 := RestaurantProps.CurrentDepartment;
    iqi.q.ParamByName('id_articles').asInt64 := id_article;
    iqi.ExecQuery;
  end else begin
    if iq.q.fields[0].AsInteger = 0 then
      newval := 1
    else
      newval := 0;
    sqlstr :=
      'update article_position_ex' +
      '   set disabled = :newval' +
      ' where id_articles = :id_articles';
    if RestaurantProps.CurrentSalesArea>0 then
      sqlstr := sqlstr + ' and id_sales_area = :id_sales_area ';
    iq := wt.GetPreparedIIbSql(sqlstr);
    iq.q.ParamByName('newval').asInteger    := newval;
    iq.q.ParamByName('id_articles').asInt64 := id_article;
    if RestaurantProps.CurrentSalesArea>0 then
      iq.q.ParamByName('id_sales_area').asInt64 := RestaurantProps.CurrentSalesArea;
    iq.ExecQuery;
  end;
  AddArticleAbsentSA(wt, id_article, RestaurantProps.CurrentDepartment, 0);
  wt.commit;
  PosAsyncTasksThread.AddTask(TAsyncDisableArtEventTask.Create(id_article, RestaurantProps.CurrentDepartment, RestaurantProps.CurrentSalesArea, newval=0));

  CashArticlePOSDataset.IndexName :='artidx';
  if  CashArticlePOSDataset.FindKey([IntToStr(id_article)]) then begin
    while not CashArticlePOSDataset.eof do begin
      bNeedDelete := false;
      if RestaurantProps.CurrentSalesArea=0 then
         bNeedDelete := true
      else begin
        if RestaurantProps.CurrentSalesArea=StrToInt64Def(CashArticlePOSDataset.FieldByName('id_sa').AsString,0) then
          bNeedDelete := true
      end;
      if bNeedDelete then begin
        CashArticlePOSDataset.edit;
        CashArticlePOSDataset.FieldByName('disabled').AsInteger := 1-Abs(CashArticlePOSDataset.FieldByName('disabled').AsInteger);
        CashArticlePOSDataset.FieldByName('dtupdate').AsDatetime := now;
        CashArticlePOSDataset.post;
      end;
      CashArticlePOSDataset.Next;
      if StrToInt64Def(CashArticlePOSDataset.FieldByName('id_articles').AsString,0)<>id_article then exit;
    end;
  end;
end;

class procedure TBLRAlgArticleManage.AddArticleAbsentSA(wt: IWTran; aId_art: Int64; aId_dep: Int64; aDisabled: Integer);
var
  qr: IIBSQL;
begin
  assert(assigned(wt));
  qr := wt.GetPreparedIIbSQL(
    'insert' +
    '  into article_position_ex(id_sales_area, id_department, id_articles, pos, disabled) ' +
    'select aa.id_sales_area, :id_department, aa.id_articles, aa.sequence, :disabled' +
    '  from article_available aa' +
    ' where id_articles = :id_articles' +
    '   and coalesce(aa.is_active, 0) = 1' +
    '   and aa.id_sales_area not in (' +
    '         select apx.id_sales_area' +
    '           from article_position_ex apx' +
    '          where apx.id_articles = aa.id_articles' +
    '       )'
  );
  qr.q.ParamByName('id_articles').asInt64 := aId_art;
  qr.q.ParamByName('id_department').asInt64 := aId_dep;
  qr.q.ParamByName('disabled').asInteger := aDisabled;
  qr.ExecQuery;
end;

class procedure TBLRAlgArticleManage.DelArticleUnavailableSA(wt: IWTran; aId_art: Int64);
var
  qr: IIBSQL;
begin
  assert(assigned(wt));
  qr := wt.GetPreparedIIbSQL(
    'delete' +
    '  from article_position_ex ap' +
    ' where ap.id_articles = :id_articles' +
    '   and not exists (' +
    '         select null' +
    '           from article_available aa' +
    '          where ap.id_articles = aa.id_articles' +
    '            and ap.id_sales_area = aa.id_sales_area' +
    '            and coalesce(aa.is_active, 0) = 1' +
    '       )'
  );
  qr.q.ParamByName('id_articles').asInt64 := aId_art;
  qr.ExecQuery;
end;

function TBLRAlgArticleManage.GetHint: widestring;
begin
  if FCurState = easNone then
    Result := plugin.TranslatePOS('BLRAlgU', 'Please choose edit mode...', 'State hint')
  else if FCurState = easDisable then
    Result := plugin.TranslatePOS('BLRAlgU', 'Enable/Disable articles', 'State hint')
  else if FCurState = easReorder then begin
    if FreorderMode = rmAbsolute then
      Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange articles (absolute mode)', 'State hint')
    else if FreorderMode = rmRelative then
      Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange articles (relative mode)', 'State hint')
    else if FreorderMode = rmExchange then
      Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange articles (exchange mode)', 'State hint');
  end else if FCurState = easPrice then
    Result := plugin.TranslatePOS('BLRAlgU', 'Change article price', 'State hint')
  else if FCurState = easSalesUM then
    Result := plugin.TranslatePOS('BLRAlgU', 'Choose article to change sales U/M', 'State hint');
end;

class function TBLRAlgArticleManage.GetStaticGuid: string;
begin
    result := '5bc05f4a-f577-47c8-a7fb-f7c195cf36c4';
end;

procedure TBLRAlgArticleManage.OnCancel;
begin
  if FFilter then begin
    UntillApp.ClearChangeFlags;
    FStrFilter := '';
    FFilter    := false;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end else
    // Update DB table "sales_area" in order to all RegisterWatchingTables datasets referesh their Data on other PCs
    DummyUpdateSalesArea;
  inherited;
end;

procedure TBLRAlgArticleManage.OnChildFinished(child: TBLAlgStep);
begin
  inherited;
  if child is TBLRChangeArticleSalesUM then begin
    FreeOnLastChild := false;
    exit;
  end;
  if child is TBLRChangeArticlePrice then begin
    FreeOnLastChild := false;
    if child.Result <> blCancel then
      FCurState := easDisable;
    exit;
  end;
end;

procedure TBLRAlgArticleManage.OnOK;
begin
  if FFilter then begin
    UntillApp.ClearChangeFlags;
    FStrFilter := '';
    FFilter    := false;
    exit;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
  end else
    // Update DB table "sales_area" in order to all RegisterWatchingTables datasets referesh their Data on other PCs
    DummyUpdateSalesArea;
  inherited;
end;

procedure TBLRAlgArticleManage.SetCurState(const Value: TEditArticleState);
begin
  FCurState := Value;
end;

procedure TBLRAlgArticleManage.ResetDatasets;
begin
  RestaurantProps.CurrentSalesArea := 0;
  RestaurantProps.CurrentDepartment := 0;
  RestaurantProps.CurrentArticle := 0;
  SendClearDepList;
  SendClearArtList;
  RefreshDataset(0);
  SendClearSASelection;
end;

procedure TBLRAlgArticleManage.RefreshDataset(id_article : Int64);
var evCheck : TPMAnalyseChangeFlags;
    iFlag   : Int64;
    info    : String;
begin
  iFlag := 0;
  if FCurState in [ easNone ] then
    iFlag := 1;
  if id_article > 0 then begin
    info := EVENT_INFO_UPDATE_EDIT_ART_LIST_EX;
    iFlag := id_article;
  end else
    info := EVENT_INFO_UPDATE_EDIT_ART_LIST;
  evCheck := TPMAnalyseChangeFlags.Create(iFlag, info, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, evCheck);
  finally
    evCheck.Free;
  end;
end;

procedure TBLRAlgArticleManage.ResetArticleOrder;
var iq : IIBSQL;
    wt : IWtran;
begin
  if (RestaurantProps.CurrentDepartment=0) or (RestaurantProps.CurrentSalesArea=0) then exit;
  wt := upos.UntillDB.getWTran;
  iq := wt.GetPreparedIIbSql('execute procedure UPDATE_POS_ARTICLE_POSITION(:id_dep, :id_sa)');
  iq.q.Params[0].asInt64 := RestaurantProps.CurrentDepartment;
  iq.q.Params[1].asInt64 := RestaurantProps.CurrentSalesArea;
  iq.ExecQuery;
  wt.commit;
  CashMainDatasetsNoArt;
  CashArticlePOSDatasets;
  RefreshDataset(0);
end;

procedure TBLRAlgArticleManage.ResetArticleOrderAll;
var iq : IIBSQL;
    wt : IWtran;
begin
  wt := upos.UntillDB.getWTran;
  iq := wt.GetPreparedIIbSql('execute procedure UPDATE_POS_ARTICLE_POSITION_All');
  iq.ExecQuery;
  wt.commit;
  CashMainDatasetsNoArt;
  CashArticlePOSDatasets;
  RefreshDataset(0);
end;

procedure TBLRAlgArticleManage.ProcessMessage(msg: TBLMessage);
var priceStep : TBLRChangeArticlePrice;
    umStep    : TBLRChangeArticleSalesUM;
    id_price  : Int64;
begin
  if msg is TBLRMsgStockCreatePO then begin
    CreateReorderPO;
    exit;
  end;
  if msg is TBLMessageBackSpace then begin
    UntillApp.ClearChangeFlags;
    if length(FStrFilter) > 0 then
      delete(FStrFilter, Length(FStrFilter),1);
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLMessageClear then begin
    UntillApp.ClearChangeFlags;
    FStrFilter := '';
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLMessageInput then begin
    UntillApp.ClearChangeFlags;
    FStrFilter := FStrFilter + TBLMessageInput(msg).s;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgNeedSearchArticle) and (FCurState <> easReorder) then begin
    FStrFilter   := '';
    FFilter      := true;
    exit;
  end;
  if (msg is TBLRMsgResetSalesArea) and (FCurState <> easReorder) then begin
    UntillApp.ClearChangeFlags;
    RestaurantProps.CurrentSalesArea := 0;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgResetArticleOrder) and (FCurState = easReorder) then begin
    UntillApp.ClearChangeFlags;
    if TBLRMsgResetArticleOrder(msg).ResetType = 0 then
      ResetArticleOrder
    else
      ResetArticleOrderAll;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgSwitchReorderMode) and (FCurState = easReorder) then begin
    UntillApp.ClearChangeFlags;
    if FReorderMode = rmRelative then
      FReorderMode := rmAbsolute
    else if FReorderMode = rmAbsolute then
      FReorderMode := rmExchange
    else if FReorderMode = rmExchange then
      FReorderMode := rmRelative;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgSalesArea) then begin
    RestaurantProps.CurrentSalesArea := TBLRMsgSalesArea(msg).id;
    UntillApp.ClearChangeFlags;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLRMsgNeedArticlePrice then begin
    UntillApp.ClearChangeFlags;
    FCurState := easPrice;
    SendUpdateSa;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLRMsgNeedArticlesSalesUM then begin
    UntillApp.ClearChangeFlags;
    FCurState := easSalesUm;
    SendUpdateSa;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLRMsgNeedArticlesReorder then begin
    UntillApp.ClearChangeFlags;
    FCurState := easReorder;
    SendUpdateSa;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLRMsgDepartmentMessage then begin
    RestaurantProps.CurrentDepartment := TBLRMsgDepartmentMessage(msg).id_department;
    RefreshDataset(0);
    exit;
  end;
  if msg is TBLRMsgNeedDisableArticles then begin
    UntillApp.ClearChangeFlags;
    fCurState := easDisable;
    ResetDatasets;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLRMsgArticle then begin
    if fCurState = easDisable then begin
      DoEnableArticle(TBLRMsgArticle(msg).id);
      RefreshDataset(TBLRMsgArticle(msg).id);
    end else if FCurState = easPrice then begin
      priceStep := TBLRChangeArticlePrice.Create(Self.bla, Self);
      id_price := GetFirstPriceID;
      priceStep.InitStep(TBLRMsgArticle(msg).id,id_price, 0, 0);
      priceStep.SendUpdateOptions;
      RefreshSAPrices;
    end else if FCurState = easSalesUM then begin
      umStep := TBLRChangeArticleSalesUM.Create(Self.bla, Self, TBLRMsgArticle(msg).id);
      umStep.SendUpdateUM;
    end;
    exit;
  end;
  if msg is TBLMsgReorderButtonPosition then begin
    SendReorderArticle(
      TBLMsgReorderButtonPosition(msg).SrcId,
      TBLMsgReorderButtonPosition(msg).DestId,
      TBLMsgReorderButtonPosition(msg).Offset
    );
    exit;
  end;
  inherited;
end;

function TBLRAlgArticleManage.GetFirstPriceID : Int64;
var iq : IIBSQL;
begin
  result := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select first 1 id from prices where is_active=1 order by number');
  iq.ExecQuery;
  if iq.eof then exit;

  result := StrToInt64Def(iq.Fields[0].AsString,0);
end;

procedure TBLRAlgArticleManage.SendReorderArticle(ASrcId, ADestId: Int64; AOffset: Integer);
var
  e: TPMReorderArticleEvent;
begin
  e := TPMReorderArticleEvent.Create(0, '', uetOther);
  try
    e.SrcId := ASrcId;
    e.DestId := ADestId;
    e.Offset := AOffset;
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
  UntillApp.AnalyseChangeDSFlags;
end;


{ TBLRChangeArticlePrice }

function TBLRChangeArticlePrice.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgArtOpt then exit;
  if msg = TBLRMsgNeedDisableArticles then exit;
  if msg = TBLRMsgArticle then exit;
  if msg = TBLRMsgPriceLevel then exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRChangeArticlePrice.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited  Create(ABla, AParent, true, '', '');
  fid_articles := 0;
  fid_prices   := 0;
  fid_option   := 0;
  fopt_type    := 0;
  fprice       := 0;
  fid_currency := upos.MainCurrency.Id;
end;

function TBLRChangeArticlePrice.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Change article price', 'State hint');
end;

class function TBLRChangeArticlePrice.GetStaticGuid: string;
begin
  result := '{02935dfe-e92b-4d53-8cbf-aa43f701770e}';
end;

procedure TBLRChangeArticlePrice.InitStep(_id_articles, _id_prices,
  _id_option : Int64; _opt_type : Integer);
begin
  fid_articles := _id_articles;
  fid_prices   := _id_prices;
  fid_option   := _id_option;
  fopt_type    := _opt_type;
  if fid_prices>0 then
    self.UpdatePrices;
end;

procedure TBLRChangeArticlePrice.OnOK;
var q, iq : IIBSQL;
    wt : IWtran;
    arts : Array of Int64;
begin
  if fid_articles <=0 then exit;
  if fid_prices   <=0 then exit;
  if fid_currency <=0 then
    fid_currency := upos.MainCurrency.Id;
  wt := upos.UntillDB.getWTran;
  q := wt.GetPreparedIIbSql('update ARTICLES set id=id where id=:id_art ');
  q.q.ParamByName('id_art').asInt64 := fid_articles;
  q.ExecQuery;
  if (fopt_type=0) then begin
    iq := wt.GetPreparedIIbSql('select count(*) from ARTICLE_PRICES where id_articles=:id_art and id_prices=:id_pr ');
    iq.q.ParamByName('id_art').asInt64 := fid_articles;
    iq.q.ParamByName('id_pr').asInt64  := fid_prices;
    iq.ExecQuery;
    if iq.q.fields[0].asInteger>0 then begin
      q := wt.GetPreparedIIbSql('update ARTICLE_PRICES set price=:price, id_currency=:id_cur where id_articles=:id_art and id_prices=:id_pr ');
      q.q.ParamByName('id_art').asInt64 := fid_articles;
      q.q.ParamByName('id_pr').asInt64  := fid_prices;
      q.q.ParamByName('price').asCurrency  := fprice;
      q.q.ParamByName('id_cur').asInt64 := fid_currency;
      q.ExecQuery;
    end;
  end else if (fopt_type=1) and (fid_option>0) then begin
    iq := wt.GetPreparedIIbSql('select OPTION_ARTICLE_PRICES.ID_OPTION_ARTICLE from OPTION_ARTICLE_PRICES '
      + ' join OPTION_ARTICLE on OPTION_ARTICLE.id=OPTION_ARTICLE_PRICES.ID_OPTION_ARTICLE and OPTION_ARTICLE.is_active=1 '
      + ' and  OPTION_ARTICLE.id_options=:id_options '
      + ' where id_articles=:id_art and id_prices=:id_pr ');
    iq.q.ParamByName('id_art').asInt64 := fid_articles;
    iq.q.ParamByName('id_options').asInt64 := fid_option;
    iq.q.ParamByName('id_pr').asInt64  := fid_prices;
    iq.ExecQuery;
    if StrToInt64Def(iq.q.fields[0].AsString,0)>0 then begin
      q := wt.GetPreparedIIbSql('update OPTION_ARTICLE_PRICES set price=:price, id_currency=:id_cur '
        + ' where ID_OPTION_ARTICLE=:id_oap and id_prices=:id_pr ');
      q.q.ParamByName('id_oap').asInt64 := StrToInt64Def(iq.q.fields[0].AsString,0);
      q.q.ParamByName('id_pr').asInt64  := fid_prices;
      q.q.ParamByName('price').asCurrency  := fprice;
      q.q.ParamByName('id_cur').asInt64 := fid_currency;
      q.ExecQuery;
    end;
  end else if (fopt_type=2) and (fid_option>0) then begin
    iq := wt.GetPreparedIIbSql('select BONUS_GROUPS_ARTICLES_PRICES.ID_BONUS_GROUPS_ARTICLES from BONUS_GROUPS_ARTICLES_PRICES '
      + ' join BONUS_GROUPS_ARTICLES on BONUS_GROUPS_ARTICLES.id=BONUS_GROUPS_ARTICLES_PRICES.ID_BONUS_GROUPS_ARTICLES and BONUS_GROUPS_ARTICLES.is_active=1 '
      + ' and  BONUS_GROUPS_ARTICLES.ID_BONUS_GROUPS=:ID_BONUS_GROUPS '
      + ' where id_articles=:id_art and id_prices=:id_pr ');
    iq.q.ParamByName('id_art').asInt64 := fid_articles;
    iq.q.ParamByName('ID_BONUS_GROUPS').asInt64 := fid_option;
    iq.q.ParamByName('id_pr').asInt64  := fid_prices;
    iq.ExecQuery;
    if StrToInt64Def(iq.q.fields[0].AsString,0)>0 then begin
      q := wt.GetPreparedIIbSql('update BONUS_GROUPS_ARTICLES_PRICES set price=:price, id_currency=:id_cur '
        + ' where ID_BONUS_GROUPS_ARTICLES=:id_oap and id_prices=:id_pr');
      q.q.ParamByName('id_oap').asInt64 := StrToInt64Def(iq.q.fields[0].AsString,0);
      q.q.ParamByName('id_pr').asInt64  := fid_prices;
      q.q.ParamByName('price').asCurrency  := fprice;
      q.q.ParamByName('id_cur').asInt64 := fid_currency;
      q.ExecQuery;
    end;
  end;
  wt.commit;
  setlength(arts,1);
  arts[0] := fid_articles;
  CashArticlePrices( arts );
  if fid_articles > 0 then
    RefreshArtPrice(fid_articles);
end;

procedure TBLRChangeArticlePrice.RefreshArtPrice(id_article : Int64);
var evCheck : TPMAnalyseChangeFlags;
    iFlag   : Int64;
    info    : String;
begin
  info := EVENT_INFO_UPDATE_EDIT_ART_LIST_EX;
  iFlag := id_article;
  evCheck := TPMAnalyseChangeFlags.Create(iFlag, info, uetUpdate);
  try
    UPos.Un.SendEvent(URL_ART_PRICE, evCheck);
  finally
    evCheck.Free;
  end;
end;

procedure TBLRChangeArticlePrice.ProcessMessage(msg: TBLMessage);
begin
  inherited;
  if msg is TBLRMsgArtOpt then begin
    if (fid_articles=0) then exit;
    InitStep(fid_articles, fid_prices,
      TBLRMsgArtOpt(msg).id_option, TBLRMsgArtOpt(msg).opt_type);
    exit;
  end;
  if msg is TBLRMsgNeedDisableArticles then begin
    Self.Free;
    exit;
  end;
  if (msg is TBLMessageInput) or (msg is TBLMessageBackspace) then begin
    fprice := StrToCurrDef(SystemDataProvider.NumericValue,0);
    exit;
  end;
  if msg is TBLRMsgArticle then begin
    InitStep(TBLRMsgArticle(msg).id, fid_prices, 0, 0);
    SendUpdateOptions;
    exit;
  end;
  if msg is TBLRMsgPriceLevel then begin
    id_prices := StrToInt64Def(TBLRMsgPriceLevel(msg).id,0);
    exit;
  end;
end;

procedure TBLRChangeArticlePrice.SendUpdateOptions;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, 'UpdateEditArticleOptions', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
  UntillApp.AnalyseChangeDSFlags;
end;

procedure TBLRChangeArticlePrice.Setid_articles(const Value: Int64);
begin
  Fid_articles := Value;
end;

procedure TBLRChangeArticlePrice.Setid_currency(const Value: Int64);
begin
  Fid_currency := Value;
end;

procedure TBLRChangeArticlePrice.Setid_option(const Value: Int64);
begin
  Fid_option := Value;
end;

procedure TBLRChangeArticlePrice.Setid_prices(const Value: Int64);
begin
  untillapp.ClearChangeFlags;
  Fid_prices := Value;
  UpdatePrices;
  untillapp.AnalyseChangeFlags;
end;

procedure TBLRChangeArticlePrice.Setopt_type(const Value: Integer);
begin
  Fopt_type := Value;
end;

procedure TBLRChangeArticlePrice.Setprice(const Value: Currency);
begin
  Fprice := Value;
end;

function TBLRChangeArticlePrice.GetBOArticlePrice(id_article, id_price, id_option: Int64; opt_type : Integer;
  var price : Currency; var valuta : Int64) : boolean;
var iq : IIBSQL;
begin
  result := false;
  SystemDataProvider.NumericValue := '0';
  price  := 0;
  valuta := 0;
  if (id_article = 0) and (id_price=0) then exit;
  if opt_type=0 then begin
    iq := upos.UntillDB.GetPreparedIIbSql('select * from ARTICLE_PRICES '
      + ' where id_articles=:id_article and id_prices=:id_price');
  end else begin
    if opt_type=1 then begin
      iq := upos.UntillDB.GetPreparedIIbSql('select OPTION_ARTICLE_PRICES.* from OPTION_ARTICLE_PRICES'
        + ' join OPTION_ARTICLE on OPTION_ARTICLE.id=OPTION_ARTICLE_PRICES.ID_OPTION_ARTICLE'
        + ' and OPTION_ARTICLE.ID_ARTICLES=:id_article and OPTION_ARTICLE.ID_OPTIONS=:id_option and OPTION_ARTICLE.is_active=1'
        + ' where OPTION_ARTICLE_PRICES.id_prices=:id_price and OPTION_ARTICLE_PRICES.is_active=1');
      iq.q.parambyname('id_option').AsInt64 := id_option;
    end else if opt_type=2 then begin
      iq := upos.UntillDB.GetPreparedIIbSql('select BONUS_GROUPS_ARTICLES_PRICES.* from BONUS_GROUPS_ARTICLES_PRICES'
        + ' join BONUS_GROUPS_ARTICLES on BONUS_GROUPS_ARTICLES.id=BONUS_GROUPS_ARTICLES_PRICES.ID_BONUS_GROUPS_ARTICLES'
        + ' and BONUS_GROUPS_ARTICLES.ID_ARTICLES=:id_article and BONUS_GROUPS_ARTICLES.ID_BONUS_GROUPS=:id_option and BONUS_GROUPS_ARTICLES.is_active=1'
        + ' where BONUS_GROUPS_ARTICLES_PRICES.id_prices=:id_price and BONUS_GROUPS_ARTICLES_PRICES.is_active=1');
      iq.q.parambyname('id_option').AsInt64 := id_option;
    end else
      exit;
  end;

  iq.q.parambyname('id_article').AsInt64 := fid_articles;
  iq.q.parambyname('id_price').AsInt64   := fid_prices;
  iq.ExecQuery;
  if not iq.eof then begin
    price  := iq.FieldByName('price').asCurrency;
    valuta := StrToInt64Def(iq.FieldByName('id_currency').AsString,0);
    result := true;
    exit;
  end;
end;

procedure TBLRChangeArticlePrice.UpdatePrices;
var _price : currency;
    _valuta : Int64;
begin
  if fid_articles = 0 then exit;
  if fid_prices   = 0 then exit;
  fprice         := 0;
  fid_currency   := 0;
  if GetBOArticlePrice(id_articles, id_prices, id_option, opt_type, _price, _valuta) then begin
    fprice         := _price;
    fid_currency  := _valuta;
  end;
end;

{ TBLRMsgArtOpt }

procedure TBLRMsgArtOpt.Setid_option(const Value: Int64);
begin
  Fid_option := Value;
end;

procedure TBLRMsgArtOpt.Setopt_type(const Value: Integer);
begin
  Fopt_type := Value;
end;

{ TBLRChangeArticleSalesUM }

function TBLRChangeArticleSalesUM.CanAcceptMessage(
  msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgIngCat then Exit;
  if msg = TBLMessageClear then Exit;
  if msg = TBLRMsgSwitchArtIngRec then exit;
  if msg = TBLRMsgRecipe then exit;
  if msg = TBLRMsgStockIngredient then exit;
  if msg = TBLRMsgSendUM then exit;
  if msg = TBLRMsgArticle then exit;
  if msg = TBLMessageOK then exit;
  if msg = TBLMessageCancel then exit;
  if msg = TBLRMsgSupplier then exit;
  Result := false;
end;

constructor TBLRChangeArticleSalesUM.Create(ABla: TBLAlg; AParent: TBLAlgStep;
  _id_articles : Int64);
begin
  inherited Create(ABla, AParent);
  fid_articles := _id_articles;
  fid_um       := GetArticleSalesUM(fid_articles);
  InitStep;
end;

procedure TBLRChangeArticleSalesUM.SaveCleanUM;
var q  : IIBSQL;
    wt : IWTran;
    strSQL : String;
begin
  if fid_articles=0 then exit;
  Fid_supp   := 0;
  Fid_um     := 0;
  Fid_inv    := 0;
  Fid_ingcat := 0;
  Fid_rec    := 0;
  wt         := upos.untilldb.getWTran;
  strSQL     := 'update articles set article_type=0, ID_SUPPLIERS=null, '
    + ' ID_INVENTORY_ITEM=null, ID_RECIPE=null, ID_UNITY_SALES=null where id=:id';
  q := wt.GetPreparedIIbSql( strSQL );
  q.q.ParamByName('id').asInt64    := fid_articles;
  q.ExecQuery;
  wt.commit;
end;

procedure TBLRChangeArticleSalesUM.SaveNewUM;
var q  : IIBSQL;
    wt : IWTran;
    strSQL : String;
begin
  if fid_articles=0 then exit;
  if (fid_inv=0) and (id_rec=0) then
    plugin.RaisePosException('Please define linked ingredient/recipe','BLRAlgArticleManageU');
  if (fid_inv>0) and (id_supp=0) then
    plugin.RaisePosException('Please define Supplier','BLRAlgArticleManageU');
  if fid_um=0 then
    plugin.RaisePosException('Please define usage unity of measure','BLRAlgArticleManageU');

  wt := upos.untilldb.getWTran;
  strSQL := 'update articles set ID_UNITY_SALES=:id_um';
  if fid_rec=0 then
    strSQL := strSQL + ' , ID_SUPPLIERS=:ID_SUPPLIERS, ID_INVENTORY_ITEM=:ID_INVENTORY_ITEM, article_type=1'
  else
    strSQL := strSQL + ' , ID_RECIPE=:ID_RECIPE, article_type=2';

  strSQL := strSQL + ' where id=:id';
  q := wt.GetPreparedIIbSql( strSQL );
  q.q.ParamByName('id').asInt64    := fid_articles;
  q.q.ParamByName('id_um').asInt64 := fid_um;
  if fid_rec=0 then begin
    q.q.ParamByName('ID_SUPPLIERS').asInt64 := fid_supp;
    q.q.ParamByName('ID_INVENTORY_ITEM').asInt64 := fid_inv;
  end else
    q.q.ParamByName('ID_RECIPE').asInt64 := fid_rec;

  q.ExecQuery;
  wt.commit;
end;

procedure TBLRChangeArticleSalesUM.SendUpdateStock;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, '', uetUpdate);
  try
    UPos.Un.SendEvent(URL_STOCK_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure TBLRChangeArticleSalesUM.SendUpdateUM;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, 'UpdateEditArticleUM', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

function TBLRChangeArticleSalesUM.GetArticleSalesUM(id_articles: Int64): Int64;
var iq : IIBSQL;
begin
  result :=0;
  iq := upos.UntillDB.GetPreparedIIbSql('select * from articles where id = :id');
  iq.q.params[0].asInt64 := id_articles;
  iq.ExecQuery;
  if iq.eof then exit;

  result := StrToInt64Def(iq.q.fieldByName('id_unity_sales').asString,0);
end;

function TBLRChangeArticleSalesUM.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Change article sales unity of measure', 'State hint');
end;

class function TBLRChangeArticleSalesUM.GetStaticGuid: string;
begin
  result := 'b3ecdb27-5678-4375-bbdc-b966358317a5';
end;

procedure TBLRChangeArticleSalesUM.InitStep;
var iq : IIBSQL;
begin
  id_supp := 0;
  id_inv  := 0;
  id_rec  := 0;
  if fid_articles=0 then exit;
  FumStep := sumsUM;

  iq := upos.UntillDB.GetPreparedIIbSql('select ID_INVENTORY_ITEM, ID_RECIPE, ID_SUPPLIERS from articles where id=:id');
  iq.q.Params[0].AsInt64 := fid_articles;
  iq.ExecQuery;
  if iq.eof then exit;

  id_inv  := StrToInt64Def(iq.q.fields[0].AsString,0);
  id_rec  := StrToInt64Def(iq.q.fields[1].AsString,0);
  id_supp := StrToInt64Def(iq.q.fields[2].AsString,0);

  if fid_um=0 then begin
    FumStep := sumsIng; // Ingredient must be chosen first
    if (id_inv=0) then begin
      if (id_rec=0) then
        FumStep := sumsSup // Ingredient must be chosen first
      else
        FumStep := sumsRec; // Recipe must be chosen first
    end
  end;

end;

procedure TBLRChangeArticleSalesUM.OnOK;
begin
  SaveNewUM;
  inherited;
end;

procedure TBLRChangeArticleSalesUM.ProcessMessage(msg: TBLMessage);
var id_unity,id_umc : Int64;
    umName : String;
begin
  inherited;
  if msg is TBLRMsgIngCat  then begin
    Fid_ingcat := TBLRMsgIngCat(msg).id;
    SendUpdateStock;
    SendUpdateUM;
    exit;
  end;
  if msg is TBLMessageClear then begin
    if FumStep = sumsUM then begin
      if fid_rec = 0 then begin
        FumStep := sumsIng;
        if fid_inv = 0 then begin
          FumStep := sumsSup;
          Fid_ingcat := 0;
        end;
      end else begin
        FumStep := sumsRec;
        Fid_ingcat := 0;
      end;
    end else if FumStep in [sumsIng, sumsRec] then begin
      FumStep := sumsSup;
      Fid_ingcat := 0;
    end else if FumStep = sumsSup then begin
      SaveCleanUM;
      FumStep := sumsSup;
      Fid_ingcat := 0;
    end;
    SendUpdateStock;
    SendUpdateUM;
    exit;
  end;
  if (msg is TBLRMsgSwitchArtIngRec) and (FumStep in [sumsSup, sumsRec,sumsIng]) then begin
    if FumStep in [sumsSup, sumsIng] then
      FumStep := sumsRec
    else begin
      if id_supp>0 then
        FumStep := sumsIng
      else
        FumStep := sumsSup
    end;
    id_inv  := 0;
    id_rec  := 0;
    id_um   := 0;
    SendUpdateUM;
    SendUpdateStock;
    exit;
  end;
  if msg is TBLRMsgSupplier then begin
    id_supp := StrToInt64Def(TBLRMsgSupplier(msg).id,0);
    id_inv := 0;
    id_rec := 0;
    id_um  := 0;
    FumStep := sumsIng;
    SendUpdateUM;
    SendUpdateStock;
    exit;
  end;
  if (msg is TBLRMsgStockIngredient) then begin
    id_inv := TBLRMsgStockIngredient(msg).id;
    id_rec := 0;
    id_unity := GetInventoryItemUM(upos.UntillDB, id_inv);
    if id_unity>0 then begin
      GetDefaultUM(upos.UntillDB, id_unity, sttUsage, id_umc, umName);
      id_um  := id_umc;
    end;
    FumStep := sumsUM;
    SendUpdateUM;
    exit;
  end;
  if (msg is TBLRMsgRecipe) then begin
    id_rec := TBLRMsgRecipe(msg).id;
    id_inv := 0;
    id_unity := GetRecipeUM(upos.UntillDB, id_rec);
    if id_unity>0 then begin
      GetDefaultUM(upos.UntillDB, id_unity, sttUsage, id_umc, umName);
      id_um  := id_umc;
    end;
    FumStep := sumsUM;
    SendUpdateUM;
    exit;
  end;
  if msg is TBLRMsgSendUM then begin
    fid_um := TBLRMsgSendUM(msg).id;
    SendUpdateUM;
    exit;
  end;
  if msg is TBLRMsgArticle then begin
    if ((id_supp>0) or (id_rec>0)) and (id_um>0) then
      SaveNewUM;
    fid_articles := TBLRMsgArticle(msg).id;
    fid_um       := GetArticleSalesUM(fid_articles);
    InitStep;
    SendUpdateUM;
    exit;
  end;
end;

procedure TBLRChangeArticleSalesUM.Setid_articles(const Value: Int64);
begin
  Fid_articles := Value;
end;
procedure TBLRChangeArticleSalesUM.Setid_inv(const Value: Int64);
begin
  Fid_inv := Value;
end;

procedure TBLRChangeArticleSalesUM.Setid_rec(const Value: Int64);
begin
  Fid_rec    := Value;
  Fid_ingcat := 0;
end;

procedure TBLRChangeArticleSalesUM.Setid_supp(const Value: Int64);
begin
  Fid_supp := Value;
  Fid_ingcat := 0;
end;

{ TBLRMsgSendUM }

procedure TBLRMsgSendUM.Setid(const Value: Int64);
begin
  Fid := Value;
end;

{ TBLRSelectArticle }

function TBLRSelectArticle.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgArticle then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRSelectArticle.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  SendUpdateDep;
end;

class function TBLRSelectArticle.DatasetParamClass: TUntillDbItemsListDatasetParamClass;
begin
  result := TArticleDataSetParam;
end;

function TBLRSelectArticle.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Choose article', 'State hint');
end;

function TBLRSelectArticle.GetItemName(aid_art: Int64): String;
begin
   result := RestaurantCashDatasetsU.GetArticleLangName(aid_art, upos.LanguageId);
end;

class function TBLRSelectArticle.GetStaticGuid: string;
begin
  result := '4744f221-cec5-46b2-afe7-552d304b82ab';
end;

{ TBLRSelectDepartment }

function TBLRSelectDepartment.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgDepartmentMessage then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRSelectDepartment.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  SendUpdateDep;
end;

class function TBLRSelectDepartment.DatasetParamClass: TUntillDbItemsListDatasetParamClass;
begin
  result := TDepartmentDataSetParam;
end;

function TBLRSelectDepartment.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Choose department', 'State hint');
end;

function TBLRSelectDepartment.GetItemName(aid_art: Int64): String;
begin
   result := RestaurantCashDatasetsU.GetDepLangName(aid_art, upos.LanguageId);
end;

class function TBLRSelectDepartment.GetStaticGuid: string;
begin
  result := '56f8fafd-b456-4710-82d0-3a37870488b5';
end;

procedure TBLRSelectDepartment.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgDepartmentMessage then begin
    inherited;
    SendUpdateEditArt;
  end else
    inherited;
end;

{ TBLRSelectSalesArea }

function TBLRSelectSalesArea.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgSalesArea then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

class function TBLRSelectSalesArea.DatasetParamClass: TUntillDbItemsListDatasetParamClass;
begin
  result := TSalesAreaDataSetParam;
end;

function TBLRSelectSalesArea.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Choose sales area', 'State hint');
end;

function TBLRSelectSalesArea.GetItemName(aid_art: Int64): String;
begin
   result := CacheSalesAreasRange.GetSalesAreaName(aid_art);
end;

class function TBLRSelectSalesArea.GetStaticGuid: string;
begin
  result := '488F61B1-739A-46E5-BD57-3296B91B09B2';
end;

procedure TBLRSelectSalesArea.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgSalesArea then begin
    inherited;
    SendUpdateEditArt;
  end else
    inherited;
end;

{ TBLRAlgDepManage }

function TBLRAlgDepManage.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgResetArticleOrder then Exit;
  if msg = TBLRMsgSwitchReorderMode then Exit;
  if msg = TBLRMsgSalesArea then Exit;
  if msg = TBLRMsgResetSalesArea then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRAlgDepManage.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  FReorderMode := rmRelative;
  SendUpdateSa;
  RefreshDataset(0);
end;

function TBLRAlgDepManage.GetHint: widestring;
begin
  if FreorderMode = rmAbsolute then
    Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange departments (absolute mode)', 'State hint')
  else if FreorderMode = rmRelative then
    Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange departments (relative mode)', 'State hint')
  else if FreorderMode = rmExchange then
    Result := plugin.TranslatePOS('BLRAlgU', 'Rearrange departments (exchange mode)', 'State hint');
end;

class function TBLRAlgDepManage.GetStaticGuid: string;
begin
  result := 'e142bd28-79cf-4a07-ad49-c03f6d94685a'
end;

procedure TBLRAlgDepManage.OnCancel;
begin
  DummyUpdateSalesArea;
  inherited;
end;

procedure TBLRAlgDepManage.OnChildFinished(child: TBLAlgStep);
begin
  if child is TBLRChangeArticleSalesUM then begin
    FreeOnLastChild := false;
    exit;
  end;
  inherited;
end;

procedure TBLRAlgDepManage.OnOK;
begin
  DummyUpdateSalesArea;
  inherited;
end;

procedure TBLRAlgDepManage.ProcessMessage(msg: TBLMessage);
begin
  inherited;

  if (msg is TBLRMsgResetSalesArea) then begin
    UntillApp.ClearChangeFlags;
    RestaurantProps.CurrentSalesArea := 0;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgResetArticleOrder) then begin
    UntillApp.ClearChangeFlags;
    if TBLRMsgResetArticleOrder(msg).ResetType = 0 then
      ResetDepOrder
    else
      ResetDepOrderAll;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgSwitchReorderMode) then begin
    UntillApp.ClearChangeFlags;
    if FReorderMode = rmRelative then
      FReorderMode := rmAbsolute
    else if FReorderMode = rmAbsolute then
      FReorderMode := rmExchange
    else if FReorderMode = rmExchange then
      FReorderMode := rmRelative;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if (msg is TBLRMsgSalesArea) then begin
    RestaurantProps.CurrentSalesArea := TBLRMsgSalesArea(msg).id;
    UntillApp.ClearChangeFlags;
    SendUpdateDep;
    RefreshDataset(0);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;

end;

procedure TBLRAlgDepManage.RefreshDataset(id_dep: Int64);
var evCheck : TPMAnalyseChangeFlags;
    info    : String;
begin
  if id_dep>0 then begin
    info := EVENT_INFO_UPDATE_EDIT_DEP_LIST_EX;
  end else
    info := EVENT_INFO_UPDATE_EDIT_DEP_LIST;
  evCheck := TPMAnalyseChangeFlags.Create(0, info, uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, evCheck);
  finally
    evCheck.Free;
  end;
end;

procedure TBLRAlgDepManage.ResetDepOrder;
var iq : IIBSQL;
    wt : IWtran;
begin
  if (RestaurantProps.CurrentSalesArea=0) then exit;
  wt := upos.UntillDB.getWTran;
  iq := wt.GetPreparedIIbSql('execute procedure UPDATE_POS_DEP_POSITION(:id_sa)');
  iq.q.Params[0].asInt64 := RestaurantProps.CurrentSalesArea;
  iq.ExecQuery;
  wt.commit;
  CashDepPOSDatasets;
  CacheDepartments ( GetPOSScreenGroup );
  RefreshDataset(0);
end;

procedure TBLRAlgDepManage.ResetDepOrderAll;
var iq : IIBSQL;
    wt : IWtran;
begin
  wt := upos.UntillDB.getWTran;
  iq := wt.GetPreparedIIbSql('execute procedure UPDATE_POS_DEP_POSITION_All');
  iq.ExecQuery;
  wt.commit;
  CashDepPOSDatasets;
  CacheDepartments ( GetPOSScreenGroup );
  RefreshDataset(0);
end;

initialization
  RegisterClass(TBLRMsgNeedDisableArticles);
  RegisterClass(TBLRMsgNeedArticlePrice);
  RegisterClass(TBLRMsgNeedArticlesReorder);
  RegisterClass(TBLRMsgSwitchReorderMode);
  RegisterClass(TBLRMsgResetArticleOrder);
  RegisterClass(TBLRMsgArtOpt);
  RegisterClass(TBLRMsgNeedArticlesSalesUM);
  RegisterClass(TBLRMsgSendUM);
  RegisterClass(TBLRMsgRecipe);
  RegisterClass(TBLRMsgSwitchArtIngRec);
  RegisterClass(TBLRMsgIngCat);
  RegisterClass(TBLRMsgStockCreatePO);
end.
