create table option_tree (
    id u_id,
    opt_number integer,
    id_options_parent bigint,
    id_options_child bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint optree_pk primary key (id)
);
commit;
grant all on option_tree to untilluser;
commit;
execute procedure register_sync_table_ex('option_tree', 'b', 1);
commit;
execute procedure register_bo_table('option_tree', '', '');
commit;

