update SPECIAL_WORDS set number=1 where lower(keyname)='with';
commit;

update SPECIAL_WORDS set number=2 where lower(keyname)='without';
commit;

update SPECIAL_WORDS set number=3 where lower(keyname)='lot';
commit;

update SPECIAL_WORDS set number=4 where lower(keyname)='little';
commit;

update SPECIAL_WORDS set number=5 where lower(keyname)='separate';
commit;

update SPECIAL_WORDS set number=6 where lower(keyname)='dontmake';
commit;

delete from SPECIAL_WORDS where number is null;
commit;

