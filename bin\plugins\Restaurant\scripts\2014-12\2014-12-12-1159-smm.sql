set term !!;
CREATE OR ALTER procedure BCQ_VANDUIJNEN_ITEM (
    ID_VANDUIJNEN bigint,
    THIS_PC_NAME varchar(50))
as
declare variable ID_LCU_LOCATION bigint;
declare variable ID_LOCATION bigint;
declare variable ID_USER bigint;
declare variable ID_GROUP bigint;
declare variable ID_ARTICLE bigint;
declare variable LCU bigint;
declare variable ID bigint;
declare variable NEW_VAR integer;
declare variable LCU_DISPENSER integer;
declare variable VAND_WAITER integer;
declare variable VAND_PLU integer;
declare variable QUANTITY integer;
declare variable RECEIVED timestamp;
declare variable CLIENT_CARD varchar(50);
declare variable THIS_PC_ID bigint;
declare variable ID_COMPUTERS bigint;
begin

  select ID, LCU_DISPENSER, VAND_WAITER, VAND_PLU, QUANTITY, RECEIVED, CLIENT_CARD, ID_COMPUTERS
  from VANDUIJNEN
  where ID = :ID_VANDUIJNEN
  into :ID, :LCU_DISPENSER, :VAND_WAITER, :VAND_PLU, :QUANTITY, :RECEIVED, :CLIENT_CARD, :ID_COMPUTERS;

  select id from COMPUTERS where COMP_ACTIVE=1 and upper(NAME)=upper(:THIS_PC_NAME) into :THIS_PC_ID;

  if (not(ID is null)) then begin

     -- get dispencer number
     select first 1 DISP.ID, DISP.ID_BECO_LOCATIONS
     from VD_LCU_DISPENSERS DISP
     where DISP.LCU_NUMBER = :LCU_DISPENSER and
           IS_ACTIVE = 1
     into :LCU, :ID_LCU_LOCATION;

    -- get user id
    select first 1 ID_UNTILL_USERS
    from WAITERS, UNTILL_USERS
    where WAITERS.ID_UNTILL_USERS = UNTILL_USERS.ID and
          WAITERS.NUMBER_VANDUIJNEN = :VAND_WAITER and
          WAITERS.NUMBER_VANDUIJNEN != 0 and
          UNTILL_USERS.IS_ACTIVE = 1
    into ID_USER;
  
    -- get location
    select first 1 ID_LOCATIONS, ID_ARTICLES
    from BECO_ARTICLE_LOCATIONS BAL, ARTICLES A, BECO_LOCATIONS BL
    where BAL.ID_ARTICLES = A.ID and
          BAL.ID_LOCATIONS = BL.ID and
          PLU_NUMBER = :VAND_PLU and
          BAL.IS_ACTIVE = 1 and
          A.IS_ACTIVE = 1 and
          BL.IS_ACTIVE = 1
    into ID_LOCATION, ID_ARTICLE;
  
    -- is location? BUT: lcu location is leading over article location
    if ((coalesce(:ID_LOCATION, 0) <> 0) and (coalesce(:ID_LCU_LOCATION, 0) = 0)) then
    begin
      insert into VD_TURNOVER (POSTED, ID_UNTILL_USERS, ID_ARTICLES, ID_VD_GROUP, QUANTITY, ACTN, ID_VD_LCU_DISPENSERS,
                               ID_VD_LOCATION)
      values (:RECEIVED, :ID_USER, :ID_ARTICLE, null, :QUANTITY, 1, :LCU, :ID_LOCATION);
      if (:THIS_PC_ID = :ID_COMPUTERS) then
      begin
          execute procedure BEVERAGE_TAP2ORDER(:CLIENT_CARD, :ID_ARTICLE, :QUANTITY, :ID_COMPUTERS, :ID_USER);
      end
    end
    else
    begin
      -- group or article
      select first 1 ID
      from VD_GROUPS
      where NUMBER = :VAND_PLU and
            IS_ACTIVE = 1
      into ID_GROUP;

      select first 1 ID
      from ARTICLES
      where PLU_NUMBER_VANDUIJNEN = :VAND_PLU and
            IS_ACTIVE = 1
      into ID_ARTICLE;
  
      if (:THIS_PC_ID = :ID_COMPUTERS) then
      begin
          execute procedure BEVERAGE_TAP2ORDER(:CLIENT_CARD, :ID_ARTICLE, :QUANTITY, :ID_COMPUTERS, :ID_USER);
      end

      if (coalesce(:ID_GROUP, 0) <> 0) then
      begin
        insert into VD_TURNOVER (POSTED, ID_UNTILL_USERS, ID_ARTICLES, ID_VD_GROUP, QUANTITY, ACTN, ID_VD_LCU_DISPENSERS,
                                 ID_VD_LOCATION)
        values (:RECEIVED, :ID_USER, null, coalesce(:ID_GROUP, -1), :QUANTITY, 1, :LCU, :ID_LCU_LOCATION);
      end
      else
      begin
        insert into VD_TURNOVER (POSTED, ID_UNTILL_USERS, ID_ARTICLES, ID_VD_GROUP, QUANTITY, ACTN, ID_VD_LCU_DISPENSERS,
                                 ID_VD_LOCATION)
        values (:RECEIVED, :ID_USER, coalesce(:ID_ARTICLE, -1), null, :QUANTITY, 1, :LCU, :ID_LCU_LOCATION);
      end
    end
  end

end
!!
commit
!!
set term ;!!
