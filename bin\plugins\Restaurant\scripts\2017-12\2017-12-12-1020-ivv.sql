create table report_groups (
    id u_id,
    gr_number integer,
    name varchar(100),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint rpg_pk primary key (id)
);
commit;
grant all on report_groups to untilluser;
commit;
execute procedure register_sync_table_ex('report_groups', 'b', 1);
commit;
execute procedure register_bo_table('report_groups', '', '');
commit;


