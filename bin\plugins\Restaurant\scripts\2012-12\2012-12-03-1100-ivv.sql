SET TERM ^ ;

create or alter procedure FIX_UPDATE_OIF_PRICES
as
declare variable  id_bill bigint;
declare variable  id_order_item bigint;
declare variable  id_articles bigint;
declare variable  id_option_article bigint;
declare variable  id_price bigint;
declare variable  price decimal(17,4);
declare variable  vat_percent decimal(17,4);
declare variable  vat double precision;
declare variable  id_menu bigint;
declare variable  menu_id bigint;
declare variable  rowbeg int;
begin

  /* Procedure Text */
  for
    select id_bill from delay_bills
        where schedule is not null and deleted is null into :id_bill do begin
        for
            select order_item.id, order_item.rowbeg, order_item.id_menu, order_item.id_prices,  order_item.id_articles, order_item.id_option_article
                from order_item
                join orders on orders.id=order_item.id_orders
                where orders.id_bill=:id_bill into :id_order_item, :rowbeg, :id_menu, :id_price, :id_articles, :id_option_article do begin
                  if (rowbeg=1) then begin
                    select first 1 price, vat
                    from article_prices where id_articles=:id_articles and id_prices=:id_price and is_active=1 into :price, :vat_percent;
                  end else begin
                    select first 1 price, vat
                    from option_article_prices where id_option_article=:id_option_article and id_prices=:id_price and is_active=1 into :price, :vat_percent;
                  end
                  vat = (price * (vat_percent / 100) ) / (1 + (vat_percent / 100));
                  update order_item set price=:price, vat=:vat, original_price=:price where id=:id_order_item;
                  if (:id_menu>0) then begin
                      for select menu_item.id, menu_item.rowbeg, menu_item.id_prices, menu_item.id_articles, menu_item.id_option_article
                      from menu_item
                      where menu_item.id_menu=:id_menu into :menu_id, :rowbeg, :id_price, :id_articles, :id_option_article do begin
                        select first 1 price, vat
                        from option_article_prices where id_option_article=:id_option_article and id_prices=:id_price and is_active=1 into :price, :vat_percent;
                        vat = (price * (vat_percent / 100) ) / (1 + (vat_percent / 100));
                        update menu_item set price=:price, vat=:vat, original_price=:price where id=:menu_id;
                      end
                  end
            end
    end
end^

SET TERM ; ^

/* Existing privileges on this procedure */

GRANT EXECUTE ON PROCEDURE FIX_UPDATE_OIF_PRICES TO SYSDBA;
GRANT EXECUTE ON PROCEDURE FIX_UPDATE_OIF_PRICES TO UNTILLUSER;

