SET TERM ^ ;

create or alter procedure GET_EXACTGLOBE_VAT (
    DATE_FROM timestamp,
    DATE_TO timestamp)
returns (
    AMOUNT decimal(17,4),
    BNAME varchar(50),
    ACC varchar(100),
    VAT_SIGN varchar(10))
as
begin
  for
select  order_item.vat*sold_articles.quantity*sold_articles.sa_coef amount,
        bookkeeping.name bname, bookkeeping.account acc, order_item.vat_sign vat_sign
    from  order_item
    inner join orders on orders.id=order_item.id_orders
    inner join bill on bill.id=orders.id_bill and bill.pbill_number is null
    inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id )
    inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    join articles on articles.id = order_item.id_articles
    left outer join order_item_bookp on order_item_bookp.id_order_item = order_item.id
    left outer join bookkeeping on bookkeeping.id = order_item_bookp.id_bookkeeping_vat
    
union all
    select pbill_item.vat*pbill_item.quantity amount, bookkeeping.name bname,
        bookkeeping.account acc, pbill_item.vat_sign vat_sign
    from  pbill_item
    inner join pbill on pbill.id=pbill_item.id_pbill and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null
    join pbill_return on pbill_return.id_pbill=pbill.id
    join articles on articles.id = pbill_item.id_articles
    left outer join pbill_item_bookp on pbill_item_bookp.id_pbill_item = pbill_item.id
    left outer join bookkeeping on bookkeeping.id = pbill_item_bookp.id_bookkeeping_vat
union all
    select menu_item.vat * menu_item.quantity * sold_articles.quantity * sold_articles.sa_coef,
    bookkeeping.name bname, bookkeeping.account acc, menu_item.vat_sign vat_sign
    from  menu_item
    inner join order_item on order_item.id_menu = menu_item.id_menu
    inner join orders on orders.id=order_item.id_orders
    inner join bill on bill.id=orders.id_bill and bill.pbill_number is null
    inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id )
    inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    join articles on articles.id = order_item.id_articles
    left outer join menu_item_bookp on menu_item_bookp.id_menu_item = menu_item.id
    left outer join bookkeeping on bookkeeping.id = menu_item_bookp.id_bookkeeping_vat
    into :amount, :bname, :acc, :vat_sign
  do begin
     suspend;
  end
end^

SET TERM ; ^
