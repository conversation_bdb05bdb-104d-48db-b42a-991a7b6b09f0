create table zip_codes(
    id u_id,
    number integer,
    code varchar(20),
    name varchar(50),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint zip_codes_pk primary key (id)
);
commit;
grant all on zip_codes to untilluser;
commit;
execute procedure register_sync_table_ex('zip_codes', 'b', 1);
commit;
execute procedure register_bo_table('zip_codes', '', '');
commit;

