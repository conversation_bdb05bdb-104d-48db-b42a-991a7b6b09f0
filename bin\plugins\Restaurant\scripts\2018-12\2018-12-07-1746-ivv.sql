create table menu_item_bookp (
    id u_id,
    id_menu_item bigint,
    id_bookkeeping_turnover bigint,
    id_bookkeeping_vat bigint,
    constraint menu_item_bookp_pk primary key (id),
    constraint menu_item_bookp_fk1 foreign key (id_menu_item) references menu_item(id),
    constraint menu_item_bookp_fk2 foreign key (id_bookkeeping_turnover) references BOOKKEEPING(id),
    constraint menu_item_bookp_fk3 foreign key (id_bookkeeping_vat) references BOOKKEEPING(id)
);
commit;
grant all on menu_item_bookp to untilluser;
commit;
execute procedure register_sync_table_ex('menu_item_bookp', 'p', 1);
commit;
 