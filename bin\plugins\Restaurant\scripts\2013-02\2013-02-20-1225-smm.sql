set term !! ;
CREATE or ALTER PROCEDURE update_beconet_counters
as
declare variable hac_nr integer;
declare variable max_id bigint;
begin
    for
    select coalesce(hac_nr, 0), max(coalesce(external_id, -1)) from van<PERSON><PERSON><PERSON><PERSON> group by hac_nr
    into :hac_nr, :max_id do begin
      delete from vars where varname = 'BeconetLastID' || :hac_nr;
      insert into VARS (varname, varvalue) values ('BeconetLastID' || :hac_nr, :max_id);
    end
end
!!
commit
!!
grant execute on procedure update_beconet_counters to untilluser
!!
commit
!!
set term ; !!

execute procedure update_beconet_counters;
commit;
