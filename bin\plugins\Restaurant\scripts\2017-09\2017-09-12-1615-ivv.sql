create table article_options_defaults (
    id u_id,
    id_article_options bigint,
    id_option_article bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint article_options_defaults_pk primary key (id),
	constraint article_options_defaults_fk1 foreign key (id_article_options) references article_options(id),
	constraint article_options_defaults_fk2 foreign key (id_option_article) references option_article(id)
);
commit;
grant all on article_options_defaults to untilluser;
commit;
execute procedure register_sync_table_ex('article_options_defaults', 'b', 1);
commit;
execute procedure register_bo_table('article_options_defaults', 'id_article_options', 'article_options');
commit;
execute procedure register_bo_table('article_options_defaults', 'id_option_article', 'option_article');
commit;





