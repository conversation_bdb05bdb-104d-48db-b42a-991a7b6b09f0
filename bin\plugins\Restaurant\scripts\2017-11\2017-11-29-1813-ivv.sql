create table order_coupon_items (
    id u_id,
    id_order_item bigint,
    id_coupon_item bigint,
    constraint orcpi_pk primary key (id),
    constraint orcpi_fk1 foreign key (id_order_item) references order_item(id),
    constraint orcpi_fk2 foreign key (id_coupon_item) references coupon_items(id)
);
commit;
grant all on order_coupon_items to untilluser;
commit;
execute procedure register_sync_table_ex('order_coupon_items', 'p', 1);
commit;
