create table option_article_spec_price (
    id u_id,
    id_OPTION_ARTICLE_PRICES bigint,
    id_special_words bigint,
    price decimal(17,4),
    constraint oasp_pk primary key (id),
    constraint oasp_fk1 foreign key (id_OPTION_ARTICLE_PRICES) references OPTION_ARTICLE_PRICES(id),
    constraint oasp_fk2 foreign key (id_special_words) references special_words(id)
);
commit;
grant all on option_article_spec_price to untilluser;
commit;
execute procedure register_sync_table_ex('option_article_spec_price', 'b', 1);
commit;
execute procedure register_bo_table('option_article_spec_price', 'id_option_article_prices', 'option_article_prices');
commit;

