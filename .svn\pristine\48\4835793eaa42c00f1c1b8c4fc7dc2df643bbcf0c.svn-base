[Start]
**************************************************
TimeStamp=16.08.2016 12:36
Interval=0.000211874998058192;30.12.1899 0:00:18
[Class]
Name=TBLMessageKeyboardString
TimeStamp=16.08.2016 12:36
Interval=1.13657370093279E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:36
Interval=1.31018532556482E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:36
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:36
Interval=1.65740784723312E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=2
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.08.2016 12:36
Interval=1.41319396789186E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=15000037962
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:36
Interval=1.04745413409546E-5;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000090725
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:36
Interval=2.5879628083203E-5;30.12.1899 0:00:02
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:36
Interval=9.43287159316242E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000139
id_option=15000038142
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:36
Interval=9.68750100582838E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:36
Interval=9.05092747416347E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000466
id_option=15000038243
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:36
Interval=9.69906977843493E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:36
Interval=8.54166864883155E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000229
id_option=15000038037
[Class]
Name=TBLMessageOk
TimeStamp=16.08.2016 12:36
Interval=4.46759077021852E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
------------------Warme Dranken------------------                               
 2      <USER> <GROUP> bonus     52,00    26,00   9,02   42,98 21,0%         42,98   
--------------------Separate2--------------------                               
 2      <USER> <GROUP>                                                 1,00          
--------------------Separate3--------------------                               
 2      <USER> <GROUP>                                                            
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:36
Interval=9.05092747416347E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:36
Interval=1.27314706332982E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:36
Interval=1.76157409441657E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=7
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.08.2016 12:36
Interval=1.17129602585919E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:37
Interval=1.05555591289885E-5;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=5000000200
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:37
Interval=1.33101857500151E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=10000016099
id_option=5000000109
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:37
Interval=2.04282405320555E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=7
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:37
Interval=3.16666628350504E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000208
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=16.08.2016 12:37
Interval=2.73611149168573E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 7      <USER>              <GROUP>,60     1,80   2,19   10,41 21,0%         10,41   
        7     Belogolowka                  27,62                                
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.0949072020594E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.04169885162264E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:37
Interval=5.68055547773838E-5;30.12.1899 0:00:04
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=7
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.08.2016 12:37
Interval=1.39351832331158E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:37
Interval=7.85879819886759E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000208
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=16.08.2016 12:37
Interval=1.10416658571921E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.62152791745029E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=16.08.2016 12:37
Interval=1.05671279015951E-5;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=16.08.2016 12:37
Interval=1.13426358439028E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
------------------Warme Dranken------------------                               
 2      <USER> <GROUP> bonus     52,00    26,00   9,02   42,98 21,0%         42,98   
--------------------Separate1--------------------                               
 7      <USER>              <GROUP>,60     1,80   2,19   10,41 21,0%         10,41   
        7     Vodka                        27,62                                
 6      Orval                                                                   
 8      Orval               21,60     2,70   1,96   19,64 10,0%         19,64   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        113,82      113,82      0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.84027740033343E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:37
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:37
Interval=2.97569495160133E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=2
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.08.2016 12:37
Interval=8.86573980096728E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=15000037962
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:37
Interval=1.78356494870968E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000090725
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:37
Interval=2.6782407076098E-5;30.12.1899 0:00:02
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:37
Interval=8.1249963841401E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000139
id_option=15000038142
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:37
Interval=8.36805702419952E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:37
Interval=6.24999665888026E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000466
id_option=15000038243
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:41
Interval=1.80671340785921E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=1
s=7
[Class]
Name=TBLRMsgOption
TimeStamp=16.08.2016 12:41
Interval=7.1527756517753E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000229
id_option=15000038037
[Class]
Name=TBLMessageOk
TimeStamp=16.08.2016 12:41
Interval=4.20138530898839E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
------------------Warme Dranken------------------                               
 2      <USER> <GROUP> bonus     52,00    26,00   9,02   42,98 21,0%         42,98   
--------------------Separate2--------------------                               
 2      <USER> <GROUP>                                                 1,00          
--------------------Separate3--------------------                               
 2      <USER> <GROUP>                                                            
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:41
Interval=1.21643533930182E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.08.2016 12:41
Interval=2.1990854293108E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=221
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:41
Interval=1.01157420431264E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=2
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.08.2016 12:41
Interval=6.93286710884422E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:41
Interval=8.62268643686548E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000208
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:41
Interval=1.47685204865411E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=2
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:41
Interval=9.95370355667546E-5;30.12.1899 0:00:08
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000208
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=16.08.2016 12:41
Interval=0.000104340280813631;30.12.1899 0:00:09
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=3
[Class]
Name=TBLRMsgArticle
TimeStamp=16.08.2016 12:41
Interval=1.53009241330437E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000175
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=16.08.2016 12:41
Interval=3.33796269842423E-5;30.12.1899 0:00:02
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 2      <USER> <GROUP> Cl.                                                             
 1      Pils 25 Cl.          1,70     1,70   0,30    1,40 21,0%         1,40    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=16.08.2016 12:41
Interval=1.38889299705625E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
------------------Warme Dranken------------------                               
 2      <USER> <GROUP> bonus     52,00    26,00   9,02   42,98 21,0%         42,98   
--------------------Separate1--------------------                               
 4      <USER>                                                                   
 <GROUP>      Pils 25 Cl.                                                             
 1      Pils 25 Cl.          1,70     1,70   0,30    1,40 21,0%         1,40    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        53,70       53,70       0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=16.08.2016 12:41
Interval=1.03240745374933E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=16.08.2016 12:41
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=16.08.2016 12:41
Interval=2.7465277526062E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
