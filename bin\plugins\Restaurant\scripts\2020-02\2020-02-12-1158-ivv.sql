create table smartcard_tables(
    id u_id,
    tableno          integer,
    id_smartcards     bigint,
    is_active             smallint,
    IS_ACTIVE_MODIFIED         timestamp,
    IS_ACTIVE_MODIFIER         varchar(30),
    constraint smartcard_tables_pk primary key (id),
    constraint smartcard_tables_fk0 foreign key (id_smartcards) references smartcards(id)
);
commit;
grant all on smartcard_tables to untilluser;
commit;
execute procedure register_sync_table_ex('smartcard_tables', 'b', 1);
commit;
execute procedure register_bo_table('smartcard_tables', 'id_smartcards', 'smartcards');
commit;

