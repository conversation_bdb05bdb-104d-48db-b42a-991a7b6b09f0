create table card_table_reserve (
    id u_id,
    id_smartcards bigint,
    id_untill_users bigint,
    tableno integer,
    res_dt timestamp,
    pcname varchar(50),
    res_state integer,
    constraint card_table_reserve_pk primary key (id),
    constraint card_table_reserve_fk1 foreign key (id_smartcards) references smartcards (id),
    constraint card_table_reserve_fk2 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on card_table_reserve to untilluser;
commit;
execute procedure register_sync_table_ex('card_table_reserve', 'p', 1);
commit;


