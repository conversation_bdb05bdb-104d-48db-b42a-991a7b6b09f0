alter table size_modifier_item add ml_name blob;
commit;

create table article_size_modifier_price(
    id u_id,
    id_article_prices 	bigint,
    id_size_modifier_item	bigint,
    price		decimal(17,4),	
    is_active 		smallint,
    IS_ACTIVE_MODIFIED 	timestamp,
    IS_ACTIVE_MODIFIER 	varchar(30),
    constraint article_sm_price_pk primary key (id),
    constraint article_sm_price_fk1 foreign key (id_article_prices) references article_prices(id),
    constraint article_sm_price_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on article_size_modifier_price to untilluser;
commit;
execute procedure register_sync_table_ex('article_size_modifier_price', 'b', 1);
commit;
execute procedure register_bo_table('article_size_modifier_price', 'id_size_modifier_item', 'size_modifier_item');
commit;
execute procedure register_bo_table('article_size_modifier_price', 'id_article_prices', 'article_prices');
commit;
