create table disabled_order_printers (
    id u_id,
    id_printers bigint,
    id_computers bigint,
    id_untill_users bigint,
    dt timestamp,
    constraint dop_pk primary key (id),
    constraint dop_fk1 foreign key (id_printers) references printers(id),
    constraint dop_fk2 foreign key (id_computers) references computers(id),
    constraint dop_fk3 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on disabled_order_printers to untilluser;
commit;
execute procedure register_sync_table_ex('disabled_order_printers', 'p', 1);
commit;


