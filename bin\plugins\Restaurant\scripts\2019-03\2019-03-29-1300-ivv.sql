create table KS_WORKFLOW_ITEMS_ALLERGENS (
    id u_id,
    ID_KS_WORKFLOW_ITEMS bigint,
    id_allergens bigint,
    constraint kswia_pk primary key (id),
    constraint kswia_fk1 foreign key (ID_KS_WORKFLOW_ITEMS) references KS_WORKFLOW_ITEMS(id),
    constraint kswia_fk2 foreign key (id_allergens) references allergens(id)
);
commit;
grant all on KS_WORKFLOW_ITEMS_ALLERGENS to untilluser;
commit;
execute procedure register_sync_table_ex('KS_WORKFLOW_ITEMS_ALLERGENS', 'p', 1);
commit;


