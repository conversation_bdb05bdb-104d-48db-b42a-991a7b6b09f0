unit BlrTipsU;

interface
uses Classes, BLAlgU, BLRAlgU, UntillDBU, PosAlgU, DB, RestaurantPluginU,
     BLRMainU, SysUtils, UntillPOSU, Dialogs, CurrencyU, NamedVarsU, ClassesU,
     Math, NumbersU, BLRAlgReopenBillU;

type

  TBLRMsgEditPSPTips = class(TBLMessage);

  TBLRMsgCloseBillType= class(TBLMessage)
  private
    FbillType : Integer;
    procedure SetbillType(const Value: Integer);
  published
    property billType : Integer read FbillType write SetbillType;
  end;

  TBLREditPSPTips   = class (TBLAlgStep)
  private
    FTipsType : Integer; // 0:only With no tips; 1:only With tips; 2:All Bills
    procedure SetTipsType(const Value: Integer);
    procedure SendRefresh;
  protected
    procedure OnChildFinished(child: TBLAlgStep); override;
  public
    property TipsType : Integer read FTipsType write SetTipsType;
    class function GetStaticGuid: string; override;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep); override;
    function GetHint: widestring; override;
    function CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
  end;

  TBLRGetTipAmount = class (TBLAlgStep)
  private
    FtipsType : TTipsType;
    FCurrentTipAmount      : String;
    FMainCurrencyTipAmount : Currency;
    fTipsPrinter : Int64;
    fTipsTicket : Int64;
    fbBeforePayment : Boolean;
    bFirstTime :Boolean;
    fidpbill: Int64;
    fidpayments: Int64;
    fidpbillpayments: Int64;
    fprice: Currency;
    fid_currency : Int64;
    procedure ProcessTips(ResetTips: boolean);
    procedure SetCurrentTipAmount(const Value: String);
    procedure Setidpayments(const Value: Int64);
    procedure Setidpbill(const Value: Int64);
    procedure Setidpbillpayments(const Value: Int64);
    procedure Setprice(const Value: Currency);
    procedure SettipsType(const Value: TTipsType);
    procedure RecalcMainCurrencyValue;
  public
    property tipsType : TTipsType read FtipsType write SettipsType;
    property idpbill: Int64 read Fidpbill write Setidpbill;
    property idpayments: Int64 read Fidpayments write Setidpayments;
    property idpbillpayments: Int64 read Fidpbillpayments write Setidpbillpayments;
    property price: Currency read Fprice write Setprice;
    property MainCurrencyTipAmount : Currency read FMainCurrencyTipAmount;
    property CurrentTipAmount : String read FCurrentTipAmount write SetCurrentTipAmount;
    property TipsPrinter : Int64 read FTipsPrinter write FTipsPrinter;
    property TipsTicket : Int64 read FTipsTicket write FTipsTicket;
    property bBeforePayment : Boolean read fbBeforePayment write fbBeforePayment;
    property id_currency : Int64 read fid_currency;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep); override;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure OnOK; override;
    procedure OnCancel; override;
    procedure OnInput(s: widestring); override;
    procedure ProcessMessage(msg: TBLMessage); override;
  end;

  TBLRGetPSPTipAmount = class (TBLRGetTipAmount)
  public
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
  end;

implementation
uses UrlNotifierU, ClientDataProviderU, DevicesU, CommonU, BLRPrintU, BLRAlgPaymentU,
  TipsU, BLSysU, PSPU, PSP_OnCardU, PSP_DibsU, PSP_PayzoneU, PSP_PaywareU, PSP_DatacapU,
  PSP_MercuryU, PSP_JavaDriverU, BlrBillU, PaymentsU, JournalLogsU,
  RestaurantJLogU, FDMCacheU, ProformaU, BLRBillUtilsU;
{ TBLREditPSPTips }

function TBLREditPSPTips.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgNeedProforma then Exit;
  if msg = TBLMessageCancel then Exit;
  if msg = TBLMessageOK then Exit;
  if msg = TBLRMsgResetTip then Exit;
  if msg = TBLRMsgEnterTipAmount then Exit;
  if msg = TBLRMsgGotoPBill then Exit;
  if msg = TBLRMsgCloseBillType then Exit;
  if msg = TBLRMsgPrintBill then Exit;
  if msg = TBLRMsgNeedCashTips then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLREditPSPTips.Create(ABla: TBLAlg; AParent: TBLAlgStep);
var ue : TPMAnalyseChangeDatasetFlags;
begin
  inherited;
  FTipsType := 0;
  ue := TPMAnalyseChangeDatasetFlags.Create(-1, 'ShowClosedBills', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_REFRESH_BILL_LIST, ue);
  finally
    FreeAndnil(ue);
  end;
end;

function TBLREditPSPTips.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Edit PSP tips', 'State hint');
end;

class function TBLREditPSPTips.GetStaticGuid: string;
begin
  result := '{F9733BC2-7099-4fab-B208-B64E6CDA5ED4}';
end;

procedure TBLREditPSPTips.OnChildFinished(child: TBLAlgStep);
begin
  Self.FreeOnLastChild := true;
  if (child is TBLRGetPSPTipAmount) or (child is TBLRGetTipAmount) then begin
    Self.FreeOnLastChild := false;
    SendRefresh;
    exit;
  end;
  inherited;
end;

procedure TBLREditPSPTips.ProcessMessage(msg: TBLMessage);
var idpbill, idpayments, idpbillpayments : Int64;
    price : Currency;
    gta   : TBLRGetPSPTipAmount;
    gtb   : TBLRGetTipAmount;
    ue    : TPMAnalyseChangeDatasetFlags;
    ProformaPrinter : Int64;
    ProformaLayout  : Int64;
    cc: TCleanCashDeviceConfiguration;
begin

  if msg is TBLRMsgResetTip then begin
    idpbill := blr.ControlBillData.BillID;
    if idpbill>0 then begin
      if CanProcessTips(idpbill, idpayments, idpbillpayments, price) then begin
        gta:=TBLRGetPSPTipAmount.Create(self.bla, self);
        gta.bBeforePayment := False;
        gta.idpbill     := idpbill;
        gta.idpayments  := idpayments;
        gta.idpbillpayments:= idpbillpayments;
        gta.price       := 0;
      end;
      posalg.SendMessage(TBLRMsgResetTip.Create);
      SendRefresh;
    end;
    exit;
  end;
  if msg is TBLRMsgCloseBillType then begin
    FTipsType := TBLRMsgCloseBillType(msg).billType;
    SendRefresh;
    exit;
  end;
  if msg is TBLRMsgGotoPBill then begin
    ue := TPMAnalyseChangeDatasetFlags.Create(TBLRMsgGotoPBill(msg).Direction, 'ShowClosedBills', uetUpdate);
    try
      UPos.Un.SendEvent(URL_POS_REFRESH_BILL_LIST, ue);
    finally
      FreeAndnil(ue);
    end;
    exit;
  end;
  if msg is TBLRMsgNeedProforma then begin
    ProformaPrinter := TBLRMsgNeedProforma(msg).id_printer;
    ProformaLayout := TBLRMsgNeedProforma(msg).id_ticket;
    ClientDataProvider.ClientID := IntToStr(blr.ActiveBill.id_clients);
    if not TBLRMsgNeedProforma(msg).IgnorePrint then
      PrintBill(blr.ActiveBill, TPrintProforma.Yes(TBLRMsgNeedProforma(msg)), ProformaPrinter, ProformaLayout);
    exit;
  end;
  if msg is TBLRMsgPrintBill then begin
    ProformaPrinter := TBLRMsgPrintBill(msg).PrinterId;
    ProformaLayout  := TBLRMsgPrintBill(msg).LayoutId;
    ClientDataProvider.ClientID := IntToStr(blr.ActiveBill.id_clients);
    cc := GetFDMCache(upos.UntillDB).Conf();

    if assigned(cc) and (cc.Model <> TCleanCashDeviceConfiguration.MODEL_FRANCE) then begin
      if GetReprintCount(upos.UntillDB, blr.ControlBillData.BillID) > 0 then
      begin
        Plugin.RaisePosException('Bill can only be reprinted once since CleanCash installed', 'TBLREditPSPTips.ProcessMessage');
        exit;
      end;
    end;

    PrintBill(blr.ActiveBill, TPrintProforma.No(), ProformaPrinter, ProformaLayout, True);
    if blr.ControlBillData.BillID>0 then
      BillReprinted(upos.UntillDB, blr.ControlBillData.BillID, upos.GetPOSNow);
    exit;
  end;
  if msg is TBLRMsgNeedCashTips then begin
    if blr.ControlBillData.BillStatus = blsActive then begin
      gtb := TBLRGetTipAmount.Create(self.bla, self);
      gtb.tipsType       := tptCash;
      gtb.idpbill        := blr.ControlBillData.BillID;
      gtb.bBeforePayment := false;
      gtb.TipsPrinter    := (msg as TBLRMsgNeedCashTips).printer;
      gtb.TipsTicket     := (msg as TBLRMsgNeedCashTips).ticket;
    end;
    exit;
  end;
  if msg is TBLRMsgEnterTipAmount then begin
    idpbill := blr.ControlBillData.BillID;
    if CanProcessTips(idpbill, idpayments, idpbillpayments, price) then begin
      gta:=TBLRGetPSPTipAmount.Create(self.bla, self);
      gta.TipsPrinter := (msg as TBLRMsgEnterTipAmount).printer;
      gta.TipsTicket  := (msg as TBLRMsgEnterTipAmount).ticket;
      gta.bBeforePayment := False;
      gta.idpbill     := idpbill;
      gta.idpayments  := idpayments;
      gta.idpbillpayments:= idpbillpayments;
      gta.price       := price;
    end;
    exit;
  end;
  inherited;
end;

procedure TBLREditPSPTips.SendRefresh;
var ue : TPMAnalyseChangeDatasetFlags;
begin
  ue := TPMAnalyseChangeDatasetFlags.Create(-1, 'ShowClosedBills', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_REFRESH_BILL_LIST, ue);
  finally
    FreeAndnil(ue);
  end;
end;

procedure TBLREditPSPTips.SetTipsType(const Value: Integer);
begin
  FTipsType := Value;
end;

{ TBLRMsgCloseBillType }

procedure TBLRMsgCloseBillType.SetbillType(const Value: Integer);
begin
  FbillType := Value;
end;

{ TBLRGetTipAmount }

function TBLRGetTipAmount.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgResetTip then exit;
  if msg = TBLMessageInput then Exit;
  if msg = TBLMessageBackSpace then Exit;
  if msg = TBLRMsgChooseCurrency then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRGetTipAmount.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  bFirstTime       := True;
  CurrentTipAmount := '0';
  FtipsType        := tptEFT;
  fid_currency     := blr.PaymentCurrency;
end;

function TBLRGetTipAmount.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Enter tip amount', 'State hint');
end;

class function TBLRGetTipAmount.GetStaticGuid: string;
begin
  result:='{F93282AF-A904-4981-A20B-59239DE9A8A7}'
end;

procedure TBLRGetTipAmount.OnCancel;
begin
  CurrentTipAmount := '0';
  inherited;
end;

procedure TBLRGetTipAmount.OnInput(s: widestring);
var
  ss :String;
begin
  if bFirstTime then begin
     CurrentTipAmount := '';
     bFirstTime := False;
  end;
  if Trim(s) = '.' then s := FormatSettings.DecimalSeparator;

  if (Trim(s) = 'd') then begin
    delete(FCurrentTipAmount, Length(FCurrentTipAmount) ,1);
    CurrentTipAmount :=FCurrentTipAmount;
    ss := CurrentTipAmount;
  end else
    ss := CurrentTipAmount + s;

  if IsFloat(ss) then begin
    if StrToFloat(ss) <= upos.MaxAmount then
      CurrentTipAmount := ss;
  end else
    CurrentTipAmount := '0';
end;

procedure TBLRGetTipAmount.OnOK;
begin
  if not bBeforePayment then begin
    if IsFloat(CurrentTipAmount)  then begin
        ProcessTips(False);
        TBLSShowMessage.Create(self.bla, Self, Plugin.Translate('BLRAlgU','Tip added and approved'), mtInformation);
    end;
  end else
    inherited;
end;

procedure TBLRGetTipAmount.RecalcMainCurrencyValue;
begin
  FMainCurrencyTipAmount := ConvertCustomToMainCurrency(upos.UntillDB, StrToCurrDef(FCurrentTipAmount,0), fid_currency);
end;

procedure TBLRGetTipAmount.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgChooseCurrency then begin
    fid_currency := TBLRMsgChooseCurrency(msg).id_currency;
    RecalcMainCurrencyValue;
    exit;
  end;
  if msg is TBLRMsgResetTip then begin
    ProcessTips(True);
    Self.Free;
    exit;
  end;
  if msg is TBLMessageBackSpace then begin
    delete(FCurrentTipAmount, Length(FCurrentTipAmount) ,1);
    CurrentTipAmount :=FCurrentTipAmount;
    exit;
  end;
  inherited;
end;

procedure TBLRGetTipAmount.ProcessTips(ResetTips: boolean);
var
  eft: TCustomPSP;
  psp: TOnCardPSP20;
  pspd: TDibsPSP;
  pspp: TPayzonePSP;
  psppy: TPaywarePSP;
  pspdc: TDatacapPSP;
  pspm: TMercuryPSP;
  pspj: TJavaDriverPSP;
  bill: TBLRBill;
  iq, iqVat: IIBSQL;
  extraamount: Currency;
  middlevat: Currency;
  orderid: String;
  cinfo: TCurrencyInfo;
  tran: IWTran;
  PrinterId, BillLayout: Int64;
  bHasEftPayment: boolean;
  bTipsModify: Boolean;
  rt: TPSPRequestType;
  ip: IUntillPayment;
  cdata: INamedVars;
  TranNum: TNumber;
begin

  bHasEftPayment:=false;
  bill:=blr.ActiveBill;
  orderid:=bill.pnumber.ToString;
  cinfo:=upos.MainCurrency;
  assert(assigned(bill));

  if not ResetTips then begin
    extraamount:=FMainCurrencyTipAmount;
  end else
    extraamount := 0;

  if FtipsType = tptEFT then begin
    ip := GetPayment(upos.UntillDB, idpayments);
    iq:=upos.UntillDB.GetIIbSql;
    iq.SetText('select * from pbill_card_payments_info where id_pbill_payments=:id_pbill_payments and coalesce(tip_approvement, 0)=0');
    iq.q.params[0].AsInt64:=idpbillpayments;
    iq.ExecQuery;
    bTipsModify := false;
    if not iq.Eof then begin // PSP was used


      bHasEftPayment := true;

      iqVat:=upos.UntillDB.GetIIbSql;
      iqVat.SetText('select Sum(vat)/Sum(price) from pbill_payments where id_pbill =:id and price <> 0');
      iqVat.q.Params[0].AsInt64 := idpbill;
      iqVat.ExecQuery;
      assert(not iqVat.Eof);
      middlevat:=iqVat.q.fields[0].AsDouble;

      eft := PspCreateDevice(upos.UntillDB, idpayments, upos.UserId);
      if eft = nil then exit;

      bTipsModify := (eft.TipsReplaced) and not (ip.kind = PAYMENT_GIFT_CARD); // For gift cards always add tips, not replace

      tran := Upos.UntillDB.getWTran();

      if ip.kind = PAYMENT_GIFT_CARD then
        if ResetTips then begin
          RollbackPspTips(tran, idpbill, idpbillpayments);
          price := bill.GetActualPSPTipsAmount(nil, idpbill);
          rt := pspGiftReload;
        end else
          rt := pspGiftTipHandle
      else begin
        if ResetTips then
          rt := pspResetTip
        else
          rt := pspTipHandle;
      end;

      if not eft.isRequestSupported(rt) then
        Plugin.RaiseException('Operation not supported by EFT');

      if (eft is TOnCardPSP20) then begin
        psp:=TOnCardPSP20(eft);
        try
          psp.ExtraAmount:=bill.GetActualPSPTipsAmount(nil, idpbill) + extraamount;
          psp.ControlNo:=iq.FieldByName('controlno').AsString;
          psp.ChargeType:=iq.FieldByName('chargetype').AsString;
          psp.CardNoEnterType:=iq.FieldByName('cardnoentertype').AsString;
          psp.CardNumber:=iq.FieldByName('pan').AsString;
          psp.ExpireDate:=iq.FieldByName('expire').AsString;
          psp.Track2Data:=iq.FieldByName('track2data').AsString;

          psp.DoRequest(SimpleRoundTo(price, -cinfo.Round),
                   SimpleRoundTo(middlevat * price, -cinfo.Round),
                   cinfo,
                   SysDateTimeToLocal(UPos.GetPOSNow),
                   bill.pnumber.Number,
                   orderid,
                   rt);

        finally
          freeandnil(psp);
        end;
      end else if (eft is TDibsPSP) then begin
        pspd:=TDibsPSP(eft);
        try
          pspd.DoRequest(bill.GetActualPSPTipsAmount(nil, idpbill) + extraamount,
                   0,
                   GetDibsTipsCurrency(),
                   SysDateTimeToLocal(UPos.GetPOSNow),
                   bill.pnumber.Number,
                   iq.FieldByName('controlno').AsString,
                   rt);
        finally
          freeandnil(pspd);
        end;
      end else if (eft is TPayzonePSP) then begin
        pspp := TPayzonePSP(eft);
        try
          pspp.Tip := extraamount;
          pspp.OTNumber := iq.FieldByName('field1').AsString;
          try
            pspp.DoRequest(price + extraamount,
                   0,
                   cinfo,
                   SysDateTimeToLocal(UPos.GetPOSNow),
                   bill.pnumber.Number,
                   orderid,
                   rt);
          except
            on e: exception do begin
              CheckDeclinedTransaction(eft, e);
              raise;
            end;
          end;

          bill.SaveCardPaymentInfo(tran, idpbillpayments,
            true, bill.CreateEftPaymentResultRec(eft, '', false));
        finally
          freeandnil(pspp);
        end;
      end else if (eft is TPaywarePSP) then begin
        psppy := TPaywarePSP(eft);
        try
          psppy.Tips := extraamount;
          psppy.TroutD := iq.FieldByName('controlno').AsString;
          psppy.DoRequest(price + extraamount,
                 0,
                 cinfo,
                 SysDateTimeToLocal(UPos.GetPOSNow),
                 bill.pnumber.Number,
                 orderid,
                 rt);

          bill.SaveCardPaymentInfo(tran, idpbillpayments,
            true, bill.CreateEftPaymentResultRec(eft, '', false));
        finally
          freeandnil(psppy);
        end;
      end else if (eft is TDatacapPSP) then begin
        pspdc := TDatacapPSP(eft);
        try
          pspdc.Tips := extraamount;
          pspdc.AuthCode := iq.FieldByName('auth_number').AsString;
          pspdc.AcqRefData := iq.FieldByName('field2').AsString;
          pspdc.RecordNo := iq.FieldByName('field1').AsString;
          pspdc.Reference := iq.FieldByName('controlno').AsString;
          pspdc.PrePaidPAN := iq.FieldByName('prepaid_pan').AsString;

          pspdc.DoRequest(price,
                 0,
                 cinfo,
                 SysDateTimeToLocal(UPos.GetPOSNow),
                 bill.pnumber.Number,
                 orderid,
                 rt);

          if not ResetTips then
            bill.SaveCardPaymentInfo(tran, idpbillpayments,
              true, bill.CreateEftPaymentResultRec(eft, '', false));
        finally
          freeandnil(pspdc);
        end;
      end else if (eft is TMercuryPSP) then begin
        if extraamount<>0 then begin
          pspm := TMercuryPSP(eft);
          try
            pspm.Tips := extraamount;
            pspm.AuthCode := iq.FieldByName('auth_number').AsString;
            pspm.AcqRefData := iq.FieldByName('field2').AsString;
            pspm.ProcessData := iq.FieldByName('field3').AsString;
            pspm.RecordNo := iq.FieldByName('field1').AsString;
            pspm.Reference := iq.FieldByName('controlno').AsString;
            pspm.PrePaidPAN := iq.FieldByName('prepaid_pan').AsString;

            pspm.DoRequest(price,
                   0,
                   cinfo,
                   SysDateTimeToLocal(UPos.GetPOSNow),
                   bill.pnumber.Number,
                   orderid,
                   rt);

            if not ResetTips then
              bill.SaveCardPaymentInfo(tran, idpbillpayments,
                true, bill.CreateEftPaymentResultRec(eft, '', false));
          finally
            freeandnil(pspm);
          end;
        end;
      end else if (eft is TJavaDriverPSP) then begin
        pspj := TJavaDriverPSP(eft);
        try
          cdata := GetCardPaymentData(upos.UntillDB, idpbillpayments, TEftFields.TICKET_FIELDS);
          pspj.Tips := extraamount;
          pspj.LoadPaymentData(cdata, 0);
          pspj.DoRequest(price,
                 0,
                 cinfo,
                 SysDateTimeToLocal(UPos.GetPOSNow),
                 bill.pnumber.Number,
                 orderid,
                 rt);
          if not ResetTips then
            bill.SaveCardPaymentInfo(tran, idpbillpayments,
              true, bill.CreateEftPaymentResultRec(eft, '', false));

        finally
          freeandnil(pspj);
        end;
      end else
        assert(false, 'Wrong EFT kind');

      tran.commit;

    end;
  end else begin
    bTipsModify     := true;
    idpbillpayments := 0;
  end;

  iq:=upos.UntillDB.GetIIbSql;
  iq.SetText('select p.name pname, tableno, b.number tnumber, b.suffix tsuffix, b.failurednumber tfailured, '+
      'pb.id_sales_area said from pbill_payments pp join payments p on p.id=pp.id_payments '+
      'join pbill pb on pb.id=pp.id_pbill join bill b on b.id=pb.id_bill where pp.id=:id');
  iq.q.params[0].AsInt64:=idpbillpayments;
  iq.ExecQuery;

  TranNum := TNumber.Create(
      iq.FieldByName('tnumber').AsInteger,
      iq.FieldByName('tsuffix').AsString,
      iq.FieldByName('tfailured').AsInteger
  );

  SaveBillTips(upos.UntillDB, extraamount, idpbill, idpbillpayments, bTipsModify, ResetTips);

  if not ResetTips then begin

    JournalLog.Log(upos.UntillDB, NewJDetails(jeTips, TRJDSubtype.TIPS_ADDED, upos.UserId)
    		.AddData(TRJDetails.KEY_PAYMENT_MODE, iq.FieldByName('pname').AsString)
    		.AddData(TRJDetails.KEY_AMOUNT, CurrToStr(extraamount, FmtSettings))
    		,TJournalEntryExtra.Create(iq.FieldByName('tableno').AsInteger,
        TranNum, NewNumber, iq.FieldByName('said').AsInt64));

  end else begin

    JournalLog.Log(upos.UntillDB, NewJDetails(jeTips, TRJDSubtype.TIPS_RESET, upos.UserId)
    		.AddData(TRJDetails.KEY_PAYMENT_MODE, iq.FieldByName('pname').AsString)
    		.AddData(TRJDetails.KEY_AMOUNT, CurrToStr(extraamount, FmtSettings))
    		,TJournalEntryExtra.Create(iq.FieldByName('tableno').AsInteger,
        TranNum, NewNumber, iq.FieldByName('said').AsInt64));

  end;


  bill.LoadPBill(idpbill);

  PrinterId := FTipsPrinter;

  if FTipsTicket>0 then begin

    if PrinterId=0 then
      GetBillPrinterlAndLayout(0, PrinterId, BillLayout, bHasEftPayment);

    PrintBill(blr.ActiveBill, TPrintProforma.No(), PrinterId, FTipsTicket);
  end;

end;

procedure TBLRGetTipAmount.SetCurrentTipAmount(const Value: String);
begin
  FCurrentTipAmount := Value;
  if (fid_currency = 0) or (fid_currency = upos.MainCurrency.Id) then
    FMainCurrencyTipAmount := StrToCurrDef(FCurrentTipAmount,0)
  else
    FMainCurrencyTipAmount := ConvertCustomToMainCurrency(upos.UntillDB, StrToCurrDef(FCurrentTipAmount,0), fid_currency);
end;

procedure TBLRGetTipAmount.Setidpayments(const Value: Int64);
begin
  Fidpayments := Value;
end;

procedure TBLRGetTipAmount.Setidpbill(const Value: Int64);
begin
  Fidpbill := Value;
end;

procedure TBLRGetTipAmount.Setidpbillpayments(const Value: Int64);
begin
  Fidpbillpayments := Value;
end;

procedure TBLRGetTipAmount.Setprice(const Value: Currency);
begin
  Fprice := Value;
end;

procedure TBLRGetTipAmount.SettipsType(const Value: TTipsType);
begin
  FtipsType := Value;
end;

{ TBLRGetPSPTipAmount }

function TBLRGetPSPTipAmount.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Enter PSP tip amount', 'State hint');
end;

class function TBLRGetPSPTipAmount.GetStaticGuid: string;
begin
  result := '{42646084-21CF-4640-9ADE-31B7E7F0CC83}';
end;


initialization
  RegisterClass(TBLRMsgEditPSPTips);
  RegisterClass(TBLRMsgCloseBillType);

end.
