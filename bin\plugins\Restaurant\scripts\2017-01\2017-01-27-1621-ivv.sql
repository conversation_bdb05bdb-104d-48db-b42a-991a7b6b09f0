create table ks_wf_counters (
    id u_id,
    id_wf  bigint,
    id_ksc bigint,
    constraint kswfcs_pk primary key (id),
    constraint kswfcs_fk1 foreign key (id_wf) references ks_workflow(id),
    constraint kswfcs_fk2 foreign key (id_ksc) references KITCHEN_SCREEN_COUNTERS(id)
);
commit;
grant all on ks_wf_counters to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_counters', 'p', 1);
commit;
