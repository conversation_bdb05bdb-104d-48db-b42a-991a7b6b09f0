alter table jlog 
    add kind integer,
    add id_untill_users bigint,
    add hht_addr varchar(30),
    add table_nr integer,
    add ticket_nr integer,
    add id_sales_area bigint,
    add transaction_nr integer,
    add constraint jlog_users_fk foreign key (id_untill_users) references untill_users(id),
    add constraint jlog_sa_fk foreign key (id_sales_area) references sales_area(id);
commit;

update jlog set id_untill_users=9999999999;
commit;

SET TERM ^ ;
EXECUTE BLOCK AS BEGIN
  IF (SUBSTRING(RDB$GET_CONTEXT('SYSTEM', 'ENGINE_VERSION') FROM 1 FOR 2) = '2.') THEN
    EXECUTE STATEMENT '
update RDB$RELATION_FIELDS set
RDB$NULL_FLAG = 1
where (RDB$FIELD_NAME = ''ID_UNTILL_USERS'') and
(RDB$RELATION_NAME = ''JLOG'');
';
  ELSE
    EXECUTE STATEMENT 'ALTER TABLE JLOG ALTER COLUMN ID_UNTILL_USERS SET NOT NULL';
END^
SET TERM ; ^
commit;
