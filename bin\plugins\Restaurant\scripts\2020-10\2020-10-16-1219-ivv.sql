set term !! ;
create or alter procedure fix_smartcard_tables
as 
  declare variable server_no integer;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;
                                                               
	insert into SMARTCARD_TABLE_CARDS(id_smartcard_tables, id_smartcards, rfid_code, is_active)
	select id, id_smartcards, rfid_code, 1
	from SMARTCARD_TABLES
	where is_active=1;

  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_smartcard_tables to untilluser
!!
commit
!!
execute procedure fix_smartcard_tables
!!
commit
!!
set term ; !!

