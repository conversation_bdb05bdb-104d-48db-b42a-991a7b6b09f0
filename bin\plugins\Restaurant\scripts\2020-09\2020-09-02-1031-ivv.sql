create table dep_position_ex (
    id u_id,
    id_sales_area bigint,
    id_department bigint,
    pos integer,
    constraint dep_position_ex_pk primary key (id),
    constraint dep_pos_ex_fk1 foreign key (id_sales_area) references sales_area (id),
    constraint dep_pos_ex_fk2 foreign key (id_department) references department (id)
);
commit;
execute procedure register_sync_table_ex('dep_position_ex', 'b', 1);
commit;
execute procedure register_bo_table('dep_position_ex', 'id_sales_area', 'sales_area');
commit;



