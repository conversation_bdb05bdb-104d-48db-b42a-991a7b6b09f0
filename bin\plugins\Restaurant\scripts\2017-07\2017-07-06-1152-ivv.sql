create table deposit_invoice (
    id u_id,
    id_deposit bigint,
    id_invoice bigint,
    constraint deposit_invoice_pk primary key (id),
	constraint deposit_invoice_fk1 foreign key (id_deposit) references account_deposits(id),
	constraint deposit_invoice_fk2 foreign key (id_invoice) references PRINTED_INVOICES(id)
);
commit;
grant all on deposit_invoice to untilluser;
commit;
execute procedure register_sync_table_ex('deposit_invoice', 'p', 1);
commit;





