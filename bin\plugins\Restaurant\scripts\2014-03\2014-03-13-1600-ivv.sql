create table discount_groups (
    id u_id,
    name varchar(50),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint dgr_pk primary key (id)
);
commit;
grant all on discount_groups to untilluser;
commit;
execute procedure register_sync_table_ex('discount_groups', 'b', 1);
commit;
execute procedure register_bo_table('discount_groups', '', '');
commit;
