set term !!;
CREATE OR ALTER procedure BEVERAGE_TAP2ORDER (
    CLIENT_CARD varchar(50),
    ID_ARTICLES bigint,
    QUANTITY integer,
    ID_COMPUTERS bigint,
    ID_UNTILL_USERS bigint)
as
declare variable ID_T2O_GROUP bigint;
declare variable ID_CLIENTS bigint;
declare variable T2O_ENABLED smallint;
declare variable AO_ORDER_DIRECT_SALES smallint;
declare variable AO_ORDER_TO_TABLE smallint;
begin
    if (not (:ID_ARTICLES is null)) then
    begin
        select first 1 coalesce(ao_order_direct_sales, 0), coalesce(ao_order_to_table, 0)
            from restaurant_computers where id_computers=:ID_COMPUTERS
            into :AO_ORDER_DIRECT_SALES, :AO_ORDER_TO_TABLE;
        select first 1 coalesce(t2o_enabled, 0) from restaurant_vars into t2o_enabled;
        if ((:t2o_enabled > 0) and not (:CLIENT_CARD is null)) then
        begin
            if (:t2o_enabled = 1) then
            begin
                select first 1 id_clients from smartcards
                    join clients on clients.id=id_clients and clients.is_active=1 and smartcards.IS_ACTIVE=1
                    join SMARTCARD_GROUPS gr on gr.id = smartcards.ID_SMARTCARD_GROUPS and gr.IS_ACTIVE=1
                    where upper(t2o_number)=upper(:CLIENT_CARD) into id_clients;
            end else begin
                select id from clients where upper(SMARTCARD_UID)=upper(:CLIENT_CARD) into id_clients;
            end

            if (not (:ID_CLIENTS is null)) then
            begin
                select first 1 id_t2o_groups from t2o_members where id_clients=:ID_CLIENTS and coalesce(is_active,0)=1 into id_t2o_group;
                if (not (:id_t2o_group is null)) then
                    INSERT into T2O_ORDER_QUEUE (id_clients, id_t2o_groups, id_articles, quantity, attempts, is_active, created)
                      values (null, :id_t2o_group, :id_articles, :QUANTITY, 0, 1, CURRENT_TIMESTAMP);
                else
                    INSERT into T2O_ORDER_QUEUE (id_clients, id_t2o_groups, id_articles, quantity, attempts, is_active, created)
                      values (:id_clients, null, :id_articles, :QUANTITY, 0, 1, CURRENT_TIMESTAMP);
            end else begin
                INSERT into T2O_ORDER_QUEUE (id_clients, id_t2o_groups, CLIENT_CARD, id_articles, quantity, attempts, is_active, created)
                  values (null, null, :CLIENT_CARD, :id_articles, :QUANTITY, 0, 1, CURRENT_TIMESTAMP);
            end
        end else if ((:AO_ORDER_DIRECT_SALES=1) and not (:ID_UNTILL_USERS is null)) then
        begin
            INSERT into T2O_ORDER_QUEUE (id_articles, quantity, attempts, is_active, created, id_untill_users)
              values (:id_articles, :QUANTITY, 0, 1, CURRENT_TIMESTAMP, :id_untill_users);
        end else if (:AO_ORDER_TO_TABLE=1) then
        begin
            INSERT into T2O_ORDER_QUEUE (id_articles, quantity, attempts, is_active, created)
              values (:id_articles, :QUANTITY, 0, 1, CURRENT_TIMESTAMP);
        end
    end
end
!!
commit
!!
set term ;!!
