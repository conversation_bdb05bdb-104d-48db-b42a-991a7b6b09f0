SET TERM ^ ;

create or alter procedure GETORDER<PERSON>EMOSIG_AKS (
    ID_ORDER_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable MENU_ITEM_OSIG varchar(1024);
declare variable ID_MENU_ITEM bigint;
declare variable ID_BILL bigint;
declare variable ID_MENU numeric(15,2);
begin
  execute procedure GETORDERITEMOSIG(:id_order_item) returning_values(:osig);

  select orders.ID_BILL from order_item join orders on order_item.ID_ORDERS = orders.ID
    where order_item.id = :id_order_item into :id_bill;

  select order_item.ID_MENU from order_item where order_item.id = :id_order_item into :id_menu;

  osig = osig || '_B' || id_bill;

  for select menu_item.ID from menu_item where menu_item.ID_MENU = :id_menu
    and menu_item.rowbeg = 1 INTO :id_menu_item do begin
    EXECUTE procedure GETMENUITEMOSIG(:id_menu_item) returning_values(:menu_item_osig);
    osig = osig || '/' || menu_item_osig;
  end
  suspend;
end
^

SET TERM ; ^

COMMIT;

GRANT EXECUTE ON PROCEDURE GETORDERITEMOSIG_AKS TO UNTILLUSER;

COMMIT;