create table cal_seat_requests (
	id u_id,
	id_callers bigint,
	id_users_open bigint,
	id_users_close bigint,
	open_datetime timestamp,
	close_datetime timestamp,
	pcname varchar(50),
	name varchar(100),
	covers integer,
    constraint cal_seat_requests_pk primary key (id),
    constraint cal_seat_requests_fk1 foreign key (id_callers) references callers(id),
    constraint cal_seat_requests_fk2 foreign key (id_users_open) references untill_users(id),
    constraint cal_seat_requests_fk3 foreign key (id_users_close) references untill_users(id)
);
commit;
grant all on cal_seat_requests to untilluser;
commit;
execute procedure register_sync_table_ex('cal_seat_requests', 'p', 1);
commit;

create table cal_orders (
	id u_id,
	id_orders bigint,
	id_callers bigint,
	id_users_open bigint,
	id_users_close bigint,
	open_datetime timestamp,
	close_datetime timestamp,
	pcname varchar(50),
	
    constraint cal_orders_pk primary key (id),
    constraint cal_orders_fk1 foreign key (id_callers) references callers(id),
    constraint cal_orders_fk2 foreign key (id_users_open) references untill_users(id),
    constraint cal_orders_fk3 foreign key (id_users_close) references untill_users(id),
    constraint cal_orders_fk4 foreign key (id_orders) references orders(id)
);
commit;
grant all on cal_orders to untilluser;
commit;
execute procedure register_sync_table_ex('cal_orders', 'p', 1);
commit;

create table cal_seat_request_bills (
	id u_id,
	id_cal_seat_requests bigint,
	id_untill_users bigint,
	id_bill bigint,
	pcname varchar(50),
	
    constraint cal_seat_request_bills primary key (id),
    constraint cal_seat_request_bills_fk1 foreign key (id_cal_seat_requests) references cal_seat_requests(id),
    constraint cal_seat_request_bills_fk2 foreign key (id_untill_users) references untill_users(id),
    constraint cal_seat_request_bills_fk3 foreign key (id_bill) references bill(id)
);
commit;
grant all on cal_seat_request_bills to untilluser;
commit;
execute procedure register_sync_table_ex('cal_seat_request_bills', 'p', 0);
commit;

alter table restaurant_computers 
	add temp_orders_table_from integer,
	add temp_orders_table_to integer;
commit;
