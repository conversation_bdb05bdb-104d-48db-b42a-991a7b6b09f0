create table complete_meal (
    id u_id,
    id_pbill bigint,
    id_untill_users bigint,
    cm_datetime timestamp,
    cm_pcname varchar(50),
    cm_quantity integer,
    cm_price decimal(17,4),
    constraint complete_meal_pk primary key (id),
    constraint complete_meal_fk1 foreign key (id_pbill) references pbill (id),
    constraint complete_meal_fk2 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on complete_meal to untilluser;
commit;
execute procedure register_sync_table_ex('complete_meal', 'p', 1);
commit;
