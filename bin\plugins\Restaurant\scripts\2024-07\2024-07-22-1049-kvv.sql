create table bill_printer_selections (
  id u_id,
  name varchar(100),
  is_active smallint,
  is_active_modified timestamp,
  is_active_modifier varchar(30),
  constraint bill_printer_selections_pk primary key (id)
);
commit;
grant all on bill_printer_selections to untilluser;
commit;
execute procedure register_sync_table_ex('bill_printer_selections', 'b', 1);
commit;
execute procedure register_bo_table('bill_printer_selections', '', '');
commit;

create table bill_printer_sel_items (
  id u_id,
  id_bill_printer_selections u_id, 
  name varchar(100),
  id_printers u_id,
  is_active smallint,
  is_active_modified timestamp,
  is_active_modifier varchar(30),
  constraint bill_printer_sel_items_pk primary key (id),
  constraint bill_printer_sel_items_fk1 foreign key (id_bill_printer_selections) references bill_printer_selections(id),
  constraint bill_printer_sel_items_fk2 foreign key (id_printers) references printers(id)
);
commit;
grant all on bill_printer_sel_items to untilluser;
commit;
execute procedure register_sync_table_ex('bill_printer_sel_items', 'b', 1);
commit;
execute procedure register_bo_table('bill_printer_sel_items', 'id_bill_printer_selections', 'bill_printer_selections');
commit;

create table bill_printer_sel_avail (
  id u_id,
  id_bill_printer_selections u_id, 
  id_computers bigint,
  id_falcon_terminals bigint, 
  id_ordermans bigint,
  is_active smallint,
  is_active_modified timestamp,
  is_active_modifier varchar(30),
  constraint bill_printer_sel_avail_pk primary key (id),
  constraint bill_printer_sel_avail_fk1 foreign key (id_bill_printer_selections) references bill_printer_selections(id),
  constraint bill_printer_sel_avail_fk2 foreign key (id_computers) references computers(id),
  constraint bill_printer_sel_avail_fk3 foreign key (id_falcon_terminals) references falcon_terminals(id),
  constraint bill_printer_sel_avail_fk4 foreign key (id_ordermans) references ordermans(id)
);
commit;
grant all on bill_printer_sel_avail to untilluser;
commit;
execute procedure register_sync_table_ex('bill_printer_sel_avail', 'b', 1);
commit;
execute procedure register_bo_table('bill_printer_sel_avail', 'id_bill_printer_selections', 'bill_printer_selections');
commit;
