#pragma description Untill Database Tasks Binary Data Structures
#pragma author Augment Agent
#pragma version 1.0

// Базовые типы данных Delphi/Pascal
using SmallInt = s16;
using Integer = s32;
using Boolean = u8;
using TDateTime = double; // Delphi TDateTime как double (дни с 30.12.1899)

// Структура для чтения строк из потока (AnsiString)
struct AnsiString {
    Integer length;
    char data[length];
} [[format("format_ansi_string")]];

// Структура для чтения широких строк из потока (WideString)  
struct WideString {
    Integer length;
    char16 data[length];
} [[format("format_wide_string")]];

// Функции форматирования для отображения строк
fn format_ansi_string(AnsiString str) {
    return str.data;
};

fn format_wide_string(WideString str) {
    return str.data;
};

// ========== TCompactDBTask Parameters ==========
// Структура параметров для задачи компактирования базы данных
struct TCompactDBTaskParams {
    SmallInt version;
    Boolean onlyBackup;
    Boolean repair;
    
    if (version > 0) {
        Integer days;
    }
    
    if (version > 1) {
        Boolean dontReboot;
    }
    
    if (version > 2) {
        WideString backupPath;
    }
    
    if (version > 3) {
        Boolean skipAggr;
        WideString sysdbaPwd;
    }
} [[format("format_compact_task")]];

fn format_compact_task(TCompactDBTaskParams params) {
    return std::format("CompactDB v{}: backup={}, repair={}, days={}", 
                      params.version, params.onlyBackup, params.repair, 
                      params.version > 0 ? params.days : 0);
};

// ========== TExportDBTask Parameters ==========
// Структура параметров для задачи экспорта базы данных
struct TExportDBTaskParams {
    SmallInt version;
    AnsiString formatGuid;
    AnsiString formatName;
    Boolean customTill;
    TDateTime dtTill;
    
    if (version > 1) {
        Boolean customFrom;
        TDateTime dtFrom;
    }
    
    if (version > 2) {
        Boolean monthBreaksPeriod;
    }
} [[format("format_export_task")]];

fn format_export_task(TExportDBTaskParams params) {
    return std::format("ExportDB v{}: format={}", params.version, params.formatName.data);
};

// ========== TEndOfDayTask Parameters ==========
// Структура параметров для задачи закрытия дня
struct TEndOfDayTaskParams {
    SmallInt version;
    WideString taskParams;
} [[format("format_eod_task")]];

fn format_eod_task(TEndOfDayTaskParams params) {
    return std::format("EndOfDay v{}: params={}", params.version, params.taskParams.data);
};

// ========== TAggregatesRecalculationDBTask Parameters ==========
// Структура параметров для задачи пересчета агрегатов
struct TAggregatesRecalculationTaskParams {
    SmallInt version;
    AnsiString sysdbaPwd;
} [[format("format_aggregates_task")]];

fn format_aggregates_task(TAggregatesRecalculationTaskParams params) {
    return std::format("AggregatesRecalc v{}", params.version);
};

// ========== TAggregatesRemovalDBTask Parameters ==========
// Структура параметров для задачи удаления агрегатов
struct TAggregatesRemovalTaskParams {
    SmallInt version;
    AnsiString sysdbaPwd;
} [[format("format_aggregates_removal_task")]];

fn format_aggregates_removal_task(TAggregatesRemovalTaskParams params) {
    return std::format("AggregatesRemoval v{}", params.version);
};

// ========== TFDMArchivePeriodDBTask Parameters ==========
// Структура параметров для задачи архивирования периода FDM
struct TFDMArchivePeriodTaskParams {
    SmallInt version;
    TDateTime dtFrom;
    TDateTime dtTo;
    Boolean isSingleDay;
} [[format("format_fdm_archive_task")]];

fn format_fdm_archive_task(TFDMArchivePeriodTaskParams params) {
    return std::format("FDMArchive v{}: singleDay={}", params.version, params.isSingleDay);
};

// ========== Универсальная структура для определения типа задачи ==========
// Используется для автоматического определения типа задачи по первым байтам
struct DBTaskHeader {
    SmallInt version;
    u8 peek_data[16]; // Просмотр следующих байтов для определения типа
} [[format("format_task_header")]];

fn format_task_header(DBTaskHeader header) {
    return std::format("Task Header v{}", header.version);
};

// ========== Основная структура для разбора ==========
// Выберите нужную структуру в зависимости от типа задачи
struct UntillDBTaskData {
    // Раскомментируйте нужную структуру:
    
    // TCompactDBTaskParams compactTask;
    // TExportDBTaskParams exportTask;
    // TEndOfDayTaskParams eodTask;
    // TAggregatesRecalculationTaskParams aggregatesTask;
    // TAggregatesRemovalTaskParams aggregatesRemovalTask;
    // TFDMArchivePeriodTaskParams fdmArchiveTask;
    
    // Или используйте заголовок для определения типа:
    DBTaskHeader header;
};

// Размещаем основную структуру в начале файла
UntillDBTaskData taskData @ 0x00;
