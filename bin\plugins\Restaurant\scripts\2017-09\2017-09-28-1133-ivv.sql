alter table coupons
	add id_coupon_templates bigint,
	add id_articles bigint,
	add constraint coupons_FK2 foreign key (id_coupon_templates) references coupon_templates(id),
	add constraint coupons_FK3 foreign key (id_articles) references articles(id);
commit;
execute procedure register_bo_table_ex('coupons', 'id_articles', 'articles',1);
commit;
execute procedure register_bo_table('coupons', 'id_coupon_templates', 'coupon_templates');
commit;











