create table rent_periods (
    id u_id,
    number integer,
    name varchar(50),
    comments varchar(250),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint rent_periods_pk primary key (id)
);
commit;
grant all on rent_periods to untilluser;
commit;
execute procedure register_sync_table_ex('rent_periods', 'b', 1);
commit;
execute procedure register_bo_table('rent_periods', '', '');
commit;



