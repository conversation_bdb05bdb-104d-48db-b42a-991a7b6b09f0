CREATE TABLE BCQ_CREDIT (
    SER INTEGER,
    P<PERSON>U integer,
    QUANTITY integer,
    WAITER integer,
    id_beco_locations bigint,
    constraint bcq_credit_fk1 foreign key (id_beco_locations) references beco_locations(id)
);
commit;
create generator gen_bcq_credit;
commit;
grant all on bcq_credit to untilluser;
commit;

alter table articles add bc_debitcredit smallint;
commit;

set term !! ;
create or alter trigger bcq_credit_ins_trigger for bcq_credit
active before insert
as
begin
    if (new.ser is null) then begin
      new.ser = gen_id(gen_bcq_credit, 1);
    end
end
!!
commit
!!
set term ; !!
