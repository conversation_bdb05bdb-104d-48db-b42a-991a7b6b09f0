SET TERM ^ ;

CREATE OR ALTER procedure GET_END_OF_DAY_TURNOVER (
    DATE_FROM timestamp,
    DATE_TO timestamp)
returns (
    AMOUNT decimal(17,4),
    AMOUNT_VAT decimal(17,4),
    QUANTITY integer,
    DEPNAME varchar(80),
    DEPNUMBER integer,
    SANUMBER integer)
as
begin
      for
        select sum(v.price), sum(v.vat), sum(v.quantity), v.department_name, v.department_number, v.sales_area_number
        from view_turnover_incl_room v
        join articles on v.article_id=articles.id and coalesce(articles.article_hash,0)=0
        where close_datetime >= :DATE_FROM and close_datetime < :DATE_TO
        group by department_name, department_number,
             sales_area_id, sales_area_name, sales_area_number
        into :AMOUNT, :AMOUNT_VAT, :QUANTITY,
             :DEPNAME, :DEPNUMBER, :SANUMBER
     do begin
         suspend;
     end
end
^


SET TERM ; ^

COMMIT;
