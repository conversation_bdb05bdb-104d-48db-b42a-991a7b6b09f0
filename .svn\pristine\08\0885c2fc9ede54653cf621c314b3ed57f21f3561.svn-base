unit StockDatasetsU;

interface

uses UntillDataSetsU, UntillDBU, Classes, SysUtils, CommonU, DataSetParamsFram,
  UrlNotifierU, UntillPosU, UntillGraphicElementsU, NamedParamsU, IBQuery,
  DB, DBClient, UntillequU, dialogs, IBSQL, RestaurantDSU, graphics,
  UntillPluginU, MemTableU, BOUntillDBU, DatasetFieldFormatU, SQLParamsU,
  RestaurantPluginU, KernelDatasetsU, Math, StockManagerU, StockEntityU,
  RestaurantDataSetsU, UTF8U, IngDataProviderU, BLRReorderU , ClassesU,
  RegClassesU;

type

  TIngScreenDatasetType = (isdtGeneral, isdtOrderProposal, isdtInvoice, isdtChangePO, isdtFreePO, isdtReturnGoods);
  TStockReportsContext = class(TUntillDatasetContext)
  public
    class function GetCaption: WideString; override;
  end;

  TStockTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure AddNamedParamsConditions(Select: TSQLSelectParams);
    procedure FillNamedParamsConditions(Select: TSQLSelectParams; Q: IIBSQL);
  end;

  TStockInitCountTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsGrouping: Boolean; override;
    class function SupportsOptimization: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TStockIngredientTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsGrouping: Boolean; override;
    class function SupportsOptimization: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TStockSupplierTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TStockBalanceTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TStockTurnoverTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TStockTurnoverSummaryTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TTurnoverHistoryTicketDataSet = class(TRestaurantTicketDataSet)
  private
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function SupportsOptimization: Boolean; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext): Boolean; override;
  end;

  TStockArticlesTicketDataSet = class(TStockTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext) : Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
  end;

  TStockInvoicesTicketDataSet = class(TStockTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    function GetStringRepresentation(FieldName: String;
      FormatSpec: TDataSetFieldFormat): WideString; override;

    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
  end;

  TStockInvoicesItemTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TInitInvoiceItemsTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext) : Boolean; override;
  end;

  TPurchaseOrderTicketDataSet = class(TStockTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    function GetStringRepresentation(FieldName: String;
      FormatSpec: TDataSetFieldFormat): WideString; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
  end;

  TPurchaseOrderItemTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    class function SupportsOptimization: Boolean; override;

    function GetSQLQuery: TSQLQueryParams; override;
    function GetStringRepresentation(FieldName: String;
      FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TStockAdjustmentTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;

    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TStockAdjustmentItemTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TCountSheetTicketDataSet = class(TStockTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
    class function SupportsOptimization: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext) : Boolean; override;
  end;

  TRecipeCostTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    class function SupportsOptimization: Boolean; override;
    function GetSQLQuery: TSQLQueryParams; override;
  end;

  TRecipeItemsTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    function GetStringRepresentation(FieldName: String;
      FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TReorderTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    class function SupportsOptimization: Boolean; override;

    function GetStringRepresentation(FieldName: String;
      FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TPOInfoDataSet = class(TRestaurantInfoDataSet)
  protected
    FPos : Integer;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetCaption: WideString; override;
    function    GetCurPos: Integer; override;
    procedure   ChangeCurPos(NewPos: Integer; bKeepOldPositions: Boolean = false); override;
    function    GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TReorderInfoDataSet = class(TRestaurantInfoDataSet)
  private
    sm : TStockManager;
    procedure CustomRefreshReorderDS(aDataset: TClientDataset;
      listType : TStockDocType);
  protected
    FPos : Integer;
    function GetProposalType: TStockDocType;
    function GetPosIng(aIngId: Int64): Integer;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetCaption: WideString; override;
    function    GetCurPos: Integer; override;
    procedure   ChangeCurPos(NewPos: Integer; bKeepOldPositions: Boolean = false); override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TInvoiceItemsInfoDataSet = class(TReorderInfoDataSet)
  protected
    function  GetDatasetPOID : Int64; virtual;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
  end;

  TPOModItemsInfoDataSet = class(TInvoiceItemsInfoDataSet)
  public
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
  end;

  TPOItemsInfoDataSet = class(TInvoiceItemsInfoDataSet)
  protected
    function  GetDatasetPOID : Int64; override;
  public
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
  end;

  TPONewItemsInfoDataSet = class(TReorderInfoDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName :String): Boolean; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TSupplierContactInfoDataSet = class(TRestaurantInfoDataSet)
  protected
    FPos : Integer;
    function GetPosContact(aContactId: Int64): Integer;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB)
      : WideString; override;
    class function GetCaption: WideString; override;
    function    GetCurPos: Integer; override;
    procedure   ChangeCurPos(NewPos: Integer; bKeepOldPositions: Boolean = false); override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TSalesAndCostTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;
    class function GetFieldHint(FieldName: String; DB: TUntillDB)    : WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext) : Boolean; override;
    class function SupportsOptimization: Boolean; override;

    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    function GetSQLQuery: TSQLQueryParams; override;
  end;

  TIngredentsSoldTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TSalesCostBaseTicketDataSet = class(TRestaurantTicketDataSet)
  private
    function GetOpendayQuery : string; virtual; abstract;
    procedure FillOpenValues(id_od : Int64; dt1, dt2: TDatetime);
  protected
    class function IsDetailed: boolean; virtual;
    procedure FillPurchValues(dt1, dt2 : TDatetime);
    procedure FillAdjValues(dt1, dt2 : TDatetime);
    procedure FillSoldValues(dt1, dt2 : TDatetime);
  public
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;
    procedure InitializeData; override;
  end;

  TSalesAndCostIngredentsTicketDataSet = class(TSalesCostBaseTicketDataSet)
  protected
    function GetOpendayQuery : string; override;
    class function IsDetailed: boolean; override;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;

    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
    class function SupportsOptimization: Boolean; override;
  end;

  TPhysicalCountTicketDataSet = class(TSalesCostBaseTicketDataSet)
  private
    sm : TStockManager;
    function GetOpendayQuery : string; override;
    procedure GetPreviousPhysCount(aid_pc : Int64; var afrom_dt, atill_dt: TDatetime);
  protected
    class function IsDetailed: boolean; override;
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB): WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function SupportsGrouping: Boolean; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockLocationsScreenDataSet = class(TRestaurantScreenDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function AffectVisibility: Boolean; override;
    procedure GetCurrentButtonContent(Content
      : TDataSetButtonContent); override;
    function GetVisibility: Boolean; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TSuppliersScreenDataSet = class(TRestaurantScreenDataSet)
    protected
      procedure RefreshData; override;
    public
      class function GetParamsFrame: TDataSetParamsFrameClass; override;
      class function AffectVisibility : Boolean; override;
      class function GetHint : WideString; override;
      class function GetName: String; override;
      class function GetCaption: WideString; override;
      class function GetShortCaption: WideString; override;
      procedure OnURLEvent(url: string; ue: TUrlEvent); override;
      function GetVisibility: Boolean; override;
      procedure InitializeData; override;
      procedure ButtonClick(Button: IDataSetButton); override;
      procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
      constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
      destructor Destroy; override;
  end;

  TUMCScreenDataSet = class(TRestaurantScreenDataSet)
  protected
    function GetActType : TStockUMType; virtual; abstract;
    procedure RefreshData; override;
  public
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TCountUMCScreenDataSet = class(TUMCScreenDataSet)
  protected
    function GetActType : TStockUMType; override;
  public
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
  end;

  TPurchaseUMCScreenDataSet = class(TUMCScreenDataSet)
  protected
    function GetActType : TStockUMType; override;
  public
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
  end;

  TPOScreenDataSet = class(TRestaurantScreenDataSet)
  protected
    procedure RefreshData; override;
  public
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    class function GetParamsFrame:TDataSetParamsFrameClass; override;
    class function GetFieldCaption(FieldName: String; db: TUntillDB): WideString; override;
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    constructor Create(AOwner:TComponent; ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TPOOrderedScreenDataSet = class(TPOScreenDataSet)
  public
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
  end;

  TIngReNormalTicketDataSet = class(TRestaurantTicketDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function SupportsGrouping: Boolean; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb) : WideString; override;
    class function GetHint: WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    class function IsReportDataset: Boolean; override;
    class function IsAppliableForContext(Context: TUntillDatasetContext)
      : Boolean; override;
  end;

  TIngredientsInfoDataSet = class(TRestaurantInfoDataSet)
  private
    FPos : Integer;
    mgr  : TStockManager;
  protected
    procedure RefreshData; override;
    function GetPosIng (aIngId : Int64 ): Integer;
  public
    procedure InitializeData; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb) : WideString; override;
    class function GetHint: WideString; override;
    class function GetName: String; override;
    class function GetCaption: WideString; override;
    function    GetCurPos: Integer; override;
    procedure   ChangeCurPos(NewPos: Integer; bKeepOldPositions: Boolean = false); override;
    procedure   OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner: TComponent; ANamedParams: TNamedParameters); override;
    destructor Destroy; override;
  end;

  TTransferIngredientsInfoDataSet = class(TRestaurantInfoDataSet)
  private
    FPos : Integer;
    mgr  : TStockManager;
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb) : WideString; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    function    GetCurPos: Integer; override;
    procedure   ChangeCurPos(NewPos: Integer; bKeepOldPositions: Boolean = false); override;
    procedure   OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner: TComponent; ANamedParams: TNamedParameters); override;
    destructor Destroy; override;
  end;

  TIngredientsScreenDataSet = class(TRestaurantScreenDataSet)
  private
    mgr  : TStockManager;
  protected
    function GetIngExtraInfo(ObjectId : Int64) : TExtraButtonContent;
    function GetCurrencyValue(ObjectId : Int64;
      field_Name: string): TExtraButtonContent;
    procedure RefreshData; override;
    function  GetButtonExtraData(ObjectId: Int64): TExtraButtonContent; override;
    function  GetButtonExtraData2(ObjectId: Int64): TExtraButtonContent; override;
  public
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    class function GetExtra1Caption: String; override;
    class function GetExtra2Caption: String; override;
    class function GetHint: WideString; override;
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TSupplierItemsScreenDataSet = class(TRestaurantScreenDataSet)
  private
    function GetUmName(aid_supplier_item : Int64) : string;
  protected
    procedure RefreshData; override;
    function  GetButtonExtraData(ObjectId: Int64): TExtraButtonContent; override;
  public
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    class function GetExtra1Caption: String; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    class function AffectVisibility: Boolean; override;
    function GetVisibility: Boolean; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TIngredientsPhysCountScreenDataSet = class(TIngredientsScreenDataSet)
  public
    class function GetExtra2Caption: String; override;
    function  GetButtonExtraData2(ObjectId: Int64): TExtraButtonContent; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDB) : WideString; override;
    class function GetHint: WideString; override;
    class function GetParamsFrame: TDataSetParamsFrameClass; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
  end;

  TRecipeScreenDataSet = class(TRestaurantScreenDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TIngCatScreenDataSet = class(TRestaurantScreenDataSet)
  protected
    procedure RefreshData; override;
    procedure WriteCurrentId(Value: Int64); override;
    function  ReadCurrentId(var Value: Int64): Boolean; override;
  public
    procedure ButtonClick(Button: IDataSetButton); override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    procedure GetCurrentButtonContent(Content: TDataSetButtonContent); override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockCommonSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName: String): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TStockIngSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    function  NeedRefresh :boolean; virtual;
    procedure RefreshData; override;
    procedure FillDataset(ingProv : TIngDataReadOnlyProvider; id_loc, id_supp : Int64); virtual;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName: String): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
  end;

  TStockPOSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function  GetVisibility(FieldName :String): Boolean; override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    function  GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockMultiPOSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    function  GetVisibility(FieldName :String): Boolean; override;
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockInvoiceSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function  GetVisibility(FieldName :String): Boolean; override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
  end;

  TStockPOIngSingleDataSet = class(TStockIngSingleDataSet)
  protected
    function  NeedRefresh :boolean; override;
    procedure RefreshData; override;
    procedure FillDataset(ingProv : TIngDataReadOnlyProvider; id_loc, id_supp : Int64); override;
  public
    procedure InitializeData; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockSingleDataSet = class(TRestaurantSingleDataSet)
  private
    sm     : TStockManager;
  protected
    procedure RefreshData; override;
  public
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function GetVisibility(FieldName: String): Boolean; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TEditIngredientsScreenDataSet = class(TIngredientsScreenDataSet)
  public
    class function AffectVisibility : Boolean; override;
    class function GetHint: WideString; override;
    class function GetCaption: WideString; override;
    class function GetShortCaption: WideString; override;
    function GetVisibility: Boolean; override;
  end;

  TStockSupplierSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    function  GetVisibility(FieldName :String): Boolean; override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    function GetStringRepresentation(FieldName: String; FormatSpec: TDataSetFieldFormat): WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

  TStockSupplierContactSingleDataSet = class(TRestaurantSingleDataSet)
  protected
    procedure RefreshData; override;
  public
    procedure OnURLEvent(url: string; ue: TUrlEvent); override;
    procedure InitializeData; override;
    class function GetHint: WideString; override;
    class function GetFieldCaption(FieldName: String; DB: TUntillDb): WideString; override;
    class function GetCaption: WideString; override;
    constructor Create(AOwner :TComponent;ANamedParams:TNamedParameters); override;
    destructor Destroy; override;
  end;

procedure GenerateStockRep(Untilldb: TRegUntillDB; TicketID: Int64;
  Anp: TNamedParameters; AUserId: Int64);

implementation

uses ProgressU, UntillAppU, StockInvoiceU, StockAdjustmentU,
  TicketPreviewFrm, TicketProcU, TicketsU, PurchaseOrderTypeParamsFram,
  PrinterCommU, BLRAlgU,BlrArticleU, CurrencyU,CommonStringsU,
  KernelSettingsU, ThreadObserverU, DateUtils, SelectEntityItemDlg,
  StockingLocationEntityManager,BLRAlgStockBalanceU, POSAlgU,
  BLRAlgArticleManageU, RestaurantCashDatasetsU, BLRReservationU,
  DatePeriodsU, BLRAlgIngManagerU, StockLocationTypeParamsFram,
  StockSupplierTypeParamsFram, BLSysU, OrderProposalTypeParamsFram,
  IngScreenlTypeParamsFram, SystemDataProviderU, StockCashDatasetsU, BLRSupplier,
  SupplierU, KernelCashDatasetsU, BLRPOoverviewU, StockPurchaseOrderU,
  ClientScreenParamsFram, InventoryItemEntityManager, BLRAlgStockTransferU,
  RestaurantDatasetParamsU, RestaurantCommonStringsU;
{ TStockBalanceTicketDataSet }

function GetReorderFieldName(FieldName : String) : String;
begin
  if Uppercase(FieldName) = Uppercase('supplier_article') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier article number')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('um_stock') then
    result := Plugin.Translate('StockDatasetsU', 'Stock unity of measure')
  else if Uppercase(FieldName) = Uppercase('inventory_class_number') then
    result := Plugin.Translate('StockDatasetsU', 'Category number')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Single stock price')
  else if Uppercase(FieldName) = Uppercase('total_stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Total price')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Price')
  else if Uppercase(FieldName) = Uppercase('par') then
    result := Plugin.Translate('StockDatasetsU', 'Par amount')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand')
  else if Uppercase(FieldName) = Uppercase('onhand_rnd') then
    result := Plugin.Translate('StockDatasetsU', 'On hand (rounded)')
  else if Uppercase(FieldName) = Uppercase('onhand_stock') then
    result := Plugin.Translate('StockDatasetsU', 'On hand in stock u/m')
  else if Uppercase(FieldName) = Uppercase('OrderQty') then
    result := Plugin.Translate('StockDatasetsU', 'Quantity to order')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Unity of measure')
  else if Uppercase(FieldName) = Uppercase('reorder_type') then
    result := Plugin.Translate('StockDatasetsU', 'Reorder type')
  else if Uppercase(FieldName) = Uppercase('ordered_today') then
    result := Plugin.Translate('StockDatasetsU', 'Ordered today')
  else if Uppercase(FieldName) = Uppercase('notes') then
    result := Plugin.Translate('StockDatasetsU', 'Notes')
  else if Uppercase(FieldName) = Uppercase('id_supplier') then
    result := ''
  else if Uppercase(FieldName) = Uppercase('supplier') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier')
  else if Uppercase(FieldName) = Uppercase('supplier_desc') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier description')
  else if Uppercase(FieldName) = Uppercase('id_suppinv') then
    result := ''
end;

procedure InitReorderDS(ds : TUntillDataSet);
begin
  with ds do begin
    AddFieldDef('id_inventory_item', ftLargeint);
    AddFieldDef('inventory_class_number', ftInteger);
    AddFieldDef('inventory_class', ftWideString, 100);
    AddFieldDef('inventory_item', ftWideString, 100);
    AddFieldDef('inventory_item_number', ftInteger);
    AddFieldDef('price', ftCurrency);
    AddFieldDef('onhand', ftFloat);
    AddFieldDef('onhand_stock', ftFloat);
    AddFieldDef('OrderQty', ftFloat);
    AddFieldDef('onhand_rnd', ftFloat);
    AddFieldDef('reorder_type', ftInteger);
    AddFieldDef('um', ftWideString, 50);
    AddFieldDef('um_stock', ftWideString, 50);
    AddFieldDef('par', ftCurrency);
    AddFieldDef('notes', ftWideString, 250);
    AddFieldDef('ordered_today', ftCurrency);
    AddFieldDef('stock_price', ftCurrency);
    AddFieldDef('total_stock_price', ftCurrency);
    AddFieldDef('id_supplier', ftLargeint);
    AddFieldDef('supplier', ftWideString, 255);
    AddFieldDef('supplier_desc', ftWideString, 255);
    AddFieldDef('supplier_article', ftWideString, 50);
    AddFieldDef('id_suppinv', ftLargeint);
  end;
end;

procedure RefreshReorderDS(ds : TClientDataset; id_supplier : Int64;
  bInfoDataset : boolean; listType : TStockDocType);
var qSel: IIBSQL;
  bNeedAdd : Boolean;
  par : Currency;
  idxLine : Integer;
  reorderStep : TBLRStockDoc;
  id_suppinv, id_supp, id_ing : Int64;
  rd : TReorderData;
  bResultList : boolean;
  sm: TStockManager;
  id_um_purch : Int64;
  newprice : Currency;
  newqty   : Double;
  newqtyR   : Double;
  orderedToday : Double;
  d1,d2 : TDatetime;
begin
  qSel := BLRReorderU.GetReorderIngQuery(id_supplier);
  if id_supplier > 0 then
    qSel.q.ParamByName('ID_SUPPLIERS').AsInt64 := id_supplier;
  qSel.ExecQuery;

  idxLine := 0;
  sm := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
  try
    GetRange(upos.GetPOSNow, d1, d2);
    while not qSel.eof do begin

      id_ing  := StrToInt64Def(qSel.Q.FieldByName('id').AsString,0);
      id_supp := StrToInt64Def(qSel.Q.FieldByName('id_suppliers').AsString,0);
      id_suppinv := StrToInt64Def(qSel.Q.FieldByName('id_suppinv').AsString,0);
      par      := qSel.Q.FieldByName('reorder_par').AsCurrency;
      if TReorderType(qSel.Q.FieldByName('reorder_type').asInteger) = invrtPeriodic then begin // periodic par
        par      := StockManagerU.GetIngredientParValue(upos.UntillDB, upos.GetPOSNow, id_ing);
        bNeedAdd := (qSel.Q.FieldByName('onhand').asCurrency <= par) and (par>0);
      end else
        bNeedAdd := (qSel.Q.FieldByName('onhand').asCurrency <= qSel.Q.FieldByName('reorder_min').asCurrency)
          and (qSel.Q.FieldByName('reorder_min').asCurrency > 0);

      if bNeedAdd and bInfoDataset and (posalg.GetCurrentStep is TBLRStockReorder) then begin
        bResultList := (listType = soptResult);
        // Need to check if user did not delete it
        if bResultList then // in result list only move items are shown
          bNeedAdd := assigned(TBLRStockReorder(posalg.GetCurrentStep).GetReorderItemData(id_suppinv))
        else // Original list - items , which wree not moved to show yet
          bNeedAdd := not assigned(TBLRStockReorder(posalg.GetCurrentStep).GetReorderItemData(id_suppinv))
      end;

      if bNeedAdd then begin
        ds.Append;

        // For Info dataset we have to assign group field
        orderedToday := StockManagerU.GetPOQuantityInPurchaseUM(upos.UntillDB, id_ing, id_supplier, d1, d2);
        if bInfoDataset then
          ds.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(idxLine);
        ds.FieldByName('id_inventory_item').asString       := IntToStr(id_ing);
        ds.FieldByName('id_supplier').asString             := IntToStr(id_supp);
        ds.FieldByName('id_suppinv').asString              := IntToStr(id_suppinv);
        ds.FieldByName('supplier').asString                := GetSupplierData(id_supp).name;
        ds.FieldByName('supplier_desc').asString           := qSel.Q.FieldByName('sdesc').AsString;
        ds.FieldByName('onhand_stock').AsCurrency          := qSel.Q.FieldByName('onhand').AsDouble;
        ds.FieldByName('OnHand').AsCurrency                := qSel.Q.FieldByName('onhand').AsDouble;
        ds.FieldByName('price').AsCurrency                 := 0;
        ds.FieldByName('ordered_today').AsCurrency       := orderedToday;
        if TReorderType(qSel.Q.FieldByName('reorder_type').asInteger) = invrtPeriodic then begin // periodic par
          ds.FieldByName('par').AsCurrency            := par;
          ds.FieldByName('OrderQty').AsCurrency       := par - qSel.Q.FieldByName('onhand').AsCurrency;
        end else begin
          ds.FieldByName('par').AsCurrency            := qSel.Q.FieldByName('reorder_par').AsCurrency;
          ds.FieldByName('OrderQty').AsCurrency       := qSel.Q.FieldByName('reorder_par').AsCurrency - qSel.Q.FieldByName('onhand').AsCurrency;
        end;
        ds.FieldByName('um').asString                 := qSel.Q.FieldByName('um').asString;
        ds.FieldByName('um_stock').asString           := qSel.Q.FieldByName('um').asString;

        if bInfoDataset then begin
          id_um_purch := StrToInt64Def(qSel.Q.FieldByName('ID_UNITY_PURCHASE').asString,0);
          if id_supplier > 0 then begin
            // Need recalc quantity to Purchase UM
            ds.FieldByName('um').asString := qSel.Q.FieldByName('um2').asString;
            newqty   := ds.FieldByName('OrderQty').AsCurrency;
            newprice := 0;
            sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
            ds.FieldByName('OrderQty').AsCurrency := newqty;
            newqty   := ds.FieldByName('onhand').AsCurrency;
            newqtyR  := ds.FieldByName('onhand').AsCurrency;
            sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
            ds.FieldByName('onhand').AsCurrency := newqty;
            sm.ConvertUM(nil, sttPurchase, id_um_purch, newqtyR, newprice, false, true);
            ds.FieldByName('onhand_rnd').AsCurrency            := newqtyR;
            newqty   := ds.FieldByName('par').AsCurrency;
            sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
            ds.FieldByName('par').AsCurrency := newqty;
          end;
          if (posalg.GetCurrentStep is TBLRStockDoc)
            and (listType = soptResult) // reclaced values are shown only in result list
          then begin
            reorderStep := TBLRStockDoc(posalg.GetCurrentStep);
            // If dataset is filled for Reorder BL step
            ds.FieldByName('OrderQty').AsCurrency          := 0;
            ds.FieldByName('notes').AsString               := '';
            ds.FieldByName('price').AsCurrency             := 0;
            ds.FieldByName('stock_price').AsCurrency       := 0;
            ds.FieldByName('total_stock_price').AsCurrency := 0;
            rd := reorderStep.GetReorderItemData(id_suppinv);
            if assigned(rd) then begin
              ds.FieldByName('OrderQty').AsCurrency := rd.quantity;
              ds.FieldByName('notes').AsString      := rd.notes;
              ds.FieldByName('price').AsCurrency    := rd.price;
              // Recalc back to stock price
              if id_um_purch>0 then begin
                newqty   := rd.quantity;
                newprice := rd.price;
                sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, true);
                ds.FieldByName('stock_price').AsCurrency := newprice;
                ds.FieldByName('total_stock_price').AsCurrency := newprice * newqty;
              end;
            end;
          end;
        end else begin
          // calc ReorderToday to main UM
          orderedToday := sm.GetPOQuantityInStockUm(upos.UntillDB, id_ing, d1, d2);
          ds.FieldByName('OrderQty').AsCurrency := ds.FieldByName('OrderQty').AsCurrency - orderedToday;
        end;

        ds.FieldByName('inventory_class_number').asInteger := qSel.Q.FieldByName('inventory_class_number').asInteger;
        ds.FieldByName('inventory_class').asString         := qSel.Q.FieldByName('inventory_class').asString;
        ds.FieldByName('inventory_item_number').asInteger  := qSel.Q.FieldByName('item_number').asInteger;
        ds.FieldByName('inventory_item').asString          := qSel.Q.FieldByName('inventory_item').asString;
        ds.FieldByName('reorder_type').asInteger           := qSel.Q.FieldByName('reorder_type').asInteger;
        ds.FieldByName('supplier_article').asString       := qSel.Q.FieldByName('supplier_article_number').asString;

        ds.Post;
      end;
      Inc(idxLine);
      qSel.Next;
    end;
  finally
    FreeAndNil(sm);
  end;
end;

procedure GenerateStockRep(Untilldb: TRegUntillDB; TicketID: Int64;
  Anp: TNamedParameters; AUserId: Int64);
var
  prevdlg: TTicketPreviewForm;
  MS: TMemoryStream;
  script: TPrintScript;
  qq: IIBSQL;
  CurRepType: TRepType;
  printmode: TTicketPreviewFormMode;
begin
  qq := Untilldb.GetPreparedIIbSql('select * from tickets where id =:id');
  qq.Q.Params[0].asString := IntToStr(TicketID);
  qq.ExecQuery;
  if qq.eof then
    exit;

  if qq.Q.FieldByName('a4print').asInteger = 0 then
    CurRepType := rtPOS
  else
    CurRepType := rtA4;

  UPos.SetTicketsLang(PosKernelSettings.Settings.LangOfService);
  prevdlg := TTicketPreviewForm.Create(nil);
  prevdlg.Untilldb := Untilldb;
  try
    MS := TMemoryStream.Create;
    try
      qq.FieldByName('content').SaveToStream(MS);
      MS.Position := 0;
      script := TPrintScriptFactory.MakeScript(MS, AUserId, Untilldb, Anp,
        nil);
      try
        prevdlg.Frame.PrintButtonSecurity.Text :=
          PosKernelSettings.Settings.PosPrintBtnSecurity;
        if qq.FieldByName('usedefaulta4prn').asInteger = 0 then
          printmode := pmPreviewAndPrint
        else
          printmode := pmPrintToDefaultA4Printer;
        if qq.FieldByName('custom_font').asString = '' then
          prevdlg.Execute(script, CurRepType, nil, qq.FieldByName('guid')
            .asString, printmode, MS, upos.NamedParams, AUserId)
        else
          prevdlg.ExecuteCustomFontPrinting(MS, UPos.NamedParams, nil,
            qq.FieldByName('custom_font').asString, printmode, AUserId);
      finally
        FreeAndNil(script);
      end;
    finally
      MS.Free;
    end;
  finally
    prevdlg.Free;
  end;
end;

class function TStockBalanceTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock balance',
    'Dataset caption');
end;

class function TStockBalanceTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('inventory_class_number') then
    result := Plugin.Translate('StockDatasetsU', 'Category number')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('amount') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Value')
  else if Uppercase(FieldName) = Uppercase('average_cost') then
    result := Plugin.Translate('StockDatasetsU', 'Average cost')
  else if Uppercase(FieldName) = Uppercase('last_cost') then
    result := Plugin.Translate('StockDatasetsU', 'Last cost')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('UM') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('active') then
    result := Plugin.Translate('StockDatasetsU', 'Active')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockBalanceTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Contains current stock balance',
    'Dataset hint');
end;

class function TStockBalanceTicketDataSet.GetName: String;
begin
  result := 'StockBalanceTicketDataSet';
end;

procedure TStockBalanceTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('inventory_class_number', ftInteger);
  AddFieldDef('inventory_class', ftWideString, 100);
  AddFieldDef('amount', ftFloat);
  AddFieldDef('UM', ftWideString, 50);
  AddFieldDef('price', ftFloat);
  AddFieldDef('average_cost', ftFloat);
  AddFieldDef('last_cost', ftFloat);
  AddFieldDef('active', ftSmallInt);
  AddFieldDef('stock_location', ftWideString, 50);
end;

class function TStockBalanceTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockBalanceTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TStockBalanceTicketDataSet.RefreshData;
var
  iq: IIBSQL;
  ip: IShowProgress;
  i: Integer;
  str: String;
  av_price : Currency;
begin
  inherited;
  i := 0;

  if FNamedParams.Params[TShowProgressDatasetParam.Key] is TPointerStorage then
    ip := IShowProgress(TPointerStorage(FNamedParams.Params
      [TShowProgressDatasetParam.Key]).Value)
  else
    ip := nil;

  str := 'select b.id, b.item_number, b.name inventory_item, a.amount, a.price,'
    + ' a.id_inventory_item, c.inv_category_number, c.group_name, d.name um, b.is_active,'
    + ' a.id_stock_locations id_loc from '
    + ' stock_balance a '
    + ' inner join inventory_item b on a.id_inventory_item = b.id and b.is_active=1'
    + ' inner join INVENTORY_CATEGORIES c on c.id = b.id_inventory_categories'
    + ' inner join unity d on b.id_unity = d.id and d.is_active=1';

  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    str := str + ' inner join stocking_locations e on e.id=a.id_stock_locations and e.is_active=1'
      + ' where upper(e.description)=upper(:desc)';
  iq := FUntillDB.GetPreparedIIbSql(str);
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    iq.Q.ParamByName('desc').asString :=
      UTF8_Encode(TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key])
      .WideStringValue);
  iq.ExecQuery;
  while not iq.eof do
  begin
    if assigned(ip) then
      ip.ShowProgress('', i, 100);
    inc(i);
    if i > 100 then
      i := 0;
    av_price := GetAveragePrice(FUntillDB, StrToInt64Def(iq.FieldByName('id').asString, 0));
    DataSet.Append;
    DataSet.FieldByName('id').asString := iq.FieldByName('id').asString;
    DataSet.FieldByName('inventory_class_number').asInteger :=
      iq.FieldByName('inv_category_number').asInteger;
    DataSet.FieldByName('inventory_class').asString :=
      iq.FieldByName('group_name').asString;
    DataSet.FieldByName('inventory_item_number').asInteger :=
      iq.FieldByName('item_number').asInteger;
    DataSet.FieldByName('inventory_item').asString :=
      iq.FieldByName('inventory_item').asString;
    DataSet.FieldByName('amount').AsFloat := iq.FieldByName('amount').AsDouble;
    DataSet.FieldByName('price').AsCurrency := av_price * iq.FieldByName('amount').AsDouble;
    DataSet.FieldByName('um').asString := iq.FieldByName('um').asString;
    DataSet.FieldByName('average_cost').AsFloat := av_price;
    DataSet.FieldByName('last_cost').AsFloat := GetInventoryItemLastPrice(FUntillDB,StrToInt64Def(iq.FieldByName('id').asString, 0));
    DataSet.FieldByName('active').asInteger := iq.FieldByName('is_active').asInteger;
    DataSet.FieldByName('stock_location').asString := StockCashDatasetsU.GetStockLocationName(StrToInt64Def(iq.FieldByName('id_loc').asString, 0), upos.LanguageId);
    DataSet.Post;
    iq.Next;
  end;

end;

class function TStockBalanceTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

{ TStockInvoicesTicketDataSet }

class function TStockInvoicesTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock invoices',
    'Dataset caption');
end;

class function TStockInvoicesTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('INVOICE_NUMBER') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice number')
  else if Uppercase(FieldName) = Uppercase('invoice_type') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice type')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else if Uppercase(FieldName) = Uppercase('conduct_dt') then
    result := Plugin.Translate('StockDatasetsU', 'Conduct date & time')
  else if Uppercase(FieldName) = Uppercase('invoice_date') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice date')
  else if Uppercase(FieldName) = Uppercase('supplier') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier')
  else if Uppercase(FieldName) = Uppercase('supplier_number') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier number')
  else if Uppercase(FieldName) = Uppercase('reference') then
    result := Plugin.Translate('StockDatasetsU', 'Reference')
  else if Uppercase(FieldName) = Uppercase('balanced') then
    result := Plugin.Translate('StockDatasetsU', 'Balanced')
  else if Uppercase(FieldName) = Uppercase('total') then
    result := strTotal
  else if Uppercase(FieldName) = Uppercase('freight') then
    result := Plugin.Translate('StockDatasetsU', 'Freight')
  else if Uppercase(FieldName) = Uppercase('tax') then
    result := Plugin.Translate('StockDatasetsU', 'Tax')
  else if Uppercase(FieldName) = Uppercase('other_pays') then
    result := Plugin.Translate('StockDatasetsU', 'Other payments')
  else if Uppercase(FieldName) = Uppercase('invoice_amt') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice amount')
  else if Uppercase(FieldName) = Uppercase('non_inventory') then
    result := Plugin.Translate('StockDatasetsU', 'Non inventory')
  else if Uppercase(FieldName) = Uppercase('user_name') then
    result := Plugin.Translate('StockDatasetsU', 'User')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockInvoicesTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Invoices list for the period',
    'Dataset hint');
end;

class function TStockInvoicesTicketDataSet.GetName: String;
begin
  result := 'StockInvoicesTicketDataSet';
end;

function TStockInvoicesTicketDataSet.GetSQLQuery: TSQLQueryParams;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('stock_invoice') do begin

    AddField('id').Required := True;
    AddField('invoice_type');
    AddField('invoice_date');
    AddField('INVOICE_NUMBER');
    AddField('reference');
    AddField('balanced');
    AddField('total', 'invoice_amt');
    AddField('freight');
    AddField('tax');
    AddField('other_pays');
    AddField('non_inventory');
    AddField('conduct_datetime','conduct_dt');
    AddField('total - freight - tax - other_pays - non_inventory', 'total');
    AddField('id_suppliers').Required := True;
    AddCondition('stock_invoice.is_active=1');
    with AddJoin('suppliers', 's', jInner) do
    begin
      AddCondition('s.id = stock_invoice.id_suppliers');
      AddCondition('s.is_active=1');
      AddField('s.name', 'supplier');
      AddField('s.sup_number', 'supplier_number');
    end;
    with AddJoin('stocking_locations', 'sl', jInner) do
    begin
      AddCondition('stock_invoice.id_stock_locations = sl.id');
      AddCondition('sl.is_active=1');
      AddField('sl.description', 'stock_location');
    end;
    with AddJoin('untill_users', 'u', jInner) do
    begin
      AddCondition('u.id = stock_invoice.id_untill_users');
      AddField('u.name', 'user_name');
    end;

    Params.Add(TReportPeriodDatasetParam.Key, 'invoice_date');
    Params.Add(TReportUserDatasetParam.Key, 'id_untill_users');
    Params.Add(TStockInvoiceDatasetParameter.Key, 'stock_invoice.id');
    Params.Add(TStockLocationDataSetParam.Key, 'sl.id');

  end;
end;

function TStockInvoicesTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'conduct_dt') then begin
    if DataSet.FieldByName(FieldName).asFloat=0 then
      result := ''
    else
      result := inherited GetStringRepresentation(FieldName, FormatSpec);
    exit;
  end;
  if (trim(lowercase(FieldName)) = 'invoice_type') then begin
    case TInvoiceType(DataSet.FieldByName(FieldName).asInteger) of
      invtPO:
        result := Plugin.Translate('StockDatasetsU', 'PO invoice');
      invtInvoice:
        result := Plugin.Translate('StockDatasetsU', 'Invoice');
      invtTransferIn:
        result := Plugin.Translate('StockDatasetsU', 'Transfer in');
      invtCashPaidOuts:
        result := Plugin.Translate('StockDatasetsU', 'Cash payouts');
      invtAdjustments:
        result := Plugin.Translate('StockDatasetsU', 'Adjustments');
    end;
    exit;
  end;
  if (trim(lowercase(FieldName)) = 'balanced') then
  begin
    if DataSet.FieldByName(FieldName).asBoolean then
      result := Plugin.Translate('StockDatasetsU', 'false')
    else
      result := Plugin.Translate('StockDatasetsU', 'true');
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TStockInvoicesTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('invoice_type', ftWideString, 255);
  AddFieldDef('invoice_date', ftDateTime);
  AddFieldDef('supplier_number', ftInteger);
  AddFieldDef('supplier', ftWideString, 50);
  AddFieldDef('id_suppliers', ftWideString, 50);
  AddFieldDef('reference', ftWideString, 50);
  AddFieldDef('balanced', ftBoolean);
  AddFieldDef('total', ftCurrency);
  AddFieldDef('freight', ftFloat);
  AddFieldDef('tax', ftFloat);
  AddFieldDef('other_pays', ftFloat);
  AddFieldDef('invoice_amt', ftFloat);
  AddFieldDef('non_inventory', ftFloat);
  AddFieldDef('user_name', ftWideString, 50);
  AddFieldDef('stock_location', ftWideString, 50);
  AddFieldDef('conduct_dt', ftDatetime);
  AddFieldDef('INVOICE_NUMBER', ftInteger);
end;

class function TStockInvoicesTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockInvoicesTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TStockInvoicesTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockInvoicesTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TStockInvoicesTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TStockAdjustmentTicketDataSet }

class function TStockAdjustmentTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock adjustments',
    'Dataset caption');
end;

class function TStockAdjustmentTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'adjustment_type') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment type')
  else if Uppercase(FieldName) = Uppercase('adjustment_dt') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment date')
  else if Uppercase(FieldName) = Uppercase('conduct_dt') then
    result := Plugin.Translate('StockDatasetsU', 'Conduct date & time')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location from')
  else if Uppercase(FieldName) = Uppercase('stock_location_to') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location to')
  else if Uppercase(FieldName) = Uppercase('adjustment_date') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment date & time')
  else if Uppercase(FieldName) = Uppercase('reference') then
    result := Plugin.Translate('StockDatasetsU', 'Reference')
  else if Uppercase(FieldName) = Uppercase('transfer_to_name') then
    result := Plugin.Translate('StockDatasetsU', 'Transfer-to unit')
  else if Uppercase(FieldName) = Uppercase('user_name') then
    result := Plugin.Translate('StockDatasetsU', 'User')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockAdjustmentTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Adjustments list for the period', 'Dataset hint');
end;

class function TStockAdjustmentTicketDataSet.GetName: String;
begin
  result := 'Stock adjustments';
end;

function TStockAdjustmentTicketDataSet.GetSQLQuery: TSQLQueryParams;
var id_adj : Int64;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  id_adj := 0;
  with result.AddSelect('stock_adjustment') do begin

    AddField('id').Required := True;
    AddField('adjustment_type');
    AddField('adjustment_date');
    AddField('f_LocalHourDateOf(adjustment_date)', 'adjustment_dt');
    AddField('reference');
    AddField('conduct_datetime','conduct_dt');
    AddCondition('stock_adjustment.is_active=1');
    AddCondition('stock_adjustment.conducted = 1');
    if upos.NamedParams.Contains(NAMED_PARAM_STOCK_ADJUSTMENT) then begin
      id_adj := TInt64(upos.NamedParams.Params[NAMED_PARAM_STOCK_ADJUSTMENT]).Value;
      AddCondition('stock_adjustment.id = ' + IntToStr(id_adj));
    end;

    with AddJoin('stock_units', 's', jLeftOuter) do
    begin
      AddCondition('s.id = stock_adjustment.id_transfer_to');
      AddCondition('s.is_active=1');
      AddField('s.name', 'transfer_to_name');
    end;
    with AddJoin('untill_users', 'u', jInner) do
    begin
      AddCondition('u.id = stock_adjustment.id_untill_users');
      AddCondition('u.is_active=1');
      AddField('u.name', 'user_name');
    end;
    with AddJoin('stocking_locations', 'sl1', jLeftOuter) do
    begin
      AddCondition('stock_adjustment.id_stock_locations = sl1.id');
      AddCondition('sl1.is_active=1');
      AddField('sl1.description', 'stock_location');
    end;
    with AddJoin('stocking_locations', 'sl2', jLeftOuter) do
    begin
      AddCondition('stock_adjustment.id_stock_locations_to = sl2.id');
      AddCondition('sl2.is_active=1');
      AddField('sl2.description', 'stock_location_to');
    end;
    if id_adj=0 then begin
      Params.Add(TReportPeriodDatasetParam.Key, 'adjustment_date');
      Params.Add(TReportUserDatasetParam.Key, 'id_untill_users');
      Params.Add(TStockLocationDataSetParam.Key, 'sl1.id');
    end;

  end;
end;

function TStockAdjustmentTicketDataSet.GetStringRepresentation
  (FieldName: String; FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'conduct_dt') then begin
    if DataSet.FieldByName(FieldName).asFloat=0 then
      result := ''
    else
      result := inherited GetStringRepresentation(FieldName, FormatSpec);
    exit;
  end;
  if SameText(FieldName,'adjustment_type') then begin
    case TAdjustmentType(DataSet.FieldByName(FieldName).asInteger) of
      adjTransferOut:
        result := Plugin.Translate('StockDatasetsU', 'Transfer out');
      adjNonRetailSale:
        result := Plugin.Translate('StockDatasetsU', 'Non retail sale');
      adjEmpConsumption:
        result := Plugin.Translate('StockDatasetsU', 'Employee consumption');
      adjCharity:
        result := Plugin.Translate('StockDatasetsU', 'Charity');
      adjPromotion:
        result := Plugin.Translate('StockDatasetsU', 'Promotion');
      adjRawWaste:
        result := Plugin.Translate('StockDatasetsU', 'Raw waste');
      adjFinishedWaste:
        result := Plugin.Translate('StockDatasetsU', 'Finished waste');
      adjAutoTransferLoc:
        result := Plugin.Translate('StockDatasetsU', 'POS auto adjustment');
      adjOther:
        result := Plugin.Translate('StockDatasetsU', 'Other');
      adjTransferLoc:
        result := Plugin.Translate('StockDatasetsU','Transfer between locations');
      adjPhisicalCount:
        result := Plugin.Translate('StockDatasetsU','Physical stock count');
      adjPhysicalEntry:
        result := Plugin.Translate('StockDatasetsU','Physical stock entry');
      else
        result := '';
    end;
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TStockAdjustmentTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('adjustment_type', ftWideString, 255);
  AddFieldDef('adjustment_date', ftDateTime);
  AddFieldDef('reference', ftWideString, 50);
  AddFieldDef('transfer_to_name', ftWideString, 50);
  AddFieldDef('user_name', ftWideString, 50);
  AddFieldDef('stock_location', ftWideString, 50);
  AddFieldDef('stock_location_to', ftWideString, 50);
  AddFieldDef('conduct_dt', ftDatetime);
  AddFieldDef('adjustment_dt', ftDateTime);
end;

class function TStockAdjustmentTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockAdjustmentTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TStockAdjustmentTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockAdjustmentTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True
end;

class function TStockAdjustmentTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TPurchaseOrderTicketDataSet }

class function TPurchaseOrderTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Purchase orders',
    'Dataset caption');
end;

class function TPurchaseOrderTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('customer_nr') then
    result := Plugin.Translate('StockDatasetsU', 'Customer nr.')
  else if Uppercase(FieldName) = Uppercase('number') then
    result := Plugin.Translate('StockDatasetsU', 'Number')
  else if Uppercase(FieldName) = Uppercase('order_type') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase order type')
  else if Uppercase(FieldName) = Uppercase('conduct_dt') then
    result := Plugin.Translate('StockDatasetsU', 'Conduct date & time')
  else if Uppercase(FieldName) = Uppercase('order_date') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase order date')
  else if Uppercase(FieldName) = Uppercase('reqired_date') then
    result := Plugin.Translate('StockDatasetsU', 'Required date')
  else if Uppercase(FieldName) = Uppercase('reference') then
    result := Plugin.Translate('StockDatasetsU', 'Reference')
  else if Uppercase(FieldName) = Uppercase('description') then
    result := StrDescName
  else if Uppercase(FieldName) = Uppercase('user_name') then
    result := Plugin.Translate('StockDatasetsU', 'User')
  else if Uppercase(FieldName) = Uppercase('supplier') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier')
  else if Uppercase(FieldName) = Uppercase('supplier_number') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier number')
  else if Uppercase(FieldName) = Uppercase('id') then
    result := ''
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TPurchaseOrderTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Purchase order list for the period', 'Dataset hint');
end;

class function TPurchaseOrderTicketDataSet.GetName: String;
begin
  result := 'PurchaseOrderTicketDataSet';
end;

class function TPurchaseOrderTicketDataSet.GetParamsFrame
  : TDataSetParamsFrameClass;
begin
  result := TPurchaseOrderTypeParamsFrame;
end;

function TPurchaseOrderTicketDataSet.GetSQLQuery: TSQLQueryParams;
var
  bPOType: Integer;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  bPOType := 2;
  if FStoredParams.Count >= 1 then
  begin
    if trim(FStoredParams.Strings[0]) = '0' then
      bPOType := 1
    else if trim(FStoredParams.Strings[0]) = '1' then
      bPOType := 0;
  end;

  with result.AddSelect('purchase_order') do
  begin

    AddField('id').Required := True;
    AddField('order_type');
    AddField('order_date');
    AddField('reqired_date');
    AddField('PO_NUMBER', 'number');
    AddField('reference');
    AddField('description');
    AddField('conduct_datetime','conduct_dt');
    AddField('id_suppliers').Required := True;
    if bPOType = 0 then
      AddCondition('purchase_order.conducted = 1')
    else if bPOType = 1 then
      AddCondition('purchase_order.conducted = 0');
    AddCondition('purchase_order.is_active = 1');
    with AddJoin('suppliers', 's', jInner) do begin
      AddCondition('s.id = purchase_order.id_suppliers');
      AddCondition('s.is_active = 1');
      AddField('s.name', 'supplier');
      AddField('s.sup_number', 'supplier_number');
      AddField('s.customer_nr','customer_nr');
    end;
    with AddJoin('untill_users', 'u', jInner) do
    begin
      AddCondition('u.id = purchase_order.id_untill_users');
      AddCondition('u.is_active = 1');
      AddField('u.name', 'user_name');
    end;

    if assigned(upos.NamedParams.Params[TPurchaseOrderDatasetParameter.Key])
      and (upos.NamedParams.Params[TPurchaseOrderDatasetParameter.Key] is TInt64) then begin
        Params.Add(TPurchaseOrderDatasetParameter.Key, 'purchase_order.id');
    end else begin
      Params.Add(TReportPeriodDatasetParam.Key, 'order_date');
      Params.Add(TReportUserDatasetParam.Key, 'id_untill_users');
    end;
  end;
end;

function TPurchaseOrderTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'conduct_dt') then begin
    if DataSet.FieldByName(FieldName).asFloat=0 then
      result := ''
    else
      result := inherited GetStringRepresentation(FieldName, FormatSpec);
    exit;
  end;
  if (trim(lowercase(FieldName)) = 'order_type') then
  begin
    case DataSet.FieldByName(FieldName).asInteger of
      0:
        result := Plugin.Translate('StockDatasetsU', 'Open order');
      1:
        result := Plugin.Translate('StockDatasetsU', 'Return of goods');
    end;
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TPurchaseOrderTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeInt);
  AddFieldDef('order_type', ftInteger);
  AddFieldDef('order_date', ftDateTime);
  AddFieldDef('reqired_date', ftDateTime);
  AddFieldDef('supplier', ftWideString, 50);
  AddFieldDef('supplier_number', ftInteger);
  AddFieldDef('customer_nr', ftWideString, 50);
  AddFieldDef('id_suppliers', ftWideString, 50);
  AddFieldDef('reference', ftWideString, 50);
  AddFieldDef('description', ftWideString, 250);
  AddFieldDef('user_name', ftWideString, 50);
  AddFieldDef('conduct_dt', ftDatetime);
  AddFieldDef('number', ftInteger);
end;

class function TPurchaseOrderTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TPurchaseOrderTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TPurchaseOrderTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TPurchaseOrderTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True
end;

class function TPurchaseOrderTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TStockTurnoverTicketDataSet }

class function TStockTurnoverTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock turnover',
    'Dataset caption');
end;

class function TStockTurnoverTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if SameText(FieldName,'onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if Uppercase(FieldName) = Uppercase('dayweek') then
    result := Plugin.Translate('StockDatasetsU', 'Day of week')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('sdatetime') then
    result := Plugin.Translate('StockDatasetsU', 'Turnover date/time')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('amount') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Value')
  else if Uppercase(FieldName) = Uppercase('reason') then
    result := Plugin.Translate('StockDatasetsU', 'Comment')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockTurnoverTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Contains stock turnover data for period', 'Dataset hint');
end;

class function TStockTurnoverTicketDataSet.GetName: String;
begin
  result := 'StockTurnoverTicketDataSet';
end;

function TStockTurnoverTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'dayweek')
    and (DataSet.FieldByName(FieldName).asInteger>0) then begin
    Result := DaysOfWeek[DataSet.FieldByName(FieldName).asInteger-1];
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TStockTurnoverTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 255);
  AddFieldDef('inventory_class', ftWideString, 255);
  AddFieldDef('sdatetime', ftDateTime);
  AddFieldDef('amount', ftFloat);
  AddFieldDef('price', ftCurrency);
  AddFieldDef('reason', ftWideString, 255);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('stock_location', ftWideString, 100);
  AddFieldDef('dayweek', ftInteger);
  AddFieldDef('onhand', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
end;

class function TStockTurnoverTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockTurnoverTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockTurnoverTicketDataSet.RefreshData;
var iq        : IIBSQL;
    str       : String;
begin
  str := 'select ii.id, st.sdatetime, st.quantity amount, '
    + ' standart_price price, reason,  extract(WEEKDAY from sdatetime) dayweek, '
    + ' ii.item_number inventory_item_number, ii.name inventory_item, unity.name um,'
    + ' ic.group_name inventory_class, sl.description stock_location, sl.id idsl,'
    + ' stock_balance.amount onhand, coalesce(ii.manual_price, stock_balance.price) average_price'
    + ' from stock_turnover st '
    + ' join inventory_item ii on ii.id = st.id_inventory_item and ii.is_active=1'
    + ' join unity on unity.id = ii.id_unity and unity.is_active=1'
    + ' join inventory_categories ic on ic.id = ii.id_inventory_categories '
    + ' join stocking_locations sl on sl.id = st.id_stock_locations and sl.is_active=1 '
    + ' left outer join stock_balance on stock_balance.id_inventory_item=ii.id '
    + '   and stock_balance.id_stock_locations=sl.id'
    + ' where st.sdatetime between :sd and :ed ';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then begin
    if TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value > 0 then
      str := str + ' and sl.id=:idsl'
  end;
  iq := FUntillDB.GetPreparedIIbSql(str);
  TReportPeriodDatasetParam.applyTo(iq.q, FNamedParams, 'sd', 'ed');
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    iq.q.ParamByName('idsl').AsInt64 := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;

  iq.ExecQuery;

  while not iq.eof do begin
    Dataset.Append;
    Dataset.FieldByName('inventory_item_number').AsInteger := iq.q.FieldByName('inventory_item_number').asInteger;
    Dataset.FieldByName('inventory_item').AsString := iq.q.FieldByName('inventory_item').AsString;
    Dataset.FieldByName('inventory_class').AsString := iq.q.FieldByName('inventory_class').AsString;
    Dataset.FieldByName('sdatetime').AsDatetime := iq.q.FieldByName('sdatetime').AsDatetime;
    Dataset.FieldByName('amount').AsCurrency := iq.q.FieldByName('amount').AsCurrency;
    Dataset.FieldByName('price').AsCurrency := iq.q.FieldByName('price').AsCurrency;
    Dataset.FieldByName('reason').AsString := iq.q.FieldByName('reason').AsString;
    Dataset.FieldByName('um').AsString := iq.q.FieldByName('um').AsString;
    Dataset.FieldByName('stock_location').AsString := iq.q.FieldByName('stock_location').AsString;
    Dataset.FieldByName('dayweek').AsInteger := iq.q.FieldByName('dayweek').asInteger;
    Dataset.FieldByName('onhand').AsCurrency := iq.q.FieldByName('onhand').AsCurrency;
    Dataset.FieldByName('average_price').AsCurrency := iq.q.FieldByName('average_price').AsCurrency;
    Dataset.Post;
    iq.next;
  end;

end;

class function TStockTurnoverTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

{ TStockTurnoverSummaryTicketDataSet }

class function TStockTurnoverSummaryTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Summary stock turnover',
    'Dataset caption');
end;

class function TStockTurnoverSummaryTicketDataSet.GetFieldCaption
  (FieldName: String; DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('total_cost') then
    result := Plugin.Translate('StockDatasetsU', 'Value')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Average cost')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockTurnoverSummaryTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU','Contains summary stock turnover for period', 'Dataset hint');
end;

class function TStockTurnoverSummaryTicketDataSet.GetName: String;
begin
  result := 'StockTurnoverSummaryTicketDataSet';
end;

procedure TStockTurnoverSummaryTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('inventory_item', ftString, 255);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_class', ftString, 255);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('price', ftFloat);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('stock_location', ftWideString, 50);
  AddFieldDef('total_cost', ftCurrency);
end;

class function TStockTurnoverSummaryTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockTurnoverSummaryTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockTurnoverSummaryTicketDataSet.RefreshData;
var
  iq: IIBSQL;
  str: string;
begin
  inherited;
  str := 'select Sum(quantity) quantity, Sum(standart_price * quantity) total_cost, unity.name um, '
    + ' case when Sum(abs(quantity)) > 0 then Sum(standart_price * abs(quantity))/Sum(abs(quantity)) else 0 end average_value, '
    + ' item_number inventory_item_number, inventory_item.name inventory_item, group_name inventory_class, sl.description stock_location '
    + ' from stock_turnover '
    + ' join inventory_item on inventory_item.id = stock_turnover.id_inventory_item '
    + ' join unity on unity.id = inventory_item.id_unity '
    + ' join inventory_categories on inventory_item.id_inventory_categories = inventory_categories.id '
    + ' join stocking_locations sl on stock_turnover.id_stock_locations = sl.id '
    + ' where 1=1 ';
	if TReportPeriodDatasetParam.assgnedAt(FNamedParams) then begin
    str := str + ' and sdatetime>=:fromdatetime';
    str := str + ' and sdatetime<=:tilldatetime';
  end;
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    str := str + ' and sl.id=:loc';
  str := str +
    ' group by unity.name, item_number, inventory_item.name, group_name, sl.description ';
  iq := UPos.Untilldb.GetPreparedIIbSql(str);

  TReportPeriodDatasetParam.applyTo(iq.q, FNamedParams, 'fromdatetime', 'tilldatetime');
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    iq.Q.ParamByName('loc').AsInt64 :=
      TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  iq.ExecQuery;

  while not iq.eof do
  begin
    DataSet.Append;
    DataSet.FieldByName('inventory_item_number').asInteger :=
      iq.FieldByName('inventory_item_number').asInteger;
    DataSet.FieldByName('inventory_item').asString :=
      iq.FieldByName('inventory_item').asString;
    DataSet.FieldByName('um').asString := iq.FieldByName('um').asString;
    DataSet.FieldByName('total_cost').AsCurrency := iq.FieldByName('total_cost')
      .AsCurrency;
    DataSet.FieldByName('quantity').AsFloat :=
      iq.FieldByName('quantity').AsFloat;
    DataSet.FieldByName('inventory_class').asString :=
      iq.FieldByName('inventory_class').asString;
    DataSet.FieldByName('price').AsCurrency := iq.FieldByName('average_value')
      .AsCurrency;
    DataSet.FieldByName('stock_location').asString :=
      iq.FieldByName('stock_location').asString;
    iq.Next;
  end;
end;

class function TStockTurnoverSummaryTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

{ TStockInvoicesItemTicketDataSet }

class function TStockInvoicesItemTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock invoice items',
    'Dataset caption');
end;

class function TStockInvoicesItemTicketDataSet.GetFieldCaption
  (FieldName: String; DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('vat') then
    result := Plugin.Translate('StockDatasetsU', 'V.A.T.%')
  else if Uppercase(FieldName) = Uppercase('supplier_article') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier article number')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Price')
  else if Uppercase(FieldName) = Uppercase('last_price') then
    result := Plugin.Translate('StockDatasetsU', 'Last price')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('supplier_item') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier item')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('invoice_reference') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice reference')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('notes') then
    result := Plugin.Translate('StockDatasetsU', 'Notes')
  else if Uppercase(FieldName) = Uppercase('supplier_um') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase U/M')
  else if SameText(FieldName, 'ID_INVENTORY_ITEM') then
    result := ''
  else if SameText(FieldName, 'ID_SUPPLIER_ITEM') then
    result := ''
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockInvoicesItemTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Invoice items for stock invoice', 'Dataset hint');
end;

class function TStockInvoicesItemTicketDataSet.GetName: String;
begin
  result := 'StockInvoicesItemTicketDataSet';
end;

function TStockInvoicesItemTicketDataSet.GetSQLQuery: TSQLQueryParams;
var
  SI: TStockInvoicesTicketDataSet;
begin
  SI := TStockInvoicesTicketDataSet
    (FindParentDataset(TStockInvoicesTicketDataSet));

  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('stock_invoice_item') do begin
    AddField('id').Required := True;
    AddField('quantity');
    AddField('price');
    AddField('notes');
    AddField('ID_INVENTORY_ITEM');
    AddField('ID_SUPPLIER_ITEM');
    AddCondition('stock_invoice_item.is_active=1');

    with AddJoin('inventory_item', '', jInner) do
    begin
      AddCondition('inventory_item.id = stock_invoice_item.id_inventory_item');
      AddField('name', 'inventory_item');
      AddField('item_number', 'inventory_item_number');
    end;
    with AddJoin('inventory_categories', '', jInner) do
    begin
      AddCondition
        ('inventory_item.id_inventory_categories = inventory_categories.id');
      AddField('group_name', 'inventory_class');
      AddField('VAT_PERCENT', 'vat');
    end;
    with AddJoin('stock_invoice', '', jInner) do
    begin
      AddCondition('stock_invoice.id = stock_invoice_item.id_stock_invoice');
      AddCondition('stock_invoice.is_active=1');
      AddField('reference', 'invoice_reference');
    end;
    with AddJoin('supplier_item', '', jInner) do
    begin
      AddCondition('supplier_item.id = stock_invoice_item.id_supplier_item');
      AddField('description', 'supplier_item');
      AddField('supplier_article_number','supplier_article')
    end;
    with AddJoin('unity_conversion', '', jInner) do
    begin
      AddCondition('supplier_item.id_unity_purchase = unity_conversion.id');
    end;
    with AddJoin('unity', '', jInner) do
    begin
      AddCondition('unity.id = unity_conversion.id_unity_convert');
      AddField('name', 'supplier_um');
    end;
    if not assigned(SI) then begin
      Params.Add(TReportPeriodDatasetParam.Key,'stock_invoice.invoice_date');
    end else begin
      AddCondition('id_stock_invoice = ' + SI.DataSet.FieldByName('id').asString);
    end;
  end;
end;

procedure TStockInvoicesItemTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('invoice_reference', ftWideString, 255);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('supplier_item', ftWideString, 50);
  AddFieldDef('supplier_um', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('price', ftCurrency);
  AddFieldDef('notes', ftWideString, 255);
  AddFieldDef('ID_INVENTORY_ITEM', ftLargeint);
  AddFieldDef('ID_SUPPLIER_ITEM', ftLargeint);
  AddFieldDef('supplier_article', ftWideString, 50);
  AddFieldDef('vat', ftCurrency);
end;

class function TStockInvoicesItemTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockInvoicesItemTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockInvoicesItemTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockInvoicesItemTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TStockInvoicesItemTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TStockAdjustmentItemTicketDataSet }

class function TStockAdjustmentItemTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock adjustment items',
    'Dataset caption');
end;

class function TStockAdjustmentItemTicketDataSet.GetFieldCaption
  (FieldName: String; DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if SameText(FieldName,'onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('adjustment_reference') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment reference')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('invoice_reference') then
    result := Plugin.Translate('StockDatasetsU', 'Invoice reference')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('notes') then
    result := Plugin.Translate('StockDatasetsU', 'Notes')
  else if SameText(FieldName,'price') then
    result := Plugin.Translate('StockDatasetsU', 'Price')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockAdjustmentItemTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock adjustment items',
    'Dataset caption');
end;

class function TStockAdjustmentItemTicketDataSet.GetName: String;
begin
  result := 'StockAdjustmentItemTicketDataSet';
end;

function TStockAdjustmentItemTicketDataSet.GetSQLQuery: TSQLQueryParams;
var
  SI: TStockAdjustmentTicketDataSet;
begin
  SI := TStockAdjustmentTicketDataSet
    (FindParentDataset(TStockAdjustmentTicketDataSet));
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('stock_adjustment_item') do begin

    AddField('id').Required := True;
    AddField('quantity');
    AddField('notes');
    AddField('price');
    AddCondition('stock_adjustment_item.is_active=1');

    with AddJoin('inventory_item', '', jInner) do begin
      AddCondition
        ('inventory_item.id = stock_adjustment_item.id_inventory_item');
      AddCondition('inventory_item.is_active=1');
      AddField('item_number', 'inventory_item_number');
      AddField('name', 'inventory_item');
    end;
    with AddJoin('unity', '', jInner) do begin
      AddCondition('inventory_item.id_unity = unity.id');
      AddCondition('unity.is_active=1');
      AddField('name', 'um');
    end;
    with AddJoin('inventory_categories', '', jInner) do begin
      AddCondition('inventory_item.id_inventory_categories = inventory_categories.id');
      AddCondition('inventory_categories.is_active=1');
      AddField('group_name', 'inventory_class');
    end;
    with AddJoin('stock_adjustment', '', jInner) do begin
      AddCondition
        ('stock_adjustment.id = stock_adjustment_item.id_stock_adjustment');
      AddCondition('stock_adjustment.is_active=1');
      AddField('reference', 'adjustment_reference');
    end;
    with AddJoin('stock_ing_balance', '', jleftOuter) do
    begin
      AddCondition('stock_ing_balance.ID_INVENTORY_ITEM=INVENTORY_ITEM.id');
      AddField('onhand', 'onhand');
      AddField('coalesce(inventory_item.manual_price, average_price)', 'average_price');
    end;

    if not assigned(SI) then begin
      Params.Add(TReportPeriodDatasetParam.Key,
        'stock_adjustment.adjustment_date');
    end
    else
    begin
      AddCondition('id_stock_adjustment = ' + SI.DataSet.FieldByName('id')
        .asString);
    end;

  end;
end;

procedure TStockAdjustmentItemTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('adjustment_reference', ftWideString, 255);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('notes', ftWideString, 250);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('onhand', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
  AddFieldDef('price', ftCurrency);
end;

class function TStockAdjustmentItemTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockAdjustmentItemTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TStockAdjustmentItemTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockAdjustmentItemTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True
end;

class function TStockAdjustmentItemTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TPurchaseOrderItemTicketDataSet }

class function TPurchaseOrderItemTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Purchase order items',
    'Dataset caption');
end;

class function TPurchaseOrderItemTicketDataSet.GetFieldCaption
  (FieldName: String; DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('supplier_article') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier article number')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('supplier_item') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier item description')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('order_reference') then
    result := Plugin.Translate('StockDatasetsU', 'Order reference')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('notes') then
    result := Plugin.Translate('StockDatasetsU', 'Notes')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Units of measure')
  else if Uppercase(FieldName) = Uppercase('stock_db_convUM') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase U/M')
  else if Uppercase(FieldName) = Uppercase('conversion') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase U/M convertion')
  else if Uppercase(FieldName) = Uppercase('total') then
    result := strTotal
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TPurchaseOrderItemTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Purchase order items',
    'Dataset caption');
end;

class function TPurchaseOrderItemTicketDataSet.GetName: String;
begin
  result := 'PurchaseOrderItemTicketDataSet';
end;

function TPurchaseOrderItemTicketDataSet.GetSQLQuery: TSQLQueryParams;
var
  PO: TPurchaseOrderTicketDataSet;
begin
  PO := TPurchaseOrderTicketDataSet
    (FindParentDataset(TPurchaseOrderTicketDataSet));

  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('purchase_order_item') do
  begin

    AddField('id').Required := True;
    AddField('quantity');
    AddField('quantity * price', 'total');
    AddField('price');
    AddField('notes');
    AddCondition('purchase_order_item.is_active=1');

    with AddJoin('inventory_item', '', jInner) do
    begin
      AddCondition('inventory_item.id = purchase_order_item.id_inventory_item');
      AddCondition('inventory_item.is_active=1');
      AddField('item_number', 'inventory_item_number');
      AddField('name', 'inventory_item');
    end;
    with AddJoin('inventory_categories', '', jInner) do
    begin
      AddCondition
        ('inventory_item.id_inventory_categories = inventory_categories.id');
      AddCondition('inventory_categories.is_active=1');
      AddField('group_name', 'inventory_class');
    end;
    with AddJoin('purchase_order', '', jInner) do
    begin
      AddCondition('purchase_order.id = purchase_order_item.id_purchase_order');
      AddCondition('purchase_order.is_active=1');
      AddField('reference', 'order_reference');
    end;
    with AddJoin('supplier_item', '', jInner) do
    begin
      AddCondition('supplier_item.id = purchase_order_item.id_supplier_item');
      AddCondition('supplier_item.is_active=1');
      AddField('description', 'supplier_item');
      AddField('supplier_article_number', 'supplier_article');
    end;
    with AddJoin('unity_conversion', '', jInner) do
    begin
      AddCondition('supplier_item.id_unity_purchase = unity_conversion.id');
      AddCondition('unity_conversion.is_active=1');
      AddField('factor', 'stock_db_factor').Required := True;
      AddField('conv_operator', 'stock_db_conv_operator').Required := True;
      AddField('id', 'conversion');
    end;
    with AddJoin('unity', 'main_unity', jInner) do
    begin
      AddCondition('unity_conversion.id_unity = main_unity.id');
      AddCondition('main_unity.is_active=1');
      AddField('main_unity.name', 'UM').Required := true;
    end;
    with AddJoin('unity', 'unity_conv', jInner) do
    begin
      AddCondition('unity_conversion.id_unity_convert = unity_conv.id');
      AddCondition('unity_conv.is_active=1');
      AddField('unity_conv.name', 'stock_db_convUM').Required := True;
    end;

    if assigned(PO) then begin
      AddCondition('id_purchase_order = ' + PO.DataSet.FieldByName('id')
        .asString)
    end else begin
      Params.Add(TReportPeriodDatasetParam.Key,
        'purchase_order.order_date');
    end;
  end;
end;

function TPurchaseOrderItemTicketDataSet.GetStringRepresentation
  (FieldName: String; FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'conversion') then
  begin
    result := DataSet.FieldByName('stock_db_ConvUm').asString + ' ';
    case DataSet.FieldByName('stock_db_conv_operator').asInteger of
      0:
        result := result + Plugin.Translate('StockDatasetsU', 'multiplied',
          'Dataset caption');
      1:
        result := result + Plugin.Translate('StockDatasetsU', 'devided',
          'Dataset caption');
    end;
    result := result + ' ' +
      FloatToStr(SimpleRoundTo(DataSet.FieldByName('stock_db_factor').AsFloat,-4)) + ' = ';
    result := result + ' ' + DataSet.FieldByName('UM').asString;
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TPurchaseOrderItemTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('order_reference', ftWideString, 250);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('supplier_item', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('price', ftFloat);
  AddFieldDef('notes', ftWideString, 250);
  AddFieldDef('UM', ftWideString, 50);
  AddFieldDef('Conversion', ftWideString, 50);
  AddFieldDef('total', ftFloat);
  AddFieldDef('stock_db_ConvUm', ftWideString, 50);
  AddFieldDef('stock_db_factor', ftFloat);
  AddFieldDef('stock_db_conv_operator', ftInteger);
  AddFieldDef('supplier_article', ftWideString, 50);
end;

class function TPurchaseOrderItemTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TPurchaseOrderItemTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TPurchaseOrderItemTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TPurchaseOrderItemTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TPurchaseOrderItemTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TPhysicalCountTicketDataSet }

constructor TPhysicalCountTicketDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(upos_) then
    sm := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
end;

destructor TPhysicalCountTicketDataSet.Destroy;
begin
  FreeAndNil( sm );
  inherited;
end;

class function TPhysicalCountTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Physical count sheet',
    'Dataset caption');
end;

class function TPhysicalCountTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'variance_value') then
    result := Plugin.Translate('StockDatasetsU', 'Variance value')
  else if SameText(FieldName,'average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if Uppercase(FieldName) = Uppercase('id_unity_counting') then
    result := ''
  else if Uppercase(FieldName) = Uppercase('count_frequencies') then
    result := Plugin.Translate('StockDatasetsU', 'Count frequency')
  else if Uppercase(FieldName) = Uppercase('conduct_dt') then
    result := Plugin.Translate('StockDatasetsU', 'Conduct date & time')
  else if Uppercase(FieldName) = Uppercase('notes') then
    result := Plugin.Translate('StockDatasetsU', 'Notes')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Total stock value')
  else if Uppercase(FieldName) = Uppercase('sdatetime') then
    result := Plugin.Translate('StockDatasetsU', 'Date')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock locaction')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Physical amount')
  else if Uppercase(FieldName) = Uppercase('onhand_loc_um') then
    result := Plugin.Translate('StockDatasetsU', 'Theoretical amount')
  else if Uppercase(FieldName) = Uppercase('onhand') then
    result := Plugin.Translate('StockDatasetsU', 'Theoretical amount(usage U/M)')
  else if Uppercase(FieldName) = Uppercase('variance') then
    result := Plugin.Translate('StockDatasetsU', 'Variance')
  else if Uppercase(FieldName) = Uppercase('stocking_um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('counting_um') then
    result := Plugin.Translate('StockDatasetsU', 'Count U/M')
  else if Uppercase(FieldName) = Uppercase('sale_price') then
    result := Plugin.Translate('StockDatasetsU', 'Sale price')
  else if Uppercase(FieldName) = Uppercase('quantity_stocking') then
    result := Plugin.Translate('StockDatasetsU', 'Physical amount (Usage U/M)')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TPhysicalCountTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Physical counting data',
    'Dataset caption');
end;

class function TPhysicalCountTicketDataSet.GetName: String;
begin
  result := 'PhysicalCountTicketDataSet';
end;

function TPhysicalCountTicketDataSet.GetOpendayQuery: string;
begin
    result := 'select sum(quantity), Sum(total_stock_price), inventory_item_locations.id_inventory_item, sum(onhand) onhand'
      + ' from stock_physical_count_item '
      + ' join inventory_item_locations on stock_physical_count_item.id_inventory_item_locations=inventory_item_locations.id '
      + ' join STOCK_PHYSICAL_COUNT a on a.id=stock_physical_count_item.ID_STOCK_PHYSICAL_COUNT '
      + ' where a.SDATETIME = (select max(b.SDATETIME) '
      + '       from STOCK_PHYSICAL_COUNT b '
      + '       where B.is_active=1 and B.conducted = 1 and a.ID_COUNT_FREQUENCIES=b.ID_COUNT_FREQUENCIES '
      + '          and a.ID_STOCK_LOCATIONS=b.ID_STOCK_LOCATIONS '
      + '          and b.SDATETIME < (select sdatetime from STOCK_PHYSICAL_COUNT c where c.is_active=1 and c.id =:id)) '
end;

function TPhysicalCountTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
var id_inv       : Int64;
  SPrice         : Currency;

  function GetOnHandUM : Currency;
  var id_loc_um : Int64;
      Sprice : Currency;
      newqty : Double;
  begin
    id_loc_um := StrToInt64Def(DataSet.FieldByName('id_unity_counting').asString,0);
    Sprice    := 0;
    newqty    := DataSet.FieldByName('onhand').AsCurrency;
    if id_loc_um > 100 then
      sm.ConvertUM(nil, sttCounting, id_loc_um, newqty, Sprice, false);
    result := newqty ;
  end;

begin
  if SameText(FieldName, 'onhand_loc_um') then begin
    result := CurrToStr( GetOnHandUM );
  end else if SameText(FieldName, 'conduct_dt')then begin
    if DataSet.FieldByName(FieldName).asFloat=0 then
      result := ''
    else
      result := inherited GetStringRepresentation(FieldName, FormatSpec);
  end else if SameText(FieldName, 'sale_price')then begin
    id_inv := StrToInt64Def(DataSet.FieldByName('id').asString,0);
    SPrice := BlrArticleU.GetIngredientArticle(upos.UntillDB,id_inv);
    if SPrice<>0 then
      result := CurrToStr(SPrice)
    else
      result := inherited GetStringRepresentation(FieldName, FormatSpec);
  end else
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TPhysicalCountTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('id_unity_counting', ftLargeint);
  AddFieldDef('count_frequencies', ftWideString, 255);
  AddFieldDef('sdatetime', ftDateTime);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('location', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('quantity_stocking', ftFloat);
  AddFieldDef('onhand', ftFloat);
  AddFieldDef('onhand_loc_um', ftFloat);
  AddFieldDef('variance', ftFloat);
  AddFieldDef('stocking_um', ftWideString, 50);
  AddFieldDef('counting_um', ftWideString, 50);
  AddFieldDef('sale_price', ftCurrency);
  AddFieldDef('conduct_dt', ftDatetime);
  AddFieldDef('notes', ftString, 100);
  AddFieldDef('average_price', ftCurrency);
  AddFieldDef('variance_value', ftCurrency);
end;

class function TPhysicalCountTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TPhysicalCountTicketDataSet.IsDetailed: boolean;
begin
  result := true;
end;

class function TPhysicalCountTicketDataSet.IsReportDataset: Boolean;
begin
  result := True
end;

procedure TPhysicalCountTicketDataSet.GetPreviousPhysCount(aid_pc : Int64;
  var afrom_dt, atill_dt : TDatetime);
var qSel      : IIBSQL;
begin
  afrom_dt := 0;
  atill_dt := upos.GetPOSNow;
  if aid_pc = 0 then
    qSel := upos.UntillDB.GetPreparedIIBSQL('select first 1 SDATETIME dt1, 0 dt2 from STOCK_PHYSICAL_COUNT order by SDATETIME desc ')
  else begin
    qSel := upos.UntillDB.GetPreparedIIBSQL('select max(a.SDATETIME) dt1, max(b.SDATETIME) dt2 from STOCK_PHYSICAL_COUNT a '
      + ' left outer join STOCK_PHYSICAL_COUNT b on a.ID_STOCK_LOCATIONS=b.ID_STOCK_LOCATIONS'
      + '   and a.ID_COUNT_FREQUENCIES=b.ID_COUNT_FREQUENCIES'
    + ' where a.SDATETIME < b.SDATETIME and b.id=:id and a.is_active=1 and b.is_active=1');
    qSel.q.ParamByName('id').AsInt64 := aid_pc;
  end;
  qSel.ExecQuery;
  if qSel.eof then exit;

  afrom_dt := qSel.FieldByName('dt1').asDatetime;
  if aid_pc = 0 then
    atill_dt := upos.GetPosNow
  else begin
    if qSel.FieldByName('dt2').isNull then begin
      qSel := upos.UntillDB.GetPreparedIIBSQL('select SDATETIME dt2 from STOCK_PHYSICAL_COUNT where id=:id');
      qSel.q.ParamByName('id').AsInt64 := aid_pc;
      qSel.ExecQuery;
    end;

    atill_dt := qSel.FieldByName('dt2').asDatetime;
  end;

end;

procedure TPhysicalCountTicketDataSet.RefreshData;
var strSql    : String;
    qSel      : IIBSQL;
    bOneSheet : boolean;
    from_dt, till_dt : TDatetime;
    period    : TSimplePair<TDateTime, TDateTime>;
    id_od     : Int64;
begin
  inherited;
  strSql := 'select a.quantity, '
    + ' a.quantity_stocking, a.onhand, a.onhand onhand_loc_um, a.total_stock_price stock_price,'
    + ' a.quantity_stocking - a.onhand variance, a.notes, ph.sdatetime sdatetime,'
    + ' ph.conduct_datetime conduct_dt, cf.description count_frequencies, '
    + ' sl.id_inventory_item id, sl.id_unity_counting id_unity_counting,'
    + ' un1.name counting_um, inv.name inventory_item, inv.sale_price sale_price,'
    + ' un2.name stocking_um, group_name inventory_class, loc.description location, '
    + ' inv.manual_price, ph.id id_ph,'
    + ' case when sbl.amount=0 then '
          + ' (select first 1 tr.standart_price from stock_turnover tr '
          + '    where tr.ID_INVENTORY_ITEM=sbl.ID_INVENTORY_ITEM '
          + '    and tr.ID_STOCK_LOCATIONS=loc.id order by tr.sdatetime, tr.id) '
          + ' else sbl.price/sbl.amount end average_price,'
    + ' (quantity_stocking - onhand)* (case when sbl.amount=0 then '
          + ' (select first 1 tr.standart_price from stock_turnover tr '
          + '    where tr.ID_INVENTORY_ITEM=sbl.ID_INVENTORY_ITEM '
          + '    and tr.ID_STOCK_LOCATIONS=loc.id order by tr.sdatetime, tr.id) '
          + ' else sbl.price/sbl.amount end) variance_value, a.updated'
    + ' from stock_physical_count_item a '
    + ' join stock_physical_count ph on ph.id = a.id_stock_physical_count'
    + ' join count_frequencies cf on cf.id = ph.id_count_frequencies'
    + ' join inventory_item_locations sl on sl.id = a.id_inventory_item_locations'
    + ' join unity_conversion on sl.id_unity_counting = unity_conversion.id'
    + ' join unity un1 on un1.id = unity_conversion.id_unity_convert'
    + ' join inventory_item inv on inv.id = sl.id_inventory_item'
    + ' join unity un2 on un2.id = inv.id_unity'
    + ' join inventory_categories on inv.id_inventory_categories = inventory_categories.id'
    + ' join stocking_locations loc on loc.id = sl.id_stocking_locations'
    + ' join stock_balance sbl on sbl.ID_INVENTORY_ITEM=inv.id and sbl.id_stock_locations=loc.id'
    + ' where a.updated=1 and ph.is_active=1 ';

  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    strSql := strSql + ' and loc.id=:id_stock_locations ';
  if FNamedParams.Params[TCountFrequencyDatasetParameter.Key] is TInt64WS then
    strSql := strSql + ' and inv.id_count_frequencies=:id_count_frequencies ';
  bOneSheet := false;
  if assigned(upos_.NamedParams.Params[TPhysicalReportDatasetParameter.Key]) then
     bOneSheet := TInt64(upos_.NamedParams.Params[TPhysicalReportDatasetParameter.Key]).Value > 0;
  if bOneSheet then begin
    id_od := TInt64WS(FNamedParams.Params[TPhysicalReportDatasetParameter.Key]).Value;
    strSql := strSql + ' and a.id_stock_physical_count=:id_stock_physical_count';
    GetPreviousPhysCount(id_od, from_dt, till_dt);
    from_dt := IncSecond(from_dt,1);
  end else begin
    strSql := strSql + ' and sdatetime between :dt1 and :dt2';
  	period := TSimplePair<TDateTime, TDateTime>(FNamedParams.Params[TReportPeriodDatasetParam.Key]);
    if assigned(period) then begin
      from_dt := period.Left;
      till_dt := period.Right;
    end;
  end;

  qSel := upos.UntillDB.GetPreparedIIbSql( strSql );

  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    qSel.Q.ParamByName('id_stock_locations').asInt64   := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Value;
  if FNamedParams.Params[TCountFrequencyDatasetParameter.Key] is TInt64WS then
    qSel.Q.ParamByName('id_count_frequencies').AsInt64 := TInt64WS(FNamedParams.Params[TCountFrequencyDatasetParameter.Key]).Value;
  if bOneSheet then
    qSel.Q.ParamByName('id_stock_physical_count').asInt64   := TInt64WS(FNamedParams.Params[TPhysicalReportDatasetParameter.Key]).Value
  else
    TReportPeriodDatasetParam.applyTo(qSel.q, FNamedParams, 'dt1', 'dt2');

  qSel.ExecQuery;
  if qSel.eof then exit;

  id_od := 0;
  while not qSel.Eof do begin
    Dataset.Append;
    Dataset.FieldByName('id').AsString := qSel.FieldByName('id').AsString;
    Dataset.FieldByName('id_unity_counting').AsString := qSel.FieldByName('id_unity_counting').AsString;
    Dataset.FieldByName('count_frequencies').AsString := qSel.FieldByName('count_frequencies').AsString;
    Dataset.FieldByName('sdatetime').AsDatetime := qSel.FieldByName('sdatetime').AsDatetime;
    Dataset.FieldByName('inventory_item').AsString := qSel.FieldByName('inventory_item').AsString;
    Dataset.FieldByName('inventory_class').AsString := qSel.FieldByName('inventory_class').AsString;
    Dataset.FieldByName('location').AsString := qSel.FieldByName('location').AsString;
    Dataset.FieldByName('quantity').AsCurrency := qSel.FieldByName('quantity').AsCurrency;
    Dataset.FieldByName('quantity_stocking').AsCurrency := qSel.FieldByName('quantity_stocking').AsCurrency;
    Dataset.FieldByName('onhand').AsCurrency := qSel.FieldByName('onhand').AsCurrency;
    Dataset.FieldByName('onhand_loc_um').AsCurrency := qSel.FieldByName('onhand_loc_um').AsCurrency;
    Dataset.FieldByName('stocking_um').AsString := qSel.FieldByName('stocking_um').AsString;
    Dataset.FieldByName('counting_um').AsString := qSel.FieldByName('counting_um').AsString;
    Dataset.FieldByName('sale_price').AsCurrency := qSel.FieldByName('sale_price').AsCurrency;
    Dataset.FieldByName('stock_price').AsCurrency := qSel.FieldByName('stock_price').AsCurrency;
    Dataset.FieldByName('conduct_dt').AsDatetime := qSel.FieldByName('conduct_dt').AsDatetime;
    Dataset.FieldByName('notes').AsString := qSel.FieldByName('notes').AsString;
    if qSel.FieldByName('manual_price').IsNull then
      Dataset.FieldByName('average_price').AsCurrency   := qSel.FieldByName('average_price').AsCurrency
    else
      Dataset.FieldByName('average_price').AsCurrency   := qSel.FieldByName('manual_price').AsCurrency;
    Dataset.FieldByName('open_stock_qty').AsCurrency    := 0;
    Dataset.FieldByName('open_stock_price').AsCurrency  := 0;

    Dataset.FieldByName('variance').AsCurrency          := 0;
    Dataset.FieldByName('variance_value').AsCurrency    := 0;
    if qSel.FieldByName('updated').AsInteger=1 then begin
      Dataset.FieldByName('variance').AsCurrency := qSel.FieldByName('variance').AsCurrency;
      Dataset.FieldByName('variance_value').AsCurrency    := qSel.FieldByName('variance_value').AsCurrency;
    end;

    id_od   := StrToInt64Def(qSel.FieldByName('id_ph').AsString,0);
    till_dt := IncSecond(qSel.FieldByName('sdatetime').AsDatetime,1);

    Dataset.Post;

    qSel.Next;
  end;

  // Update Opened values
  if id_od  > 0 then
    FillOpenValues(id_od, from_dt, till_dt);

  // Update sales
  FillSoldValues(from_dt, till_dt);

  // Update adjustments
  FillAdjValues(from_dt, till_dt);

  // Update invoices
  FillPurchValues(from_dt, till_dt);

end;

class function TPhysicalCountTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True
end;

{ TStockReportsContext }

class function TStockReportsContext.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock reports',
    'Reports context caption');
end;

{ TStockTicketDataSet }

procedure TStockTicketDataSet.AddNamedParamsConditions
  (Select: TSQLSelectParams);
var
  j: Integer;
begin
  inherited;
  for j := 0 to Select.Params.Count - 1 do
  begin
    if not assigned(FNamedParams.Params[Select.Params.Name[j]]) then
      continue;
    if (Select.Params.Name[j] = TStockLocationDataSetParam.Key) then
      Select.AddCondition(format('%s = :%s', [Select.Params.Val[j],
        Select.Params.Name[j]]));
    if (Select.Params.Name[j] = TPhysicalReportDatasetParameter.Key) then
      Select.AddCondition(format('%s = :%s', [Select.Params.Val[j],
        Select.Params.Name[j]]));
  end;
end;

procedure TStockTicketDataSet.FillNamedParamsConditions
  (Select: TSQLSelectParams; Q: IIBSQL);
var
  j: Integer;
begin
  inherited;
  for j := 0 to Select.Params.Count - 1 do
  begin
    if not assigned(FNamedParams.Params[Select.Params.Name[j]]) then
      continue;
    if
      (Select.Params.Name[j] = TPhysicalReportDatasetParameter.Key) or
      (Select.Params.Name[j] = TStockLocationDataSetParam.Key) then
    begin
      Q.Q.ParamByName(Select.Params.Name[j]).AsInt64 :=
        TInt64(FNamedParams.Params[Select.Params.Name[j]]).Value;
    end;
  end;
end;

{ TStockSupplierTicketDataSet }

class function TStockSupplierTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Suppliers', 'Dataset caption');
end;

class function TStockSupplierTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if FieldName = Uppercase('name') then
    result := Plugin.Translate('StockDatasetsU', 'Name')
  else if Uppercase(FieldName) = Uppercase('address') then
    result := Plugin.Translate('StockDatasetsU', 'Address')
  else if Uppercase(FieldName) = Uppercase('country') then
    result := Plugin.Translate('StockDatasetsU', 'Country')
  else if Uppercase(FieldName) = Uppercase('phone') then
    result := Plugin.Translate('StockDatasetsU', 'Phone')
  else if Uppercase(FieldName) = Uppercase('fax') then
    result := Plugin.Translate('StockDatasetsU', 'Fax')
  else if Uppercase(FieldName) = Uppercase('email') then
    result := Plugin.Translate('StockDatasetsU', 'E-mail')
  else if Uppercase(FieldName) = Uppercase('website') then
    result := Plugin.Translate('StockDatasetsU', 'Website')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockSupplierTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Contains suppliers',
    'Dataset hint');
end;

class function TStockSupplierTicketDataSet.GetName: String;
begin
  result := 'StockSupplierTicketDataSet';
end;

function TStockSupplierTicketDataSet.GetSQLQuery: TSQLQueryParams;
var
  SI: TStockInvoicesTicketDataSet;
  PO: TPurchaseOrderTicketDataSet;
  Ing: TStockIngredientTicketDataSet;
begin
  PO := nil;
  Ing := nil;
  SI := TStockInvoicesTicketDataSet
    (FindParentDataset(TStockInvoicesTicketDataSet));
  if not assigned(SI) then begin
    PO := TPurchaseOrderTicketDataSet
      (FindParentDataset(TPurchaseOrderTicketDataSet));
    if not assigned(PO) then
      Ing:= TStockIngredientTicketDataSet
        (FindParentDataset(TStockIngredientTicketDataSet));
  end;

  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('suppliers') do begin

    AddField('id').Required := True;
    AddField('name');
    AddField('address');
    AddField('phone');
    AddField('fax');
    AddField('email');
    AddField('website');
    AddCondition('suppliers.is_active=1');

    with AddJoin('countries', '', jInner) do begin
      AddCondition('countries.id = suppliers.id_countries');
      AddCondition('countries.is_active=1');
      AddField('name', 'country');
    end;

    if assigned(SI) then begin
      with AddJoin('stock_invoice', '', jInner) do
      begin
        Required := True;
        AddCondition('stock_invoice.id_suppliers = suppliers.id');
        AddCondition('stock_invoice.id_suppliers = ' + SI.DataSet.FieldByName('id_suppliers').asString);
        AddCondition('stock_invoice.is_active=1');
      end;
    end else if assigned(PO) then begin
      with AddJoin('purchase_order', '', jInner) do begin
        Required := True;
        AddCondition('purchase_order.id_suppliers = suppliers.id');
        AddCondition('purchase_order.id_suppliers = ' + PO.DataSet.FieldByName('id_suppliers').asString);
        AddCondition('purchase_order.is_active=1');
      end;
    end else if assigned(Ing) then begin
      with AddJoin('SUPPLIER_ITEM', '', jInner) do begin
        Required := True;
        AddCondition('SUPPLIER_ITEM.id_suppliers = suppliers.id');
        AddCondition('SUPPLIER_ITEM.ID_INVENTORY_ITEM = ' + Ing.DataSet.FieldByName('id').asString);
        AddCondition('SUPPLIER_ITEM.is_active=1');
      end;
    end;

  end;
end;

procedure TStockSupplierTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('name', ftWideString, 50);
  AddFieldDef('address', ftWideString, 255);
  AddFieldDef('country', ftWideString, 25);
  AddFieldDef('phone', ftWideString, 25);
  AddFieldDef('fax', ftWideString, 25);
  AddFieldDef('email', ftWideString, 25);
  AddFieldDef('website', ftWideString, 100);
end;

class function TStockSupplierTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockSupplierTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockSupplierTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockSupplierTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TRecipeCostTicketDataSet }

class function TRecipeCostTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Recipe cost', 'Dataset caption');
end;

class function TRecipeCostTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('recipe') then
    result := Plugin.Translate('StockDatasetsU', 'Recipe')
  else if Uppercase(FieldName) = Uppercase('recipe_number') then
    result := Plugin.Translate('StockDatasetsU', 'Recipe number')
  else if Uppercase(FieldName) = Uppercase('food_group') then
    result := Plugin.Translate('StockDatasetsU', 'Group')
  else if Uppercase(FieldName) = Uppercase('unit_produced') then
    result := Plugin.Translate('StockDatasetsU', 'Units produced')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Unity of measure')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TRecipeCostTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Calculation ingredients amount and prices for recipes', 'Dataset caption');
end;

class function TRecipeCostTicketDataSet.GetName: String;
begin
  result := 'RecipeCostTicketDataSet';
end;

procedure TRecipeCostTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('recipe', ftWideString, 100);
  AddFieldDef('recipe_number', ftInteger);
  AddFieldDef('food_group', ftWideString, 100);
  AddFieldDef('unit_produced', ftFloat);
  AddFieldDef('UM', ftWideString, 100);
end;

class function TRecipeCostTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TRecipeCostTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TRecipeCostTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TRecipeCostTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

function TRecipeCostTicketDataSet.GetSQLQuery: TSQLQueryParams;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('recipe') do
  begin

    AddField('id', 'id').Required := True;
    AddField('name', 'recipe');
    AddField('recipe_number', 'recipe_number');
    AddField('unit_produced');
    AddCondition('recipe.is_active=1');

    with AddJoin('food_group', '', jInner) do
    begin
      AddCondition('food_group.id = recipe.id_food_group');
      AddCondition('food_group.is_active=1');
      AddField('name', 'food_group');
    end;
    with AddJoin('unity', '', jInner) do
    begin
      AddCondition('unity.id = recipe.id_unity_produced');
      AddCondition('unity.is_active=1');
      AddField('name', 'um');
    end;
  end;
end;

class function TRecipeCostTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TReorderTicketDataSet }

class function TReorderTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Reorder report',
    'Dataset caption');
end;

class function TReorderTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  result := GetReorderFieldName(fieldName);
end;

class function TReorderTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Lists quantities of inventory items needed to meet the requirements of anticipated sales',
    'Dataset caption');
end;

class function TReorderTicketDataSet.GetName: String;
begin
  result := 'ReorderTicketDataSet';
end;

function TReorderTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'reorder_type') then
  begin
    if SameText(DataSet.FieldByName(FieldName).asString, '0') then
      result := Plugin.Translate('StockDatasetsU', 'Simple Par')
    else if SameText(DataSet.FieldByName(FieldName).asString, '1') then
      result := Plugin.Translate('StockDatasetsU', 'Periodic Par');
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TReorderTicketDataSet.InitializeData;
begin
  inherited;
  InitReorderDS(self);
end;

class function TReorderTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TReorderTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TReorderTicketDataSet.RefreshData;
begin
  inherited;
  RefreshReorderDS(Dataset,0,false, soptOriginal);
end;

class function TReorderTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TReorderTicketDataSet.SupportsOptimization: Boolean;
begin
  result := false;
end;

{ TSalesAndCostTicketDataSet }

class function TSalesAndCostTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Sales and cost',
    'Dataset caption');
end;

class function TSalesAndCostTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Unity of measure')
  else if Uppercase(FieldName) = Uppercase('vat') then
    result := Plugin.Translate('StockDatasetsU', 'V.A.T.')
  else if Uppercase(FieldName) = Uppercase('department_number') then
    result := Plugin.Translate('StockDatasetsU', 'Department number')
  else if Uppercase(FieldName) = Uppercase('article_number') then
    result := Plugin.Translate('StockDatasetsU', 'Article number')
  else if Uppercase(FieldName) = Uppercase('sold_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Sold quantity')
  else if Uppercase(FieldName) = Uppercase('sold_price') then
    result := Plugin.Translate('StockDatasetsU', 'Total sold')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Theoretical cost')
  else if Uppercase(FieldName) = Uppercase('profit') then
    result := Plugin.Translate('StockDatasetsU', 'Gross profit')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TSalesAndCostTicketDataSet.GetFieldHint(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M of ingredient')
  else if Uppercase(FieldName) = Uppercase('department_number') then
    result := Plugin.Translate('StockDatasetsU', 'Pos department number')
  else if Uppercase(FieldName) = Uppercase('article_number') then
    result := Plugin.Translate('StockDatasetsU', 'Pos article number')
  else if Uppercase(FieldName) = Uppercase('sold_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Sold ingredients quantity')
  else if Uppercase(FieldName) = Uppercase('sold_price') then
    result := Plugin.Translate('StockDatasetsU', 'Total ingredients sold')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Theoretical ingredient cost (calculated based on supplies)')
  else if Uppercase(FieldName) = Uppercase('profit') then
    result := Plugin.Translate('StockDatasetsU', 'Gross profit')
  else
    result := inherited GetFieldHint(FieldName, DB);
end;

class function TSalesAndCostTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Sales and Cost stock report','Dataset caption');
end;

class function TSalesAndCostTicketDataSet.GetName: String;
begin
  result := 'SalesAndCostTicketDataSet';
end;

function TSalesAndCostTicketDataSet.GetSQLQuery: TSQLQueryParams;
var strArtName : string;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('order_item') do begin
    AddField('price * quantity * menu_quantity', 'sold_price').Required := True;
    AddField('quantity * menu_quantity', 'sold_qty').Required := True;
    AddField('(price - coalesce(order_item_stock_price.price,0)) * quantity * menu_quantity','profit').Required := True;
    AddField('vat * quantity * menu_quantity', 'vat');
    with AddJoin('order_item_stock_price', '', jLeftOuter) do begin
      AddCondition('order_item_stock_price.id_order_item = order_item.id');
      AddField('coalesce(price,0) * order_item.quantity * order_item.menu_quantity',
        'stock_price').Required := True;
    end;
    with AddJoin('orders', '', jInner) do begin
      AddCondition('orders.id = order_item.id_orders');
      AddField('ord_datetime', 'sac_ord_datetime').Required := True;
    end;
    strArtName := '(case when coalesce(size_modifier_item.smi_name,'''')='''' then articles.name else size_modifier_item.smi_name || '' '' || articles.name end)';
    with AddJoin('articles', '', jInner) do begin
      AddCondition('articles.id = order_item.id_articles');
      AddField(strArtName, 'article').Required := True;
      AddField('article_number').Required := True;
    end;
    with AddJoin('department', '', jInner) do begin
      AddCondition('department.id = articles.id_departament');
      AddField('department.name', 'department').Required := True;
      AddField('dep_number', 'department_number').Required := True;
    end;
    with AddJoin('ORDER_ITEM_SIZES', '', jLeftOuter) do begin
        AddCondition('order_item.id = ORDER_ITEM_SIZES.id_order_item')
    end;
    with AddJoin('ARTICLE_SIZE_MODIFIER', '', jLeftOuter) do begin
        AddCondition('ARTICLE_SIZE_MODIFIER.id_articles = order_item.id_articles');
        AddCondition('ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM=ORDER_ITEM_SIZES.ID_SIZE_MODIFIER_ITEM');
    end;
    with AddJoin('SIZE_MODIFIER_ITEM', '', jLeftOuter) do begin
      AddCondition('SIZE_MODIFIER_ITEM.id=ORDER_ITEM_SIZES.ID_SIZE_MODIFIER_ITEM');
    end;
    with AddJoin('unity_conversion', '', jLeftOuter) do begin
      AddCondition('unity_conversion.id = coalesce(ARTICLE_SIZE_MODIFIER.id_unity_sales, articles.id_unity_sales)');
    end;
    with AddJoin('unity', '', jLeftOuter) do begin
      AddCondition('unity_conversion.id_unity_convert = unity.id');
      AddField('name', 'um').Required := True;;
    end;
    Params.Add(TReportPeriodDatasetParam.Key, 'orders.ord_datetime');
  end;
  with result.AddSelect('menu_item') do begin
    AddField('price * quantity * order_item.quantity', 'sold_price').Required := True;
    AddField('quantity * order_item.quantity', 'sold_qty').Required := True;
    AddField('(price - coalesce(order_item_stock_price.price,0)) * quantity * order_item.quantity','profit').Required := True;
    AddField('vat * quantity * order_item.quantity', 'vat');
    with AddJoin('order_item_stock_price', '', jLeftOuter) do begin
      AddCondition('order_item_stock_price.id_menu_item = menu_item.id');
      AddField('coalesce(price,0) * order_item.quantity * menu_item.quantity','stock_price').Required := True;
    end;
    with AddJoin('order_item', '', jInner) do begin
      AddCondition('order_item.id_menu = menu_item.id_menu');
    end;
    with AddJoin('orders', '', jInner) do begin
      AddCondition('orders.id = order_item.id_orders');
      AddField('ord_datetime', 'sac_ord_datetime').Required := True;
    end;
    strArtName := '(case when coalesce(size_modifier_item.smi_name,'''')='''' then articles.name else size_modifier_item.smi_name || '' '' || articles.name end)';
    with AddJoin('articles', '', jInner) do begin
      AddCondition('articles.id = menu_item.id_articles');
      AddField(strArtName, 'article').Required := True;
      AddField('article_number').Required := True;
    end;
    with AddJoin('department', '', jInner) do begin
      AddCondition('department.id = articles.id_departament');
      AddField('department.name', 'department').Required := True;
      AddField('dep_number', 'department_number').Required := True;
    end;
    with AddJoin('ORDER_ITEM_SIZES', '', jLeftOuter) do begin
        AddCondition('menu_item.id = ORDER_ITEM_SIZES.id_menu_item')
    end;
    with AddJoin('ARTICLE_SIZE_MODIFIER', '', jLeftOuter) do begin
        AddCondition('ARTICLE_SIZE_MODIFIER.id_articles = menu_item.id_articles');
        AddCondition('ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM=ORDER_ITEM_SIZES.ID_SIZE_MODIFIER_ITEM');
    end;
    with AddJoin('SIZE_MODIFIER_ITEM', '', jLeftOuter) do begin
      AddCondition('SIZE_MODIFIER_ITEM.id=ORDER_ITEM_SIZES.ID_SIZE_MODIFIER_ITEM');
    end;
    with AddJoin('unity_conversion', '', jLeftOuter) do begin
      AddCondition('unity_conversion.id = coalesce(ARTICLE_SIZE_MODIFIER.id_unity_sales, articles.id_unity_sales)');
    end;
    with AddJoin('unity', '', jLeftOuter) do begin
      AddCondition('unity_conversion.id_unity_convert = unity.id');
      AddField('name', 'um').Required := True;;
    end;
    Params.Add(TReportPeriodDatasetParam.Key, 'orders.ord_datetime');
  end;
end;

function TSalesAndCostTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TSalesAndCostTicketDataSet.InitializeData;
begin
  inherited;
  with DataSet do begin
    with FieldDefs.AddFieldDef do begin
      Name := 'sac_ord_datetime';
      DataType := ftDateTime;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'department_number';
      DataType := ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'department';
      DataType := ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'article_number';
      DataType := ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'article';
      DataType := ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'id_menu';
      DataType := ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'um';
      DataType := ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'sold_qty';
      DataType := ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'sold_price';
      DataType := ftFloat;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'stock_price';
      DataType := ftFloat;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'profit';
      DataType := ftFloat;
    end;
    with FieldDefs.AddFieldDef do begin
      Name := 'vat';
      DataType := ftCurrency;
    end;
  end;
end;

class function TSalesAndCostTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TSalesAndCostTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TSalesAndCostTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TSalesAndCostTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TSalesAndCostTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TStockIngredientTicketDataSet }

class function TStockIngredientTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Ingredients', 'Dataset hint');
end;

class function TStockIngredientTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if SameText(FieldName,'onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if FieldName = Uppercase('name') then
    result := Plugin.Translate('StockDatasetsU', 'Name')
  else if Uppercase(FieldName) = Uppercase('number') then
    result := Plugin.Translate('StockDatasetsU', 'Number')
  else if Uppercase(FieldName) = Uppercase('category') then
    result := Plugin.Translate('StockDatasetsU', 'Class')
  else if Uppercase(FieldName) = Uppercase('frequency') then
    result := Plugin.Translate('StockDatasetsU', 'Count frequency')
  else if Uppercase(FieldName) = Uppercase('default_yield') then
    result := Plugin.Translate('StockDatasetsU', 'Default yield')
  else if Uppercase(FieldName) = Uppercase('UM') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('reorder_type') then
    result := Plugin.Translate('StockDatasetsU', 'Reorder type')
  else if Uppercase(FieldName) = Uppercase('reorder_min') then
    result := Plugin.Translate('StockDatasetsU', 'Reorder minimium')
  else if Uppercase(FieldName) = Uppercase('reorder_par') then
    result := Plugin.Translate('StockDatasetsU', 'Reorder par')
  else if Uppercase(FieldName) = Uppercase('purchase_UM') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase U/M')
  else if Uppercase(FieldName) = Uppercase('purchase_price') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase price')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockIngredientTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Contains ingredients',
    'Dataset hint');
end;

class function TStockIngredientTicketDataSet.GetName: String;
begin
  result := 'StockIngredientTicketDataSet';
end;

function TStockIngredientTicketDataSet.GetSQLQuery: TSQLQueryParams;
var supp: TStockSupplierTicketDataSet;
begin
  supp := TStockSupplierTicketDataSet (FindParentDataset(TStockSupplierTicketDataSet));

  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('inventory_item') do begin

    AddField('id').Required := True;
    AddField('item_number', 'number');
    AddField('name');
    AddField('default_yield');
    AddField('reorder_type');
    AddField('reorder_min');
    AddField('reorder_par');
    AddCondition('inventory_item.is_active=1');

    if assigned(supp) then begin
      with AddJoin('SUPPLIER_ITEM', '', jInner) do begin
        Required := True;
        AddField('price','purchase_price');
        AddField('ID_UNITY_PURCHASE', 'purchase_um');
        AddCondition('SUPPLIER_ITEM.id_suppliers = ' + supp.DataSet.FieldByName('id').asString);
        AddCondition('SUPPLIER_ITEM.id_inventory_item = inventory_item.id ');
        AddCondition('SUPPLIER_ITEM.is_active=1');
      end;
    end else begin
      AddField('0','purchase_price');
      AddField('0', 'purchase_um');
    end;
    with AddJoin('inventory_categories', '', jInner) do begin
      AddCondition('inventory_categories.id = inventory_item.id_inventory_categories');
      AddCondition('inventory_categories.is_active=1');
      AddField('group_name', 'category');
    end;
    with AddJoin('count_frequencies', '', jInner) do begin
      AddCondition('count_frequencies.id = inventory_item.id_count_frequencies');
      AddCondition('count_frequencies.is_active=1');
      AddField('description', 'frequency');
    end;
    with AddJoin('UNITY', '', jInner) do
    begin
      AddCondition('UNITY.id = inventory_item.ID_UNITY');
      AddField('name', 'um');
    end;
    with AddJoin('stock_ing_balance', '', jleftOuter) do
    begin
      AddCondition('stock_ing_balance.ID_INVENTORY_ITEM=INVENTORY_ITEM.id');
      AddField('onhand', 'onhand');
      AddField('coalesce(inventory_item.manual_price, average_price)',  'average_price');
    end;
  end;
end;

function TStockIngredientTicketDataSet.GetStringRepresentation
  (FieldName: String; FormatSpec: TDataSetFieldFormat): WideString;
begin
  if SameText(FieldName, 'purchase_um') then begin
      result := GetUMCByConvId(upos.UntillDB, StrToInt64Def(DataSet.FieldByName(FieldName).asString,0));
    exit;
  end else if SameText(FieldName, 'reorder_type') then begin
    case DataSet.FieldByName(FieldName).asInteger of
      0: result := Plugin.Translate('StockDatasetsU', 'Simple Par');
      1: result := Plugin.Translate('StockDatasetsU', 'Periodic Par');
    end;
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TStockIngredientTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('number', ftInteger);
  AddFieldDef('name', ftWideString, 50);
  AddFieldDef('category', ftWideString, 50);
  AddFieldDef('frequency', ftWideString, 50);
  AddFieldDef('default_yield', ftFloat);
  AddFieldDef('UM', ftWideString, 50);
  AddFieldDef('reorder_type', ftWideString, 50);
  AddFieldDef('reorder_min', ftFloat);
  AddFieldDef('reorder_par', ftFloat);
  AddFieldDef('onhand', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
  AddFieldDef('purchase_UM', ftWideString, 50);
  AddFieldDef('purchase_price', ftCurrency);
end;

class function TStockIngredientTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockIngredientTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockIngredientTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockIngredientTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

class function TStockIngredientTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TStockInitCountTicketDataSet }

class function TStockInitCountTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Initial count sheet',
    'Dataset hint');
end;

class function TStockInitCountTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('location') then
    result := Plugin.Translate('StockDatasetsU', 'Location')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('shelf_code') then
    result := Plugin.Translate('StockDatasetsU', 'Shelf code')
  else if Uppercase(FieldName) = Uppercase('UM') then
    result := Plugin.Translate('StockDatasetsU', 'Unity of measure')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockInitCountTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU',
    'Shows ingredients to be initially counted', 'Dataset hint');
end;

class function TStockInitCountTicketDataSet.GetName: String;
begin
  result := 'StockInitCountTicketDataSet';
end;

function TStockInitCountTicketDataSet.GetSQLQuery: TSQLQueryParams;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('inventory_item_locations') do
  begin

    AddCondition('inventory_item_locations.is_active=1');
    AddField('shelf', 'shelf_code');
    with AddJoin('inventory_item', '', jInner) do
    begin
      AddCondition
        ('inventory_item.id = inventory_item_locations.id_inventory_item');
      AddCondition('inventory_item.is_active=1');
      AddField('inventory_item.name', 'inventory_item');
      AddField('inventory_item.item_number', 'inventory_item_number');
    end;
    with AddJoin('stocking_locations', '', jInner) do
    begin
      Required := True;
      AddCondition
        ('stocking_locations.id = inventory_item_locations.id_stocking_locations');
      AddCondition('stocking_locations.is_active=1');
      AddField('description', 'location');
      Params.Add(TStockLocationDataSetParam.Key, 'stocking_locations.id');
    end;
    with AddJoin('inventory_categories', '', jInner) do
    begin
      AddCondition
        ('inventory_categories.id = inventory_item.id_inventory_categories');
      AddCondition('inventory_categories.is_active=1');
      AddField('group_name', 'inventory_class');
    end;
    with AddJoin('unity_conversion', '', jInner) do
    begin
      AddCondition
        ('unity_conversion.id = inventory_item_locations.id_unity_counting');
      AddCondition('unity_conversion.is_active=1');
      AddCondition('inventory_categories.is_active=1');
    end;
    with AddJoin('unity', '', jInner) do
    begin
      AddCondition('unity.id = unity_conversion.id_unity_convert');
      AddCondition('unity.is_active=1');
      AddField('name', 'um');
    end;
    AddCondition(' not exists( select * from stock_balance ' +
      ' where stock_balance.id_inventory_item = inventory_item_locations.id_inventory_item '
      + ' and stock_balance.id_stock_locations=inventory_item_locations.id)');
  end;
end;

procedure TStockInitCountTicketDataSet.InitializeData;
begin
  inherited;
  with DataSet do
  begin
    with FieldDefs.AddFieldDef do
    begin
      Name := 'inventory_class';
      DataType := ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do
    begin
      Name := 'inventory_item_number';
      DataType := ftInteger;
    end;
    with FieldDefs.AddFieldDef do
    begin
      Name := 'inventory_item';
      DataType := ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do
    begin
      Name := 'shelf_code';
      DataType := ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do
    begin
      Name := 'location';
      DataType := ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do
    begin
      Name := 'UM';
      DataType := ftWideString;
      Size := 50;
    end;
  end;

end;

class function TStockInitCountTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockInitCountTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TStockInitCountTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockInitCountTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

class function TStockInitCountTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TRecipeItemsTicketDataSet }

class function TRecipeItemsTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Recipe items',
    'Dataset caption');
end;

class function TRecipeItemsTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('seq_number') then
    result := Plugin.Translate('StockDatasetsU', 'Sequence number')
  else if Uppercase(FieldName) = Uppercase('active') then
    result := Plugin.Translate('StockDatasetsU', 'Active')
  else if Uppercase(FieldName) = Uppercase('type') then
    result := strTypeCaption
  else if Uppercase(FieldName) = Uppercase('item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Item number')
  else if Uppercase(FieldName) = Uppercase('item_name') then
    result := Plugin.Translate('StockDatasetsU', 'Item name')
  else if Uppercase(FieldName) = Uppercase('amount_used') then
    result := Plugin.Translate('StockDatasetsU', 'Amount used')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('conversion') then
    result := Plugin.Translate('StockDatasetsU', 'U/M conversion')
  else if Uppercase(FieldName) = Uppercase('cost') then
    result := Plugin.Translate('StockDatasetsU', 'Cost')
  else if Uppercase(FieldName) = Uppercase('last_unit_cost') then
    result := Plugin.Translate('StockDatasetsU', 'Last unit cost')
  else if Uppercase(FieldName) = Uppercase('yield') then
    result := Plugin.Translate('StockDatasetsU', 'Yield')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TRecipeItemsTicketDataSet.GetHint: WideString;
begin

end;

class function TRecipeItemsTicketDataSet.GetName: String;
begin
  result := 'RecipeItemsTicketDataSet';
end;

function TRecipeItemsTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'type') then
  begin
    if DataSet.FieldByName(FieldName).asBoolean = True then
      result := Plugin.Translate('StockDatasetsU', 'I')
    else
      result := Plugin.Translate('StockDatasetsU', 'R');
    exit;
  end;
  if (trim(lowercase(FieldName)) = 'active') then
  begin
    if DataSet.FieldByName(FieldName).asBoolean = True then
      result := Plugin.Translate('StockDatasetsU', 'yes')
    else
      result := Plugin.Translate('StockDatasetsU', 'no');
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TRecipeItemsTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('seq_number', ftInteger);
  AddFieldDef('id_recipe_from', ftLargeint);
  AddFieldDef('id_inventory_item', ftLargeint);
  AddFieldDef('active', ftBoolean);
  AddFieldDef('type', ftBoolean);
  AddFieldDef('item_number', ftInteger);
  AddFieldDef('item_name', ftWideString, 100);
  AddFieldDef('amount_used', ftFloat);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('conversion', ftWideString, 50);
  AddFieldDef('cost', ftFloat);
  AddFieldDef('last_unit_cost', ftFloat);
  AddFieldDef('yield', ftInteger);
end;

class function TRecipeItemsTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TRecipeItemsTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TRecipeItemsTicketDataSet.RefreshData;
var
  iq: IIBSQL;
  ip: IShowProgress;
  i: Integer;
  R: TRecipeCostTicketDataSet;
  id_recipe: Int64;
  sm: TStockManager;
  ce: TStockEntity;
  recipe: WideString;
  oldprice, newprice: Currency;
  oldqty, newqty: double;
  wt : IWTran;
begin
  inherited;
  R := TRecipeCostTicketDataSet(FindParentDataset(TRecipeCostTicketDataSet));
  if not assigned(R) then
    exit;

  id_recipe := StrToInt64Def(R.DataSet.FieldByName('id').asString, 0);
  recipe := R.DataSet.FieldByName('recipe').asString;
  i := 0;
  if FNamedParams.Params[TShowProgressDatasetParam.Key] is TPointerStorage then
    ip := IShowProgress(TPointerStorage(FNamedParams.Params
      [TShowProgressDatasetParam.Key]).Value)
  else
    ip := nil;

  wt := FUntillDB.getWTran;
  iq := wt.GetPreparedIIbSql
    ('select a.id, a.id_recipe_from, a.id_inventory_item,' +
    ' a.seq_number, a.id_inventory_item, a.override_yield, a.used_amount,' +
    ' a.id_unity_used,' +
    ' case when a.id_recipe_from is null then b.name else c.name end item_name,'
    + ' case when a.id_recipe_from is null then b.item_number else c.recipe_number end item_number,'
    + ' case when a.id_recipe_from is null then b.is_active else 1 end isactive'
    + ' from recipe_item a '
    + ' left outer join inventory_item b on b.id = a.id_inventory_item and b.is_active=1'
    + ' left outer join recipe c on c.id = a.id_recipe_from  and c.is_active=1'
    + ' where a.is_active=1 and (not a.id_recipe_from is null or not a.id_inventory_item is null) and a.id_recipe =:id_recipe'
    + '    and (b.id is not null or c.id is not null)');

  iq.Q.Params[0].AsInt64 := id_recipe;

  sm := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
  try
    iq.ExecQuery;
    while not iq.eof do begin
      if assigned(ip) then
        ip.ShowProgress('', i, 100);
      inc(i);
      if i > 100 then
        i := 0;
      DataSet.Append;
      DataSet.FieldByName('id').asString := iq.FieldByName('id').asString;
      DataSet.FieldByName('seq_number').asInteger :=
        iq.FieldByName('seq_number').asInteger;
      DataSet.FieldByName('id_recipe_from').asString :=
        iq.FieldByName('id_recipe_from').asString;
      DataSet.FieldByName('id_inventory_item').asString :=
        iq.FieldByName('id_inventory_item').asString;
      DataSet.FieldByName('item_name').asString :=
        iq.FieldByName('item_name').asString;
      DataSet.FieldByName('item_number').asInteger :=
        iq.FieldByName('item_number').asInteger;
      DataSet.FieldByName('active').asBoolean :=
        (iq.FieldByName('isactive').asInteger <> 0);
      DataSet.FieldByName('type').asBoolean :=
        (StrToInt64Def(iq.FieldByName('id_recipe_from').asString, 0) = 0);
      DataSet.FieldByName('amount_used').AsFloat :=
        iq.FieldByName('used_amount').AsDouble;
      DataSet.FieldByName('um').asString := GetMasterUMName(FUntillDB,
        StrToInt64Def(iq.FieldByName('id_unity_used').asString, 0));
      DataSet.FieldByName('conversion').asString :=
        GetCacheUMCName(StrToInt64Def(iq.FieldByName('id_unity_used').asString, 0));
      ce := TStockEntity.Create(TStockEntityItemDS);
      try
        if not DataSet.FieldByName('type').asBoolean then
        begin
          ce.LoadFromRecipe(FUntillDB,
            StrToInt64Def(iq.FieldByName('id_recipe_from').asString, 0),
            UPos.getPosNow, false, false, 0, '', etRestaurant);
          DataSet.FieldByName('cost').AsCurrency :=
            sm.CalculateEntityPrice(ce, 0);
          DataSet.FieldByName('last_unit_cost').AsCurrency :=
            sm.CalculateEntityPrice(ce, 0);
        end
        else
        begin
          DataSet.FieldByName('cost').AsCurrency :=
            GetAveragePrice(FUntillDB,
            StrToInt64Def(iq.FieldByName('id_inventory_item').asString, 0));
          DataSet.FieldByName('last_unit_cost').AsCurrency :=
            GetInventoryItemLastPrice(FUntillDB,
            StrToInt64Def(iq.FieldByName('id_inventory_item').asString, 0));
        end;
        oldprice := DataSet.FieldByName('cost').AsCurrency;
        oldqty := DataSet.FieldByName('amount_used').AsFloat;
        newprice := oldprice * oldqty;
        newqty := oldqty;
        sm.ConvertUM(wt, sttUsage, StrToInt64Def(iq.FieldByName('id_unity_used')
          .asString, 0), newqty, newprice, false);
        DataSet.FieldByName('cost').AsCurrency := newprice;
        newprice := DataSet.FieldByName('last_unit_cost').AsCurrency * oldqty;
        newqty := oldqty;
        sm.ConvertUM(wt, sttUsage, StrToInt64Def(iq.FieldByName('id_unity_used')
          .asString, 0), newqty, newprice, false);
        DataSet.FieldByName('last_unit_cost').AsCurrency := newprice;
        DataSet.FieldByName('yield').asInteger := iq.FieldByName('override_yield').asInteger;
      finally
        FreeAndNil(ce);
      end;
      DataSet.Post;
      iq.Next;
    end;
    wt.commit;
  finally
    FreeAndNil(sm);
  end;
end;

class function TRecipeItemsTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

{ TCountSheetTicketDataSet }

class function TCountSheetTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Count sheet',
    'Dataset caption');
end;

class function TCountSheetTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if SameText(FieldName,'onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('location') then
    result := Plugin.Translate('StockDatasetsU', 'Location')
  else if Uppercase(FieldName) = Uppercase('shelf_code') then
    result := Plugin.Translate('StockDatasetsU', 'Shelf code')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Count U/M')
  else if Uppercase(FieldName) = Uppercase('count_frequency') then
    result := Plugin.Translate('StockDatasetsU', 'Count frequency')
  else if Uppercase(FieldName) = Uppercase('stock_um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TCountSheetTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Count sheet',
    'Dataset caption');
end;

class function TCountSheetTicketDataSet.GetName: String;
begin
  result := 'CountSheetTicketDataSet';
end;

function TCountSheetTicketDataSet.GetSQLQuery: TSQLQueryParams;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('inventory_item_locations') do begin
    AddField('id').Required := True;
    AddField('shelf', 'shelf_code');
    AddCondition('inventory_item_locations.is_active=1');
    with AddJoin('stocking_locations', '', jInner) do begin
      AddCondition('inventory_item_locations.id_stocking_locations = stocking_locations.id');
      AddCondition('inventory_item_locations.is_active=1');
      AddField('description', 'location');
    end;
    with AddJoin('inventory_item', '', jInner) do
    begin
      AddCondition
        ('inventory_item.id = inventory_item_locations.id_inventory_item');
      AddCondition('inventory_item.is_active=1');
      AddField('item_number', 'inventory_item_number');
      AddField('name', 'inventory_item');
    end;
    with AddJoin('inventory_categories', '', jInner) do
    begin
      AddCondition
        ('inventory_item.id_inventory_categories = inventory_categories.id');
      AddField('group_name', 'inventory_class');
    end;
    with AddJoin('unity_conversion', '', jInner) do
    begin
      AddCondition
        ('unity_conversion.id = inventory_item_locations.id_unity_counting');
      AddCondition('unity_conversion.is_active=1');
    end;
    with AddJoin('unity', 'u', jInner) do
    begin
      AddCondition('u.id = unity_conversion.id_unity_convert');
      AddCondition('u.is_active=1');
      AddField('name', 'um');
    end;
    with AddJoin('unity', 'u1', jInner) do
    begin
      AddCondition('u1.id = inventory_item.id_unity');
      AddCondition('u1.is_active=1');
      AddField('name', 'stock_um');
    end;
    with AddJoin('stock_balance', 'sbl', jleftOuter) do
    begin
      AddCondition('sbl.ID_INVENTORY_ITEM=INVENTORY_ITEM.id and sbl.id_stock_locations=inventory_item_locations.id_stocking_locations');
      AddField('amount', 'onhand');
      AddField('coalesce(inventory_item.manual_price, price)',  'average_price');
    end;
    Params.Add(TCountFrequencyDatasetParameter.Key,
      'inventory_item.id_count_frequencies');
    Params.Add(TStockLocationDataSetParam.Key,
      'inventory_item_locations.id_stocking_locations');
    if assigned(FNamedParams.Params[TCountTypeDatasetParameter.Key]) then
      if TInt64(FNamedParams.Params[TCountTypeDatasetParameter.Key]).Value = 1 then
        AddCondition(' not exists( select * from stock_balance ' +
          ' where stock_balance.id_inventory_item = inventory_item_locations.id_inventory_item '
          + ' and stock_balance.id_stock_locations=' +
          IntToStr(TInt64(FNamedParams.Params[TLocationDataSetParam.Key])
          .Value) + ')');
  end;
end;

procedure TCountSheetTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('count_frequency', ftWideString, 255);
  AddFieldDef('Location', ftWideString, 255);
  AddFieldDef('shelf_code', ftWideString, 255);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('stock_um', ftWideString, 50);
  AddFieldDef('onhand', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
end;

class function TCountSheetTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TCountSheetTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TCountSheetTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TCountSheetTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TCountSheetTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TSalesAndCostIngredentsTicketDataSet }

class function TSalesAndCostIngredentsTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Cost of sales report',
    'Dataset caption');
end;

class function TSalesAndCostIngredentsTicketDataSet.GetFieldCaption
  (FieldName: String; DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('virtual_sale_price') then
    result := Plugin.Translate('StockDatasetsU', 'Virtual sales price')
  else if Uppercase(FieldName) = Uppercase('average_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('variance_price') then
    result := Plugin.Translate('StockDatasetsU', 'Variance value')
  else if Uppercase(FieldName) = Uppercase('counted_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Counted')
  else if Uppercase(FieldName) = Uppercase('variance') then
    result := Plugin.Translate('StockDatasetsU', 'Variance')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_class_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class number')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Cost')
  else if Uppercase(FieldName) = Uppercase('close_stock_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Closing stock amount')
  else if Uppercase(FieldName) = Uppercase('close_stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Closing stock value')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TSalesAndCostIngredentsTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Cost of sales report',
    'Dataset caption');
end;

class function TSalesAndCostIngredentsTicketDataSet.GetName: String;
begin
  result := 'TSalesAndCostIngredentsTicketDataSet';
end;

function TSalesAndCostIngredentsTicketDataSet.GetOpendayQuery: string;
begin
  result := 'select sum(quantity), Sum(amount), id_inventory_item, 0 onhand '
      + ' from stock_open_day_item '
      + ' join stock_open_day on stock_open_day.id=stock_open_day_item.id_stock_open_day '
      + ' where stock_open_day.close_action=1 and stock_open_day.id=:id';
end;

procedure TSalesAndCostIngredentsTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item_number', ftWideString, 50);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('inventory_class_number', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 100);
  AddFieldDef('close_stock_qty', ftCurrency);
  AddFieldDef('close_stock_price', ftCurrency);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('counted_qty', ftCurrency);
  AddFieldDef('variance', ftCurrency);
  AddFieldDef('variance_price', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
  AddFieldDef('virtual_sale_price', ftCurrency);
end;

class function TSalesAndCostIngredentsTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TSalesAndCostIngredentsTicketDataSet.IsDetailed: boolean;
begin
  result := false;
end;

class function TSalesAndCostIngredentsTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TSalesAndCostIngredentsTicketDataSet.RefreshData;
var
  qq: IIBSQL;
  query: String;
  from_dt, till_dt: TDatetime;
  id_od, id_cd: Int64;
  period: TSimplePair<TDateTime, TDateTime>;
begin
  qq := FUntillDB.GetPreparedIIbSql('select first 1 * from stock_open_day ' +
    'where close_action=1 and sdatetime>=:fromdatetime and sdatetime<=:tilldatetime '
    + 'order by sdatetime desc');

	period := TSimplePair<TDateTime, TDateTime>(FNamedParams.Params[TReportPeriodDatasetParam.Key]);
  TReportPeriodDatasetParam.applyTo(qq.q, FNamedParams, 'fromdatetime', 'tilldatetime');
  qq.ExecQuery;
  if qq.eof then
    Plugin.RaisePosException('Stock not closed');
  till_dt := qq.Q.FieldByName('sdatetime').AsDateTime;
  id_cd := StrToInt64Def(qq.Q.FieldByName('id').asString, 0);

  qq := FUntillDB.GetPreparedIIbSql('select first 1 * from stock_open_day ' +
    'where close_action=1 and sdatetime< :tilldatetime ' +
    'order by sdatetime desc');
  qq.Q.ParamByName('tilldatetime').AsDateTime := till_dt;
  qq.ExecQuery;
  if qq.eof then
  begin
    from_dt := IncYear(till_dt, -10);
    id_od := 0;
  end
  else
  begin
    from_dt := qq.Q.FieldByName('sdatetime').AsDateTime;
    id_od := StrToInt64Def(qq.Q.FieldByName('id').asString, 0);
  end;
  query := 'select distinct inventory_item.id id_inventory_item, inventory_item.item_number, '
    + ' inventory_item.name, inventory_item.MANUAL_PRICE, stock_balance.price average_price,'
    + ' INVENTORY_CATEGORIES.INV_CATEGORY_NUMBER, INVENTORY_CATEGORIES.group_name, '
    + ' unity.name um, stock_balance.amount, inventory_item.SALE_PRICE '
    + ' from stock_turnover '
    + ' join inventory_item on inventory_item.id = stock_turnover.id_inventory_item '
    + ' join INVENTORY_CATEGORIES on INVENTORY_CATEGORIES.id = inventory_item.id_inventory_categories '
    + ' join unity on unity.id = inventory_item.id_unity '
    + ' join stocking_locations sl on sl.id = stock_turnover.id_stock_locations and sl.is_active=1 '
    + ' left outer join stock_balance on stock_balance.id_inventory_item=inventory_item.id '
    + '   and stock_balance.id_stock_locations = sl.id'
    + ' where sdatetime>=:fromdatetime and sdatetime<=:tilldatetime ';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    query := query + ' and stock_turnover.id_stock_locations=:loc';
  qq := FUntillDB.GetPreparedIIbSql(query);
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    qq.Q.ParamByName('loc').AsInt64 :=
      TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  qq.Q.ParamByName('fromdatetime').AsDateTime := period.Left;
  qq.Q.ParamByName('tilldatetime').AsDateTime := till_dt;

  qq.ExecQuery;
  while not qq.eof do begin
    DataSet.Append;
    if not qq.FieldByName('MANUAL_PRICE').IsNull then
      DataSet.FieldByName('average_price').AsCurrency := qq.FieldByName('MANUAL_PRICE').AsCurrency
    else begin
      if qq.FieldByName('amount').AsCurrency = 0 then
        DataSet.FieldByName('average_price').AsCurrency := qq.FieldByName('average_price').AsCurrency
      else
        DataSet.FieldByName('average_price').AsCurrency :=
          qq.FieldByName('average_price').AsCurrency / qq.FieldByName('amount').AsFloat;
    end;
    DataSet.FieldByName('virtual_sale_price').AsCurrency := qq.FieldByName('SALE_PRICE').AsCurrency;
    DataSet.FieldByName('sold_qty').AsFloat := 0;
    DataSet.FieldByName('stock_price').AsFloat := 0;
    DataSet.FieldByName('sold_price').AsFloat := 0;
    DataSet.FieldByName('id').asString :=
      qq.Q.FieldByName('id_inventory_item').asString;
    DataSet.FieldByName('inventory_item_number').asString :=
      qq.Q.FieldByName('item_number').asString;
    DataSet.FieldByName('inventory_item').asString :=
      qq.Q.FieldByName('name').asString;
    DataSet.FieldByName('inventory_class_number').asString :=
      qq.Q.FieldByName('INV_CATEGORY_NUMBER').asString;
    DataSet.FieldByName('inventory_class').asString :=
      qq.Q.FieldByName('group_name').asString;
    DataSet.FieldByName('um').asString := qq.Q.FieldByName('um').asString;
    DataSet.FieldByName('open_stock_qty').AsFloat := 0;
    DataSet.FieldByName('open_stock_price').AsFloat := 0;
    DataSet.FieldByName('close_stock_qty').AsFloat := 0;
    DataSet.FieldByName('close_stock_price').AsFloat := 0;
    DataSet.FieldByName('purchase_qty').AsFloat := 0;
    DataSet.FieldByName('purchase_price').AsFloat := 0;
    DataSet.FieldByName('adjusted_qty').AsFloat := 0;
    DataSet.FieldByName('adjusted_price').AsFloat := 0;
    DataSet.FieldByName('counted_qty').AsCurrency := 0;
    DataSet.FieldByName('variance').AsCurrency    := 0;
    DataSet.FieldByName('variance_price').AsCurrency  := 0;
    DataSet.Post;
    qq.Next;
  end;

  // Update Opened values
  FillOpenValues(id_od, from_dt, till_dt);

  // Update Closed values
  query := 'select sum(quantity), Sum(amount), id_inventory_item ' +
    ' from stock_open_day_item ' +
    ' join stock_open_day on stock_open_day.id=stock_open_day_item.id_stock_open_day '
    + ' where stock_open_day.close_action=1 and stock_open_day.id=:id_cd ';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    query := query + ' and stock_open_day_item.ID_STOCKING_LOCATIONS=:loc';
  query := query + ' group by id_inventory_item';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    query := query + ',stock_open_day_item.ID_STOCKING_LOCATIONS';
  qq := FUntillDB.GetPreparedIIbSql(query);
  qq.Q.ParamByName('id_cd').AsInt64 := id_cd;
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    qq.Q.ParamByName('loc').AsInt64 :=
      TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  qq.ExecQuery;
  while not qq.eof do
  begin
    DataSet.First;
    while not DataSet.eof do
    begin
      if StrToInt64Def(DataSet.FieldByName('id').asString, 0)
        = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0) then
      begin
        DataSet.edit;
        DataSet.FieldByName('close_stock_qty').AsCurrency   := qq.Q.fields[0].AsDouble;
        DataSet.FieldByName('close_stock_price').AsCurrency := qq.Q.fields[1].AsDouble;
        DataSet.Post;
        break;
      end;
      DataSet.Next;
    end;
    qq.Next;
  end;

  // Update sales
  FillSoldValues(from_dt, till_dt);

  // Update adjustments
  FillAdjValues(from_dt, till_dt);

  // Update invoices
  FillPurchValues(from_dt, till_dt);

  // Update Physiscal count values
  query := 'select sum(QUANTITY_STOCKING), sum(onhand) onhand, '
    + ' INVENTORY_ITEM_LOCATIONS.id_inventory_item, sum(total_stock_price) total   '
    + ' from STOCK_PHYSICAL_COUNT_ITEM '
    + ' join STOCK_PHYSICAL_COUNT on STOCK_PHYSICAL_COUNT.id = STOCK_PHYSICAL_COUNT_ITEM.ID_STOCK_PHYSICAL_COUNT '
    + ' join INVENTORY_ITEM_LOCATIONS on INVENTORY_ITEM_LOCATIONS.id=STOCK_PHYSICAL_COUNT_ITEM.ID_INVENTORY_ITEM_LOCATIONS'
    + ' where STOCK_PHYSICAL_COUNT.sdatetime between :d1 and :d2 and STOCK_PHYSICAL_COUNT_ITEM.updated=1';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    query := query + ' and INVENTORY_ITEM_LOCATIONS.ID_STOCKING_LOCATIONS=:loc';
  query := query + ' group by INVENTORY_ITEM_LOCATIONS.id_inventory_item';
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    query := query + ',INVENTORY_ITEM_LOCATIONS.ID_STOCKING_LOCATIONS';
  qq := FUntillDB.GetPreparedIIbSql(query);
  qq.Q.ParamByName('d1').AsDatetime := from_dt;
  qq.Q.ParamByName('d2').AsDatetime := till_dt;
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    qq.Q.ParamByName('loc').AsInt64 :=
      TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  qq.ExecQuery;
  while not qq.eof do begin
    DataSet.First;
    while not DataSet.eof do
    begin
      if StrToInt64Def(DataSet.FieldByName('id').asString, 0)
        = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0) then
      begin
        DataSet.edit;
        DataSet.FieldByName('counted_qty').AsCurrency     := qq.Q.fields[0].AsCurrency;
        DataSet.FieldByName('variance').AsCurrency        := qq.Q.fields[0].AsCurrency - qq.Q.fields[1].AsCurrency;
        DataSet.FieldByName('variance_price').AsCurrency  := 0;
        if qq.Q.fieldByName('onhand').AsCurrency<>0 then
          DataSet.FieldByName('variance_price').AsCurrency  := (qq.Q.fieldByName('total').AsCurrency / qq.Q.fieldByName('onhand').AsCurrency) * DataSet.FieldByName('variance').AsCurrency;
        DataSet.Post;
        break;
      end;
      DataSet.Next;
    end;
    qq.Next;
  end;

end;

class function TSalesAndCostIngredentsTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

class function TSalesAndCostIngredentsTicketDataSet.
  SupportsOptimization: Boolean;
begin
  result := false;
end;

{ TIngredentsSoldTicketDataSet }

class function TIngredentsSoldTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Sold stock ingredients',
    'Dataset caption');
end;

class function TIngredentsSoldTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if SameText(FieldName,'onhand') then
    result := Plugin.Translate('StockDatasetsU', 'On hand amount')
  else if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_class_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class number')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Average cost')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('sold_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Sold amount')
  else if Uppercase(FieldName) = Uppercase('sold_price') then
    result := Plugin.Translate('StockDatasetsU', 'Sold value')
  else if Uppercase(FieldName) = Uppercase('supplier_name') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier name')
  else if Uppercase(FieldName) = Uppercase('location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else if Uppercase(FieldName) = Uppercase('single_price') then
    result := Plugin.Translate('StockDatasetsU', 'Single sales price')
  else if Uppercase(FieldName) = Uppercase('dayweek') then
    result := Plugin.Translate('StockDatasetsU', 'Day of week')
  else if Uppercase(FieldName) = Uppercase('num_dayweek') then
    result := Plugin.Translate('StockDatasetsU', 'Day of week quantity')
end;

class function TIngredentsSoldTicketDataSet.GetHint: WideString;
begin

end;

class function TIngredentsSoldTicketDataSet.GetName: String;
begin
  result := 'TIngredentsSoldTicketDataSet';
end;

function TIngredentsSoldTicketDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if (trim(lowercase(FieldName)) = 'dayweek') then begin
    Result := DaysOfWeek[DataSet.FieldByName(FieldName).asInteger-1];
    exit;
  end;
  result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

procedure TIngredentsSoldTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('inventory_class_number', ftInteger);
  AddFieldDef('inventory_class', ftWideString, 100);
  AddFieldDef('stock_price', ftCurrency);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('sold_qty', ftFloat);
  AddFieldDef('sold_price', ftCurrency);
  AddFieldDef('single_price', ftCurrency);
  AddFieldDef('supplier_name', ftString, 100);
  AddFieldDef('location', ftWideString, 100);
  AddFieldDef('dayweek', ftInteger);
  AddFieldDef('num_dayweek', ftInteger);
  AddFieldDef('onhand', ftCurrency);
end;

class function TIngredentsSoldTicketDataSet.IsAppliableForContext
  (Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TIngredentsSoldTicketDataSet.IsReportDataset: Boolean;
begin
  result := True;
end;

procedure TIngredentsSoldTicketDataSet.RefreshData;
var
  qq: IIBSQL;
  query: String;
begin
  query := 'select stock_turnover.standart_price, stock_turnover.quantity, stock_turnover.sales_price, '
    + ' inventory_item.id id_inventory_item, inventory_item.item_number, inventory_item.name, '
    + ' INVENTORY_CATEGORIES.INV_CATEGORY_NUMBER, INVENTORY_CATEGORIES.group_name, '
    + ' unity.name um, SUPPLIERS.name sname, STOCKING_LOCATIONS.DESCRIPTION location, '
    + ' extract(WEEKDAY from stock_turnover.sdatetime) dayweek, stock_balance.amount onhand '
    + ' from stock_turnover '
    + ' left outer join STOCKING_LOCATIONS on STOCKING_LOCATIONS.id=stock_turnover.ID_STOCK_LOCATIONS'
    + ' join inventory_item on inventory_item.id = stock_turnover.id_inventory_item '
    + ' join INVENTORY_CATEGORIES on INVENTORY_CATEGORIES.id = inventory_item.id_inventory_categories '
    + ' join unity on unity.id = inventory_item.id_unity and unity.is_active=1'
    + ' left outer join SUPPLIER_TURNOVER on SUPPLIER_TURNOVER.id = stock_turnover.id_SUPPLIER_TURNOVER '
    + ' left outer join SUPPLIER_ITEM on SUPPLIER_ITEM.id = SUPPLIER_TURNOVER.id_SUPPLIER_ITEM '
    + '   and SUPPLIER_ITEM.is_active=1'
    + ' left outer join stock_balance on stock_balance.id_inventory_item=inventory_item.id'
    + '   and stock_balance.id_stock_locations=STOCKING_LOCATIONS.id '
    + ' left outer join SUPPLIERS on SUPPLIERS.id = SUPPLIER_ITEM.ID_SUPPLIERS and SUPPLIERS.is_active=1'
    + ' where stock_turnover.entity_type=0 ';

  if TReportPeriodDatasetParam.assgnedAt(FNamedParams) then begin
    query := query + ' and stock_turnover.sdatetime>=:fromdatetime';
    query := query + ' and stock_turnover.sdatetime<=:tilldatetime';
  end;

  qq := FUntillDB.GetPreparedIIbSql(query);

  TReportPeriodDatasetParam.applyTo(qq.q, FNamedParams, 'fromdatetime', 'tilldatetime');

  qq.ExecQuery;
  while not qq.eof do begin
    DataSet.Append;
    DataSet.FieldByName('sold_qty').AsFloat := -(qq.Q.FieldByName('quantity').AsDouble);
    if SimpleRoundTo(qq.Q.FieldByName('quantity').AsDouble, -4) <> 0 then begin
      DataSet.FieldByName('stock_price').AsCurrency  := qq.Q.FieldByName('standart_price').AsDouble;
      DataSet.FieldByName('sold_price').AsCurrency   := qq.Q.FieldByName('sales_price').AsDouble * DataSet.FieldByName('sold_qty').AsFloat;
      DataSet.FieldByName('single_price').AsCurrency := qq.Q.FieldByName('sales_price').AsDouble;
    end;
    DataSet.FieldByName('id').asString := qq.Q.FieldByName('id_inventory_item').asString;
    DataSet.FieldByName('inventory_item_number').asString := qq.Q.FieldByName('item_number').asString;
    DataSet.FieldByName('inventory_item').asString := qq.Q.FieldByName('name').asString;
    DataSet.FieldByName('inventory_class_number').asString := qq.Q.FieldByName('INV_CATEGORY_NUMBER').asString;
    DataSet.FieldByName('inventory_class').asString := qq.Q.FieldByName('group_name').asString;
    DataSet.FieldByName('um').asString := qq.Q.FieldByName('um').asString;
    DataSet.FieldByName('supplier_name').asString := qq.Q.FieldByName('sname').asString;
    DataSet.FieldByName('dayweek').asInteger := qq.Q.FieldByName('dayweek').asInteger;
    DataSet.FieldByName('onhand').asCurrency := qq.Q.FieldByName('onhand').asCurrency;
    if qq.Q.FieldByName('location').IsNull then
      DataSet.FieldByName('location').asString := Plugin.Translate('StockDatasetsU', 'Unknown','Empty stocking location name')
    else
      DataSet.FieldByName('location').asString := qq.Q.FieldByName('location').asString;
    DataSet.FieldByName('num_dayweek').asInteger := WeekDaysInPeriod(qq.Q.FieldByName('dayweek').asInteger,
      qq.Q.ParamByName('fromdatetime').AsDateTime,qq.Q.ParamByName('tilldatetime').AsDateTime);
    DataSet.Post;
    qq.Next;
  end;
end;

class function TIngredentsSoldTicketDataSet.SupportsGrouping: Boolean;
begin
  result := True;
end;

{ TStockLocationsScreenDataSet }

class function TStockLocationsScreenDataSet.AffectVisibility: Boolean;
begin
  result := True;
end;

procedure TStockLocationsScreenDataSet.ButtonClick(Button: IDataSetButton);
var m     : TBLRMsgStockLocation;
    md    : TBLRMsgStockLocationTo;
    bFrom : boolean;
begin
  inherited;
  if FStoredParams.Count=0 then
    bFrom  := false
  else begin
    bFrom := StrToIntDef(FStoredParams[0],0)=1;
  end;
  if (PosAlg.GetCurrentStep is BLRAlgStockTransfer) and not bFrom then begin
    md    := TBLRMsgStockLocationTo.Create;
    md.id := IntToStr(Button.GetButtonRecordId);
    PosAlg.SendMessage(md);
  end else begin
    m     := TBLRMsgStockLocation.Create;
    m.id  := IntToStr(Button.GetButtonRecordId);
    PosAlg.SendMessage(m);
  end;
end;

constructor TStockLocationsScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
end;

destructor TStockLocationsScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
  inherited;
end;

class function TStockLocationsScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock locations',
    'Dataset hint');
end;

procedure TStockLocationsScreenDataSet.GetCurrentButtonContent
  (Content: TDataSetButtonContent);
begin
  with Content do
    Text := UTF8_Decode(DataSet.FieldByName('name').asString);
end;

class function TStockLocationsScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'List of stock locations', 'Dataset hint');
end;

class function TStockLocationsScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TStockLocationTypeParamsFrame;
end;

class function TStockLocationsScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'St.loc.', 'Dataset hint');
end;

function TStockLocationsScreenDataSet.GetVisibility: Boolean;
begin
  result := (PosAlg.CurrentStateGUID = TBLRGetStockLocation.GetStaticGUID)
    or (PosAlg.CurrentStateGUID = TBLRStockLocation.GetStaticGUID)
    or ((PosAlg.CurrentStateGUID = TBLRAlgIngManage.GetStaticGUID)
      and (TBLRAlgIngManage(PosAlg.GetCurrentStep).CurState=eisLocation)
      and ((FStoredParams.Count = 0) or (FStoredParams[0]='0')));
end;

procedure TStockLocationsScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('Name', ftWideString, 50);
end;

procedure TStockLocationsScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_GENERAL) then begin
    if (posalg.GetCurrentStep is BLRAlgStockTransfer) then
      Refresh
    else if (posalg.GetCurrentStep is TBLRAlgAddIngLocation) then
      Refresh
    else if untillapp.GetPosForm.Editor.ActiveScreen.UntillDataSets.IsRegistered(Self) then
      if (posalg.GetCurrentStep is TBLRAlgIngManage)
        and (TBLRAlgIngManage(posalg.GetCurrentStep).CurState = eisLocation) then
          Refresh
      else if (posalg.GetCurrentStep is TBLRStockBalanceControl)
        and (TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType in [sbftTransfer]) then
          Refresh
  end;
  inherited;
end;

procedure TStockLocationsScreenDataSet.RefreshData;
var Q: IIBSQL;
    sqlstr : String;
    id_loc : Int64;
    bFrom  : Boolean;
begin
  inherited;
  sqlstr := 'select id, description from stocking_locations where is_active=1';

  if FStoredParams.Count=0 then
    bFrom  := false
  else begin
    bFrom := StrToIntDef(FStoredParams[0],0)=1;
  end;

  if (posalg.GetCurrentStep is TBLRAlgIngManage) then begin
    if (TBLRAlgIngManage(posalg.GetCurrentStep).CurIngID>0) then
      sqlstr := sqlstr + ' and id in (select ID_STOCKING_LOCATIONS from '
      + ' INVENTORY_ITEM_LOCATIONS iis where iis.is_active=1 and iis.ID_INVENTORY_ITEM='
      + IntToStr(TBLRAlgIngManage(posalg.GetCurrentStep).CurIngID) + ')'
  end else if posalg.GetCurrentStep is TBLRAlgAddIngLocation then
    sqlstr := sqlstr + ' and not id in (select ID_STOCKING_LOCATIONS from '
    + ' INVENTORY_ITEM_LOCATIONS iis where iis.is_active=1 and iis.ID_INVENTORY_ITEM='
    + IntToStr(TBLRAlgAddIngLocation(posalg.GetCurrentStep).IngDataProvider.id) + ')'
  else if (posalg.GetCurrentStep is TBLRStockLocation) then
    sqlstr := sqlstr + ' and id <> ' + IntToStr(PosRestaurantSettings.Settings.StockLocation)
  else if (PosAlg.GetCurrentStep is BLRAlgStockTransfer) then begin
    if not bFrom then
      sqlstr := sqlstr + ' and id <> ' + IntToStr(TBLRStockBalanceControl(PosAlg.GetCurrentStep).LocationId)
  end;
  Q := FUntillDB.GetPreparedIIBSQL(sqlStr);
  Q.ExecQuery;
  if Q.eof then
    exit;

  while not Q.eof do begin
    if (StrToInt64Def(Q.Q.fields[0].asString,0)<>PosRestaurantSettings.Settings.StockLocation)
      or ( not ((posalg.GetCurrentStep is TBLRStockBalanceControl) and
        (TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType=sbftTransfer) )) then begin
      DataSet.Append;
      id_loc := StrToInt64Def(Q.Q.fields[0].asString,0);
      DataSet.FieldByName('id').asString   := IntToStr(id_loc);
      DataSet.FieldByName('name').asString := StockCashDatasetsU.GetStockLocationName(id_loc, upos.LanguageId);
      DataSet.Post;
    end;
    Q.Next;
  end;

  if posalg.GetCurrentStep is TBLRStockBalanceControl then begin
    if TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID=0 then begin
      DataSet.first;
      TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID := StrToInt64Def(DataSet.FieldByName('id').asString,0);
      BecameNotEmpty;
    end else begin
      SelectedRecordID := (TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID);
    end;
  end else if posalg.GetCurrentStep is TBLRAlgIngManage then begin
        if (TBLRAlgIngManage(posalg.GetCurrentStep).CurState = eisLocation)
        and (TBLRAlgIngManage(posalg.GetCurrentStep).CurLocationID=0) then begin
      if not TBLRAlgIngManage(posalg.GetCurrentStep).IgnoreCat then
        SelectedRecordId := 0
      else begin
        DataSet.first;
        TBLRAlgIngManage(posalg.GetCurrentStep).CurLocationID := StrToInt64Def(DataSet.FieldByName('id').asString,0);
        BecameNotEmpty;
      end;
    end else begin
      SelectedRecordID := TBLRAlgIngManage(posalg.GetCurrentStep).CurLocationID;
    end;
  end else if posalg.GetCurrentStep is TBLRAlgAddIngLocation then begin
  end;
end;

{ TInitInvoiceItemsTicketDataSet }

class function TInitInvoiceItemsTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Inital count items', 'Dataset caption');
end;

class function TInitInvoiceItemsTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('supplier_number') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier number')
  else if Uppercase(FieldName) = Uppercase('supplier_item') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('invoice_date') then
    result := Plugin.Translate('StockDatasetsU', 'Date/time')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else if Uppercase(FieldName) = Uppercase('supplier_um') then
    result := Plugin.Translate('StockDatasetsU', 'Purchase U/M')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TInitInvoiceItemsTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Initial count of invoice items', 'Dataset hint');
end;

class function TInitInvoiceItemsTicketDataSet.GetName: String;
begin
  result := 'TInitInvoiceItemsTicketDataSet';
end;

function TInitInvoiceItemsTicketDataSet.GetSQLQuery: TSQLQueryParams;
begin
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('stock_invoice_item') do begin

    AddField('id').Required := True;
    AddField('quantity');
    AddField('price');
    AddCondition('stock_invoice_item.is_active=1');
    AddCondition('stock_invoice_item.quantity<>0');

    with AddJoin('inventory_item', '', jInner) do begin
      AddCondition('inventory_item.id = stock_invoice_item.id_inventory_item');
      AddCondition('inventory_item.is_active=1');
      AddField('name', 'inventory_item');
      AddField('item_number', 'inventory_item_number');
    end;
    with AddJoin('inventory_categories', '', jInner) do begin
      AddCondition('inventory_item.id_inventory_categories = inventory_categories.id');
      AddCondition('inventory_categories.is_active=1');
      AddField('group_name', 'inventory_class');
    end;
    with AddJoin('stock_invoice', '', jInner) do begin
      AddCondition('stock_invoice.id = stock_invoice_item.id_stock_invoice');
      AddCondition('stock_invoice.is_active=1');
      AddField('invoice_date', 'invoice_date');
    end;
    with AddJoin('stocking_locations', '', jInner) do begin
      AddCondition('stocking_locations.id = stock_invoice.id_stock_locations');
      AddField('description', 'stock_location');
    end;
    with AddJoin('supplier_item', '', jInner) do begin
      AddCondition('supplier_item.id = stock_invoice_item.id_supplier_item');
      AddCondition('supplier_item.is_active=1');
    end;
    with AddJoin('suppliers', '', jInner) do begin
      AddCondition('suppliers.id=supplier_item.id_suppliers ');
      AddField('name', 'supplier_item');
      AddField('sup_number', 'supplier_number');
    end;
    with AddJoin('unity_conversion', '', jInner) do begin
      AddCondition('supplier_item.id_unity_purchase = unity_conversion.id');
      AddCondition('unity_conversion.is_active=1');
    end;
    with AddJoin('unity', '', jInner) do begin
      AddCondition('unity.id = unity_conversion.id_unity_convert');
      AddCondition('unity.is_active=1');
      AddField('name', 'supplier_um');
    end;
    Params.Add(TReportPeriodDatasetParam.Key, 'stock_invoice.invoice_date');
  end;
end;

procedure TInitInvoiceItemsTicketDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('supplier_number', ftInteger);
  AddFieldDef('supplier_item', ftWideString, 50);
  AddFieldDef('supplier_um', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('price', ftFloat);
  AddFieldDef('stock_location', ftWideString, 50);
  AddFieldDef('invoice_date', ftDateTime);
end;

class function TInitInvoiceItemsTicketDataSet.IsAppliableForContext(
  Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TInitInvoiceItemsTicketDataSet.IsReportDataset: Boolean;
begin
  result := true;
end;

procedure TInitInvoiceItemsTicketDataSet.RefreshData;
begin

end;

class function TInitInvoiceItemsTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

class function TInitInvoiceItemsTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TTurnoverHistoryTicketDataSet }

class function TTurnoverHistoryTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock history turnover','Dataset caption');
end;

class function TTurnoverHistoryTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('inventory_item') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient')
  else if Uppercase(FieldName) = Uppercase('inventory_item_number') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient number')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Usage U/M')
  else if Uppercase(FieldName) = Uppercase('price') then
    result := Plugin.Translate('StockDatasetsU', 'Value')
  else if Uppercase(FieldName) = Uppercase('inventory_class') then
    result := Plugin.Translate('StockDatasetsU', 'Ingredient class')
  else if Uppercase(FieldName) = Uppercase('quantity') then
    result := Plugin.Translate('StockDatasetsU', 'Amount')
  else if Uppercase(FieldName) = Uppercase('stock_location') then
    result := Plugin.Translate('StockDatasetsU', 'Stock location')
  else if Uppercase(FieldName) = Uppercase('op_datetime') then
    result := Plugin.Translate('StockDatasetsU', 'Operation date/time')
  else if Uppercase(FieldName) = Uppercase('op_comment') then
    result := Plugin.Translate('StockDatasetsU', 'Operation description')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TTurnoverHistoryTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Show turnover history by ingredients', 'Dataset hint');
end;

class function TTurnoverHistoryTicketDataSet.GetName: String;
begin
  result := 'TTurnoverHistoryTicketDataSet';
end;


procedure TTurnoverHistoryTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('inventory_item', ftWideString, 50);
  AddFieldDef('inventory_item_number', ftInteger);
  AddFieldDef('inventory_class', ftWideString, 50);
  AddFieldDef('quantity', ftFloat);
  AddFieldDef('price', ftFloat);
  AddFieldDef('um', ftWideString, 50);
  AddFieldDef('stock_location', ftWideString, 50);
  AddFieldDef('op_datetime', ftDatetime);
  AddFieldDef('op_comment', ftWideString, 50);
end;

class function TTurnoverHistoryTicketDataSet.IsAppliableForContext(
  Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TTurnoverHistoryTicketDataSet.IsReportDataset: Boolean;
begin
  result := true;
end;

procedure TTurnoverHistoryTicketDataSet.RefreshData;
var iq: IIBSQL;
    ip: IShowProgress;
    str: String;
    strName : String;

    function GetComment(_comment : String) : String;
    var s : String;
        ilen, ipos : Integer;
        iqq: IIBSQL;
        id_doc : Int64;
    begin

      result := '';
      ipos := Pos('(' + TURNOVER_REASON_ADJUSTMENT + ':', _comment);
      if ipos>0 then begin  // Adjustment
        ilen := Length(_comment) - ipos - Length(TURNOVER_REASON_ADJUSTMENT) - 2;
        id_doc := StrToInt64Def(Copy(_comment, ipos + Length(TURNOVER_REASON_ADJUSTMENT) + 2, ilen),0);
        if id_doc>0 then begin
          iqq := FUntillDb.GetPreparedIIbSql('select * from stock_adjustment where id=:id');
          iqq.q.Params[0].AsInt64 := id_doc;
          iqq.ExecQuery;
          if not iqq.eof then begin
            strName := Plugin.Translate('StockDatasetsU', 'Adjustment');
            result := strName + ': Ref="' + iqq.q.fieldByName('reference').asString + '" ';
          end;
        end;
        exit;
      end;
      ipos := Pos('(' + TURNOVER_REASON_INVOICE + ':', _comment);
      if ipos>0 then begin  // Invoice
        ilen := Length(_comment) - ipos - Length(TURNOVER_REASON_INVOICE) - 2;
        id_doc := StrToInt64Def(Copy(_comment, ipos + Length(TURNOVER_REASON_INVOICE) + 2, ilen),0);
        if id_doc>0 then begin
          iqq := FUntillDb.GetPreparedIIbSql('select * from stock_invoice where id=:id');
          iqq.q.Params[0].AsInt64 := id_doc;
          iqq.ExecQuery;
          if not iqq.eof then begin
            if StrToInt64Def(iqq.q.fieldByName('id_suppliers').asString,0)=0 then begin
              strName := Plugin.Translate('StockDatasetsU', 'Inital counting') ;
              result := strName;
            end else  begin
              strName := Plugin.Translate('StockDatasetsU', 'Invoice');
              result := strName + ': Ref="' + iqq.q.fieldByName('reference').asString + '" ';
            end;
          end;
        end;
        exit;
      end;
      ipos := Pos( TURNOVER_REASON_ORDER_ITEM + ':', _comment);
      if ipos>0 then begin  // Order item
        ilen := Length(_comment) - ipos - Length(TURNOVER_REASON_ORDER_ITEM);
        id_doc := StrToInt64Def(Copy(_comment, ipos + Length(TURNOVER_REASON_ORDER_ITEM) + 1, ilen),0);
        if id_doc>0 then begin
          iqq := FUntillDb.GetPreparedIIbSql('select articles.name, orders.number ' +
            ' from order_item  ' +
            ' join orders on orders.id=order_item.id_orders ' +
            ' join articles on articles.id=order_item.id_articles ' +
            ' where order_item.id=:id');
          iqq.q.Params[0].AsInt64 := id_doc;
          iqq.ExecQuery;
          if not iqq.eof then begin
            strName := Plugin.Translate('StockDatasetsU', 'Article');
            s := Plugin.Translate('StockDatasetsU', 'Order');
            result := strName + ': Name="' + iqq.q.fieldByName('name').asString + '", ' + s +
            ' N: ' + iqq.q.fieldByName('number').asString;
          end;
        end;
        exit;
      end;
    end;
begin
  inherited;

  if FNamedParams.Params[TShowProgressDatasetParam.Key] is TPointerStorage then
    ip := IShowProgress(TPointerStorage(FNamedParams.Params[TShowProgressDatasetParam.Key]).Value)
  else
    ip := nil;

  str := 'select stock_turnover.id, quantity, standart_price * quantity price, unity.name um, '
    + ' item_number inventory_item_number, inventory_item.name inventory_item, group_name inventory_class, '
    + ' sl.description stock_location, stock_turnover.sdatetime op_datetime, stock_turnover.reason op_comment'
    + ' from stock_turnover '
    + ' join inventory_item on inventory_item.id = stock_turnover.id_inventory_item '
    + ' join unity on unity.id = inventory_item.id_unity '
    + ' join inventory_categories on inventory_item.id_inventory_categories = inventory_categories.id '
    + ' left outer join stocking_locations sl on stock_turnover.id_stock_locations = sl.id '
    + ' where 1=1 ';
  if TReportPeriodDatasetParam.assgnedAt(FNamedParams) then begin
    str := str + ' and sdatetime>=:fromdatetime';
		str := str + ' and sdatetime<=:tilldatetime';
  end;
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then str := str + ' and sl.id=:loc';
  iq := FUntilldb.GetPreparedIIbSql(str);

  TReportPeriodDatasetParam.applyTo(iq.q, FNamedParams, 'fromdatetime', 'tilldatetime');
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    iq.Q.ParamByName('loc').AsInt64 := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  iq.ExecQuery;
  while not iq.eof do begin
    DataSet.Append;
    DataSet.FieldByName('inventory_item_number').asInteger := iq.FieldByName('inventory_item_number').asInteger;
    DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
    DataSet.FieldByName('um').asString := iq.FieldByName('um').asString;
    DataSet.FieldByName('price').AsCurrency := iq.FieldByName('price').AsCurrency;
    DataSet.FieldByName('quantity').AsFloat :=iq.FieldByName('quantity').AsFloat;
    DataSet.FieldByName('inventory_class').asString := iq.FieldByName('inventory_class').asString;
    DataSet.FieldByName('stock_location').asString :=iq.FieldByName('stock_location').asString;
    DataSet.FieldByName('op_datetime').asDatetime :=iq.FieldByName('op_datetime').asDatetime;
    DataSet.FieldByName('op_comment').asString :=GetComment(iq.FieldByName('op_comment').asString);
    DataSet.Post;
    iq.Next;
  end;
end;

class function TTurnoverHistoryTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

class function TTurnoverHistoryTicketDataSet.SupportsOptimization: Boolean;
begin
  result := false;
end;

{ TStockArticlesTicketDataSet }

class function TStockArticlesTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock articles', 'Dataset hint');
end;

class function TStockArticlesTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('article_number') then
    result := Plugin.Translate('StockDatasetsU', 'Article number')
  else if Uppercase(FieldName) = Uppercase('article_name') then
    result := Plugin.Translate('StockDatasetsU', 'Article name')
  else if Uppercase(FieldName) = Uppercase('um') then
    result := Plugin.Translate('StockDatasetsU', 'Sales UM')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockArticlesTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Articles, linked to ingredients', 'Dataset hint');
end;

class function TStockArticlesTicketDataSet.GetName: String;
begin
  result := 'TStockArticlesTicketDataSet';
end;

function TStockArticlesTicketDataSet.GetSQLQuery: TSQLQueryParams;
var Ing : TStockIngredientTicketDataSet;
    strArtName : string;
begin
  Ing:= TStockIngredientTicketDataSet(FindParentDataset(TStockIngredientTicketDataSet));

  strArtName := '(case when coalesce(size_modifier_item.smi_name,'''')='''' then articles.name else size_modifier_item.smi_name || '' '' || articles.name end)';
  result := TSQLQueryParams.Create(nil, Self.ClassName);
  with result.AddSelect('articles') do begin
    AddField('id').Required := True;
    AddField('article_number');
    AddField(strArtName, 'article_name');

    with AddJoin('UNITY_CONVERSION', 'UNITY_CONVERSION',jInner) do begin
      AddCondition('UNITY_CONVERSION.id = articles.ID_UNITY_SALES');
    end;
    with AddJoin('UNITY', 'UNITY', jInner) do begin
      AddCondition('UNITY_CONVERSION.id_unity_convert = UNITY.id');
      AddField('name', 'um');
    end;

    with AddJoin('ARTICLE_SIZE_MODIFIER', 'ARTICLE_SIZE_MODIFIER', jleftOuter) do begin
      AddCondition('ARTICLE_SIZE_MODIFIER.ID_ARTICLES=articles.id');
    end;
    if assigned(Ing) then begin
      with AddJoin('INVENTORY_ITEM', 'INVENTORY_ITEM', jInner) do begin
        Required := True;
        AddCondition('INVENTORY_ITEM.id = articles.ID_INVENTORY_ITEM');
        AddCondition('INVENTORY_ITEM.id=' + Ing.DataSet.FieldByName('id').asString);
        AddCondition('INVENTORY_ITEM.is_active=1');
      end;
      with AddJoin('SIZE_MODIFIER_ITEM', '', jLeftOuter) do begin
        AddCondition('SIZE_MODIFIER_ITEM.id=ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM'
        + ' and INVENTORY_ITEM.id=ARTICLE_SIZE_MODIFIER.ID_INVENTORY_ITEM');
      end;
    end else begin
      with AddJoin('SIZE_MODIFIER_ITEM', '', jLeftOuter) do begin
        AddCondition('SIZE_MODIFIER_ITEM.id=ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM'
          + ' and ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM=SIZE_MODIFIER_ITEM.id');
      end;
    end;
  end;
end;

procedure TStockArticlesTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeInt);
  AddFieldDef('article_number', ftInteger);
  AddFieldDef('article_name', ftWideString, 50);
  AddFieldDef('um', ftWideString, 50);
end;

class function TStockArticlesTicketDataSet.IsAppliableForContext(
  Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TStockArticlesTicketDataSet.IsReportDataset: Boolean;
begin
  result := true;
end;

procedure TStockArticlesTicketDataSet.RefreshData;
begin
  assert(false);
end;

class function TStockArticlesTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

class function TStockArticlesTicketDataSet.SupportsOptimization: Boolean;
begin
  result := True;
end;

{ TIngredientsScreenDataSet }

procedure TIngredientsScreenDataSet.ButtonClick(Button: IDataSetButton);
var  msg :TBLRMsgStockIngredient;
begin
  inherited;
  msg := TBLRMsgStockIngredient.Create;
  msg.id := Button.GetButtonRecordId;
  posalg.SendMessage(msg);
end;

constructor TIngredientsScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    mgr     := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
end;

destructor TIngredientsScreenDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
  FreeAndNil(mgr);
  inherited;
end;

function TIngredientsScreenDataSet.GetButtonExtraData(
  ObjectId: Int64): TExtraButtonContent;
begin
  Result := GetCurrencyValue(ObjectId, 'amount');
end;

function TIngredientsScreenDataSet.GetButtonExtraData2(
  ObjectId: Int64): TExtraButtonContent;
begin
  Result := GetIngExtraInfo(ObjectId);
end;

function TIngredientsScreenDataSet.GetCurrencyValue(ObjectId : Int64;
  field_Name : string) : TExtraButtonContent;
var bm  : TBookmark;
    rnd : Integer;
begin
  result.info:= '';
  result.WarningLevel := 0;
  if not (posalg.GetCurrentStep is TBLRStockBalanceControl)
    and not (posalg.GetCurrentStep is TBLRAlgIngManage)
    and not (posalg.GetCurrentStep is BLRAlgStockTransfer)
  then exit;
  if Dataset.eof then begin
    if Dataset.fieldByname('id').AsString = IntToStr(ObjectId) then begin
      rnd := Dataset.FieldByName('roundup').asInteger;
      result.info:= CurrToStr(RoundTo(Dataset.FieldByName(field_Name).asCurrency,-rnd)) + ' ' + Dataset.FieldByName('um').asString;
      result.WarningLevel := 0;
    end;
    exit;
  end;

  bm := Dataset.GetBookmark;
  try
    Dataset.First;
    while not Dataset.eof do begin
      if Dataset.fieldByname('id').AsString = IntToStr(ObjectId) then begin
        rnd := Dataset.FieldByName('roundup').asInteger;
        result.info:= CurrToStr(RoundTo(Dataset.FieldByName(field_Name).asCurrency,-rnd)) + ' ' + Dataset.FieldByName('um').asString;
        result.WarningLevel := 0;
        break;
      end;
      Dataset.next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;
end;

class function TIngredientsScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock ingredients','Dataset parameter');
end;

procedure TIngredientsScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
begin
  inherited;
  with Content do
    Text :=  Dataset.FieldByName('inventory_item').AsString;

  if posalg.GetCurrentStep is TBLRStockBalanceControl then begin
      if DataSet.FieldByName('ph_done').asBoolean then begin
        if DataSet.FieldByName('changed').asBoolean  then
          Content.Font.Color       := clRed
        else
          Content.Font.Color       := clGreen;
        Content.Font.Size        := 0;
        Content.CustomFont       := true;
      end;
  end;
end;

class function TIngredientsScreenDataSet.GetExtra1Caption: String;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'On hand amount','Ingredients extra 1 caption');
end;

class function TIngredientsScreenDataSet.GetExtra2Caption: String;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Extra info','Ingredients extra 2 caption');
end;

class function TIngredientsScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredients & stock balance','Dataset parameter');
end;

function TIngredientsScreenDataSet.GetIngExtraInfo(
  ObjectId: Int64): TExtraButtonContent;
var bm : TBookmark;
begin
  result.info:= '';
  result.WarningLevel := 0;
  if ObjectId <= 0 then exit;

  if Dataset.eof then begin
    if Dataset.fieldByname('id').AsString = IntToStr(ObjectId) then begin
      result.info:= Dataset.FieldByName('extrainfo').asString;
      result.WarningLevel := 0;
    end;
    exit;
  end;

  bm := Dataset.GetBookmark;
  try
    Dataset.First;
    while not Dataset.eof do begin
      if Dataset.fieldByname('id').AsString = IntToStr(ObjectId) then begin
        result.info:= Dataset.FieldByName('extrainfo').asString;
        result.WarningLevel := 0;
        break;
      end;
      Dataset.next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;
end;

class function TIngredientsScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TIngScreenTypeParamsFrame;
end;

class function TIngredientsScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ing.','Dataset parameter');
end;

procedure TIngredientsScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('amount', ftCurrency);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('ph_done', ftboolean);
  AddFieldDef('ph_amount', ftCurrency);
  AddFieldDef('notes', ftString);
  AddFieldDef('changed', ftBoolean);
  AddFieldDef('extrainfo', ftWideString, 50);
  AddFieldDef('roundup', ftInteger);
end;

procedure TIngredientsScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if   ( url = URL_STOCK_GENERAL )
    or ( url = URL_STOCK_REORDER ) then
      if untillapp.GetPosForm.Editor.ActiveScreen.UntillDataSets.IsRegistered(Self) then
        Refresh;
  inherited;
end;

procedure TIngredientsScreenDataSet.RefreshData;
var iq: IIBSQL;
    str: String;
    cum : Int64;
    qty : Double;
    price : Currency;
    showmode : Integer;

    locum  : TStockUMType;
    amount0, amount : Double;

    id_ingcat, id_inv, id_loc, id_cloc : Int64;
    id_sup_item,id_sup : Int64;
    ss : String;
    round_up : Integer;
    _norm_amount, _loc_amount, norm_amount, mydiff : Double;
    id_cat : Int64;
    d1,d2 : TDatetime;
    strHeadFilter,strFilter : String;
    wt : IWTran;
    dsType : TIngScreenDatasetType;
    rds    : TReorderDatas;
    rd     : TReorderData;
    i : Integer;
    bNeedAdd : boolean;
    par : Currency;
    invName : String;
    stDoc : TBLRStockDoc;
    bItemAdjusted : boolean;
    id_suppinv, curLocId : Int64;
    supItemData : TSupplierItemData;
    bNeedCentralCond : boolean;
    invData : TStockIngData;
    id_umcp : Int64;
    rnd     : Integer;
    prevID_UNITY_PURCHASE, prevIDIng: Int64;
    funcType : TSBFuncType;
begin
  inherited;
  if (RunMode in [armNormal, armPos]) and (Upos.StartScreen='') then exit;

  id_ingcat := 0;
  dsType := isdtGeneral;
  if (FStoredParams.count > 0) then
    dsType := TIngScreenDatasetType(StrToIntDef(FStoredParams[0],0));

  strFilter := '';
  stDoc    := nil;
  if (posalg.GetCurrentStep is TBLRStockDoc) then begin
    stDoc := TBLRStockDoc(posalg.GetCurrentStep);
    if (stDoc.SearchMode=smsdItems) and (stDoc.SearchSample<>'') then
      strFilter := UpperCase(TBLRStockDoc(posalg.GetCurrentStep).SearchSample);
  end;

  if (dsType in [isdtReturnGoods]) and (posalg.GetCurrentStep is TBLRStockBalanceControl) then begin
    str := ' select c.id, b.name inventory_item, d.name um, sum(a.amount) amount, c.ID_UNITY_PURCHASE, '
      + ' b.id_unity, c.id supp_id, c.ID_SUPPLIERS '
      + ' from inventory_item b '
      + ' inner join SUPPLIER_ITEM c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
      + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
      + ' join UNITY_CONVERSION uc on uc.ID=c.ID_UNITY_PURCHASE and uc.IS_ACTIVE=1'
      + ' left outer join stock_balance a on a.id_inventory_item = b.id '
      + ' where c.ID_SUPPLIERS=:id_sup and b.is_active=1 and C.is_preffered=1'
      + ' group by B.ITEM_NUMBER, c.id, b.name, d.name, c.ID_UNITY_PURCHASE, b.id_unity, c.id, c.ID_SUPPLIERS'
      + ' order by B.ITEM_NUMBER, ID_SUPPLIERS';
    if ss<>'' then
      str := str  + ' and upper(b.name) like ''%'+ UpperCase(ss) + '%'''
    else if strHeadFilter<>'' then
      str := str  + ' and upper(b.name) like '''+ UpperCase(strHeadFilter) + '%''';

    iq := upos.UntillDB.GetPreparedIIbSql(str);
    iq.Q.ParamByName('id_sup').asInt64  := TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierID;
    iq.ExecQuery;
    prevIDIng  := 0;
    prevID_UNITY_PURCHASE := 0;
    while not iq.eof do begin
      cum := StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
      if (cum>0)
        and ((prevIDIng<>StrToInt64Def(iq.FieldByName('id').asString,0))
        or (prevID_UNITY_PURCHASE<>StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0))) then begin
        DataSet.Append;
        DataSet.FieldByName('id').asString := iq.FieldByName('id').asString;
        DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
        qty     := iq.FieldByName('amount').AsDouble;
        id_sup_item :=StrToInt64Def(iq.FieldByName('supp_id').asString,0);
        cum := GetIdUnityPurchase(upos.UntillDB, id_sup_item);
        if cum>0 then begin
          mgr.ConvertUM(wt,sttPurchase, cum, qty, price, false);
          DataSet.FieldByName('um').asString := GetCacheUMCName( cum );
          rnd := GetCacheUMCRound( cum );
          DataSet.FieldByName('roundup').asInteger := rnd;
          DataSet.FieldByName('amount').AsFloat    := RoundTo(qty, -rnd);
        end;
        DataSet.Post;
      end;
      prevIDIng  := StrToInt64Def(iq.FieldByName('id').asString,0);
      prevID_UNITY_PURCHASE:= StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
      iq.Next;
    end;
  end else if dsType in [isdtOrderProposal, isdtInvoice, isdtChangePO] then begin
    if assigned(stDoc) then begin
      // Show items to be reordered
      id_sup := stDoc.CurSupplierID;

      if stDoc.ActiveListType = soptOriginal then begin

        iq := BLRReorderU.GetReorderIngQuery(id_sup);
        if id_sup > 0 then
          iq.q.ParamByName('ID_SUPPLIERS').AsInt64 := id_sup;
        iq.ExecQuery;
        while not iq.eof do begin
          id_inv := StrToInt64Def(iq.FieldByName('id').asString,0);
          invData := GetStockIngData(id_inv);
          id_suppinv := StrToInt64Def(iq.FieldByName('id_suppinv').asString,0);
          id_umcp    :=  StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
          invName := invData.name;
          if TReorderType(iq.Q.FieldByName('reorder_type').asInteger) = invrtPeriodic then begin // periodic par
            par      := StockManagerU.GetIngredientParValue(upos.UntillDB, upos.GetPOSNow, id_inv);
            bNeedAdd := (iq.Q.FieldByName('onhand').asCurrency <= par) and (par > 0);
          end else
            bNeedAdd := (iq.Q.FieldByName('onhand').asCurrency <= iq.Q.FieldByName('reorder_min').asCurrency)
              and (iq.Q.FieldByName('reorder_min').asCurrency > 0);
           bNeedAdd := bNeedAdd and ((strFilter = '') or (pos(strFilter, UpperCase(invName)) > 0));
           if bNeedAdd then begin
            bNeedAdd := not assigned(stDoc.GetReorderItemData(id_suppinv));
            if bNeedAdd then begin
              DataSet.Append;
              DataSet.FieldByName('id').asString       := IntToStr(id_inv);
              DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
              DataSet.FieldByName('um').asString       := iq.Q.FieldByName('um').asString;
              rnd := GetCacheUMCRound( id_umcp );
              DataSet.FieldByName('roundup').asInteger := rnd;
              DataSet.FieldByName('amount').asCurrency := Roundto(iq.Q.FieldByName('onhand').asCurrency, -rnd);
              DataSet.Post;
            end;
          end;
          iq.Next;
        end;
      end else if stDoc.ActiveListType = soptResult then begin
        rds := stDoc.ReorderDataList;
        if rds.Count = 0 then exit;
          for I := 0 to Pred(  rds.Count ) do begin
            rd := rds.items[i];
            invData := GetStockIngData(rd.RDIngID);
            invName := invData.name;
            bNeedAdd := assigned(stDoc.GetReorderItemData(rd.SuppIngID));
            bNeedAdd := bNeedAdd and ((strFilter = '') or (pos(strFilter, uppercase(invName)) > 0));
            if bNeedAdd then begin
              StockManagerU.GetIngAndSuppID(upos.UntillDB, rd.SuppIngID, supItemData);
              DataSet.Append;
              DataSet.FieldByName('id').asString := IntToStr(rd.SuppIngID);
              DataSet.FieldByName('inventory_item').asString := GetInventoryItemName(upos.UntillDB, rd.RDIngID);
              DataSet.FieldByName('extrainfo').asString      := supItemData.description;
              DataSet.FieldByName('um').asString             := GetCacheUMCName(rd.SuppIDUM);
              rnd := GetCacheUMCRound( rd.SuppIDUM );
              DataSet.FieldByName('roundup').asInteger       := rnd;
              DataSet.FieldByName('amount').asCurrency       := RoundTo(rd.Quantity, -rnd);
             DataSet.Post;
            end;
          end;
      end;
      SelectedRecordId := stDoc.CurSuppIngID;
    end;
  end else begin
    wt := FUntillDB.getWTran;
    if (posalg.GetCurrentStep is TBLRStockBalanceControl)
      or (posalg.GetCurrentStep is BLRAlgStockTransfer) then begin
      strHeadFilter := TBLRStockSearchIng(posalg.GetCurrentStep).StrHeadFilter;
      ss      := TBLRStockSearchIng(posalg.GetCurrentStep).SearchSample;
      id_cloc := PosRestaurantSettings.Settings.StockLocation;
      funcType := TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType;
        if (posalg.GetCurrentStep is TBLRStockBalanceControl) and
          (funcType in [ sbftInv, sbftPO ]) then begin
          id_cat  := TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngCatID;
          str := ' select b.id, b.name inventory_item, a.amount, d.name um, c.ID_UNITY_PURCHASE, '
            + ' b.id_unity, c.id supp_id, c.ID_SUPPLIERS '
            + ' from inventory_item b '
            + ' inner join SUPPLIER_ITEM c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
            + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
            + ' join UNITY_CONVERSION uc on uc.ID=c.ID_UNITY_PURCHASE and uc.IS_ACTIVE=1'
            + ' left outer join stock_balance a on a.id_inventory_item = b.id '
            + ' and a.ID_STOCK_LOCATIONS=:id_cloc  '
            + ' where c.ID_SUPPLIERS=:id_sup and b.is_active=1 and C.is_preffered=1';

          if ss<>'' then
            str := str  + ' and upper(b.name) like ''%'+ UpperCase(ss) + '%'''
          else if strHeadFilter<>'' then
            str := str  + ' and upper(b.name) like '''+ UpperCase(strHeadFilter) + '%''';

          if id_cat>0 then
            str := str  + ' and b.ID_INVENTORY_CATEGORIES = :id_cat' ;
          str := str  + ' order by B.ITEM_NUMBER, ID_SUPPLIERS';
          iq := wt.GetPreparedIIbSql(str);
          iq.Q.ParamByName('id_cloc').asInt64 := id_cloc;
          iq.Q.ParamByName('id_sup').asInt64  := TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierID;
          if id_cat>0 then
            iq.Q.ParamByName('id_cat').asInt64 := id_cat;
          iq.ExecQuery;
          prevIDIng  := 0;
          prevID_UNITY_PURCHASE := 0;
          while not iq.eof do begin
            cum := StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
            if (cum>0)
              and ((prevIDIng<>StrToInt64Def(iq.FieldByName('id').asString,0))
              or (prevID_UNITY_PURCHASE<>StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0))) then begin
              DataSet.Append;
              DataSet.FieldByName('id').asString := iq.FieldByName('id').asString;
              DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
              qty     := iq.FieldByName('amount').AsDouble;
              id_sup_item :=StrToInt64Def(iq.FieldByName('supp_id').asString,0);
              cum := GetIdUnityPurchase(upos.UntillDB, id_sup_item);
              if cum>0 then begin
                mgr.ConvertUM(wt,sttPurchase, cum, qty, price, false);
                DataSet.FieldByName('um').asString := GetCacheUMCName( cum );
                rnd := GetCacheUMCRound( cum );
                DataSet.FieldByName('roundup').asInteger := rnd;
                DataSet.FieldByName('amount').AsFloat    := RoundTo(qty, -rnd);
              end;
              DataSet.Post;
            end;
            prevIDIng  := StrToInt64Def(iq.FieldByName('id').asString,0);
            prevID_UNITY_PURCHASE:= StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
            iq.Next;
          end;
        end else begin
          RefreshBalanceAmount;
          if (posalg.GetCurrentStep is TBLRStockBalanceControl) then begin
            id_cat   := TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngCatID;
            curLocId := TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID;
            showmode := TBLRStockBalanceControl(posalg.GetCurrentStep).showUMmode;
            locum    :=  TBLRStockBalanceControl(posalg.GetCurrentStep).UmType;
            if TBLRStockBalanceControl(posalg.GetCurrentStep).CurIngBalanceType = ingbtFree then
              showmode := 1
          end else begin
            id_cat   := RestaurantProps.CurrentStockClass;
            curLocId := BLRAlgStockTransfer(posalg.GetCurrentStep).LocationID;
            showmode := 1;
            locum    := sttCounting;
          end;
          str :=
            ' select b.id, b.name inventory_item, a.amount, d.name um, c.ID_UNITY_COUNTING, c.AVERAGE_AMOUNT, '
            + ' (select first 1 SPH.SDATETIME from '
            + '  STOCK_PHYSICAL_COUNT_ITEM SPHI'
            + '  join STOCK_PHYSICAL_COUNT SPH on SPHI.ID_STOCK_PHYSICAL_COUNT = SPH.ID'
            + '  where SPHI.ID_INVENTORY_ITEM_LOCATIONS =c.id'
            + '  order by SPH.SDATETIME desc'
            + ') SDATETIME, '
            + ' (select first 1 SA.ADJUSTMENT_DATE '
            + '  from STOCK_ADJUSTMENT_ITEM SAI'
            + '  join STOCK_ADJUSTMENT SA on SAI.ID_STOCK_ADJUSTMENT = SA.ID'
            + '  where SA.id_stock_locations_to = C.id_stocking_locations and SAI.id_inventory_item = b.id'
            + '  order by SA.ADJUSTMENT_DATE desc'
            + ') adj_DATETIME, '
            + ' (select first 1 SPHI.notes from '
            + '  STOCK_PHYSICAL_COUNT_ITEM SPHI'
            + '  join STOCK_PHYSICAL_COUNT SPH on SPHI.ID_STOCK_PHYSICAL_COUNT = SPH.ID'
            + '  where SPHI.ID_INVENTORY_ITEM_LOCATIONS =c.id'
            + '  order by SPH.SDATETIME desc'
            + ') notes,'
            + ' (select first 1 SPHI.quantity from '
            + '  STOCK_PHYSICAL_COUNT_ITEM SPHI'
            + '  join STOCK_PHYSICAL_COUNT SPH on SPHI.ID_STOCK_PHYSICAL_COUNT = SPH.ID'
            + '  where SPHI.ID_INVENTORY_ITEM_LOCATIONS =c.id'
            + '  order by SPH.SDATETIME desc'
            + ') ph_amount'
            + ', c.id id_ing_loc, c.round_up'
            + ' from inventory_item b '
            + ' inner join INVENTORY_ITEM_LOCATIONS c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
            + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
            + ' join UNITY_CONVERSION uc on uc.ID=c.ID_UNITY_COUNTING and uc.IS_ACTIVE=1'
            + ' left outer join stock_balance a on a.id_inventory_item = b.id '
            + ' and a.ID_STOCK_LOCATIONS=c.ID_STOCKING_LOCATIONS  '
            + ' where c.ID_STOCKING_LOCATIONS=:id_loc and b.is_active=1';
          bNeedCentralCond := (posalg.GetCurrentStep is TBLRStockBalanceControl)
              and (TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType=sbftTransfer);
          if bNeedCentralCond then
            str := str  + ' and exists(select * from INVENTORY_ITEM_LOCATIONS itl '
            + ' where b.is_active=1 and itl.ID_INVENTORY_ITEM=b.id and itl.ID_STOCKING_LOCATIONS=:id_cloc and itl.is_active=1)';
          if ss<>'' then
            str := str  + ' and upper(b.name) like ''%'+ UpperCase(ss) + '%'''
          else if strHeadFilter <> '' then
            str := str  + ' and upper(b.name) like '''+ UpperCase(strHeadFilter) + '%''';

          if id_cat>0 then
            str := str  + ' and b.ID_INVENTORY_CATEGORIES = :id_cat' ;
          str := str  + ' order by c.shelf, b.ITEM_NUMBER, b.id';
          iq := wt.GetPreparedIIbSql(str);
          iq.Q.ParamByName('id_loc').asInt64  := curLocId;
          if bNeedCentralCond then
            iq.Q.ParamByName('id_cloc').asInt64 := id_cloc;
          if id_cat>0 then
            iq.Q.ParamByName('id_cat').asInt64 := id_cat;
          iq.ExecQuery;
          GetRange(upos.GetPOSNow, d1, d2);
          while not iq.eof do begin
            cum :=StrToInt64Def(iq.FieldByName('ID_UNITY_COUNTING').asString,0);
            if cum>0 then begin
              qty   := 0;
              if not CashInvPeriodValueDataset.GetNormalValue(StrToInt64Def(iq.FieldByName('id_ing_loc').asString,0),
                  upos.GetPosNow, qty) then
                qty   := iq.FieldByName('AVERAGE_AMOUNT').asCurrency;

              mgr.ConvertUM(wt,sttCounting, cum, qty, price);
              id_inv     := StrToInt64Def(iq.FieldByName('id').asString,0);
              id_loc := curLocId;
              if (SimpleRoundTo(qty,-4)>SimpleRoundTo(iq.FieldByName('amount').AsDouble,-4)) or (showmode=1) then begin
                amount := iq.FieldByName('amount').AsDouble;
                DataSet.Append;
                DataSet.FieldByName('id').asString := IntToStr(id_inv);
                DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
                bItemAdjusted := (iq.FieldByName('adj_datetime').AsDatetime>=d1) and (iq.FieldByName('adj_datetime').AsDatetime<=d2);
                DataSet.FieldByName('ph_done').asBoolean := false;
                if posalg.GetCurrentStep is TBLRStockBalanceControl
                  and (TBLRStockBalanceControl(posalg.GetCurrentStep).AllowPhysEnter<>id_inv) then begin
                  DataSet.FieldByName('ph_done').asBoolean :=
                    (((iq.FieldByName('sdatetime').AsDatetime>=d1) and (iq.FieldByName('sdatetime').AsDatetime<=d2))
                      or bItemAdjusted) and (not TBLRStockBalanceControl(posalg.GetCurrentStep).IgnoreAdjustment(id_inv)) ;
                end;

                DataSet.FieldByName('changed').AsBoolean := (iq.FieldByName('ph_amount').asCurrency <> 0)
                  or (bItemAdjusted);
                DataSet.FieldByName('notes').asString    := iq.FieldByName('notes').AsString;
                rnd := iq.FieldByName('round_up').AsInteger;
                DataSet.FieldByName('roundup').asInteger := rnd;
                if DataSet.FieldByName('ph_done').asBoolean then
                  DataSet.FieldByName('ph_amount').AsCurrency := RoundTo(iq.FieldByName('ph_amount').asCurrency,-rnd)
                else
                  DataSet.FieldByName('ph_amount').AsCurrency := 0;

                if locum=sttUsage then begin
                  DataSet.FieldByName('amount').AsFloat := RoundTo(iq.FieldByName('amount').AsDouble,-rnd);
                  DataSet.FieldByName('um').asString    := iq.FieldByName('um').asString;

                  BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_inv, id_loc, amount0, cum, round_up);
                  _norm_amount := amount0;
                  _loc_amount  := amount;
                  price := 0;
                  mgr.ConvertUM(wt,sttCounting, cum, _loc_amount, price, false);
                  mydiff := RoundLess(roundto(_norm_amount,-round_up-1) - roundto(_loc_amount,-round_up-1), round_up);
                end else begin
                  amount0 := 0;
                  BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_inv, id_loc, amount0, cum, round_up);
                  DataSet.FieldByName('um').asString    := GetCacheUMCName( cum );
                  if cum>0 then begin
                    price := 0;
                    mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
                    amount := RoundTo(amount,-round_up-1);
                    DataSet.FieldByName('amount').AsFloat := amount;
                  end else begin
                    DataSet.FieldByName('amount').AsFloat := amount;
                  end;
                  norm_amount := amount0;
                  mydiff := RoundLess(RoundTo(norm_amount,-round_up-1) - RoundTo(amount,-round_up-1), round_up);
                end;
                if (mydiff=0) and (showmode=0) then
                  DataSet.Cancel
                else
                  DataSet.Post;
              end;
            end;
            iq.Next;
          end;
        end;

      if posalg.GetCurrentStep is TBLRStockBalanceControl then
        SelectedRecordId := TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngredientID;

    end else if (dsType in [isdtFreePO]) and (posalg.GetCurrentStep is TBLRCreateStockPO) then begin
      RefreshBalanceAmount;
      id_sup    := TBLRCreateStockPO(posalg.GetCurrentStep).CurSupplierID;
      id_ingcat := TBLRCreateStockPO(posalg.GetCurrentStep).CurIngCatID;
      str := 'select ii.id id_ing, ii.name, si.id id_supping, si.description, ID_UNITY_PURCHASE '
        + ' from SUPPLIER_ITEM si'
        + ' join inventory_item ii on ii.id=si.id_inventory_item and ii.is_active=1 '
        + ' join unity_conversion uc on uc.id=si.ID_UNITY_PURCHASE and uc.is_active=1 '
        + ' where si.ID_SUPPLIERS = :id_sup and si.is_active=1';
      if id_ingcat>0 then
        str := str + ' and ii.ID_INVENTORY_CATEGORIES=:id_ingcat';
      if strFilter<>'' then
         str := str  + ' and upper(ii.name) like ''%'+ UpperCase(strFilter) + '%''' ;
      str := str + ' order by ii.ITEM_NUMBER';
      iq := wt.GetPreparedIIbSql(str);
      iq.q.ParamByName('id_sup').AsInt64 := id_sup;
      if id_ingcat>0 then
       iq.q.ParamByName('id_ingcat').AsInt64 := id_ingcat;
      iq.ExecQuery;
      while not iq.eof do begin
        id_suppinv := StrToInt64Def(iq.q.FieldByName('id_supping').asString,0);
        id_inv     := StrToInt64Def(iq.q.FieldByName('id_ing').asString,0);
        id_umcp    :=  StrToInt64Def(iq.FieldByName('ID_UNITY_PURCHASE').asString,0);
        DataSet.Append;
        DataSet.FieldByName('id').AsString   := IntToStr(id_suppinv);
        DataSet.FieldByName('inventory_item').AsString := iq.q.FieldByName('name').asString;
        DataSet.FieldByName('um').asString         := GetCacheUMCName(id_umcp);
        DataSet.FieldByName('extrainfo').asString  := iq.q.FieldByName('description').asString;
        rnd := GetCacheUMCRound(id_umcp);
        DataSet.FieldByName('roundup').asInteger   := rnd;
        DataSet.FieldByName('amount').AsFloat      := RoundTo(GetCacheInvStockBalance(id_inv), -rnd);
        DataSet.Post;
        iq.next;
      end;
      SelectedRecordId := TBLRCreateStockPO(posalg.GetCurrentStep).CurSuppIngID;
    end else begin
      // Show all ingredients in db
      id_sup    := 0;
      id_loc    := 0;
      RefreshBalanceAmount;
      if posalg.GetCurrentStep is TBLRChangeArticleSalesUM then begin
        id_sup    := TBLRChangeArticleSalesUM(posalg.GetCurrentStep).id_supp;
        id_ingcat := TBLRChangeArticleSalesUM(posalg.GetCurrentStep).id_ingcat;
      end else if (posalg.GetCurrentStep is TBLRAlgIngManage)
         or (posalg.GetCurrentStep is TBLRAlgAddIngLocation)
         or (posalg.GetCurrentStep is TBLRAlgAddIngSupplier) then begin
            id_ingcat := RestaurantProps.CurrentStockClass;
            if (posalg.GetCurrentStep is TBLRAlgIngManage) then begin
              if not TBLRAlgIngManage(posalg.GetCurrentStep).IgnoreCat then begin
                if id_ingcat=0 then
                  exit;
              end else
                id_ingcat := 0;
              strFilter := TBLRAlgIngManage(posalg.GetCurrentStep).StrFilter;
              if strFilter='' then
                strHeadFilter := TBLRAlgIngManage(posalg.GetCurrentStep).strHeadFilter
              else
                strHeadFilter := '';
              id_loc  := TBLRAlgIngManage(posalg.GetCurrentStep).CurLocationID;
              id_sup := TBLRAlgIngManage(posalg.GetCurrentStep).CurSupplierID;
            end
            else begin
               if (posalg.GetCurrentStep is TBLRAlgAddIngLocation) then
                 id_loc  := TBLRAlgAddIngLocation(posalg.GetCurrentStep).oldid_loc;
               if (posalg.GetCurrentStep is TBLRAlgAddIngSupplier) then
                 id_sup  := TBLRAlgAddIngSupplier(posalg.GetCurrentStep).oldid_sup;
            end;
      end;

      str := 'select INVENTORY_ITEM.id, INVENTORY_ITEM.name ';
      str := str + ' from INVENTORY_ITEM where INVENTORY_ITEM.is_active=1';
      if id_sup>0 then
        str := str + ' and id in (select ID_INVENTORY_ITEM '
              + ' from SUPPLIER_ITEM '
              + ' join unity_conversion on unity_conversion.id=SUPPLIER_ITEM.ID_UNITY_PURCHASE and unity_conversion.is_active=1 '
              + ' where SUPPLIER_ITEM.ID_SUPPLIERS = :id_sup and SUPPLIER_ITEM.is_active=1)';
      if id_ingcat>0 then
        str := str + ' and INVENTORY_ITEM.ID_INVENTORY_CATEGORIES=:id_ingcat';
      if strFilter<>'' then
        str := str  + ' and upper(INVENTORY_ITEM.name) like ''%'+ UpperCase(strFilter) + '%'''
      else if strHeadFilter<>'' then
        str := str  + ' and upper(INVENTORY_ITEM.name) like '''+ UpperCase(strHeadFilter) + '%''';
      str := str + ' order by ITEM_NUMBER';
      iq := wt.GetPreparedIIbSql(str);
      if id_sup>0 then
        iq.q.ParamByName('id_sup').AsInt64 := id_sup;
      if id_ingcat>0 then
       iq.q.ParamByName('id_ingcat').AsInt64 := id_ingcat;
      iq.ExecQuery;
        while not iq.Eof do begin
          id_inv   := StrToInt64Def(iq.q.Fields[0].asString,0);
          if id_loc<=0 then begin
            DataSet.Append;
            DataSet.FieldByName('id').AsString        := iq.q.Fields[0].asString;
            DataSet.FieldByName('inventory_item').AsString := iq.q.Fields[1].asString;
            invData := GetStockIngData(id_inv);
            DataSet.FieldByName('um').asString        := GetCacheUMName(invData.ID_UNITY);
            DataSet.FieldByName('roundup').asInteger  := 2;
            DataSet.FieldByName('amount').AsFloat     := RoundTo(GetCacheInvStockbalance(id_inv),-2);
            DataSet.Post;
          end else begin
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_inv, id_loc, amount0, cum, round_up);
            if cum>0 then begin
              DataSet.Append;
              DataSet.FieldByName('id').AsString   := iq.q.Fields[0].asString;
              DataSet.FieldByName('inventory_item').AsString := iq.q.Fields[1].asString;
              _loc_amount  := GetCacheInvLocStockbalance(id_inv, id_loc);
              price := 0;
              mgr.ConvertUM(wt,sttCounting, cum, _loc_amount, price, false, false);
              amount := RoundTo(_loc_amount,-round_up-1);
              DataSet.FieldByName('amount').AsFloat    := _loc_amount;
              DataSet.FieldByName('um').asString       := GetCacheUMCName(cum);
              DataSet.FieldByName('roundup').asInteger := round_up;
              DataSet.Post;
            end;
          end;
          iq.next;
        end;
    end;
    wt.commit;
  end;

  Dataset.AddIndex('ingIndex', 'inventory_item', [ixCaseInsensitive], '');
  DataSet.IndexName := 'ingIndex';

end;

{ TStockSingleDataSet }

constructor TStockSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    sm := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
    UPos.Un.RegisterListener(URL_STOCK_ITEM, Self);
  end;
end;

destructor TStockSingleDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_ITEM, Self);
  FreeAndNil(sm);
  inherited;
end;

class function TStockSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock balance','Dataset parameter');
end;

class function TStockSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
var fn: string;
begin
  fn := lowercase(FieldName);
  if fn = 'new_ing_price' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New ingredient price')
  else if fn = 'inventory_class' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Current ingredient class')
  else if fn = 'ing_price' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient purchase price')
  else if fn = 'supplier' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier')
  else if fn = 'newinvamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New invoice amount')
  else if fn = 'newphysamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New physical amount')
  else if fn = 'newamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New amount')
  else if fn = 'transfer_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Transfer amount')
  else if fn = 'normal_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Normal location amount')
  else if fn = 'search_sample' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Search sample')
  else if fn = 'umtype' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Unity measure type')
  else if fn = 'showmode' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient display mode')
  else if fn = 'centralum' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Default location U/M')
  else if fn = 'centralamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Default location amount')
  else if fn = 'location' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Stock location')
  else if fn = 'stock_location_to' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Stock location transfer to')
  else if fn = 'inventory_item' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Current ingredient')
  else if fn = 'amount' then
    Result := Plugin.Translate('RestaurantDataSetsU','Current amount')
  else if FieldName = 'um' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location U/M')
  else if FieldName = 'pum' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Purchase U/M')
end;

class function TStockSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock balance','Dataset parameter');
end;

function TStockSingleDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if SameText(fieldname, 'umtype') then begin
    if Dataset.FieldByName(FieldName).AsInteger=Ord(sttUsage) then
      result := plugin.Translate('StockDatasetsU','Usage U/M')
    else
      result := plugin.Translate('StockDatasetsU','Counting U/M');
    exit;
  end;
  if SameText(fieldname, 'showmode') then begin
    if Dataset.FieldByName(FieldName).AsInteger=0 then
      result := plugin.Translate('StockDatasetsU','Not adjusted')
    else
      result := plugin.Translate('StockDatasetsU','All ingredients')
  end else
    result := inherited GetStringRepresentation(FieldName,  FormatSpec);
end;

function TStockSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  Result := false;
  if SameText(FieldName, 'ing_price') then begin
    result := (posalg.GetCurrentStep is TBLRGetIngPrice);
  end else if SameText(FieldName, 'newamount') then begin
    result := ((posalg.GetCurrentStep is TBLRStockBalanceControl)
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType = sbftTransfer)
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).SearchMode = false))
  end else if SameText(FieldName, 'transfer_amount') then begin
    result := ((posalg.GetCurrentStep is BLRAlgStockTransfer)
      and ( BLRAlgStockTransfer(posalg.GetCurrentStep).SearchMode = false))
  end else if SameText(FieldName, 'newphysamount') then begin
    result := ((posalg.GetCurrentStep is TBLRStockBalanceControl)
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType = sbftPhys)
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).SearchMode = false))
  end else if SameText(FieldName, 'newinvamount') then begin
    result := ((posalg.GetCurrentStep is TBLRStockBalanceControl)
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType in [ sbftInv, sbftPO ])
      and ( TBLRStockBalanceControl(posalg.GetCurrentStep).SearchMode = false))
  end else if SameText(FieldName, 'search_sample') then begin
    result := ((posalg.GetCurrentStep is TBLRStockBalanceControl)
      and (TBLRStockBalanceControl(posalg.GetCurrentStep).SearchMode=true));
    if not result then
      result := ((posalg.GetCurrentStep is BLRAlgStockTransfer)
        and ( BLRAlgStockTransfer(posalg.GetCurrentStep).SearchMode = true))
  end;
end;

procedure TStockSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('location', ftWideString, 100);
  AddFieldDef('amount', ftCurrency);
  AddFieldDef('newamount', ftCurrency);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('showmode', ftInteger);
  AddFieldDef('centralamount', ftCurrency);
  AddFieldDef('centralum', ftWideString, 100);
  AddFieldDef('umtype', ftInteger);
  AddFieldDef('normal_amount', ftCurrency);
  AddFieldDef('search_sample', ftString);
  AddFieldDef('newphysamount', ftCurrency);
  AddFieldDef('newinvamount', ftCurrency);
  AddFieldDef('supplier', ftWideString, 100);
  AddFieldDef('ing_price', ftCurrency);
  AddFieldDef('new_ing_price', ftCurrency);
  AddFieldDef('pum', ftWideString, 100);
  AddFieldDef('inventory_class', ftWideString, 100);
  AddFieldDef('transfer_amount', ftCurrency);
  AddFieldDef('stock_location_to', ftWideString, 100);
end;

procedure TStockSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if url=URL_STOCK_ITEM then
    Self.AnalyseChanges(ue);
  inherited;
end;

procedure TStockSingleDataSet.RefreshData;
var id_cat, id_loc, id_loc_to, id_cloc : Int64;
    id_suppInv, id_ing, id_supp : Int64;
    locum  : TStockUMType;
    cum    : Int64;
    amount0,amount : Double;
    price  : Currency;
    round_up : Integer;
    showMode : Integer;
    supp_name : String;
    suppItemData: TSupplierItemData;
    inv_data    : TStockIngData;
begin
  inherited;
  if (posalg.GetCurrentStep is TBLRGetIngPrice) then begin
      id_suppInv :=  TBLRGetIngPrice(posalg.GetCurrentStep).SuppIngID;
      StockManagerU.GetIngAndSuppID(FUntillDB, id_suppInv, suppItemData);
      id_supp := suppItemData.id_supplier;
      id_ing  := suppItemData.id_ingredient;
      cum     := suppItemData.SuppIDUM;
      id_cat  :=  TBLRGetIngPrice(posalg.GetCurrentStep).CurrentIngCatID;
      DataSet.edit;
      DataSet.FieldByName('newinvamount').asCurrency := StrToCurrDef(BLRAlgStockBalanceU.NewRealStockQuantity,0);
      DataSet.FieldByName('new_ing_price').asCurrency    := StrToCurrDef(TBLRGetIngPrice(posalg.GetCurrentStep).newprice,0);
      DataSet.FieldByName('ing_price').asCurrency    := StrToCurrDef(TBLRGetIngPrice(posalg.GetCurrentStep).price,0);
      DataSet.FieldByName('pum').asString            := StockManagerU.GetIdUnityPurchaseName(upos.UntillDB, id_suppInv);
      DataSet.FieldByName('supplier').asString       := GetSupplierData(id_supp).name;
      DataSet.FieldByName('inventory_class').asString := GetInvCat(upos.UntillDB, id_cat);
      DataSet.FieldByName('centralum').asString := DataSet.FieldByName('um').asString;
      DataSet.FieldByName('inventory_item').asString := GetInventoryItemName(upos.UntillDB, id_ing);
      id_cloc := PosRestaurantSettings.Settings.StockLocation;
      amount := GetInventoryItemBalance(upos.UntillDB, id_ing, id_cloc);
      if cum>0 then begin
        sm.ConvertUM(nil, sttPurchase, cum, amount, price, false);
        DataSet.FieldByName('centralamount').asCurrency := amount;
        DataSet.FieldByName('amount').asCurrency        := amount;
      end;
      DataSet.Post;
  end else if (posalg.GetCurrentStep is TBLRStockSearchIng) then begin
    if posalg.GetCurrentStep is BLRAlgStockTransfer then begin
      id_ing   :=  BLRAlgStockTransfer(posalg.GetCurrentStep).CurrentIngredientID;
      id_loc   :=  BLRAlgStockTransfer(posalg.GetCurrentStep).LocationID;
      id_loc_to := BLRAlgStockTransfer(posalg.GetCurrentStep).LocationTransferToID;
      locum    :=  sttCounting;
      id_cat   := RestaurantProps.CurrentStockClass;
      showMode := 1;
      supp_name:= ''
    end else  begin
      id_ing :=  TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngredientID;
      id_loc :=  TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID;
      id_loc_to := 0;
      locum  :=  TBLRStockBalanceControl(posalg.GetCurrentStep).UmType;
      id_cat :=  TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngCatID;
      showMode := TBLRStockBalanceControl(posalg.GetCurrentStep).ShowUMMode;
      supp_name:= GetSupplierData(TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierID).name;
    end;
    DataSet.edit;
      id_cloc := PosRestaurantSettings.Settings.StockLocation;
      DataSet.FieldByName('newamount').asCurrency := StrToCurrDef(BLRAlgStockBalanceU.NewRealStockQuantity,0);
      DataSet.FieldByName('transfer_amount').asCurrency := StrToCurrDef(BLRAlgStockBalanceU.NewRealStockQuantity,0);
      DataSet.FieldByName('newphysamount').asCurrency := StrToCurrDef(BLRAlgStockBalanceU.NewRealStockQuantity,0);
      DataSet.FieldByName('newinvamount').asCurrency  := StrToCurrDef(BLRAlgStockBalanceU.NewRealStockQuantity,0);
      DataSet.FieldByName('location').asString := GetStockLocationName(id_loc, upos.LanguageId);
      DataSet.FieldByName('stock_location_to').asString := GetStockLocationName(id_loc_to, upos.LanguageId);
      DataSet.FieldByName('showmode').asInteger := showMode;
      DataSet.FieldByName('search_sample').asString := TBLRStockSearchIng(posalg.GetCurrentStep).SearchSample;
      DataSet.FieldByName('supplier').asString := supp_name;
      DataSet.FieldByName('inventory_class').asString := GetInvCat(upos.UntillDB, id_cat);

      DataSet.FieldByName('umtype').asInteger := ord(locum);
      if id_ing>0 then begin
        DataSet.FieldByName('inventory_item').asString := GetInventoryItemName(upos.UntillDB, id_ing);
        if locum=sttPurchase then begin
          id_supp  :=  TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierID;
          id_suppinv :=  TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierItemID;
          if id_supp = 0  then begin
            inv_data := GetStockIngData(id_ing);
            id_suppinv := inv_data.id_first_supplier_item;
          end else if id_suppinv=0 then
            id_suppinv := GetFirstSupplierItem(upos.UntillDB, id_ing, id_supp);
          DataSet.FieldByName('pum').asString       := StockManagerU.GetIdUnityPurchaseName(upos.UntillDB, id_ing);
          DataSet.FieldByName('centralum').asString := DataSet.FieldByName('um').asString;
          amount := GetInventoryItemBalance(upos.UntillDB, id_ing, id_cloc);
          cum := GetIdUnityPurchase(upos.UntillDB, id_suppinv);
          if cum>0 then begin
            sm.ConvertUM(nil, sttPurchase, cum, amount, price, false);
            DataSet.FieldByName('centralamount').asCurrency := amount;
            DataSet.FieldByName('amount').asCurrency        := amount;
          end;
        end else if locum=sttUsage then begin
          inv_Data := GetStockIngData( id_ing );
          DataSet.FieldByName('um').asString := GetCacheUMName(inv_Data.id_unity);
          DataSet.FieldByName('centralum').asString := DataSet.FieldByName('um').asString;
          DataSet.FieldByName('centralamount').asCurrency := GetInventoryItemBalance(upos.UntillDB, id_ing, id_cloc);
          DataSet.FieldByName('amount').asCurrency := GetInventoryItemBalance(upos.UntillDB, id_ing, id_loc);
          BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
          if cum>0 then begin
            price := 0;
            sm.ConvertUM(nil, sttCounting, cum, amount0, price);
            DataSet.FieldByName('normal_amount').asCurrency := amount0;
          end else
            DataSet.FieldByName('normal_amount').asCurrency := 0;
        end else begin
          amount0 := 0;
          amount := GetInventoryItemBalance(upos.UntillDB, id_ing, id_cloc);
          BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
          DataSet.FieldByName('centralum').asString := GetCacheUMCName(cum);
          if cum>0 then begin
            price:= 0;
            sm.ConvertUM(nil, sttCounting, cum, amount, price, false);
            DataSet.FieldByName('centralamount').asCurrency := amount;
          end else
            DataSet.FieldByName('centralamount').asCurrency := 0;

          amount0 := 0;
          amount := GetInventoryItemBalance(upos.UntillDB, id_ing, id_loc);
          BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
          DataSet.FieldByName('um').asString := GetCacheUMCName(cum);
          if cum>0 then begin
            price:= 0;
            sm.ConvertUM(nil, sttCounting, cum, amount, price, false);
            DataSet.FieldByName('amount').AsFloat := amount;
          end else
            DataSet.FieldByName('amount').AsFloat := 0;
          DataSet.FieldByName('normal_amount').asCurrency := amount0;
        end
      end else begin
        DataSet.FieldByName('centralum').asString := '';
        DataSet.FieldByName('centralamount').asCurrency := 0;
        DataSet.FieldByName('um').asString := '';
        DataSet.FieldByName('amount').AsFloat := 0;
      end;
      DataSet.Post;
  end;
end;

{ TIngredientsInfoDataSet }

procedure TIngredientsInfoDataSet.ChangeCurPos(NewPos: Integer;
  bKeepOldPositions: Boolean);
var msg : TBLRMsgStockIngredient;
begin
  inherited;
  FPos := NewPos;
  msg  := TBLRMsgStockIngredient.Create;
  msg.id := StrToInt64Def(Dataset.fieldByName('id').asString,0);
  posalg.SendMessage(msg);
end;

constructor TIngredientsInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    mgr := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
end;

destructor TIngredientsInfoDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
  FreeAndNil(mgr);
  inherited;
end;

class function TIngredientsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredients stock balance','Dataset parameter');
end;

function TIngredientsInfoDataSet.GetCurPos: Integer;
begin
  result := FPos;
end;

class function TIngredientsInfoDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
var fn: string;
begin
  fn := lowercase(FieldName);
  if fn = 'normal_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Normal location amount')
  else if fn = 'new_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New amount')
  else if fn = 'centralum' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location from U/M')
  else if fn = 'centralamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location from amount')
  else if fn = 'inventory_item' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient name')
  else if fn = 'inventory_number' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient number')
  else if fn = 'amount' then
    Result := Plugin.Translate('RestaurantDataSetsU','Current amount')
  else if FieldName = 'diff' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Difference (On hand vs Normal)')
  else if FieldName = 'um' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location U/M')
end;

class function TIngredientsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredients stock balance','Dataset parameter');
end;

class function TIngredientsInfoDataSet.GetName: String;
begin
  result := 'TIngredientsInfoDataSet';
end;

function TIngredientsInfoDataSet.GetPosIng(aIngId: Int64): Integer;
begin
  result := FPos;
  if Dataset.State <> dsBrowse then exit;
  Dataset.First;
  while not Dataset.Eof do begin
    if StrToInt64Def(Dataset.FieldByName('id').AsString,0) = aIngId then begin
      result := Dataset.RecNo - 1;
      break;
    end;
    Dataset.next;
  end;
end;

procedure TIngredientsInfoDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('inventory_number', ftInteger);
  AddFieldDef('amount', ftCurrency);
  AddFieldDef('normal_amount', ftCurrency);
  AddFieldDef('centralamount', ftCurrency);
  AddFieldDef('centralum', ftWideString, 100);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('diff', ftCurrency);
  AddFieldDef('new_amount', ftCurrency);
end;

procedure TIngredientsInfoDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_GENERAL) then begin
    Refresh;
    SendPosChanged;
    exit;
  end else if (url=URL_STOCK_REORDER_SETITEM) then begin
    FPos := GetPosIng(ue.iInfo);
    SendPosChanged;
    exit;
  end;
  inherited;
end;

procedure TIngredientsInfoDataSet.RefreshData;
var iq: IIBSQL;
    str: String;
    cum : Int64;
    qty : Double;
    price : Currency;
    showmode : Integer;

    locum  : TStockUMType;
    mydiff, norm_amount,loc_amount,amount0, amount : Double;
    _norm_amount,_loc_amount : double;

    id, id_loc, id_cloc : Int64;
    ss : String;
    round_up : Integer;
    wt  : IWTran;
    idxLine : Integer;
    cnt_qty, newqty : Currency;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRStockBalanceControl) then exit;
  if TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType in [sbftPhys] then exit;

  id_cloc  := PosRestaurantSettings.Settings.StockLocation;
  id_loc   := TBLRStockBalanceControl(posalg.GetCurrentStep).LocationID;
  showmode := TBLRStockBalanceControl(posalg.GetCurrentStep).showUMmode;
  if (TBLRStockBalanceControl(posalg.GetCurrentStep).CurIngBalanceType = ingbtFree) then
    showmode := 1;

  locum    := TBLRStockBalanceControl(posalg.GetCurrentStep).UmType;

  RefreshBalanceAmount;

  ss := TBLRStockBalanceControl(posalg.GetCurrentStep).SearchSample;
  wt := FUntillDB.GetWTran;
    str := ' select b.id, b.name inventory_item, a.amount, d.name um, c.ID_UNITY_COUNTING, c.AVERAGE_AMOUNT, '
      + ' b.ITEM_NUMBER, c.id id_ing_loc'
      + ' from inventory_item b '
      + ' inner join INVENTORY_ITEM_LOCATIONS c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
      + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
      + ' left outer join stock_balance a on a.id_inventory_item = b.id '
      + ' and a.ID_STOCK_LOCATIONS=c.ID_STOCKING_LOCATIONS and b.is_active=1 '
      + ' where c.ID_STOCKING_LOCATIONS=:id_loc and b.is_active=1 '
      + ' and exists(select * from INVENTORY_ITEM_LOCATIONS itl '
        + ' where itl.ID_INVENTORY_ITEM=b.id and itl.ID_STOCKING_LOCATIONS=:id_cloc and itl.is_active=1)';
    if ss<>'' then
      str := str  + ' and upper(b.name) like ''%'+ UpperCase(ss) + '%''' ;
    iq := FUntillDB.GetPreparedIIbSql(str);
    iq.Q.ParamByName('id_loc').asInt64  := id_loc;
    iq.Q.ParamByName('id_cloc').asInt64 := id_cloc;
    iq.ExecQuery;
    idxLine := 0;
    while not iq.eof do begin
      cum :=StrToInt64Def(iq.FieldByName('ID_UNITY_COUNTING').asString,0);
      if cum>0 then begin
        qty := 0;
        if not CashInvPeriodValueDataset.GetNormalValue( StrToInt64Def(iq.FieldByName('id_ing_loc').asString,0), upos.GetPosNow, qty) then
          qty := iq.FieldByName('AVERAGE_AMOUNT').asCurrency;
        cnt_qty := qty;
        mgr.ConvertUM(wt, sttCounting, cum, qty, price);
        id      := StrToInt64Def(iq.FieldByName('id').asString,0);
        if (SimpleRoundTo(qty,-4)>SimpleRoundTo(iq.FieldByName('amount').AsDouble,-4))
            or (showmode=1) then begin
          DataSet.Append;
          DataSet.FieldByName('id').asString := IntToStr(id);
          DataSet.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(idxLine);
          DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
          DataSet.FieldByName('inventory_number').asString := iq.FieldByName('ITEM_NUMBER').asString;
          amount := GetCacheInvLocStockbalance(id, id_loc);
          if locum=sttUsage then begin
            DataSet.FieldByName('amount').AsFloat     := iq.FieldByName('amount').AsDouble;
            DataSet.FieldByName('um').asString        := iq.FieldByName('um').asString;
            DataSet.FieldByName('normal_amount').asCurrency := qty;
            DataSet.FieldByName('centralamount').asCurrency  :=GetCacheInvLocStockbalance(id, id_cloc);
            DataSet.FieldByName('centralum').asString := DataSet.FieldByName('um').asString;

            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id, id_loc, amount0, cum, round_up);
            _norm_amount := amount0;
            _loc_amount := amount;
            price := 0;
            mgr.ConvertUM(wt, sttCounting, cum, _loc_amount, price, false);
            mydiff := RoundLess(roundto(_norm_amount,-round_up-1) - roundto(_loc_amount,-round_up-1), round_up);

            loc_amount := amount;
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt, sttCounting, cum, amount0, price);
              norm_amount := amount0;
            end else
              norm_amount := 0;
            DataSet.FieldByName('diff').AsCurrency := RoundTo(norm_amount,-round_up-1) - RoundTo(loc_amount,-round_up-1);
          end else begin
            amount0 := 0;
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id, id_loc, amount0, cum, round_up);
            norm_amount := amount0;
            loc_amount := amount;
            price := 0;
            mgr.ConvertUM(wt, sttCounting, cum, loc_amount, price, false);
            mydiff := RoundLess(roundto(norm_amount,-round_up-1) - roundto(loc_amount,-round_up-1), round_up);
            DataSet.FieldByName('diff').AsCurrency := mydiff;
            DataSet.FieldByName('um').asString    := GetCacheUMCName(cum);
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
              DataSet.FieldByName('amount').AsFloat := amount;
            end else
              DataSet.FieldByName('amount').AsFloat := 0;
            DataSet.FieldByName('normal_amount').asCurrency := cnt_qty;
            amount := GetCacheInvLocStockbalance(id, id_cloc);
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id, id_cloc, amount0, cum, round_up);
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
              DataSet.FieldByName('centralamount').asCurrency := amount;
            end else
              DataSet.FieldByName('centralamount').asCurrency := 0;
            DataSet.FieldByName('centralum').asString := GetCacheUMCName(cum);
          end;
          DataSet.FieldByName('new_amount').AsCurrency := DataSet.FieldByName('diff').AsCurrency;
          if (TBLRStockBalanceControl(posalg.GetCurrentStep).CurIngBalanceType = ingbtFree) then begin
            DataSet.FieldByName('new_amount').AsCurrency := 0;
            if TBLRStockBalanceControl(posalg.GetCurrentStep).AdjustIngList.TryGetValue(id, newqty) then
              if newqty > 0 then
                DataSet.FieldByName('new_amount').AsCurrency := newqty;
          end;

          if (mydiff = 0.00) and (showmode=0) then
            DataSet.Cancel
          else
            DataSet.Post;
        end;
      end;
      Inc(idxLine);
      iq.Next;
    end;
    wt.commit;
end;

{ TIngReNormalTicketDataSet }

class function TIngReNormalTicketDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Need adjust ingredients','Dataset parameter');
end;

class function TIngReNormalTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
var fn : String;
begin
  fn := lowercase(FieldName);
  if fn = 'average_price' then
    result := Plugin.Translate('StockDatasetsU', 'Average price')
  else if fn = 'normal_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Normal location amount')
  else if fn = 'diff' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Difference')
  else if fn = 'usage_um' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Usage U/M')
  else if fn = 'stock_location' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Stock location')
  else if fn = 'new_amount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'New amount')
  else if fn = 'centralum' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Default location U/M')
  else if fn = 'centralamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Default location amount')
  else if fn = 'inventory_item' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient name')
  else if fn = 'inventory_number' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient number')
  else if fn = 'inventory_class' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient class')
  else if fn = 'inventory_class_number' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient class number')
  else if fn = 'amount' then
    Result := Plugin.Translate('RestaurantDataSetsU','Current amount')
  else if FieldName = 'um' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location U/M')
end;

class function TIngReNormalTicketDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredients need to adjust','Dataset parameter');
end;

class function TIngReNormalTicketDataSet.GetName: String;
begin
  result := 'TIngReNormalTicketDataSet';
end;

procedure TIngReNormalTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('stock_location', ftWideString, 100);
  AddFieldDef('inventory_class', ftWideString, 100);
  AddFieldDef('inventory_class_number', ftInteger);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('inventory_number', ftInteger);
  AddFieldDef('amount', ftCurrency);
  AddFieldDef('normal_amount', ftCurrency);
  AddFieldDef('centralamount', ftCurrency);
  AddFieldDef('centralum', ftWideString, 100);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('usage_um', ftWideString, 100);
  AddFieldDef('diff', ftCurrency);
  AddFieldDef('average_price', ftCurrency);
  AddFieldDef('new_amount', ftCurrency);
end;

class function TIngReNormalTicketDataSet.IsAppliableForContext(
  Context: TUntillDatasetContext): Boolean;
begin
  result := (Context is TStockReportsContext)
end;

class function TIngReNormalTicketDataSet.IsReportDataset: Boolean;
begin
  result := true;
end;

procedure TIngReNormalTicketDataSet.RefreshData;
var iq: IIBSQL;
    str: String;
    cum : Int64;
    mgr : TStockManager;
    qty : Double;
    price : Currency;
    norm_amount, loc_amount, amount0, amount : Double;
    _loc_amount : double;
    id_ing, id_loc, id_cloc : Int64;
    locum  : TStockUMType;
    round_up : Integer;
    id_curloc : Int64;
    wt : IWtran;
    cnt_qty : Currency;
    invData : TStockIngData;
    showmode : Integer;
    newqty : Currency;
    bShow  : boolean;
begin
  inherited;
  wt        := FUntilldb.getWTran;
  id_cloc   := PosRestaurantSettings.Settings.StockLocation;
  locum     := sttUsage;
  id_curloc := 0;
  id_ing    := 0;
  RefreshBalanceAmount;
  showmode := 0;
  if (posalg.IsStepInStack(TBLRStockBalanceControl.GetStaticGuid)) then begin
    locum     := sttCounting;
    if TBLRStockBalanceControl(posalg.GetCurrentStep).CurIngBalanceType = ingbtFree then
      showmode := 1;
  end;
  if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
    id_curloc := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
  mgr := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
  try
    str := ' select b.id, b.name inventory_item, a.amount, d.name um, c.ID_UNITY_COUNTING, c.AVERAGE_AMOUNT, '
      + ' b.ITEM_NUMBER, ic.DESCRIPTION, ic.INV_CATEGORY_NUMBER, sl.DESCRIPTION sl_desc, sl.id sl_id,'
      + ' c.id id_ing_loc, coalesce(b.manual_price, a.PRICE) price'
      + ' from inventory_item b '
      + ' inner join INVENTORY_CATEGORIES ic on ic.id = b.id_inventory_categories'
      + ' inner join INVENTORY_ITEM_LOCATIONS c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
      + ' inner join STOCKING_LOCATIONS sl on sl.ID=c.ID_STOCKING_LOCATIONS and sl.is_active=1 '
      + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
      + ' left outer join stock_balance a on a.id_inventory_item = b.id '
      + ' and a.ID_STOCK_LOCATIONS=c.ID_STOCKING_LOCATIONS '
      + ' where b.is_active=1 and exists(select * from INVENTORY_ITEM_LOCATIONS itl '
        + ' where itl.ID_INVENTORY_ITEM=b.id and itl.ID_STOCKING_LOCATIONS=:id_cloc and itl.is_active=1)';
    if id_curloc>0 then
      str := str + ' and c.ID_STOCKING_LOCATIONS=:id_curloc';
    if id_ing>0 then
      str := str + ' and b.id =:id_ing';

    iq := FUntillDB.GetPreparedIIbSql(str);
    iq.Q.ParamByName('id_cloc').asInt64 := id_cloc;
    if id_curloc>0 then
      iq.Q.ParamByName('id_curloc').asInt64 := id_curloc;
    if id_ing>0 then
      iq.Q.ParamByName('id_ing').asInt64 := id_ing;
    iq.ExecQuery;
    while not iq.eof do begin
      cum :=StrToInt64Def(iq.FieldByName('ID_UNITY_COUNTING').asString,0);
      if cum>0 then begin
        qty   := 0;
        if not CashInvPeriodValueDataset.GetNormalValue(StrToInt64Def(iq.FieldByName('id_ing_loc').asString,0),
            upos.GetPosNow, qty) then
          qty   := iq.FieldByName('AVERAGE_AMOUNT').asCurrency;
        cnt_qty := qty;
        mgr.ConvertUM(wt, sttCounting, cum, qty, price);
        id_ing  := StrToInt64Def(iq.FieldByName('id').asString,0);
        id_loc  := StrToInt64Def(iq.FieldByName('sl_id').asString,0);
        amount := GetCacheInvLocStockBalance(id_ing, id_loc);
        bShow  := showmode = 1;
        newqty := 0;
        if bShow then
          if not TBLRStockBalanceControl(posalg.GetCurrentStep).AdjustIngList.TryGetValue(id_ing, newqty) then
             bShow  := false;
        if (SimpleRoundTo(qty,-4)>SimpleRoundTo(iq.FieldByName('amount').AsDouble,-4)) or bShow  then begin
          DataSet.Append;
          DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;
          DataSet.FieldByName('inventory_number').asString := iq.FieldByName('ITEM_NUMBER').asString;
          DataSet.FieldByName('inventory_class_number').asInteger := iq.FieldByName('INV_CATEGORY_NUMBER').asInteger;
          DataSet.FieldByName('inventory_class').asString := iq.FieldByName('DESCRIPTION').asString;
          DataSet.FieldByName('stock_location').asString  := iq.FieldByName('sl_desc').asString;
          DataSet.FieldByName('average_price').asCurrency := iq.FieldByName('PRICE').asCurrency;
          invData := GetStockIngData(id_ing);
          DataSet.FieldByName('usage_um').asString := GetCacheUMName(invData.id_unity);

          if locum=sttUsage then begin
            DataSet.FieldByName('amount').AsFloat     := iq.FieldByName('amount').AsDouble;
            DataSet.FieldByName('um').asString        := iq.FieldByName('um').asString;
            DataSet.FieldByName('normal_amount').asCurrency := cnt_qty;
            DataSet.FieldByName('centralamount').asCurrency  := GetCacheInvLocStockBalance(id_ing, id_cloc);
            DataSet.FieldByName('centralum').asString := DataSet.FieldByName('um').asString;

            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
            _loc_amount  := amount;
            price := 0;
            mgr.ConvertUM(wt, sttCounting, cum, _loc_amount, price, false);

            loc_amount := amount;
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt, sttCounting, cum, amount0, price);
              norm_amount := amount0;
            end else
              norm_amount := 0;
            DataSet.FieldByName('diff').AsCurrency := RoundTo(norm_amount,-round_up-1) - RoundTo(loc_amount,-round_up-1);
          end else begin
            amount0 := 0;
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
            norm_amount := amount0;
            loc_amount := amount;
            price := 0;
            mgr.ConvertUM(wt, sttCounting, cum, loc_amount, price, false);
            DataSet.FieldByName('diff').AsCurrency := RoundLess(RoundTo(norm_amount,-round_up-1) - RoundTo(loc_amount,-round_up-1), round_up);

            DataSet.FieldByName('um').asString    := GetCacheUMCName(cum);
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
              DataSet.FieldByName('amount').AsFloat := amount;
            end else
              DataSet.FieldByName('amount').AsFloat := 0;
            DataSet.FieldByName('normal_amount').asCurrency := qty;

            amount := GetCacheInvLocStockbalance(id_ing, id_cloc);
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_cloc, amount0, cum, round_up);
            if cum>0 then begin
              price := 0;
              mgr.ConvertUM(wt,sttCounting, cum, amount, price, false);
              DataSet.FieldByName('centralamount').asCurrency := amount;
            end else
              DataSet.FieldByName('centralamount').asCurrency := 0;
            DataSet.FieldByName('centralum').asString := GetCacheUMCName(cum);
          end;
          DataSet.FieldByName('new_amount').AsCurrency    := DataSet.FieldByName('diff').AsCurrency;
          if (TBLRStockBalanceControl(posalg.GetCurrentStep).CurIngBalanceType = ingbtFree) then begin
            DataSet.FieldByName('new_amount').AsCurrency := 0;
            if newqty > 0 then
              DataSet.FieldByName('new_amount').AsCurrency := newqty;
          end;
          if (newqty = 0.00)then
            DataSet.Cancel
          else
            DataSet.Post;
        end;
      end;
      iq.Next;
    end;
    wt.commit;
  finally
    FreeAndNil(mgr);
  end;
end;

class function TIngReNormalTicketDataSet.SupportsGrouping: Boolean;
begin
  result := true;
end;

{ TRecipeScreenDataSet }

procedure TRecipeScreenDataSet.ButtonClick(Button: IDataSetButton);
var msg : TBLRMsgRecipe;
begin
  inherited;
  msg := TBLRMsgRecipe.Create;
  msg.id := Button.GetButtonRecordId;
  posalg.SendMessage(msg);
end;

constructor TRecipeScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
end;

destructor TRecipeScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
  inherited;
end;

class function TRecipeScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock recipes','Dataset parameter');
end;

procedure TRecipeScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
begin
  inherited;
  Content.Text := DataSet.FieldByName('name').asString;
end;

class function TRecipeScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Choose stock recipe','Dataset parameter');
end;

class function TRecipeScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Rec.','Dataset parameter');
end;

procedure TRecipeScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('name', ftWideString, 100);
end;

procedure TRecipeScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_GENERAL) then
    Refresh;
  inherited;
end;

procedure TRecipeScreenDataSet.RefreshData;
var q : IIBSQL;
begin
  inherited;
  q := FUntillDB.GetPreparedIIbSql('select id, name from recipe where is_active=1 order by RECIPE_NUMBER');
  q.ExecQuery;
  if q.eof then exit;

  while not Q.eof do begin
    DataSet.Append;
    DataSet.FieldByName('id').asString   := Q.Q.fields[0].asString;
    DataSet.FieldByName('name').asString := Q.Q.fields[1].asString;
    DataSet.Post;
    Q.Next;
  end;

end;

{ TIngCatScreenDataSet }

procedure TIngCatScreenDataSet.ButtonClick(Button: IDataSetButton);
var msg : TBLRMsgIngCat;
begin
  inherited;
  msg := TBLRMsgIngCat.Create;
  msg.id := Button.GetButtonRecordId;
  posalg.SendMessage(msg);
end;

constructor TIngCatScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
end;

destructor TIngCatScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
  inherited;
end;

class function TIngCatScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock ingredient class','Dataset parameter');
end;

procedure TIngCatScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
begin
  inherited;
  Content.Text := DataSet.FieldByName('name').asString;
end;

class function TIngCatScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Choose stock ingredient class','Dataset parameter');
end;

class function TIngCatScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ing.Cl.','Dataset parameter');
end;

procedure TIngCatScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('name', ftWideString, 100);
end;

procedure TIngCatScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_GENERAL) then begin
    Refresh;
    if SelectedRecordID=0 then
      BecameNotEmpty;
  end;
  inherited;
end;

function TIngCatScreenDataSet.ReadCurrentId(var Value: Int64): Boolean;
begin
  if Assigned(RestaurantProps) then
     Value := RestaurantProps.CurrentStockClass;
  result:=true;
end;

procedure TIngCatScreenDataSet.RefreshData;
var q    : IIBSQL;
    str  : String;
    idSup : Int64;
begin
  inherited;
  idSup := 0;
  str := 'select INVENTORY_CATEGORIES.id, INVENTORY_CATEGORIES.group_name name '
    + ' from INVENTORY_CATEGORIES '
    + ' where INVENTORY_CATEGORIES.is_active=1 ';
  if posalg.GetCurrentStep is TBLRChangeArticleSalesUM then
    if TBLRChangeArticleSalesUM(posalg.GetCurrentStep).id_supp>0 then begin
      idSup := TBLRChangeArticleSalesUM(posalg.GetCurrentStep).id_supp;
      str := str + ' and exists (select ii.* from INVENTORY_ITEM ii'
        + ' join SUPPLIER_ITEM si on si.id_INVENTORY_ITEM=ii.id and si.is_active=1 and si.ID_SUPPLIERS=:id_sup'
        + ' where ii.is_active=1 and ii.ID_INVENTORY_CATEGORIES=INVENTORY_CATEGORIES.id )';
  end;
  str := str + ' order by INVENTORY_CATEGORIES.INV_CATEGORY_NUMBER';
  q := FUntillDB.GetPreparedIIbSql(str);
  if idSup>0 then
    q.q.Params[0].AsInt64 :=idSup;
  q.ExecQuery;
  if q.eof then exit;

  while not Q.eof do begin
    DataSet.Append;
    DataSet.FieldByName('id').asString   := Q.Q.fields[0].asString;
    DataSet.FieldByName('name').asString := Q.Q.fields[1].asString;
    DataSet.Post;
    Q.Next;
  end;
    if (posalg.GetCurrentStep is TBLRStockBalanceControl) then
      SelectedRecordId := TBLRStockBalanceControl(posalg.GetCurrentStep).CurrentIngCatID
    else if (posalg.GetCurrentStep is BLRAlgStockTransfer) then
      SelectedRecordId := BLRAlgStockTransfer(posalg.GetCurrentStep).CurrentIngCatID
    else if (posalg.GetCurrentStep is TBLRChangeArticleSalesUM) then
      SelectedRecordId := TBLRChangeArticleSalesUM(posalg.GetCurrentStep).id_ingcat
    else if not (posalg.GetCurrentStep is TBLRAlgIngManage)
      and not (posalg.GetCurrentStep is TBLRAlgAddIngLocation)
      and not (posalg.GetCurrentStep is TBLRAlgAddIngSupplier) then
        SelectedRecordId := 0
    else if (posalg.GetCurrentStep is TBLRAlgIngManage)
      and TBLRAlgIngManage(posalg.GetCurrentStep).IgnoreCat then
        SelectedRecordId := 0;

end;
procedure TIngCatScreenDataSet.WriteCurrentId(Value: Int64);
begin
  inherited;
  if Assigned(RestaurantProps) then
     RestaurantProps.CurrentStockClass:=Value;
end;

{ TStockCommonSingleDataSet }

class function TStockCommonSingleDataSet.GetCaption: WideString;
begin
  Result := Plugin.Translate('RestaurantDataSetsU', 'Stock',
    'Dataset caption');
end;

class function TStockCommonSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,'po_dt_filter') then
    result:=Plugin.Translate('KernelDataSetsU', 'Current PO date/time filter')
  else if SameText(FieldName,'po_filter') then
    result:=Plugin.Translate('KernelDataSetsU', 'Current PO filter')
  else if SameText(FieldName,'ing_filter') then
    result:=Plugin.Translate('KernelDataSetsU', 'Current item filter')
  else if SameText(FieldName,'new_notes') then
    result:=Plugin.Translate('KernelDataSetsU', 'Additional ingredient info')
  else if SameText(FieldName,'new_price') then
    result:=Plugin.Translate('KernelDataSetsU', 'New ingredient price')
end;

class function TStockCommonSingleDataSet.GetHint: WideString;
begin
  Result := Plugin.Translate('StockDatasetsU', 'Common stock information', 'Dataset hint');
end;

function TStockCommonSingleDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if Sametext(FieldName, 'po_dt_filter')
    and (Dataset.FieldByName( FieldName ).asDatetime = 0) then begin
  end else
    result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

function TStockCommonSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if (sameText(FieldName, 'po_filter')) then begin
    result := ((PosAlg.GetCurrentStep is TBLRStockDoc)
      and (TBLRStockDoc(Posalg.GetCurrentStep).SearchMode = smsdDoc))
  end else if (sameText(FieldName, 'ing_filter')) then begin
    result := ((PosAlg.CurrentStateGUID = TBLRAlgIngManage.GetStaticGUID)
      and TBLRAlgIngManage(Posalg.GetCurrentStep).SearchMode)
    or ((PosAlg.GetCurrentStep is TBLRStockDoc)
      and (TBLRStockDoc(Posalg.GetCurrentStep).SearchMode = smsdItems))
    or ((PosAlg.GetCurrentStep is TBLRPOOverview)
      and TBLRPOOverview(Posalg.GetCurrentStep).SearchMode)
  end else if SameText(FieldName, 'new_notes') then
    result := (PosAlg.GetCurrentStep.ClassType = TBLRGetDocItemNotes)
  else if SameText(FieldName, 'new_price') then
    result := (PosAlg.GetCurrentStep.ClassType = TBLRGetDocItemPrice)
end;

procedure TStockCommonSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('ing_filter', ftString, 50);
  AddFieldDef('new_notes', ftString, 250);
  AddFieldDef('new_price', ftCurrency);
  AddFieldDef('po_filter', ftString, 50);
  AddFieldDef('po_dt_filter', ftDatetime);
end;

procedure TStockCommonSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  inherited;
  if ue is TPMAnalyseChangeFlags then AnalyseChanges(ue);
end;

procedure TStockCommonSingleDataSet.RefreshData;
begin
  inherited;
  DataSet.edit;
  DataSet.fields.FieldByName('ing_filter').asString := '';
  DataSet.fields.FieldByName('po_filter').asString := '';
  if (PosAlg.GetCurrentStep is TBLRAlgIngManage) then begin
    if TBLRAlgIngManage(Posalg.GetCurrentStep).SearchMode then
      DataSet.fields.FieldByName('ing_filter').asString := TBLRAlgIngManage(Posalg.GetCurrentStep).StrFilter
    else
      DataSet.fields.FieldByName('ing_filter').asString := TBLRAlgIngManage(Posalg.GetCurrentStep).StrHeadFilter;
  end else if (PosAlg.GetCurrentStep is TBLRStockDoc)
    and (TBLRStockDoc(Posalg.GetCurrentStep).SearchMode=smsdItems) then
      DataSet.fields.FieldByName('ing_filter').asString := TBLRStockDoc(Posalg.GetCurrentStep).SearchSample
  else if (PosAlg.GetCurrentStep is TBLRStockDoc)
    and (TBLRStockDoc(Posalg.GetCurrentStep).SearchMode=smsdDoc) then begin
      DataSet.fields.FieldByName('po_filter').asString      := TBLRStockDoc(Posalg.GetCurrentStep).SearchSample;
      DataSet.fields.FieldByName('po_dt_filter').asDatetime := TBLRStockDoc(Posalg.GetCurrentStep).filterPODate;
    end
  else if (PosAlg.GetCurrentStep is TBLRPOOverview)
    and TBLRPOOverview(Posalg.GetCurrentStep).SearchMode then
      DataSet.fields.FieldByName('ing_filter').asString := TBLRPOOverview(Posalg.GetCurrentStep).SearchSample;
  Dataset.FieldByName('new_notes').asString    := SystemDataProvider.NewFreeText;
  Dataset.FieldByName('new_price').asCurrency  := StrToCurrDef(SystemDataProvider.NumericValue,0);
  DataSet.Post;
end;

{ TStockIngSingleDataSet }

class function TStockIngSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Edit ingredient properties','Dataset parameter');
end;

class function TStockIngSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,SUPPLIER_MIN_AMOUNT) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier min. amount')
  else if SameText(FieldName,SUPPLIER_ORDER_DAY) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier order day of week')
  else if SameText(FieldName,SUPPLIER_DELIVERY_DAY) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier delivery day of week')
  else if SameText(FieldName,'total') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Total amount')
  else if SameText(FieldName,'inventory_item') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient name')
  else if SameText(FieldName,'min_qty') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Min. quantity')
  else if SameText(FieldName,'average_supp_price') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Average purchase price')
  else if SameText(FieldName,'par_qty') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Par. quantity')
  else if SameText(FieldName,'loc_normal_qty') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location normal amount')
  else if SameText(FieldName,'loc_cum') then
    Result := ''
  else if SameText(FieldName,'supp_desc') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier item description')
  else if SameText(FieldName,'supp_pum') then
    Result := ''
  else if SameText(FieldName,'loc_cum_name') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Count U/M')
  else if SameText(FieldName,'supp_pum_name') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Purchase U/M')
  else if SameText(FieldName,'loc_name') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Counting location')
  else if SameText(FieldName,'supp_name') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier')
  else if SameText(FieldName,'supp_price') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Contract price')
  else
    Result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockIngSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient properties','Dataset parameter');
end;

function TStockIngSingleDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if   Sametext(FieldName, SUPPLIER_DELIVERY_DAY)
    or Sametext(FieldName, SUPPLIER_ORDER_DAY) then begin
    if Dataset.FieldByName(FieldName).asInteger=0 then
      result := Plugin.Translate('RestaurantDataSetsU', 'Daily')
    else
      result := DaysOfWeek[Dataset.FieldByName(FieldName).asInteger-1];
  end else
    result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

function TStockIngSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if (posalg.GetCurrentStep is TBLRAlgIngManage) then begin
      result := (TBLRAlgIngManage(posalg.GetCurrentStep).CurState = eisGeneral)
        and (not TBLRAlgIngManage(posalg.GetCurrentStep).IngDataProvider.IsEmpty)
        and SameText(FieldName,'inventory_item');
  end else if (posalg.GetCurrentStep is TBLRAlgAddIngLocation) then begin
    result :=SameText(FieldName,'loc_name');
  end else if (posalg.GetCurrentStep is TBLRAlgAddIngSupplier) then begin
    result :=SameText(FieldName,'supp_name');
  end;
end;

procedure TStockIngSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('min_qty', ftCurrency);
  AddFieldDef('par_qty', ftCurrency);
  AddFieldDef('loc_normal_qty', ftCurrency);
  AddFieldDef('loc_cum', ftLargeint);
  AddFieldDef('supp_desc', ftString, 255);
  AddFieldDef('supp_pum', ftLargeint);
  AddFieldDef('loc_cum_name',  ftString, 255);
  AddFieldDef('supp_pum_name', ftString, 255);
  AddFieldDef('loc_name', ftString, 255);
  AddFieldDef('supp_name', ftString, 255);
  AddFieldDef('supp_price', ftCurrency);
  AddFieldDef('average_supp_price', ftCurrency);
  AddFieldDef(SUPPLIER_DELIVERY_DAY, ftInteger);
  AddFieldDef(SUPPLIER_ORDER_DAY, ftInteger);
  AddFieldDef(SUPPLIER_MIN_AMOUNT, ftCurrency);
end;

function TStockIngSingleDataSet.NeedRefresh: boolean;
begin
  result := (PosAlg.GetCurrentStep is TBLRAlgIngManage)
    or(PosAlg.GetCurrentStep is TBLRAlgAddIngLocation)
    or (PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier)
    or (PosAlg.GetCurrentStep is TBLRStockBalanceControl)
    or (PosAlg.GetCurrentStep is BLRAlgStockTransfer)
    or (posalg.GetCurrentStep is TBLRStockDoc)
end;

procedure TStockIngSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if not (ue is TPMAnalyseChangeFlags) then exit;
  if not NeedRefresh then exit;

  AnalyseChanges(ue);
end;

procedure TStockIngSingleDataSet.FillDataset(ingProv: TIngDataReadOnlyProvider; id_loc,
  id_supp: Int64);
var sd : TSupplierData;
    isd: TIngSuppData;
    ld : TIngLocData;
begin

  sd := GetSupplierData(id_supp);
  Dataset.FieldByName('inventory_item').AsString := ingProv.Name;
  Dataset.FieldByName('min_qty').asCurrency      := ingProv.Min_Qty;
  Dataset.FieldByName('par_qty').asCurrency      := ingProv.Par_Qty;
  DataSet.FieldByName('loc_cum').asString        := IntToStr(id_loc);
  DataSet.FieldByName('supp_pum').asString       := IntToStr(id_supp);
  DataSet.FieldByName('loc_name').asString       := GetStockLocationName(id_loc, upos.LanguageId);
  DataSet.FieldByName('supp_name').asString      := sd.name;
  DataSet.FieldByName('SUPPLIER_DELIVERY_DAY').asInteger := sd.delivery_day;
  DataSet.FieldByName('SUPPLIER_ORDER_DAY').asInteger    := sd.order_day;
  DataSet.FieldByName('SUPPLIER_MIN_AMOUNT').asCurrency  := sd.min_amount;

  DataSet.FieldByName('loc_normal_qty').asCurrency := 0;
  DataSet.FieldByName('loc_cum_name').asString   := '';
  ld := ingProv.GetLocationData(id_loc);
  if assigned( ld ) then begin
    DataSet.FieldByName('loc_cum_name').asString   := GetCacheUMCName(ld.id_count_um);
    DataSet.FieldByName('loc_normal_qty').asCurrency := ld.normal_qty;
  end;
  DataSet.FieldByName('supp_desc').asString      := '';
  DataSet.FieldByName('supp_price').asCurrency   := 0;
  DataSet.FieldByName('supp_pum_name').asString  := '';
  DataSet.FieldByName('average_supp_price').asCurrency := 0;
  isd := ingProv.GetSupplierData(id_supp);
  if assigned( isd ) then begin

    DataSet.FieldByName('supp_pum_name').asString  := GetCacheUMCName(isd.id_purchase_um);
    DataSet.FieldByName('supp_desc').asString      := isd.description;
    DataSet.FieldByName('supp_price').asCurrency     := isd.price;
    DataSet.FieldByName('average_supp_price').asCurrency := StockManagerU.GetAveragePriceFromInvoice(upos.UntillDB,
      TBLRAlgIngManage(PosAlg.GetCurrentStep).CurIngID, id_supp);
  end;
end;

procedure TStockIngSingleDataSet.RefreshData;
var ingProv : TIngDataProvider;
    id_loc, id_supp : Int64;
    balStep : BLRAlgStockTransfer;
    id_count_um : Int64;
    loc_amount  : double;
    round_up    : Integer;
    reorderStep : TBLRStockDoc;
    sd : TSupplierData;
begin
  inherited;
  if not NeedRefresh then exit;

  if (posalg.GetCurrentStep is BLRAlgStockTransfer) then begin
    balStep := BLRAlgStockTransfer(posalg.GetCurrentStep);

    if (balStep.CurrentIngredientID = 0) or (balStep.LocationID=0) then exit;
    Dataset.Edit;
    Dataset.FieldByName('inventory_item').AsString := GetInventoryItemName(upos.UntillDB, balStep.CurrentIngredientID);
    DataSet.FieldByName('loc_name').asString       := GetStockLocationName(balStep.LocationID, upos.LanguageId);
    GetInventoryItemOnLoc(balStep.CurrentIngredientID, balStep.LocationID, loc_amount, id_count_um, round_up);
    DataSet.FieldByName('loc_cum_name').asString   := GetCacheUMCName(id_count_um);
    Dataset.Post;
  end else begin
    if (PosAlg.GetCurrentStep is TBLRAlgIngManage) then begin
      if TBLRAlgIngManage(PosAlg.GetCurrentStep).CurIngID <=0 then
        exit;
    end;

    id_loc  := 0;
    id_supp := 0;
    ingProv := nil;
    if PosAlg.GetCurrentStep is TBLRAlgIngManage then begin
      ingProv := TBLRAlgIngManage(PosAlg.GetCurrentStep).IngDataProvider;
      id_loc  := TBLRAlgIngManage(PosAlg.GetCurrentStep).CurLocationID;
      id_supp := TBLRAlgIngManage(PosAlg.GetCurrentStep).CurSupplierID;
    end else if PosAlg.GetCurrentStep is TBLRAlgAddIngLocation then begin
      id_loc  := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).id_loc;
      ingProv := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).IngDataProvider;
    end else if PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier then begin
      ingProv := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).IngDataProvider;
      id_supp  := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).id_supp;
    end;

    if id_supp = 0 then begin
      if posalg.GetCurrentStep is TBLRStockDoc then begin
        reorderStep := TBLRCreateStockPO(posalg.GetCurrentStep);
        id_supp := reorderStep.CurSupplierID;
        sd := GetSupplierData(id_supp);
        Dataset.Edit;
        DataSet.FieldByName('supp_name').asString      := sd.name;
        Dataset.Post;
      end;
    end;

    if not assigned(ingProv) then exit;
    if ingProv.IsEmpty then exit;

    Dataset.Edit;

    FillDataset(ingProv, id_loc, id_supp);

    if (PosAlg.GetCurrentStep is TBLRAlgAddIngLocation) then begin
      DataSet.FieldByName('loc_cum_name').asString   := GetCacheUMCName(TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).id_count_um);
      DataSet.FieldByName('loc_normal_qty').asCurrency := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).normal_qty;
    end else if (PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier) then begin
      DataSet.FieldByName('supp_pum_name').asString   := GetCacheUMCName(TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).id_purchase_um);
      DataSet.FieldByName('supp_desc').asString := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).description;
      DataSet.FieldByName('supp_price').asCurrency     := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).price;
      DataSet.FieldByName('average_supp_price').asCurrency := StockManagerU.GetAveragePriceFromInvoice(upos.UntillDB,
        TBLRAlgIngManage(PosAlg.GetCurrentStep).CurIngID, id_supp);
    end;

    Dataset.Post;
  end;
end;

{ TEditIngredientsScreenDataSet }

class function TEditIngredientsScreenDataSet.AffectVisibility: Boolean;
begin
  result := true;
end;

class function TEditIngredientsScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock edit ingredients','Dataset parameter');
end;

class function TEditIngredientsScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock edit ingredients','Dataset parameter');
end;

class function TEditIngredientsScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Edt. Ing.','Dataset parameter');
end;

function TEditIngredientsScreenDataSet.GetVisibility: Boolean;
begin
  result := (PosAlg.GetCurrentStep is TBLRAlgIngManage)
    and (not TBLRAlgIngManage(Posalg.GetCurrentStep).SearchMode);
end;

{ TUMCScreenDataSet }

procedure TUMCScreenDataSet.ButtonClick(Button: IDataSetButton);
var  m: TBLRMsgUMC;
begin
  inherited;
  m := TBLRMsgUMC.Create;
  m.id := Button.GetButtonRecordId;
  PosAlg.SendMessage(m);
end;

constructor TUMCScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_UM, Self);
end;

destructor TUMCScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_UM, Self);
  inherited;
end;

procedure TUMCScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
begin
  inherited;
  with Content do
    Text := DataSet.FieldByName('name').asString;
end;

class function TUMCScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TStockLocationTypeParamsFrame;
end;

procedure TUMCScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('Name', ftWideString, 50);
end;

procedure TUMCScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_UM) then begin
    if untillapp.GetPosForm.Editor.ActiveScreen.UntillDataSets.IsRegistered(Self)
     and (posalg.GetCurrentStep is TBLRAlgIngManage) then
      Refresh
    else if posalg.GetCurrentStep is TBLRAlgAddIngLocation then
      Refresh
    else if posalg.GetCurrentStep is TBLRAlgAddIngSupplier then
      Refresh;
  end;
  inherited;
end;

procedure TUMCScreenDataSet.RefreshData;
var id_inv, id_loc, id_supp : Int64;
    id_conv : Int64;
    q : IIBSQL;
    act_type : TStockUMType;
    ingProv  : TIngDataProvider;
begin
  inherited;
  if not (PosAlg.GetCurrentStep is TBLRAlgIngManage)
    and not (PosAlg.GetCurrentStep is TBLRAlgAddIngLocation)
    and not (PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier) then exit;

  act_type := GetActType;
  id_inv := 0;
  if PosAlg.GetCurrentStep is TBLRAlgIngManage then
    id_inv := TBLRAlgIngManage(PosAlg.GetCurrentStep).CurIngID
  else if PosAlg.GetCurrentStep is TBLRAlgAddIngLocation then
    id_inv := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).IngDataProvider.id
  else if PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier then
    id_inv := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).IngDataProvider.id;

  if id_inv=0 then exit;

    q := upos.UntillDB.GetPreparedIIbSql('select uc.id'
      + ' from UNITY_CONVERSION uc'
      + ' join unity u1 on u1.id=uc.ID_UNITY_CONVERT and u1.is_active=1'
      + ' join unity u2 on u2.id=uc.ID_UNITY and u2.is_active=1'
      + ' where uc.is_active=1 and uc.act_type=:act_type and uc.ID_UNITY=:ID_UNITY' );
    q.q.ParamByName('ID_UNITY').AsInt64 := GetInventoryItemUM(upos.UntillDB, id_inv);
    q.q.ParamByName('act_type').AsInteger := Ord(act_type);
    q.ExecQuery;
    while not q.eof do begin
      Dataset.Append;
      Dataset.FieldByName('id').AsString := q.q.fields[0].AsString;
      Dataset.FieldByName('name').AsString :=
        GetCacheUMCName(StrToInt64Def(q.q.fields[0].AsString,0));
      Dataset.Post;
      q.next;
    end;

  id_conv := 0;
  id_loc := 0;
  id_supp := 0;
  ingProv := nil;
  if PosAlg.GetCurrentStep is TBLRAlgIngManage then begin
    ingProv := TBLRAlgIngManage(PosAlg.GetCurrentStep).IngDataProvider;
    id_loc := TBLRAlgIngManage(PosAlg.GetCurrentStep).CurLocationID;
    id_supp := TBLRAlgIngManage(PosAlg.GetCurrentStep).CurSupplierID;
  end else if PosAlg.GetCurrentStep is TBLRAlgAddIngLocation then begin
    ingProv := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).IngDataProvider;
    id_loc  := TBLRAlgAddIngLocation(PosAlg.GetCurrentStep).id_loc;
  end else if PosAlg.GetCurrentStep is TBLRAlgAddIngSupplier then begin
    ingProv := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).IngDataProvider;
    id_supp  := TBLRAlgAddIngSupplier(PosAlg.GetCurrentStep).id_supp;
  end;

  if not assigned(ingProv) or ingProv.IsEmpty then exit;

  if act_type = sttCounting then begin
    if assigned(ingProv.GetLocationData(id_loc)) then
      id_conv := ingProv.GetLocationData(id_loc).id_count_um;
  end else if act_type = sttPurchase then begin
    if assigned(ingProv.GetSupplierData(id_supp)) then
      id_conv := ingProv.GetSupplierData(id_supp).id_purchase_um;
  end;
  SelectedRecordID := id_conv;
end;

{ TCountUMCScreenDataSet }
function TCountUMCScreenDataSet.GetActType: TStockUMType;
begin
  result :=sttCounting;
end;

class function TCountUMCScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Count unity convertions','Dataset parameter');
end;

class function TCountUMCScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Count unity convertions','Dataset parameter');
end;

class function TCountUMCScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Cnt. UM.','Dataset parameter');
end;

{ TPurchaseUMCScreenDataSet }

function TPurchaseUMCScreenDataSet.GetActType: TStockUMType;
begin
  result :=sttPurchase;
end;

class function TPurchaseUMCScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase unity convertions','Dataset parameter');
end;

class function TPurchaseUMCScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase unity convertions','Dataset parameter');
end;

class function TPurchaseUMCScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Prch. UM.','Dataset parameter');
end;

{ TSuppliersScreenDataSet }
class function TSuppliersScreenDataSet.AffectVisibility: Boolean;
begin
  result := true
end;

procedure TSuppliersScreenDataSet.ButtonClick(Button: IDataSetButton);
var m: TBLRMsgSupplier;
begin
  inherited;
  m:=TBLRMsgSupplier.Create;
  m.id := IntToStr(Button.GetButtonRecordId);
  PosAlg.SendMessage(m);
end;

constructor TSuppliersScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    UPos.Un.RegisterListener(URL_STOCK_GENERAL, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER, Self);
  end;
end;

destructor TSuppliersScreenDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_GENERAL, Self);
  end;
  inherited;
end;

class function TSuppliersScreenDataSet.GetCaption: WideString;
begin
  result:=Plugin.Translate('RestaurantDataSetsU', 'Suppliers','Dataset caption');
end;

procedure TSuppliersScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
var cn : String;
begin
  inherited;
  with Content do begin
    Text := '';
    cn := trim(DataSet.FieldByName('customer_nr').AsString);
    if cn<>'' then
      Text:= cn + CHR(13)+CHR(10);
    Text:= Text + DataSet.FieldByName('name').AsString;
  end;
end;

class function TSuppliersScreenDataSet.GetHint: WideString;
begin
  result:=Plugin.Translate('RestaurantDataSetsU', 'List of suppliers','Dataset hint');
end;

class function TSuppliersScreenDataSet.GetName: String;
begin
  result := 'TSuppliersScreenDataSet';
end;

class function TSuppliersScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TStockSupplierTypeParamsFrame;
end;

class function TSuppliersScreenDataSet.GetShortCaption: WideString;
begin
  result:=Plugin.Translate('RestaurantDataSetsU', 'Spl.','Dataset caption');
end;

function TSuppliersScreenDataSet.GetVisibility: Boolean;
begin
  result := ((PosAlg.CurrentStateGUID = TBLRAlgIngManage.GetStaticGUID)
    and (TBLRAlgIngManage(PosAlg.GetCurrentStep).CurState=eisSupplier)
    and ((FStoredParams.Count = 0) or (FStoredParams[0]='0')));
end;

procedure TSuppliersScreenDataSet.InitializeData;
begin
  IdFieldName:='id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('name', ftWideString, 50);
  AddFieldDef('customer_nr', ftWideString, 50);
end;

procedure TSuppliersScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_GENERAL) then begin
    if untillapp.GetPosForm.Editor.ActiveScreen.UntillDataSets.IsRegistered(Self) then begin
      if (posalg.GetCurrentStep is TBLRAlgIngManage)
        and (TBLRAlgIngManage(posalg.GetCurrentStep).CurState = eisSupplier) then
          Refresh
      else if (posalg.GetCurrentStep is TBLRAlgAddIngSupplier) then
          Refresh
    end;
  end else if (url=URL_STOCK_REORDER ) then
    Refresh;
  inherited;
end;

procedure TSuppliersScreenDataSet.RefreshData;
var iq: IIBSQL;
    sqlstr : String;
begin
  inherited;
  Self.Dataset.EmptyDataset;

  sqlstr := ' select id, name, customer_nr from suppliers where is_active=1 ';
  if (posalg.GetCurrentStep is TBLRAlgIngManage) then begin
    if (TBLRAlgIngManage(posalg.GetCurrentStep).CurIngID>0) then
      sqlstr := sqlstr + ' and id in '
        + ' (select ID_SUPPLIERS from '
        + ' SUPPLIER_ITEM si where si.is_active=1 and si.ID_INVENTORY_ITEM='
        + IntToStr(TBLRAlgIngManage(posalg.GetCurrentStep).CurIngID) + ')'
  end else if posalg.GetCurrentStep is TBLRAlgAddIngSupplier then
    sqlstr := sqlstr + ' and not id in '
      + ' (select ID_SUPPLIERS from '
      + ' SUPPLIER_ITEM si where si.is_active=1 and si.ID_INVENTORY_ITEM='
      + IntToStr(TBLRAlgAddIngSupplier(posalg.GetCurrentStep).IngDataProvider.id) + ')';
  sqlstr := sqlstr + ' order by name';
  iq := upos.UntillDB.GetPreparedIIbSql(sqlstr);
  iq.ExecQuery;
  while not iq.Eof do begin
    DataSet.Append;
    DataSet.FieldByName('id').AsString   :=iq.FieldByname('id').AsString;
    DataSet.FieldByName('name').AsString :=iq.FieldByname('name').AsString;
    DataSet.FieldByName('customer_nr').AsString :=iq.FieldByname('customer_nr').AsString;
    DataSet.Post;
    iq.Next;
  end;

  if (posalg.GetCurrentStep is TBLRAlgIngManage)
      and (TBLRAlgIngManage(posalg.GetCurrentStep).CurState = eisSupplier) then begin
    if TBLRAlgIngManage(posalg.GetCurrentStep).CurSupplierID=0 then begin
      if not TBLRAlgIngManage(posalg.GetCurrentStep).IgnoreCat then
        SelectedRecordId := 0
      else begin
        DataSet.first;
        TBLRAlgIngManage(posalg.GetCurrentStep).CurSupplierID := StrToInt64Def(DataSet.FieldByName('id').asString,0);
        BecameNotEmpty;
      end;
    end else
      SelectedRecordID := TBLRAlgIngManage(posalg.GetCurrentStep).CurSupplierID
  end else if posalg.GetCurrentStep is TBLRAlgAddIngSupplier then begin
    SelectedRecordID := 0;
  end;
end;

{ TPOScreenDataSet }

procedure TPOScreenDataSet.ButtonClick(Button: IDataSetButton);
var msg : TBLRMsgStockPO;
begin
  inherited;
  msg := TBLRMsgStockPO.Create;
  msg.id := Button.GetButtonRecordId;
  posalg.SendMessage(msg);
end;

constructor TPOScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_PO, Self);
end;

destructor TPOScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_PO, Self);
  inherited;
end;

class function TPOScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase Orders - not complete','Dataset parameter');
end;

procedure TPOScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
var txt : String;
    rnd : integer;
begin
  if DataSet.eof then exit;
  with Content do begin
    txt:= DataSet.FieldByName('number').AsString + CHR(13)+CHR(10)
      + DataSet.FieldByName('name').AsString;
    if FStoredParams.count > 0  then begin
      rnd := upos.MainCurrency.Round;
      if FStoredParams.count > 1  then
        rnd := StrToIntDef(FStoredParams[1],0);
      Text := GetParamButtonCaption(FStoredParams[0], rnd);
      if Text='' then
        Text:=txt;
    end else
      Text:=txt;
  end;
end;

class function TPOScreenDataSet.GetFieldCaption(FieldName: String;
  db: TUntillDB): WideString;
begin
  if FieldName = 'number' then result:=Plugin.Translate('RestaurantDataSetsU', 'PO number');
  if FieldName = 'id' then result:='';
  if FieldName = 'name' then result:=Plugin.Translate('RestaurantDataSetsU', 'PO name');
  if FieldName = 'po_date' then result:=Plugin.Translate('RestaurantDataSetsU', 'PO date');
  if FieldName = 'po_time' then result:=Plugin.Translate('RestaurantDataSetsU', 'PO time');
  if FieldName = 'state' then result:=Plugin.Translate('RestaurantDataSetsU', 'PO state')
end;

class function TPOScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TClientScreenParamsFrame;
end;

class function TPOScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'PO.','Dataset parameter');
end;

procedure TPOScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('number', ftInteger);
  AddFieldDef('name', ftWideString, 50);
  AddFieldDef('po_date', ftString, 50);
  AddFieldDef('po_time', ftString, 50);
  AddFieldDef('state', ftString, 50);
end;

procedure TPOScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url=URL_STOCK_PO) then begin
    if untillapp.GetPosForm.Editor.ActiveScreen.UntillDataSets.IsRegistered(Self)
      and (posalg.GetCurrentStep is TBLRStockBalanceControl)
        and (TBLRStockBalanceControl(posalg.GetCurrentStep).FuncType=sbftInv) then
          Refresh
      else if (posalg.GetCurrentStep is TBLRStockInvoice) then
        Refresh;
  end;
  inherited;
end;

procedure TPOScreenDataSet.RefreshData;
var iq : IIBSQL;
    id_sup : Int64;
    strSQL : string;
    sinDoc : TBLRStockInvoice;
    strFilter : string;
    d1, d2 : TDatetime;
    postate : TStockPOState;
    bAdd    : boolean;
begin
  inherited;
  id_sup := 0;
  if  (posalg.GetCurrentStep is TBLRStockBalanceControl) then
    id_sup := TBLRStockBalanceControl(posalg.GetCurrentStep).SupplierID;
  if (posalg.GetCurrentStep is TBLRStockInvoice) then
    id_sup := TBLRStockInvoice(posalg.GetCurrentStep).CurSupplierID;

  if id_sup<=0 then exit;

  sinDoc := nil;
  strFilter := '';
  strSQL := 'select * from PURCHASE_ORDER '
    + ' where ID_SUPPLIERS=:id and is_active=1 ';
  if (posalg.GetCurrentStep is TBLRStockBalanceControl) then
    strSQL := strSQL + ' and not exists (select * from STOCK_INVOICE '
    + '   where STOCK_INVOICE.ID_PURCHASE_ORDER=PURCHASE_ORDER.id and is_active=1)'
  else if (posalg.GetCurrentStep is TBLRStockInvoice) then begin
    sinDoc := TBLRStockInvoice(posalg.GetCurrentStep);
    if (sinDoc.SearchMode = smsdDoc) then begin
      if (trim(sinDoc.SearchSample) <> '') then begin
        strFilter :=trim(sinDoc.SearchSample);
        strSQL := strSQL + ' and ((PURCHASE_ORDER.po_number like ''%'+ strFilter + '%'')'
          + ' or (upper(PURCHASE_ORDER.REFERENCE) like ''%'+ UpperCase(strFilter) + '%''))';
      end;
      if sinDoc.filterPODate <> 0 then
        strSQL := strSQL + ' and (PURCHASE_ORDER.ORDER_DATE between :d1 and :d2)';
    end;
  end;
  strSQL := strSQL + ' order by PURCHASE_ORDER.PO_NUMBER';

  iq := upos.UntillDB.GetPreparedIIbSql(strSQL);
  if (posalg.GetCurrentStep is TBLRStockInvoice) then begin
    if assigned(sinDoc) then begin
      if sinDoc.filterPODate<>0 then begin
        GetFromTillDateTime(PosKernelSettings.Settings.FromTime, PosKernelSettings.Settings.ToTime,
            SysDatetimeToLocal(sinDoc.filterPODate), d1, d2);
        iq.q.ParamByName('d1').AsDatetime := d1;
        iq.q.ParamByName('d2').AsDatetime := d2;
      end;
    end;
  end;
  iq.q.ParamByName('id').AsInt64 := id_sup;
  iq.ExecQuery;

  while not iq.Eof do begin
    postate := GetRealPOState(upos.UntillDB,  StrToInt64Def(iq.q.fieldByName('id').asString,0));
    bAdd := true;
    if (posalg.GetCurrentStep is TBLRStockInvoice) then begin
      if postate >= stpoReceived then
        bAdd := false;
    end;
    if bAdd then begin

      Dataset.Append;
      Dataset.fieldByName('id').asString        :=  iq.q.fieldByName('PO_NUMBER').asString;
      Dataset.fieldByName('number').asInteger   :=  iq.q.fieldByName('PO_NUMBER').asInteger;
      Dataset.fieldByName('name').asString      :=  iq.q.fieldByName('REFERENCE').asString;
      Dataset.fieldByName('po_date').asString   :=  FormatDateTime('DD/MM/yyyy',iq.q.fieldByName('ORDER_DATE').asDatetime);
      Dataset.fieldByName('po_time').asString   :=  FormatDateTime('hh:mm',iq.q.fieldByName('ORDER_DATE').asDatetime);
      Dataset.fieldByName('state').asString     :=  StockPurchaseOrderU.GetPOStateName( Ord(postate ));
      Dataset.Post;
    end;
    iq.next;
  end;

end;

{ TReorderInfoDataSet }

procedure TReorderInfoDataSet.ChangeCurPos(NewPos: Integer;
  bKeepOldPositions: Boolean);
var msg        : TBLRStockReorderMessage;
    id_suppinv : Int64;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRStockDoc) then exit;
  if Dataset.State <> dsBrowse then exit;

  FPos   := NewPos;
  if Dataset.recordcount >= FPos+1 then Dataset.RecNo := FPos+1;
  id_suppinv := StrToInt64Def(Dataset.fieldByName('id_suppinv').asString,0);
  if id_suppinv = 0 then exit;

  msg              := TBLRStockReorderMessage.Create;
  msg.SuppIngID    := id_suppinv;
  msg.ProposalType := GetProposalType;

  posalg.SendMessage(msg);
end;

constructor TReorderInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    sm := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
    UPos.Un.RegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER_SETITEM, Self);
    UPos.Un.RegisterListener(URL_STOCK_STOCK_PO, Self);
    UPos.Un.RegisterListener(URL_STOCK_PO_DATA, Self);
  end;
end;

destructor TReorderInfoDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER_SETITEM, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_STOCK_PO, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_PO_DATA, Self);
  end;
  FreeAndNil(sm);
  inherited;
end;

class function TReorderInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Order proposal','Dataset caption');
end;

function TReorderInfoDataSet.GetCurPos: Integer;
begin
  result := FPos;
  if not (posalg.GetCurrentStep is TBLRStockReorder) then exit;
  if TBLRStockReorder(posalg.GetCurrentStep).ActiveListType = GetProposalType then
    result := FPos
  else
    result := -10;
end;

class function TReorderInfoDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  result := GetReorderFieldName(fieldName);
end;

class function TReorderInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of ingredients to re-order','Dataset caption');
end;

class function TReorderInfoDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TOrderProposalTypeParamsFrame;
end;

function TReorderInfoDataSet.GetVisibility(FieldName :String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'inventory_item') then
    result := (PosAlg.CurrentStateGUID = TBLRStockReorder.GetStaticGUID)
end;

procedure TReorderInfoDataSet.InitializeData;
begin
  inherited;
  InitReorderDS(self);
end;

procedure TReorderInfoDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url = URL_STOCK_REORDER_SETITEM) and (ue.iInfo>0) then begin
    FPos := GetPosIng(ue.iInfo);
    if FPos <> GetCurPos then
      ChangeCurPos(FPos, true);
    SendPosChanged;

  end;
  if   (url = URL_STOCK_REORDER)
    or (url = URL_STOCK_STOCK_PO)
    or (url = URL_STOCK_PO_DATA) then begin
    Refresh;
    SendPosChanged;
  end;
  inherited;
end;

function  TReorderInfoDataSet.GetPosIng(aIngId : Int64) : Integer;
begin
  result := FPos;
  if Dataset.State <> dsBrowse then exit;
  Dataset.First;
  while not Dataset.Eof do begin
    if StrToInt64Def(Dataset.FieldByName('id_suppinv').AsString,0) = aIngId then begin
      result := Dataset.RecNo - 1;
      break;
    end;
    Dataset.next;
  end;
end;

procedure TReorderInfoDataSet.CustomRefreshReorderDS(
  aDataset : TClientDataset;listType : TStockDocType);
var par : Currency;
    idxLine : Integer;
  id_suppinv, id_supp, id_ing : Int64;
  rd : TReorderData;
  id_um_purch : Int64;
  newprice : Currency;
  newqty   : Double;
  newqtyR   : Double;
  orderedToday : Double;
  d1,d2 : TDatetime;
  rds   : TReorderDatas;
  onhand : Currency;
  i : Integer;
  qSuppItem : IIBSQL;
  reorderType : TReorderType;
begin
  idxLine := 0;
  if not (posalg.GetCurrentStep is TBLRStockDoc) then exit;
  rds := TBLRStockDoc(posalg.GetCurrentStep).ReorderDataList;
  if rds.Count=0 then exit;

    GetRange(upos.GetPOSNow, d1, d2);
    for i := 0 to Pred(rds.count) do begin
      rd := rds.Items[i];
      id_ing  := rd.RDIngID;
      id_supp := rd.RDSuppID;
      id_suppinv := rd.SuppIngID;
      onhand := StockmanagerU.GetInventoryItemBalance(upos.UntillDB,id_ing);

      qSuppItem := FUntillDB.GetPreparedIIbSql('select si.supplier_article_number,'
        + ' si.DESCRIPTION, si.ID_UNITY_PURCHASE, ii.name, ii.reorder_type, ii.reorder_min, '
        + ' ii.ITEM_NUMBER, ii.REORDER_PAR, u.name um, u2.name um2,'
        + ' ic.group_name inventory_class, ic.inv_category_number'
        + ' from supplier_item si '
        + ' join inventory_item ii on ii.id=si.id_inventory_item'
        + ' join UNITY u on u.ID = ii.ID_UNITY and u.is_active=1 '
        + ' join inventory_categories ic on ii.id_inventory_categories = ic.id '
        + ' join UNITY_CONVERSION uc on si.ID_UNITY_PURCHASE = uc.id and uc.is_active = 1 '
        + ' join UNITY u2 on uc.ID_UNITY_CONVERT = u2.id and u2.is_active=1 '
        + ' where si.id=:id');
      qSuppItem.q.ParamByName('id').AsInt64 :=id_suppinv;
      qSuppItem.ExecQuery;

      if not qSuppItem.eof then begin
        orderedToday := StockManagerU.GetPOQuantityInPurchaseUM(upos.UntillDB, id_ing, id_supp, d1, d2);
        reorderType := TReorderType(qSuppItem.Q.FieldByName('reorder_type').asInteger);
        aDataset.Append;
        aDataset.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(idxLine);
        aDataset.FieldByName('id_inventory_item').asString       := IntToStr(id_ing);
        aDataset.FieldByName('id_supplier').asString             := IntToStr(id_supp);
        aDataset.FieldByName('id_suppinv').asString              := IntToStr(id_suppinv);
        aDataset.FieldByName('supplier').asString                := GetSupplierData(id_supp).name;
        aDataset.FieldByName('supplier_desc').asString           := qSuppItem.Q.FieldByName('DESCRIPTION').AsString;
        aDataset.FieldByName('onhand_stock').AsCurrency          := onhand;
        aDataset.FieldByName('OnHand').AsCurrency                := onhand;
        aDataset.FieldByName('OrderQty').AsCurrency              := rd.quantity;
        aDataset.FieldByName('notes').AsString                   := rd.notes;
        aDataset.FieldByName('price').AsCurrency                 := rd.price;
        aDataset.FieldByName('ordered_today').AsCurrency         := orderedToday;
        aDataset.FieldByName('inventory_class_number').asInteger := qSuppItem.Q.FieldByName('inv_category_number').asInteger;
        aDataset.FieldByName('inventory_class').asString         := qSuppItem.Q.FieldByName('inventory_class').asString;
        aDataset.FieldByName('inventory_item_number').asInteger  := qSuppItem.Q.FieldByName('item_number').asInteger;
        aDataset.FieldByName('inventory_item').asString          := qSuppItem.Q.FieldByName('name').asString;
        aDataset.FieldByName('reorder_type').asInteger           := Ord(reorderType);
        aDataset.FieldByName('supplier_article').asString        := qSuppItem.Q.FieldByName('supplier_article_number').asString;

        par         := qSuppItem.Q.FieldByName('reorder_par').AsCurrency;
        if reorderType = invrtPeriodic then // periodic par
          par      := StockManagerU.GetIngredientParValue(upos.UntillDB, upos.GetPOSNow, id_ing);
        aDataset.FieldByName('par').AsCurrency        := qSuppItem.Q.FieldByName('reorder_min').AsCurrency;
        aDataset.FieldByName('OrderQty').AsCurrency   := par - onhand;
        aDataset.FieldByName('um').asString           := qSuppItem.Q.FieldByName('um').asString;
        aDataset.FieldByName('um_stock').asString     := qSuppItem.Q.FieldByName('um').asString;

        id_um_purch := StrToInt64Def(qSuppItem.Q.FieldByName('ID_UNITY_PURCHASE').asString,0);
        if id_supp > 0 then begin
          // Need recalc quantity to Purchase UM
          aDataset.FieldByName('um').asString := qSuppItem.Q.FieldByName('um2').asString;
          newqty   := aDataset.FieldByName('OrderQty').AsCurrency;
          newprice := 0;
          sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
          aDataset.FieldByName('OrderQty').AsCurrency := newqty;
          newqty   := onhand;
          newqtyR  := onhand;
          sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
          aDataset.FieldByName('onhand').AsCurrency := newqty;
          sm.ConvertUM(nil, sttPurchase, id_um_purch, newqtyR, newprice, false, true);
          aDataset.FieldByName('onhand_rnd').AsCurrency            := newqtyR;
          newqty   := qSuppItem.Q.FieldByName('reorder_min').AsCurrency;
          sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
          aDataset.FieldByName('par').AsCurrency := newqty;
        end;
        // recalculated values are shown only in result list
        if listType = soptResult then begin
          // If dataset is filled for Reorder BL step
          aDataset.FieldByName('OrderQty').AsCurrency          := 0;
          aDataset.FieldByName('notes').AsString               := '';
          aDataset.FieldByName('price').AsCurrency             := 0;
          aDataset.FieldByName('stock_price').AsCurrency       := 0;
          aDataset.FieldByName('total_stock_price').AsCurrency := 0;
          aDataset.FieldByName('OrderQty').AsCurrency := rd.quantity;
          aDataset.FieldByName('notes').AsString      := rd.notes;
          aDataset.FieldByName('price').AsCurrency    := rd.price;
          // Recalc back to stock price
          if id_um_purch>0 then begin
            newqty   := rd.quantity;
            newprice := rd.price;
            sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, true);
            aDataset.FieldByName('stock_price').AsCurrency := newprice;
            aDataset.FieldByName('total_stock_price').AsCurrency := newprice * newqty;
          end;
        end;
        aDataset.Post;

      end;

      Inc(idxLine);
    end;
  Dataset.AddIndex('ingIndex', 'inventory_item', [ixCaseInsensitive], '');
  DataSet.IndexName := 'ingIndex';
end;

procedure TReorderInfoDataSet.RefreshData;
var ord_propType : TStockDocType; // = (soptOriginal, soptResult);
begin
  inherited;
  ord_propType := GetProposalType;

  if posalg.GetCurrentStep is TBLRStockReorder then
  CustomRefreshReorderDS(Dataset, ord_propType);
end;

function TReorderInfoDataSet.GetProposalType : TStockDocType;
begin
  result := soptOriginal;
  if FStoredParams.Count > 0 then
    result := TStockDocType(StrToIntDef(FStoredParams[0],0));
end;

{ TStockPOIngSingleDataSet }

constructor TStockPOIngSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    UPos.Un.RegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.RegisterListener(URL_STOCK_PO, Self);
    UPos.Un.RegisterListener(URL_STOCK_STOCK_PO, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
end;

destructor TStockPOIngSingleDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_PO, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER_SETITEM, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_STOCK_PO, Self);
  end;
  inherited;
end;

procedure TStockPOIngSingleDataSet.FillDataset(ingProv: TIngDataReadOnlyProvider;
  id_loc, id_supp: Int64);
var reorderStep : TBLRStockDoc;
begin
  inherited;
  reorderStep := TBLRStockDoc(PosAlg.GetCurrentStep);
  Dataset.FieldByName(REORDER_QUANTITY_FIELD_NAME).asCurrency  := reorderStep.ReorderData.Quantity;
  Dataset.FieldByName(REORDER_NOTES_FIELD_NAME).asString       := reorderStep.ReorderData.Notes;
  Dataset.FieldByName(REORDER_PRICE_FIELD_NAME).asCurrency     := reorderStep.ReorderData.Price;
  Dataset.FieldByName('total').asCurrency                      := reorderStep.ReorderDataList.GetTotal;
  Dataset.FieldByName('supplier_total').asCurrency
    := reorderStep.ReorderDataList.GetTotal(reorderStep.ReorderData.RDSuppID);
end;

class function TStockPOIngSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient document properties','Dataset parameter');
end;

class function TStockPOIngSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,REORDER_QUANTITY_FIELD_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Quantity')
  else if SameText(FieldName,REORDER_NOTES_FIELD_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Item notes')
  else if SameText(FieldName,'total') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Total amount')
  else if SameText(FieldName,'supplier_total') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier total amount')
  else if SameText(FieldName,REORDER_PRICE_FIELD_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Price')
  else
    Result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockPOIngSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase order advice ingredient properties','Dataset parameter');
end;

procedure TStockPOIngSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(REORDER_QUANTITY_FIELD_NAME, ftCurrency);
  AddFieldDef(REORDER_NOTES_FIELD_NAME, ftString, 250);
  AddFieldDef(REORDER_PRICE_FIELD_NAME, ftCurrency);
  AddFieldDef('total', ftCurrency);
  AddFieldDef('supplier_total', ftCurrency);
end;

function TStockPOIngSingleDataSet.NeedRefresh: boolean;
begin
  result := (PosAlg.GetCurrentStep is TBLRStockDoc)
      or (posalg.GetCurrentStep.ClassType = TBLRGetDocItemNotes)
      or (posalg.GetCurrentStep.ClassType = TBLRGetDocItemPrice);
end;

procedure TStockPOIngSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url <> URL_STOCK_REORDER)
    and (url <> URL_STOCK_PO)
    and (url <> URL_STOCK_STOCK_PO)
    and (url <> URL_STOCK_REORDER_SETITEM) then exit;
  if not NeedRefresh then exit;

  analysechanges(ue);
end;

procedure TStockPOIngSingleDataSet.RefreshData;
var ingProv : TIngDataReadOnlyProvider;
    id_supp : Int64;
var rd : TReorderData;
begin
  inherited;
  if not NeedRefresh then exit;

  if (PosAlg.GetCurrentStep is TBLRStockDoc) then begin
    if TBLRStockDoc(PosAlg.GetCurrentStep).CurSuppIngID <=0 then
      exit;
  end;

  id_supp := 0;
  ingProv := nil;
  if PosAlg.GetCurrentStep is TBLRStockDoc then begin
    ingProv := TBLRStockDoc(PosAlg.GetCurrentStep).IngDataProvider;
    id_supp := TBLRStockDoc(PosAlg.GetCurrentStep).CurSupplierID;
    if id_supp=0 then begin// multi PO is being created
      rd := TBLRStockDoc(PosAlg.GetCurrentStep).ReorderData;
      if assigned(rd) then
        id_supp := rd.RDSuppID;
    end;
  end;

  if not assigned(ingProv) then exit;
  if ingProv.IsEmpty then exit;

  Dataset.Edit;

  FillDataset(ingProv, 0, id_supp);

  Dataset.Post;
end;

{ TStockPOSingleDataSet }

constructor TStockPOSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_PO_DATA, Self);
end;

destructor TStockPOSingleDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_PO_DATA, Self);
  inherited;
end;

class function TStockPOSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase order properties','Dataset parameter');
end;

class function TStockPOSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,'customer_nr') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Customer nr.')
  else if SameText(FieldName,REORDER_PROPOSAL_REQDT_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Delivery date')
  else if SameText(FieldName,REORDER_PROPOSAL_DT_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Date & time')
  else if SameText(FieldName,REORDER_PROPOSAL_REFERENCE_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Reference')
  else if SameText(FieldName,REORDER_PROPOSAL_DESCRIPTION_NAME) then
    Result := StrDescName
  else if SameText(FieldName,'number') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'PO number')
  else if SameText(FieldName,'state') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'PO state')
  else if SameText(FieldName,'supplier') then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Supplier')
  else
    Result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockPOSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase order advice properties','Dataset parameter');
end;

function TStockPOSingleDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if SameText(FieldName, 'state') then
    result := StockPurchaseOrderU.GetPOStateName(DataSet.FieldByName(FieldName).asInteger)
  else if SameText(FieldName, 'number') then
    result := ''
  else
    result := inherited GetStringRepresentation(FieldName,FormatSpec);
end;

function TStockPOSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, REORDER_PROPOSAL_DESCRIPTION_NAME) then
    result := (PosAlg.GetCurrentStep is TBLRStockSaveReorderPO)
      and not (PosAlg.GetCurrentStep is TBLRStockSaveMultiReorderPO)
end;

procedure TStockPOSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(REORDER_PROPOSAL_DT_NAME, ftDatetime);
  AddFieldDef(REORDER_PROPOSAL_REQDT_NAME, ftDatetime);
  AddFieldDef(REORDER_PROPOSAL_REFERENCE_NAME, ftString, 250);
  AddFieldDef(REORDER_PROPOSAL_DESCRIPTION_NAME, ftString, 250);
  AddFieldDef('number', ftInteger);
  AddFieldDef('state' , ftInteger);
  AddFieldDef('supplier', ftWideString, 100);
  AddFieldDef('customer_nr',ftWideString, 50);
end;

procedure TStockPOSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if not (ue is TPMAnalyseChangeFlags)
    and not (ue is TPMDataChangedEvent) then exit;

  if not (PosAlg.GetCurrentStep is TBLRStockSaveReorderPO)
    and not (PosAlg.GetCurrentStep is TBLRPOOverview) then exit;

  AnalyseChanges(ue);
end;

procedure TStockPOSingleDataSet.RefreshData;
var poStep     : TBLRStockSaveReorderPO;
    poStepOver : TBLRPOOverview;
    qSelPO     : IIBSQL;
    id_supp    : Int64;
    sd         : TSupplierData;
begin
  inherited;
  if (PosAlg.GetCurrentStep is TBLRStockSaveReorderPO) then begin
    poStep := TBLRStockSaveReorderPO(PosAlg.GetCurrentStep);
    id_supp := poStep.CurSupplierID;
    sd := GetSupplierData(id_supp);
    Dataset.Edit;
    Dataset.FieldByName(REORDER_PROPOSAL_REQDT_NAME).asDatetime := poStep.ReqDocdatetime;
    Dataset.FieldByName(REORDER_PROPOSAL_DT_NAME).asDatetime := poStep.Docdatetime;
    Dataset.FieldByName(REORDER_PROPOSAL_REFERENCE_NAME).asString := poStep.reference;
    Dataset.FieldByName(REORDER_PROPOSAL_DESCRIPTION_NAME).asString := poStep.Description;
    Dataset.FieldByName('supplier').asString    := sd.name;
    Dataset.FieldByName('customer_nr').asString := sd.customer_nr;
    Dataset.Post;
  end else if (PosAlg.GetCurrentStep is TBLRPOOverview) then begin
    poStepOver := TBLRPOOverview(PosAlg.GetCurrentStep);

    id_supp := poStepOver.id_supplier;
    sd := GetSupplierData(id_supp);
    qSelPO := upos.UntillDB.GetPreparedIIbSql('select * from purchase_order where id=:id');
    qSelPO.q.ParamByName('id').AsInt64 := poStepOver.CurPOID;
    qSelPO.ExecQuery;

    Dataset.Edit;
    Dataset.FieldByName(REORDER_PROPOSAL_REQDT_NAME).asDatetime := 0;
    Dataset.FieldByName(REORDER_PROPOSAL_DT_NAME).asDatetime := 0;
    Dataset.FieldByName(REORDER_PROPOSAL_REFERENCE_NAME).asString := '';
    Dataset.FieldByName(REORDER_PROPOSAL_DESCRIPTION_NAME).asString := '';
    Dataset.FieldByName('number').asInteger := 0;
    Dataset.FieldByName('state').asInteger  := -1;
    Dataset.FieldByName('supplier').asString := '';
    Dataset.Post;
    if qSelPO.Eof then exit;

    Dataset.Edit;
    Dataset.FieldByName(REORDER_PROPOSAL_DT_NAME).asDatetime        := qSelPO.q.FieldByName('ORDER_DATE').asDatetime;
    Dataset.FieldByName(REORDER_PROPOSAL_REQDT_NAME).asDatetime     := qSelPO.q.FieldByName('REQIRED_DATE').asDatetime;
    Dataset.FieldByName(REORDER_PROPOSAL_REFERENCE_NAME).asString   := qSelPO.q.FieldByName('REFERENCE').asString;
    Dataset.FieldByName(REORDER_PROPOSAL_DESCRIPTION_NAME).asString := qSelPO.q.FieldByName('DESCRIPTION').asString;
    Dataset.FieldByName('number').asInteger                         := qSelPO.q.FieldByName('PO_NUMBER').asInteger;
    Dataset.FieldByName('state').asInteger                          := qSelPO.q.FieldByName('CONDUCTED').asInteger;
    Dataset.FieldByName('supplier').asString                        := sd.name;
    Dataset.FieldByName('customer_nr').asString                     := sd.customer_nr;
    Dataset.Post;

  end;
end;

{ TStockInvoiceSingleDataSet }

class function TStockInvoiceSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock invoice properties','Dataset parameter');
end;

class function TStockInvoiceSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,REORDER_PROPOSAL_DT_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Date & time')
  else if SameText(FieldName,REORDER_PROPOSAL_REFERENCE_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Reference')
  else
    Result := inherited GetFieldCaption(FieldName, DB);
end;

class function TStockInvoiceSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock invoice properties','Dataset parameter');
end;

function TStockInvoiceSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, REORDER_PROPOSAL_REFERENCE_NAME) then
    result := (PosAlg.CurrentStateGUID = TBLRStockSaveInvoice.GetStaticGUID)
end;

procedure TStockInvoiceSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(REORDER_PROPOSAL_DT_NAME, ftDatetime);
  AddFieldDef(REORDER_PROPOSAL_REFERENCE_NAME, ftString, 250);
end;

procedure TStockInvoiceSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  inherited;
  if not (ue is TPMAnalyseChangeFlags) then exit;
  if not GetVisibility(REORDER_PROPOSAL_REFERENCE_NAME) then exit;

  AnalyseChanges(ue);
end;

procedure TStockInvoiceSingleDataSet.RefreshData;
var invStep : TBLRStockSaveInvoice;
begin
  inherited;
  if not GetVisibility(REORDER_PROPOSAL_REFERENCE_NAME) then exit;

  invStep := TBLRStockSaveInvoice(PosAlg.GetCurrentStep);

  Dataset.Edit;
  Dataset.FieldByName(REORDER_PROPOSAL_DT_NAME).asDatetime := invStep.Docdatetime;
  Dataset.FieldByName(REORDER_PROPOSAL_REFERENCE_NAME).asString := invStep.reference;
  Dataset.Post;
end;

{ TInvoiceItemsInfoDataSet }

class function TInvoiceItemsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Invoice items','Dataset caption');
end;

function TInvoiceItemsInfoDataSet.GetDatasetPOID: Int64;
begin
  result  := 0;
  if posalg.GetCurrentStep is TBLRStockInvoice then
    result  := TBLRStockInvoice(posalg.GetCurrentStep).CurPOID
end;

class function TInvoiceItemsInfoDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('Orderqty') then
    result := Plugin.Translate('StockDatasetsU', 'Ordered quantity')
  else if Uppercase(FieldName) = Uppercase('ordered_qty') then
    result := Plugin.Translate('StockDatasetsU', 'received quantity')
  else if Uppercase(FieldName) = Uppercase('diff') then
    result := Plugin.Translate('StockDatasetsU', 'Backorder')
  else if Uppercase(FieldName) = Uppercase('rest') then
    result := Plugin.Translate('StockDatasetsU', 'Rest')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TInvoiceItemsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of ingredients for invoice','Dataset caption');
end;

function TInvoiceItemsInfoDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'inventory_item') then
    result := (PosAlg.CurrentStateGUID = TBLRStockInvoice.GetStaticGUID)
end;

procedure TInvoiceItemsInfoDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('ordered_qty', ftCurrency);
  AddFieldDef('diff', ftCurrency);
  AddFieldDef('rest', ftCurrency);
end;

procedure TInvoiceItemsInfoDataSet.RefreshData;
var id_po        : Int64;
    ord_propType : TStockDocType; // = (soptOriginal, soptResult);
    qSelInv : IIBSQL;
    id_inv, id_supping : Int64;
    idxLine : Integer;
    bResultList : boolean;
    bNeedAdd    : boolean;
    reorderStep : TBLRStockInvoice;
    rd : TReorderData;
    oldrest : Currency;
    strSqlSel : String;
begin
  dataset.EmptyDataSet;
  ord_propType := GetProposalType;

  id_po        := GetDatasetPOID;
  if id_po = 0 then exit;

  strSqlSel := 'select purchase_order_item.*, '
      + ' inventory_item.item_number, inventory_item.name inventory_item, '
      + ' inventory_categories.group_name inventory_class, SUPPLIER_ITEM.supplier_article_number,'
      + ' inv_category_number inventory_class_number, upurch.name ump, uusage.name umu, '
      + ' SUPPLIER_ITEM.id id_supping, SUPPLIER_ITEM.description supp_desc'
      + ' from purchase_order_item '
      + ' inner join inventory_item on inventory_item.id = purchase_order_item.id_inventory_item and inventory_item.is_active=1'
      + ' inner join inventory_categories on inventory_item.id_inventory_categories = inventory_categories.id and inventory_categories.is_active=1'
      + ' inner join SUPPLIER_ITEM on SUPPLIER_ITEM.id=purchase_order_item.id_SUPPLIER_ITEM and SUPPLIER_ITEM.is_active=1 '
      + ' inner join UNITY_CONVERSION on UNITY_CONVERSION.id=SUPPLIER_ITEM.ID_UNITY_PURCHASE and UNITY_CONVERSION.is_active=1'
      + ' inner join unity upurch on UNITY_CONVERSION.ID_UNITY_CONVERT = upurch.id and upurch.is_active=1 '
      + ' inner join unity uusage on UNITY_CONVERSION.ID_UNITY = uusage.id and uusage.is_active=1 '
      + ' where ID_PURCHASE_ORDER=:ID_PURCHASE_ORDER and purchase_order_item.is_active=1 '
      + ' order by purchase_order_item.ID';
  qSelInv := upos.UntillDB.GetPreparedIIbSql( strSqlSel );
  qSelInv.q.ParamByName('ID_PURCHASE_ORDER').asInt64 := id_po;
  qSelInv.ExecQuery;

  idxLine := 0;
  bResultList := (ord_propType = soptResult);
  while not qSelInv.eof do begin

    id_inv     := StrToInt64Def(qSelInv.Q.FieldByName('id_inventory_item').AsString,0);
    id_supping := StrToInt64Def(qSelInv.Q.FieldByName('id_supplier_item').AsString,0);
    if posalg.GetCurrentStep is TBLRStockInvoice then begin
      // Need to check if user did not delete it
      if bResultList then // in result list only move items are shown
        bNeedAdd := assigned(TBLRStockInvoice(posalg.GetCurrentStep).GetReorderItemData(id_supping))
      else // Original list - items , which wree not moved to show yet
        bNeedAdd := not assigned(TBLRStockInvoice(posalg.GetCurrentStep).GetReorderItemData(id_supping));
    end else
      bNeedAdd := true;

    if bNeedAdd then begin
      Dataset.Append;

      // For Info dataset we have to assign group field
      Dataset.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(idxLine);
      Dataset.FieldByName('id_suppinv').asString        := qSelInv.Q.FieldByName('id_supping').asString;
      Dataset.FieldByName('id_inventory_item').asString := IntToStr( id_inv );
      Dataset.FieldByName('supplier_desc').asString     := qSelInv.Q.FieldByName('supp_desc').asString;
      Dataset.FieldByName('OnHand').AsCurrency          := StockmanagerU.GetInventoryItemBalance(upos.UntillDB,id_inv);
      Dataset.FieldByName('price').AsCurrency           := 0;
      Dataset.FieldByName('OrderQty').AsCurrency        := qSelInv.Q.FieldByName('quantity').asCurrency;
      Dataset.FieldByName('notes').AsString             := '';
      Dataset.FieldByName('price').AsCurrency           := 0;
      Dataset.FieldByName('ordered_qty').asCurrency     := 0;
      Dataset.FieldByName('rest').AsCurrency            := 0;
      if bResultList// reclaced values are shown only in result list
      then begin
          oldrest := StockManagerU.GetRestPOQuantity( upos.UntillDB, id_supping, id_po);
          if posalg.GetCurrentStep is TBLRStockInvoice then begin
            reorderStep := TBLRStockInvoice(posalg.GetCurrentStep);
            // If dataset is filled for Reorder BL step
            rd := reorderStep.GetReorderItemData(id_supping);
            if assigned(rd) then begin
              if posalg.GetCurrentStep.ClassType = TBLRStockInvoice then
                Dataset.FieldByName('OrderQty').AsCurrency    := qSelInv.Q.FieldByName('quantity').asCurrency
              else
                Dataset.FieldByName('OrderQty').AsCurrency    := rd.Quantity;
              Dataset.FieldByName('notes').AsString         := rd.notes;
              Dataset.FieldByName('price').AsCurrency       := rd.price;
              Dataset.FieldByName('ordered_qty').asCurrency := qSelInv.Q.FieldByName('quantity').asCurrency - oldrest;
              Dataset.FieldByName('diff').AsCurrency  := rd.quantity;
              Dataset.FieldByName('rest').AsCurrency  := oldrest - rd.quantity;
            end;
          end else begin
            Dataset.FieldByName('notes').AsString         := qSelInv.Q.FieldByName('NOTES').asString;
            Dataset.FieldByName('price').AsCurrency       := qSelInv.Q.FieldByName('PRICE').asCurrency;
            Dataset.FieldByName('ordered_qty').asCurrency := qSelInv.Q.FieldByName('quantity').asCurrency - oldrest;
            Dataset.FieldByName('diff').AsCurrency        := oldrest;
          end;
      end;

      Dataset.FieldByName('inventory_class_number').asInteger := qSelInv.Q.FieldByName('inventory_class_number').asInteger;
      Dataset.FieldByName('inventory_class').asString         := qSelInv.Q.FieldByName('inventory_class').asString;
      Dataset.FieldByName('inventory_item_number').asInteger  := qSelInv.Q.FieldByName('item_number').asInteger;
      Dataset.FieldByName('inventory_item').asString          := qSelInv.Q.FieldByName('inventory_item').asString;
      Dataset.FieldByName('um').asString                      := qSelInv.Q.FieldByName('ump').asString;
      Dataset.FieldByName('um_stock').asString                := qSelInv.Q.FieldByName('umu').asString;
      Dataset.FieldByName('supplier_article').asString       := qSelInv.Q.FieldByName('supplier_article_number').asString;
      Dataset.Post;
    end;
    Inc(idxLine);
    qSelInv.Next;
  end;

end;

{ TPOModItemsInfoDataSet }

class function TPOModItemsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Modified order proposal','Dataset caption');
end;

class function TPOModItemsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of ingredients in PO','Dataset caption');
end;

function TPOModItemsInfoDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'inventory_item') then
    result := (PosAlg.CurrentStateGUID = TBLRChangeStockPO.GetStaticGUID)
end;

{ TPOOrderedScreenDataSet }

class function TPOOrderedScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Purchase Orders - ordered','Dataset parameter');
end;

class function TPOOrderedScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'PO.FR.','Dataset parameter');
end;

{ TPONewItemsInfoDataSet }

constructor TPONewItemsInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    UPos.Un.RegisterListener(URL_STOCK_STOCK_PO, Self);
    UPos.Un.RegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
end;

destructor TPONewItemsInfoDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_STOCK_PO, Self);
    UPos.Un.UnRegisterListener(URL_STOCK_REORDER_SETITEM, Self);
  end;
  inherited;
end;

class function TPONewItemsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Free order proposal','Dataset caption');
end;

class function TPONewItemsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of ingredients in free PO','Dataset caption');
end;

function TPONewItemsInfoDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'inventory_item') then
    result := (PosAlg.CurrentStateGUID = TBLRCreateStockPO.GetStaticGUID)
end;

procedure TPONewItemsInfoDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url = URL_STOCK_REORDER_SETITEM) and (ue.iInfo>0) then begin
    FPos := GetPosIng(ue.iInfo);
    ChangeCurPos(FPos, true);
  end;
  if (url = URL_STOCK_STOCK_PO) then begin
    Refresh;
    SendPosChanged;
  end;
  inherited;
end;

procedure TPONewItemsInfoDataSet.RefreshData;
var idxLine : Integer;
    reorderStep : TBLRStockDoc;
    rd          : TReorderData;
    id_um_purch : Int64;
    newprice    : Currency;
    newqty      : Double;
    rds         : TReorderDatas;
    id_supp     : Int64;
    i           : Integer;
    qSel        : IIBSQL;
    sqlStr      : String;
begin
  dataset.EmptyDataSet;
  if not (posalg.GetCurrentStep is TBLRCreateStockPO) then exit;

  reorderStep := TBLRCreateStockPO(posalg.GetCurrentStep);
  id_supp := reorderStep.CurSupplierID;
  if id_supp = 0 then exit;

  rds := reorderStep.ReorderDataList;
  if rds.count = 0  then exit;

    sqlStr := 'select Sum(stock_balance.amount) onhand, '
    + ' inventory_item.item_number, inventory_item.name inventory_item, '
    + ' inventory_item.reorder_par, reorder_type, '
    + ' inventory_categories.group_name inventory_class, '
    + ' inv_category_number inventory_class_number, unity.name um, reorder_min'
    + ' , SUPPLIER_ITEM.ID_UNITY_PURCHASE, SUPPLIER_ITEM.supplier_article_number, u2.name um2'
    + ' ,SUPPLIER_ITEM.description sdesc, SUPPLIER_ITEM.id id_supping'
    + ' from stock_balance '
    + ' inner join inventory_item on inventory_item.id = stock_balance.id_inventory_item and inventory_item.is_active=1'
    + ' inner join inventory_categories on inventory_item.id_inventory_categories = inventory_categories.id and inventory_categories.is_active=1'
    + ' inner join UNITY on UNITY.ID = inventory_item.ID_UNITY and UNITY.is_active=1 '
    + ' inner join SUPPLIER_ITEM on SUPPLIER_ITEM.ID_INVENTORY_ITEM = inventory_item.id '
    + '    and SUPPLIER_ITEM.ID = :ID_SUPPLIER_ITEM and SUPPLIER_ITEM.is_active=1'
    + '    and stock_balance.id_inventory_item=SUPPLIER_ITEM.id_inventory_item'
    + ' inner join UNITY_CONVERSION on SUPPLIER_ITEM.ID_UNITY_PURCHASE = UNITY_CONVERSION.id and UNITY_CONVERSION.is_active=1'
    + ' inner join UNITY u2 on UNITY_CONVERSION.ID_UNITY_CONVERT = u2.id and u2.is_active=1'
    + ' group by inventory_item.item_number, inventory_item.name, inventory_item.reorder_par,'
    + '   group_name, inv_category_number, unity.name, inventory_item.reorder_min, reorder_type, '
    + '   reorder_min, SUPPLIER_ITEM.ID_UNITY_PURCHASE, SUPPLIER_ITEM.supplier_article_number,u2.name, '
    + '   SUPPLIER_ITEM.description, id_supping';
    qSel := upos.UntillDB.GetPreparedIIbSql(sqlStr);
    idxLine := 0;
    for i := 0 to pred(rds.count) do begin
      rd := rds.Items[i];

      qSel.q.ParamByName('ID_SUPPLIER_ITEM').AsInt64 := rd.SuppIngID;
      qSel.ExecQuery;

      Dataset.Append;
      Dataset.FieldByName(UNTILL_RECORD_GROUP_ID).asString  := IntToStr(idxLine);
      Dataset.FieldByName('id_inventory_item').asString       := IntToStr(rd.RDIngID);
      Dataset.FieldByName('reorder_type').asInteger           := qSel.FieldByName('reorder_type').asInteger;
      Dataset.FieldByName('price').AsCurrency                 := rd.Price;
      Dataset.FieldByName('par').AsCurrency                   := qSel.FieldByName('reorder_par').asCurrency;
      Dataset.FieldByName('OrderQty').AsCurrency              := rd.quantity;
      Dataset.FieldByName('notes').AsString                   := rd.notes;
      Dataset.FieldByName('inventory_class_number').asInteger := qSel.Q.FieldByName('inventory_class_number').asInteger;
      Dataset.FieldByName('inventory_class').asString         := qSel.Q.FieldByName('inventory_class').asString;
      Dataset.FieldByName('inventory_item_number').asInteger  := qSel.Q.FieldByName('item_number').asInteger;
      Dataset.FieldByName('inventory_item').asString          := qSel.Q.FieldByName('inventory_item').asString;
      Dataset.FieldByName('supplier_article').asString        := qSel.Q.FieldByName('supplier_article_number').asString;
      Dataset.FieldByName('supplier_desc').asString           := qSel.Q.FieldByName('sdesc').asString;
      Dataset.FieldByName('id_suppinv').AsString              := qSel.Q.FieldByName('id_supping').asString;

      id_um_purch := StrToInt64Def(qSel.Q.FieldByName('ID_UNITY_PURCHASE').asString,0);
      Dataset.FieldByName('um').asString := qSel.Q.FieldByName('um2').asString;
      newqty   := qSel.FieldByName('onhand').asCurrency;
      newprice := 0;
      if id_um_purch > 0 then begin
        sm.ConvertUM(nil, sttPurchase, id_um_purch, newqty, newprice, false);
        Dataset.FieldByName('onhand').AsCurrency := newqty;
      end;
      Dataset.Post;

      qSel.Close;

      Inc(idxLine);
    end;
end;

{ TStockSupplierSingleDataSet }

constructor TStockSupplierSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_SUPPLIER, Self);
end;

destructor TStockSupplierSingleDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_SUPPLIER, Self);
  inherited;
end;

class function TStockSupplierSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock edit supplier','Dataset parameter');
end;

class function TStockSupplierSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName, SUPPLIER_NAME_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Name')
  else if SameText(FieldName, SUPPLIER_EMAIL_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'E-mail')
  else if SameText(FieldName, SUPPLIER_NUMBER_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Number')
  else if SameText(FieldName, SUPPLIER_NUMBER_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'PC text')
  else if SameText(FieldName, SUPPLIER_ADDRESS_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Address')
  else if SameText(FieldName, SUPPLIER_PHONE_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Phone')
  else if SameText(FieldName, SUPPLIER_FAX_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Fax')
  else if SameText(FieldName, SUPPLIER_WEBSITE_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Website')
  else if SameText(FieldName, SUPPLIER_VAT_NR_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'V.A.T. number')
  else if SameText(FieldName, SUPPLIER_DISCOUNT_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Discount')
  else if SameText(FieldName, SUPPLIER_DELIVERY_DAY) then
    result := Plugin.Translate('StockDatasetsU', 'Delivery day')
  else if SameText(FieldName, SUPPLIER_ORDER_DAY) then
    result := Plugin.Translate('StockDatasetsU', 'Order day')
  else if SameText(FieldName, SUPPLIER_MIN_AMOUNT) then
    result := Plugin.Translate('StockDatasetsU', 'Min. order amount')
  else
    result := '';
end;

class function TStockSupplierSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Modified stock supplier data','Dataset parameter');
end;

function TStockSupplierSingleDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if   Sametext(FieldName, SUPPLIER_DELIVERY_DAY)
    or Sametext(FieldName, SUPPLIER_ORDER_DAY) then begin
    if Dataset.FieldByName(FieldName).asInteger=0 then
      result := Plugin.Translate('RestaurantDataSetsU', 'Daily')
    else
      result := DaysOfWeek[Dataset.FieldByName(FieldName).asInteger-1];
  end else
    result := inherited GetStringRepresentation(FieldName, FormatSpec);
end;

function TStockSupplierSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if sametext( FieldName, SUPPLIER_NAME_FIELD_NAME ) then
    result := posalg.GetCurrentStep is TBLRSupplier;
end;

procedure TStockSupplierSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(SUPPLIER_NAME_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_NUMBER_FIELD_NAME, ftInteger);
  AddFieldDef(SUPPLIER_PC_TEXT_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_ADDRESS_FIELD_NAME, ftWideString, 250);
  AddFieldDef(SUPPLIER_PHONE_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_FAX_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_EMAIL_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_WEBSITE_FIELD_NAME, ftWideString, 250);
  AddFieldDef(SUPPLIER_VAT_NR_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_DISCOUNT_FIELD_NAME, ftInteger);
  AddFieldDef(SUPPLIER_PC_FONT_NAME_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_PC_FONT_SIZE_FIELD_NAME, ftInteger);
  AddFieldDef(SUPPLIER_ALLOW_BACK_ORDER_FIELD_NAME, ftInteger);
  AddFieldDef(SUPPLIER_ACCOUNT_NUM_FIELD_NAME, ftWideString, 250);
  AddFieldDef(SUPPLIER_DELIVERY_DAY, ftInteger);
  AddFieldDef(SUPPLIER_ORDER_DAY, ftInteger);
  AddFieldDef(SUPPLIER_MIN_AMOUNT, ftCurrency);
  AddFieldDef('country', ftWideString, 100);
  AddFieldDef('currency', ftWideString, 100);
end;

procedure TStockSupplierSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin

  if url=URL_STOCK_SUPPLIER then begin
    AnalyseChanges( ue );
    exit;
  end;

  inherited;
end;

procedure TStockSupplierSingleDataSet.RefreshData;
var sProvider : TSupplierDataProvider;
    cnd : TCountryData;
    crd : TCurrencyData;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRSupplier) then exit;
  sProvider := TBLRSupplier(posalg.GetCurrentStep).SuppProvider;
  if not assigned( sProvider ) then exit;

  with Dataset do begin
    Edit;
    FieldByName(SUPPLIER_NAME_FIELD_NAME).asString     := sProvider.Name;
    FieldByName(SUPPLIER_NUMBER_FIELD_NAME).asInteger  := sProvider.Number;
    FieldByName(SUPPLIER_PC_TEXT_FIELD_NAME).asString  := sProvider.PC_TEXT;
    FieldByName(SUPPLIER_ADDRESS_FIELD_NAME).asString  := sProvider.ADDRESS;
    FieldByName(SUPPLIER_PHONE_FIELD_NAME).asString    := sProvider.PHONE;
    FieldByName(SUPPLIER_EMAIL_FIELD_NAME).asString    := sProvider.EMAIL;
    FieldByName(SUPPLIER_WEBSITE_FIELD_NAME).asString  := sProvider.WEBSITE;
    FieldByName(SUPPLIER_VAT_NR_FIELD_NAME).asString   := sProvider.VAT_NR;
    FieldByName(SUPPLIER_DISCOUNT_FIELD_NAME).asInteger:= sProvider.DISCOUNT;
    FieldByName(SUPPLIER_PC_FONT_NAME_FIELD_NAME).asString:= sProvider.PC_FONT_NAME;
    FieldByName(SUPPLIER_PC_FONT_SIZE_FIELD_NAME).asInteger:= sProvider.PC_FONT_SIZE;
    FieldByName(SUPPLIER_ALLOW_BACK_ORDER_FIELD_NAME).asInteger:= sProvider.ALLOW_BACK_ORDER;
    FieldByName(SUPPLIER_ACCOUNT_NUM_FIELD_NAME).asString:= sProvider.ACCOUNT_NUM;
    FieldByName(SUPPLIER_DELIVERY_DAY).asInteger       := sProvider.Delivery_day;
    FieldByName(SUPPLIER_ORDER_DAY).asInteger          := sProvider.Order_day;
    FieldByName(SUPPLIER_MIN_AMOUNT).asCurrency        := sProvider.Min_amount;
    cnd := KernelCashDatasetsU.GetCountryDataByID(sProvider.ID_COUNTRIES);
    if assigned( cnd ) then
      FieldByName('country').asString := cnd.name;
    crd := KernelCashDatasetsU.GetCurrencyDataByID(sProvider.ID_CURRENCY);
    if assigned( crd ) then
      FieldByName('currency').asString := crd.name;
    Post;
  end;

end;

{ TSupplierContactInfoDataSet }

procedure TSupplierContactInfoDataSet.ChangeCurPos(NewPos: Integer;
  bKeepOldPositions: Boolean);
var msg : TBLRStockSupplierContactMessage;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRSupplier) then exit;
  FPos := NewPos;
  msg  := TBLRStockSupplierContactMessage.Create;
  msg.ContID        := StrToInt64Def(Dataset.fieldByName('id').asString,0);
  posalg.SendMessage(msg);
end;

constructor TSupplierContactInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    UPos.Un.RegisterListener(URL_STOCK_SUPPLIER, Self);
  end;
end;

destructor TSupplierContactInfoDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_SUPPLIER, Self);
  end;
  inherited;
end;

class function TSupplierContactInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Supplier contacts','Dataset caption');
end;

function TSupplierContactInfoDataSet.GetCurPos: Integer;
begin
  result := FPos;
end;

class function TSupplierContactInfoDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('name') then
    result := Plugin.Translate('StockDatasetsU', 'Name')
  else if Uppercase(FieldName) = Uppercase('POSITN') then
    result := Plugin.Translate('StockDatasetsU', 'Position')
  else if Uppercase(FieldName) = Uppercase('PHONE') then
    result := Plugin.Translate('StockDatasetsU', 'Phone')
  else if Uppercase(FieldName) = Uppercase('FAX') then
    result := Plugin.Translate('StockDatasetsU', 'Fax')
  else if Uppercase(FieldName) = Uppercase('MOBILE') then
    result := Plugin.Translate('StockDatasetsU', 'Mobile phone')
  else if Uppercase(FieldName) = Uppercase('EMAIL') then
    result := Plugin.Translate('StockDatasetsU', 'E-mail')
  else
    result := '';
end;

class function TSupplierContactInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of supplier contacts','Dataset caption');
end;

function TSupplierContactInfoDataSet.GetPosContact(aContactId: Int64): Integer;
begin
  result := FPos;
  if Dataset.State <> dsBrowse then exit;
  Dataset.First;
  while not Dataset.Eof do begin
    if StrToInt64Def(Dataset.FieldByName('id').AsString,0) = aContactId then begin
      result := Dataset.RecNo - 1;
      break;
    end;
    Dataset.next;
  end;
end;

procedure TSupplierContactInfoDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeInt);
  AddFieldDef('name', ftString);
  AddFieldDef('POSITN', ftString);
  AddFieldDef('PHONE', ftString);
  AddFieldDef('FAX', ftString);
  AddFieldDef('MOBILE', ftString);
  AddFieldDef('EMAIL', ftString);
end;

procedure TSupplierContactInfoDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if (url = URL_STOCK_SUPPLIER_CONTACT)
    or (url = URL_STOCK_SUPPLIER) then begin
    Refresh;
    SendPosChanged;
  end;
  inherited;
end;

procedure TSupplierContactInfoDataSet.RefreshData;
var scs : TSupplierContacts;
    sc  : TSupplierContact;
    i : Integer;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRSupplier) then exit;
  scs := TBLRSupplier(posalg.GetCurrentStep).SupplierContactList;
  if not assigned(scs) then exit;
  if scs.Count = 0 then exit;

  for i := 0 to Pred(scs.count) do begin
    sc := scs.Items[i];
    Dataset.Append;
    Dataset.FieldByName(UNTILL_RECORD_GROUP_ID).asInteger := i;
    Dataset.FieldByName('id').asString := IntToStr(sc.id);
    Dataset.FieldByName('name').asString := sc.Name;
    Dataset.FieldByName('POSITN').asString := sc.Position;
    Dataset.FieldByName('PHONE').asString := sc.Phone;
    Dataset.FieldByName('Fax').asString := sc.Fax;
    Dataset.FieldByName('MOBILE').asString := sc.Mobile;
    Dataset.FieldByName('EMAIL').asString := sc.Email;
    Dataset.Post;
  end;

end;

{ TStockSupplierContactSingleDataSet }

constructor TStockSupplierContactSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_SUPPLIER_CONTACT, Self);
end;

destructor TStockSupplierContactSingleDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_SUPPLIER_CONTACT, Self);
  inherited;
end;

class function TStockSupplierContactSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Supplier contact data','Dataset parameter');
end;

class function TStockSupplierContactSingleDataSet.GetFieldCaption(
  FieldName: String; DB: TUntillDb): WideString;
begin
  if SameText(FieldName, SUPPLIER_CONTACT_NAME_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Name')
  else if SameText(FieldName, SUPPLIER_CONTACT_PHONE_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Phone')
  else if SameText(FieldName, SUPPLIER_CONTACT_FAX_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Fax')
  else if SameText(FieldName, SUPPLIER_CONTACT_EMAIL_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'E-mail')
  else if SameText(FieldName, SUPPLIER_CONTACT_POSITION_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Position')
  else if SameText(FieldName, SUPPLIER_CONTACT_MOBILE_FIELD_NAME) then
    result := Plugin.Translate('StockDatasetsU', 'Mobile phone');
end;

class function TStockSupplierContactSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Modified supplier contact data','Dataset parameter');
end;

procedure TStockSupplierContactSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(SUPPLIER_CONTACT_NAME_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_CONTACT_PHONE_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_CONTACT_FAX_FIELD_NAME, ftWideString, 50);
  AddFieldDef(SUPPLIER_CONTACT_EMAIL_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_CONTACT_POSITION_FIELD_NAME, ftWideString, 100);
  AddFieldDef(SUPPLIER_CONTACT_MOBILE_FIELD_NAME, ftWideString, 100);
end;

procedure TStockSupplierContactSingleDataSet.OnURLEvent(url: string;
  ue: TUrlEvent);
begin
  if url=URL_STOCK_SUPPLIER_CONTACT then begin
    AnalyseChanges( ue );
    exit;
  end;
  inherited;
end;

procedure TStockSupplierContactSingleDataSet.RefreshData;
var scStep :TBLRSupplier;
begin
  inherited;
  if not (PosAlg.GetCurrentStep is TBLRSupplier) then exit;

  Dataset.Edit;
  scStep := TBLRSupplier(PosAlg.GetCurrentStep);
  Dataset.FieldByName(SUPPLIER_CONTACT_NAME_FIELD_NAME).asString  := scStep.CurSupplierContact.Name;
  Dataset.FieldByName(SUPPLIER_CONTACT_PHONE_FIELD_NAME).asString := scStep.CurSupplierContact.Phone;
  Dataset.FieldByName(SUPPLIER_CONTACT_FAX_FIELD_NAME).asString   := scStep.CurSupplierContact.Fax;
  Dataset.FieldByName(SUPPLIER_CONTACT_EMAIL_FIELD_NAME).asString := scStep.CurSupplierContact.email;
  Dataset.FieldByName(SUPPLIER_CONTACT_POSITION_FIELD_NAME).asString  := scStep.CurSupplierContact.Position;
  Dataset.FieldByName(SUPPLIER_CONTACT_MOBILE_FIELD_NAME).asString  := scStep.CurSupplierContact.Mobile;
  Dataset.Post;

end;

{ TPOInfoDataSet }

procedure TPOInfoDataSet.ChangeCurPos(NewPos: Integer;
  bKeepOldPositions: Boolean);
var msg : TBLRStockPOMessage;
begin
  inherited;
  if not (posalg.GetCurrentStep is TBLRPOOverview) then exit;
  FPos   := NewPos;
  msg    := TBLRStockPOMessage.Create;
  msg.ID := StrToInt64Def(Dataset.fieldByName('id').asString,0);
  posalg.SendMessage(msg);
end;

constructor TPOInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    UPos.Un.RegisterListener(URL_STOCK_PO_OVERVIEW, Self);
  end;
end;

destructor TPOInfoDataSet.Destroy;
begin
  if assigned(UPos_) then begin
    UPos.Un.UnRegisterListener(URL_STOCK_PO_OVERVIEW, Self);
  end;
  inherited;
end;

class function TPOInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: Purchase orders','Dataset caption');
end;

function TPOInfoDataSet.GetCurPos: Integer;
begin
  result := FPos;
end;

class function TPOInfoDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if SameText(FieldName,'id') then
    result := ''
  else if SameText(FieldName,'number') then
    result := Plugin.Translate('StockDatasetsU', 'PO number')
  else if SameText(FieldName,'name') then
    result := Plugin.Translate('StockDatasetsU', 'PO reference')
  else if SameText(FieldName,'state') then
    result := Plugin.Translate('StockDatasetsU', 'PO state')
  else if SameText(FieldName,'supplier') then
    result := Plugin.Translate('StockDatasetsU', 'Supplier')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

class function TPOInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of Purchase orders','Dataset caption');
end;

function TPOInfoDataSet.GetStringRepresentation(FieldName: String;
  FormatSpec: TDataSetFieldFormat): WideString;
begin
  if SameText(FieldName, 'state') then
    result := StockPurchaseOrderU.GetPOStateName(DataSet.FieldByName(FieldName).asInteger)
  else
    result := inherited GetStringRepresentation(FieldName,FormatSpec);
end;

function TPOInfoDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'number') then
    result := (PosAlg.GetCurrentStep is TBLRPOOverview)
end;

procedure TPOInfoDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('id', ftLargeInt);
  AddFieldDef('number', ftInteger);
  AddFieldDef('name', ftString, 100);
  AddFieldDef('state', ftInteger);
  AddFieldDef('supplier', ftString, 100);
end;

procedure TPOInfoDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if url = URL_STOCK_PO_OVERVIEW then begin
    Refresh;
    SendPosChanged;
  end;
  inherited;
end;

procedure TPOInfoDataSet.RefreshData;
var sqlStr  : String;
    qSel    : IIBSQL;
    id_supp : Int64;
    IdxLine : Integer;
    searchSample : String;
begin
  inherited;

  if not (posalg.GetCurrentStep is TBLRPOOverview) then exit;

  id_supp := TBLRPOOverview(posalg.GetCurrentStep).id_supplier;
  if id_supp <= 0 then exit;

  searchSample := TBLRPOOverview(posalg.GetCurrentStep).SearchSample;
  sqlStr := 'select * from PURCHASE_ORDER '
      + ' where is_active=1 and ID_SUPPLIERS=:ID_SUPPLIERS and conducted < :closed ';
  if searchSample<>'' then
    sqlStr := sqlStr + ' and ((PURCHASE_ORDER.po_number like ''%'+ searchSample + '%'')'
      + ' or (upper(PURCHASE_ORDER.REFERENCE) like ''%'+ UpperCase(searchSample) + '%''))';
  qSel := upos.UntillDB.GetPreparedIIbSql( sqlStr );
  qSel.q.ParamByName('ID_SUPPLIERS').AsInt64 := id_supp;
  qSel.q.ParamByName('closed').AsInteger     := Ord(stpoReceived);
  qSel.ExecQuery;

  IdxLine := 0;
  while not qSel.eof do begin
    Dataset.Append;
    Dataset.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(idxLine);
    Dataset.FieldByName('id').AsString       := qSel.q.FieldByName('id').asString;
    Dataset.FieldByName('number').AsInteger  := qSel.q.FieldByName('PO_NUMBER').asInteger;
    Dataset.FieldByName('name').asString     := qSel.q.FieldByName('REFERENCE').asString;
    Dataset.FieldByName('state').asInteger   := qSel.q.FieldByName('CONDUCTED').asInteger;
    Dataset.FieldByName('supplier').asString := GetSupplierData(id_supp).name;
    Dataset.Post;
    inc(IdxLine);
    qSel.next;
  end;

  if TBLRPOOverview(posalg.GetCurrentStep).CurPOID <=0 then begin// User did not choose PO yet
    Dataset.First;
    ChangeCurPos(FPos, true);
  end;


end;

{ TPOItemsInfoDataSet }

class function TPOItemsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: order proposal overview','Dataset caption');
end;

function TPOItemsInfoDataSet.GetDatasetPOID: Int64;
begin
  result  := inherited GetDatasetPOID;
  if posalg.GetCurrentStep is TBLRPOOverview then
    result  := TBLRPOOverview(posalg.GetCurrentStep).CurPOID
end;

class function TPOItemsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('StockDatasetsU', 'Stock: list of ingredients in PO','Dataset caption');
end;

function TPOItemsInfoDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, 'inventory_item') then
    result := (PosAlg.CurrentStateGUID = TBLRPOOverview.GetStaticGUID)
end;

{ TIngredientsPhysCountScreenDataSet }

function TIngredientsPhysCountScreenDataSet.GetButtonExtraData2(
  ObjectId: Int64): TExtraButtonContent;
begin
  Result := GetCurrencyValue(ObjectId, 'ph_amount');
end;

class function TIngredientsPhysCountScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Physical count ingredients','Dataset parameter');
end;

procedure TIngredientsPhysCountScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
var txt    : String;
    curIng : Int64;
    rnd    : Integer;
begin
  inherited;
  with Content do begin
    if FStoredParams.count > 0  then begin
      rnd := upos.MainCurrency.Round;
      if FStoredParams.count > 1  then
        rnd := StrToIntDef(FStoredParams[1],0);
      Text := GetParamButtonCaption(FStoredParams[0], rnd);
      if Text='' then
        Text:=txt;
    end;
  end;
  if posalg.GetCurrentStep is TBLRStockBalanceControl then begin
    curIng  := TBLRStockBalanceControl(posalg.GetCurrentStep).AllowPhysEnter;
    if ( curIng <> -1) then begin
      Content.BtnPosDisabled   := DataSet.FieldByName('ph_done').asBoolean;
      if DataSet.FieldByName('ph_done').asBoolean then begin
        if DataSet.FieldByName('changed').asBoolean  then
          Content.Font.Color       := clRed
        else
          Content.Font.Color       := clGreen;
        Content.Font.Size        := 0;
        Content.CustomFont       := true;
      end;
    end;
  end;
end;

class function TIngredientsPhysCountScreenDataSet.GetExtra2Caption: String;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Counted amount','Ingredients extra 1 caption');
end;

class function TIngredientsPhysCountScreenDataSet.GetFieldCaption(
  FieldName: String; DB: TUntillDB): WideString;
begin
  if FieldName = 'inventory_item' then
    result:=Plugin.Translate('RestaurantDataSetsU', 'Ingredient name')
  else if FieldName = 'um' then
    result:=Plugin.Translate('RestaurantDataSetsU', 'Untity of measure')
  else if FieldName = 'notes' then
    result:=Plugin.Translate('RestaurantDataSetsU', 'Notes')
  else
    result:='';
end;

class function TIngredientsPhysCountScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Physical count ingredients','Dataset parameter');
end;

class function TIngredientsPhysCountScreenDataSet.GetParamsFrame: TDataSetParamsFrameClass;
begin
  result := TClientScreenParamsFrame;
end;

class function TIngredientsPhysCountScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Cnt.Ing.','Dataset parameter');
end;

{ TStockMultiPOSingleDataSet }

constructor TStockMultiPOSingleDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_PO_DATA, Self);
end;

destructor TStockMultiPOSingleDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_PO_DATA, Self);
  inherited;
end;

class function TStockMultiPOSingleDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Multi Purchase order','Dataset parameter');
end;

class function TStockMultiPOSingleDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDb): WideString;
begin
  if SameText(FieldName,REORDER_PROPOSAL_DT_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Date & time')
  else if SameText(FieldName,REORDER_PROPOSAL_REQDT_NAME) then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Delivery date')
end;

class function TStockMultiPOSingleDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Multi Purchase order properties','Dataset parameter');
end;

function TStockMultiPOSingleDataSet.GetVisibility(FieldName: String): Boolean;
begin
  result := false;
  if SameText(FieldName, REORDER_PROPOSAL_DT_NAME) then
    result := (PosAlg.GetCurrentStep.ClassType = TBLRStockSaveMultiReorderPO)
end;

procedure TStockMultiPOSingleDataSet.InitializeData;
begin
  inherited;
  AddFieldDef(REORDER_PROPOSAL_REQDT_NAME, ftDatetime);
  AddFieldDef(REORDER_PROPOSAL_DT_NAME, ftDatetime);
end;

procedure TStockMultiPOSingleDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  if not (ue is TPMAnalyseChangeFlags)
    and not (ue is TPMDataChangedEvent) then exit;

  if (PosAlg.GetCurrentStep is TBLRStockSaveMultiReorderPO) then
    AnalyseChanges(ue);
end;

procedure TStockMultiPOSingleDataSet.RefreshData;
var poStep : TBLRStockSaveMultiReorderPO;
begin
  inherited;
  if not (PosAlg.GetCurrentStep is TBLRStockSaveMultiReorderPO) then exit;
  Dataset.Edit;
  poStep := TBLRStockSaveMultiReorderPO(PosAlg.GetCurrentStep);
  Dataset.FieldByName(REORDER_PROPOSAL_DT_NAME).asDatetime := poStep.Docdatetime;
  Dataset.FieldByName(REORDER_PROPOSAL_REQDT_NAME).asDatetime := poStep.ReqDocdatetime;
  Dataset.Post;
end;

{ TTransferIngredientsInfoDataSet }

procedure TTransferIngredientsInfoDataSet.ChangeCurPos(NewPos: Integer;
  bKeepOldPositions: Boolean);
var msg  : TBLRMsgStockIngredient;
begin
  inherited;
  FPos := NewPos;
  msg  := TBLRMsgStockIngredient.Create;
  msg.id := StrToInt64Def(Dataset.fieldByName('id').asString,0);
  posalg.SendMessage(msg);
end;

constructor TTransferIngredientsInfoDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then begin
    mgr       := TStockManager.Create(nil, upos.UntillDB, ThreadObserverU.ThreadObserver.RefreshMainThread);
    UPos.Un.RegisterListener(URL_STOCK_ITEM, Self);
  end;
end;

destructor TTransferIngredientsInfoDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_ITEM, Self);
  FreeAndNil(mgr);
  inherited;
end;

class function TTransferIngredientsInfoDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Transfered stock ingredients','Dataset parameter');
end;

function TTransferIngredientsInfoDataSet.GetCurPos: Integer;
begin
  result := FPos;
end;

class function TTransferIngredientsInfoDataSet.GetFieldCaption(
  FieldName: String; DB: TUntillDb): WideString;
var fn: string;
begin
  fn := lowercase(FieldName);
  if fn = 'quantity' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Transfer quantity')
  else if fn = 'centralum' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location from U/M')
  else if fn = 'centralamount' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location from amount')
  else if fn = 'inventory_item' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Ingredient name')
  else if fn = 'amount' then
    Result := Plugin.Translate('RestaurantDataSetsU','Location to amount')
  else if FieldName = 'um' then
    Result := Plugin.Translate('RestaurantDataSetsU', 'Location to U/M')
end;

class function TTransferIngredientsInfoDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Transfered stock ingredients','Dataset parameter');
end;


procedure TTransferIngredientsInfoDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id';
  AddFieldDef('id', ftLargeint);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('centralamount', ftCurrency);
  AddFieldDef('centralum', ftWideString, 100);
  AddFieldDef('amount', ftCurrency);
  AddFieldDef('um', ftWideString, 100);
  AddFieldDef('quantity', ftCurrency);
end;

procedure TTransferIngredientsInfoDataSet.OnURLEvent(url: string;
  ue: TUrlEvent);
begin
  if (url=URL_STOCK_ITEM) then begin
    Refresh;
    SendPosChanged;
  end;
  inherited;
end;

procedure TTransferIngredientsInfoDataSet.RefreshData;
var iq: IIBSQL;
    str: String;
    id_ing : Int64;
    cum : Int64;
    qty : Double;
    price : Currency;
    i : Integer;
    itemList : TStringList;

    loc_amount,amount0, amount : Double;

    id_loc, id_cloc : Int64;
    round_up : Integer;
    wt  : IWTran;
begin
  Dataset.EmptyDataSet;
  if not (posalg.GetCurrentStep is BLRAlgStockTransfer) then exit;
  itemList := BLRAlgStockTransfer(posalg.GetCurrentStep).IngList;
  if not assigned(itemList) then exit;

  if itemList.Count=0 then exit;

  RefreshBalanceAmount;

  id_cloc   := BLRAlgStockTransfer(posalg.GetCurrentStep).LocationID;
  id_loc    := BLRAlgStockTransfer(posalg.GetCurrentStep).LocationTransferToID;
  wt        := FUntillDB.GetWTran;
    str := ' select b.id, b.name inventory_item, a.amount, d.name um, c.ID_UNITY_COUNTING, c.AVERAGE_AMOUNT, '
      + ' c.id id_ing_loc'
      + ' from inventory_item b '
      + ' inner join INVENTORY_ITEM_LOCATIONS c on c.ID_INVENTORY_ITEM=b.id and c.is_active=1 '
      + ' inner join unity d on b.id_unity = d.id and d.is_active=1 '
      + ' left outer join stock_balance a on a.id_inventory_item = b.id '
      + ' and a.ID_STOCK_LOCATIONS=c.ID_STOCKING_LOCATIONS and b.is_active=1 '
      + ' where b.id=:id_ing and c.ID_STOCKING_LOCATIONS=:id_loc and b.is_active=1 '
      + '   and exists(select * from INVENTORY_ITEM_LOCATIONS itl '
      + '   where itl.ID_INVENTORY_ITEM=b.id and itl.ID_STOCKING_LOCATIONS=:id_cloc and itl.is_active=1)';
    price   := 0;
    amount0 := 0;
    for i := 0 to Pred(itemList.count) do begin
      id_ing := StrToInt64Def(itemList.Names[i],0);
      if id_ing>0 then begin
        iq := FUntillDB.GetPreparedIIbSql(str);
        iq.Q.ParamByName('id_ing').asInt64  := id_ing;
        iq.Q.ParamByName('id_loc').asInt64  := BLRAlgStockTransfer(posalg.GetCurrentStep).LocationID;
        iq.Q.ParamByName('id_cloc').asInt64 := id_cloc;
        iq.ExecQuery;
        if not iq.eof then begin
          cum :=StrToInt64Def(iq.FieldByName('ID_UNITY_COUNTING').asString,0);
          if cum>0 then begin
            qty   := 0;
            if not CashInvPeriodValueDataset.GetNormalValue(StrToInt64Def(iq.FieldByName('id_ing_loc').asString,0),
                upos.GetPosNow, qty) then
              qty   := iq.FieldByName('AVERAGE_AMOUNT').asCurrency;
            mgr.ConvertUM(wt, sttCounting, cum, qty, price);
            DataSet.Append;
            DataSet.FieldByName(UNTILL_RECORD_GROUP_ID).asString := IntToStr(i);
            DataSet.FieldByName('id').asString := IntToStr(id_ing);
            DataSet.FieldByName('inventory_item').asString := iq.FieldByName('inventory_item').asString;

            amount := GetCacheInvLocStockbalance(id_ing, id_cloc);
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_cloc, amount0, cum, round_up);
            if cum>0 then begin
              loc_amount := amount;
              mgr.ConvertUM(wt, sttCounting, cum, loc_amount, price, false);
              DataSet.FieldByName('quantity').AsCurrency := StrToCurrDef(itemList.Values[itemList.Names[i]],0);
              mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
              DataSet.FieldByName('centralamount').asCurrency := amount;
              DataSet.FieldByName('centralum').asString := GetCacheUMCName(cum);
            end;
            amount := GetCacheInvLocStockbalance(id_ing, id_loc);
            BLRAlgStockBalanceU.GetInventoryItemOnLoc(id_ing, id_loc, amount0, cum, round_up);
            if cum>0 then begin
              mgr.ConvertUM(wt, sttCounting, cum, amount, price, false);
              DataSet.FieldByName('amount').AsFloat := amount;
              DataSet.FieldByName('um').asString    := GetCacheUMCName(cum);
            end;

            DataSet.Post;
          end;
        end;
      end;
    end;

    wt.commit;
end;

{ TSupplierItemsScreenDataSet }

class function TSupplierItemsScreenDataSet.AffectVisibility: Boolean;
begin
  result := true;
end;

procedure TSupplierItemsScreenDataSet.ButtonClick(Button: IDataSetButton);
var  msg :TBLRMsgStockSupplierItem;
begin
  inherited;
  msg := TBLRMsgStockSupplierItem.Create;
  msg.id := Button.GetButtonRecordId;
  posalg.SendMessage(msg);
end;

constructor TSupplierItemsScreenDataSet.Create(AOwner: TComponent;
  ANamedParams: TNamedParameters);
begin
  inherited;
  if assigned(UPos_) then
    UPos.Un.RegisterListener(URL_STOCK_SUPPLIER_ITEM, Self);
end;

destructor TSupplierItemsScreenDataSet.Destroy;
begin
  if assigned(UPos_) then
    UPos.Un.UnRegisterListener(URL_STOCK_SUPPLIER_ITEM, Self);
  inherited;
end;

function TSupplierItemsScreenDataSet.GetButtonExtraData(
  ObjectId: Int64): TExtraButtonContent;
begin
  Result.Info := GetUmName(ObjectId);
end;

class function TSupplierItemsScreenDataSet.GetCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock supplier items','Dataset parameter');
end;

procedure TSupplierItemsScreenDataSet.GetCurrentButtonContent(
  Content: TDataSetButtonContent);
begin
  inherited;
  with Content do begin
    Text := Dataset.FieldByName('supplier_item').AsString;
  end;
end;

class function TSupplierItemsScreenDataSet.GetExtra1Caption: String;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Supplier item U/M','Ingredients extra 1 caption');
end;

class function TSupplierItemsScreenDataSet.GetHint: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Stock supplier items','Dataset parameter');
end;

class function TSupplierItemsScreenDataSet.GetShortCaption: WideString;
begin
  result := Plugin.Translate('RestaurantDataSetsU', 'Sup.I.','Dataset parameter');
end;

function TSupplierItemsScreenDataSet.GetUmName(aid_supplier_item: Int64): string;
var idum : Int64;
begin
  result := '';
  idum := StockManagerU.GetIdUnityPurchase( upos.UntillDB, aid_supplier_item );
  if idum = 0 then exit;
  result :=  StockManagerU.GetUMCByConvId( upos.UntillDB, idum );
end;

function TSupplierItemsScreenDataSet.GetVisibility: Boolean;
begin
  result := ( posalg.GetCurrentStep is TBLRStockAddSupplierItems );
end;

procedure TSupplierItemsScreenDataSet.InitializeData;
begin
  inherited;
  IdFieldName := 'id_supplier_item';
  AddFieldDef('id_supplier_item', ftLargeint);
  AddFieldDef('inventory_item', ftWideString, 100);
  AddFieldDef('supplier_item', ftWideString, 100);
end;

procedure TSupplierItemsScreenDataSet.OnURLEvent(url: string; ue: TUrlEvent);
begin
  inherited;
  if url=URL_STOCK_SUPPLIER_ITEM then begin
    if not (posalg.GetCurrentStep is TBLRStockAddSupplierItems) then exit;
    refresh;
  end;
end;

procedure TSupplierItemsScreenDataSet.RefreshData;
var iq : IIBSQL;
    id_sup, id_ing : Int64;
begin
  inherited;

  if not (posalg.GetCurrentStep is TBLRStockAddSupplierItems) then exit;

  id_sup := TBLRStockAddSupplierItems(posalg.GetCurrentStep).id_sup ;
  id_ing := TBLRStockAddSupplierItems(posalg.GetCurrentStep).id_inv;
  if id_sup=0 or id_ing then exit;

  iq := upos.UntillDB.GetPreparedIIbSql('select supplier_item.id, INVENTORY_ITEM.name iname, '
    + ' supplier_item.DESCRIPTION sname'
    + ' from supplier_item '
    + ' join INVENTORY_ITEM on INVENTORY_ITEM.id=supplier_item.ID_INVENTORY_ITEM and INVENTORY_ITEM.is_active=1'
    + ' join SUPPLIERS on SUPPLIERS.id=supplier_item.ID_SUPPLIERS and SUPPLIERS.is_active=1'
    + ' where ID_INVENTORY_ITEM=:id_inv and ID_SUPPLIERS=:id_sup and supplier_item.is_active=1');
  iq.q.ParamByName('id_sup').AsInt64 := id_sup;
  iq.q.ParamByName('id_inv').AsInt64 := id_ing;
  iq.ExecQuery;
  while not iq.eof do begin
    Dataset.Append;
    Dataset.FieldByName('id_supplier_item').asString := iq.fieldByName('id').AsString;
    Dataset.FieldByName('inventory_item').asString   := iq.fieldByName('iname').AsString;
    Dataset.FieldByName('supplier_item').asString    := iq.fieldByName('sname').AsString;
    Dataset.Post;
    iq.next;
  end;
end;

{ TSalesCostBaseTicketDataSet }

procedure TSalesCostBaseTicketDataSet.FillAdjValues(dt1, dt2: TDatetime);
var query   : string;
    qq, qqd : IIBSQL;
    bm : TBookmark;
begin
  bm := Dataset.GetBookmark;
  try
    query := 'select id_inventory_item, Sum(quantity) qty, Sum(standart_price * quantity) price from stock_turnover '
      + ' where stock_turnover.sdatetime>=:from_dt and sdatetime<=:till_dt and stock_turnover.entity_type=1';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ' and stock_turnover.ID_STOCK_LOCATIONS=:loc';
    query := query + ' group by id_inventory_item';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ',stock_turnover.ID_STOCK_LOCATIONS';
    qq := FUntillDB.GetPreparedIIbSql(query);
    qq.Q.ParamByName('from_dt').AsDateTime := dt1;
    qq.Q.ParamByName('till_dt').AsDateTime := dt2;
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      qq.Q.ParamByName('loc').AsInt64 := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
    qq.ExecQuery;
    while not qq.eof do begin
      DataSet.Last;
      while not DataSet.bof do begin
        if StrToInt64Def(DataSet.FieldByName('id').asString, 0)
          = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0) then
        begin
          DataSet.edit;
          DataSet.FieldByName('adjusted_qty').AsCurrency   := qq.Q.FieldByName('qty').AsDouble;
          DataSet.FieldByName('adjusted_price').AsCurrency := qq.Q.FieldByName('price').AsDouble;

          if IsDetailed then begin
            query := 'select Sum(quantity) qty, Sum(standart_price * quantity) price, extra_type  from stock_turnover '
              + ' where stock_turnover.sdatetime>=:from_dt and sdatetime<=:till_dt and stock_turnover.entity_type=1 '
              + ' and id_inventory_item=:id_inventory_item';
            if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
              query := query + ' and stock_turnover.ID_STOCK_LOCATIONS=:loc';
            query := query + ' group by id_inventory_item, extra_type';
            if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
              query := query + ',stock_turnover.ID_STOCK_LOCATIONS';

            qqd := FUntillDB.GetPreparedIIbSql(query);
            qqd.Q.ParamByName('from_dt').AsDateTime := dt1;
            qqd.Q.ParamByName('till_dt').AsDateTime := dt2;
            qqd.Q.ParamByName('id_inventory_item').AsString := qq.Q.FieldByName('id_inventory_item').AsString;
            if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
              qqd.Q.ParamByName('loc').AsInt64 := TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
            qqd.ExecQuery;

            DataSet.FieldByName('adjusted_tr_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_tr_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_nr_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_nr_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_ec_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_ec_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_ch_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_ch_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_pm_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_pm_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_rw_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_rw_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_fw_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_fw_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_ot_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_ot_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_tl_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_tl_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_ph_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_ph_price').AsCurrency := 0;
            DataSet.FieldByName('adjusted_pe_qty').AsCurrency   := 0;
            DataSet.FieldByName('adjusted_pe_price').AsCurrency := 0;
            while not qqd.Eof do begin
              if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjTransferOut) then begin
                DataSet.FieldByName('adjusted_tr_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_tr_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjNonRetailSale) then begin
                DataSet.FieldByName('adjusted_nr_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_nr_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjEmpConsumption) then begin
                DataSet.FieldByName('adjusted_ec_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_ec_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjCharity) then begin
                DataSet.FieldByName('adjusted_ch_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_ch_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjPromotion) then begin
                DataSet.FieldByName('adjusted_pm_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_pm_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjRawWaste) then begin
                DataSet.FieldByName('adjusted_rw_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_rw_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjFinishedWaste) then begin
                DataSet.FieldByName('adjusted_fw_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_fw_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjOther) then begin
                DataSet.FieldByName('adjusted_ot_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_ot_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if TAdjustmentType(qqd.Q.fieldByName('extra_type').asInteger)in [adjTransferLoc, adjAutoTransferLoc] then begin
                DataSet.FieldByName('adjusted_tl_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_tl_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjPhisicalCount) then begin
                DataSet.FieldByName('adjusted_ph_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_ph_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end else if qqd.Q.fieldByName('extra_type').asInteger = Ord(adjPhysicalEntry) then begin
                DataSet.FieldByName('adjusted_pe_qty').AsCurrency   := (qqd.Q.FieldByName('qty').AsDouble);
                DataSet.FieldByName('adjusted_pe_price').AsCurrency := (qqd.Q.FieldByName('price').AsDouble);
              end;
              qqd.Next;
            end;
          end;

          DataSet.Post;
          break;
        end;
        DataSet.Prior;
      end;
      qq.Next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;

end;

procedure TSalesCostBaseTicketDataSet.FillPurchValues(dt1, dt2: TDatetime);
var query : string;
    qq    : IIBSQL;
    bm : TBookmark;
begin
  bm := Dataset.getBookmark;
  try
    query := 'select id_inventory_item, Sum(quantity) qty, Sum(standart_price * quantity) price from stock_turnover '
      + ' where stock_turnover.sdatetime>=:from_dt and sdatetime<=:till_dt and stock_turnover.entity_type=2';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ' and stock_turnover.ID_STOCK_LOCATIONS=:loc';
    query := query + ' group by id_inventory_item';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ',stock_turnover.ID_STOCK_LOCATIONS';
    qq := FUntillDB.GetPreparedIIbSql(query);
    qq.Q.ParamByName('from_dt').AsDateTime := dt1;
    qq.Q.ParamByName('till_dt').AsDateTime := dt2;
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      qq.Q.ParamByName('loc').AsInt64 :=
        TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
    qq.ExecQuery;
    while not qq.eof do
    begin
      DataSet.Last;
      while not DataSet.bof do begin
        if StrToInt64Def(DataSet.FieldByName('id').asString, 0)
          = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0) then
        begin
          DataSet.edit;
          DataSet.FieldByName('purchase_qty').AsCurrency := qq.Q.FieldByName('qty').AsDouble;
          DataSet.FieldByName('purchase_price').AsCurrency := qq.Q.FieldByName('price').AsDouble;
          DataSet.Post;
          break;
        end;
        DataSet.Prior;
      end;
      qq.Next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;
end;

procedure TSalesCostBaseTicketDataSet.FillOpenValues(id_od : Int64; dt1, dt2: TDatetime);
var query : string;
    qq    : IIBSQL;
    bm    : TBookmark;
begin
  bm := Dataset.GetBookmark;
  try
    query := GetOpendayQuery;
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ' and ID_STOCKING_LOCATIONS=:loc';
    query := query + ' group by id_inventory_item';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ',ID_STOCKING_LOCATIONS';
    qq := FUntillDB.GetPreparedIIbSql(query);
    qq.Q.ParamByName('ID').AsInt64 := id_od;
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      qq.Q.ParamByName('loc').AsInt64 :=
        TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
    qq.ExecQuery;
    while not qq.eof do
    begin
      DataSet.Last;
      while not DataSet.bof do begin
        if (StrToInt64Def(DataSet.FieldByName('id').asString, 0) = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0)) then
        begin
          DataSet.edit;
          DataSet.FieldByName('open_stock_qty').AsCurrency   := qq.Q.fields[0].AsDouble;
          DataSet.FieldByName('open_stock_price').AsCurrency := 0;
          if qq.Q.fieldByName('onhand').AsCurrency<>0 then
            DataSet.FieldByName('open_stock_price').AsCurrency := qq.Q.fields[1].AsDouble / qq.Q.fieldByName('onhand').AsCurrency *  qq.Q.fields[0].AsCurrency;
          DataSet.Post;
          break;
        end;
        DataSet.Prior;
      end;
      qq.Next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;
end;

procedure TSalesCostBaseTicketDataSet.FillSoldValues(dt1, dt2: TDatetime);
var query : string;
    qq    : IIBSQL;
    bm : TBookmark;
begin
  bm := Dataset.getBookmark;
  try
    query := 'select id_inventory_item, Sum(quantity) qty, Sum(standart_price * quantity) price, '
      + ' Sum(abs(sales_price * quantity)) s_price from stock_turnover '
      + ' where stock_turnover.sdatetime between :from_dt and :till_dt and stock_turnover.entity_type=0';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ' and stock_turnover.ID_STOCK_LOCATIONS=:loc';
    query := query + ' group by id_inventory_item';
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      query := query + ',stock_turnover.ID_STOCK_LOCATIONS';
    qq := FUntillDB.GetPreparedIIbSql(query);
    qq.Q.ParamByName('from_dt').AsDateTime := dt1;
    qq.Q.ParamByName('till_dt').AsDateTime := dt2;
    if FNamedParams.Params[TStockLocationDataSetParam.Key] is TInt64WS then
      qq.Q.ParamByName('loc').AsInt64 :=
        TInt64WS(FNamedParams.Params[TStockLocationDataSetParam.Key]).Int64Value;
    qq.ExecQuery;
    while not qq.eof do begin
      DataSet.Last;
      while not DataSet.bof do begin
        if StrToInt64Def(DataSet.FieldByName('id').asString, 0)
          = StrToInt64Def(qq.Q.FieldByName('id_inventory_item').asString, 0) then
        begin
          DataSet.edit;
          DataSet.FieldByName('sold_qty').AsCurrency    := qq.Q.FieldByName('qty').AsDouble;
          DataSet.FieldByName('stock_price').AsCurrency := abs(qq.Q.FieldByName('price').AsDouble);
          DataSet.FieldByName('sold_price').AsCurrency  := qq.Q.FieldByName('s_price').AsDouble;
          DataSet.Post;
          break;
        end;
        DataSet.Prior;
      end;
      qq.Next;
    end;
  finally
    Dataset.GotoBookmark(bm);
    Dataset.FreeBookmark(bm);
  end;
end;

class function TSalesCostBaseTicketDataSet.GetFieldCaption(FieldName: String;
  DB: TUntillDB): WideString;
begin
  if Uppercase(FieldName) = Uppercase('adjusted_tr_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment transfer out amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_tr_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment transfer out value')
  else if Uppercase(FieldName) = Uppercase('adjusted_nr_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment non retail amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_nr_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment non retail value')
  else if Uppercase(FieldName) = Uppercase('adjusted_ec_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment employee consumption amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_ec_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment employee consumption value')
  else if Uppercase(FieldName) = Uppercase('adjusted_ch_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment charity amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_ch_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment charity value')
  else if Uppercase(FieldName) = Uppercase('adjusted_pm_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment promotion amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_pm_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment promotion value')
  else if Uppercase(FieldName) = Uppercase('adjusted_rw_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment raw waste amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_rw_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment raw waste value')
  else if Uppercase(FieldName) = Uppercase('adjusted_fw_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment finished waste amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_fw_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment finished waste value')
  else if Uppercase(FieldName) = Uppercase('adjusted_ot_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment other amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_ot_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment other value')
  else if Uppercase(FieldName) = Uppercase('adjusted_tl_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment transfer location amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_tl_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment transfer location value')
  else if Uppercase(FieldName) = Uppercase('adjusted_ph_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment physical count amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_ph_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment physical count value')
  else if Uppercase(FieldName) = Uppercase('adjusted_pe_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment physical entry amount')
  else if Uppercase(FieldName) = Uppercase('adjusted_pe_price') then
    result := Plugin.Translate('StockDatasetsU', 'Adjustment physical entry value')
  else if Uppercase(FieldName) = Uppercase('open_stock_qty') then
    result := Plugin.Translate('StockDatasetsU', 'Opening stock amount')
  else if Uppercase(FieldName) = Uppercase('open_stock_price') then
    result := Plugin.Translate('StockDatasetsU', 'Opening stock value')
  else
    result := inherited GetFieldCaption(FieldName, DB);
end;

procedure TSalesCostBaseTicketDataSet.InitializeData;
begin
  inherited;
  AddFieldDef('open_stock_qty', ftCurrency);
  AddFieldDef('open_stock_price', ftCurrency);
  AddFieldDef('purchase_qty', ftCurrency);
  AddFieldDef('purchase_price', ftCurrency);
  AddFieldDef('adjusted_qty', ftCurrency);
  AddFieldDef('adjusted_price', ftCurrency);
  AddFieldDef('sold_qty', ftCurrency);
  AddFieldDef('sold_price', ftCurrency);
  AddFieldDef('stock_price', ftCurrency);
  if IsDetailed then begin

//        adjTransferOut, adjNonRetailSale,adjEmpConsumption, adjCharity, adjPromotion, adjRawWaste,
//    adjFinishedWaste, adjOther, adjTransferLoc, adjPhisicalCount, adjAutoTransferLoc, adjPhysicalEntry

    AddFieldDef('adjusted_tr_qty', ftCurrency);
    AddFieldDef('adjusted_tr_price', ftCurrency);
    AddFieldDef('adjusted_nr_qty', ftCurrency);
    AddFieldDef('adjusted_nr_price', ftCurrency);
    AddFieldDef('adjusted_ec_qty', ftCurrency);
    AddFieldDef('adjusted_ec_price', ftCurrency);
    AddFieldDef('adjusted_ch_qty', ftCurrency);
    AddFieldDef('adjusted_ch_price', ftCurrency);
    AddFieldDef('adjusted_pm_qty', ftCurrency);
    AddFieldDef('adjusted_pm_price', ftCurrency);
    AddFieldDef('adjusted_rw_qty', ftCurrency);
    AddFieldDef('adjusted_rw_price', ftCurrency);
    AddFieldDef('adjusted_fw_qty', ftCurrency);
    AddFieldDef('adjusted_fw_price', ftCurrency);
    AddFieldDef('adjusted_ot_qty', ftCurrency);
    AddFieldDef('adjusted_ot_price', ftCurrency);
    AddFieldDef('adjusted_tl_qty', ftCurrency);
    AddFieldDef('adjusted_tl_price', ftCurrency);
    AddFieldDef('adjusted_ph_qty', ftCurrency);
    AddFieldDef('adjusted_ph_price', ftCurrency);
    AddFieldDef('adjusted_pe_qty', ftCurrency);
    AddFieldDef('adjusted_pe_price', ftCurrency);
  end;

end;

class function TSalesCostBaseTicketDataSet.IsDetailed: boolean;
begin
  result := false;
end;

initialization

//
// ****** Screen Datasets **************************************************************
//
UntillApp.RegInfoDataSets.RegisterDataSets([TIngredientsInfoDataSet, TReorderInfoDataSet,
  TStockPOIngSingleDataSet, TInvoiceItemsInfoDataSet, TPOModItemsInfoDataSet,
  TPONewItemsInfoDataSet, TSupplierContactInfoDataSet, TPOInfoDataSet,TPOItemsInfoDataSet,
  TTransferIngredientsInfoDataSet ]);

UntillApp.RegScrDataSets.RegisterDataSets([TStockLocationsScreenDataSet,
  TIngredientsScreenDataSet, TStockSingleDataSet, TIngredientsInfoDataSet,
  TRecipeScreenDataSet, TIngCatScreenDataSet, TEditIngredientsScreenDataSet,
  TStockIngSingleDataSet, TStockCommonSingleDataSet, TCountUMCScreenDataSet,
  TPurchaseUMCScreenDataSet, TSuppliersScreenDataSet, TPOScreenDataSet,
  TReorderInfoDataSet, TStockPOIngSingleDataSet, TStockPOSingleDataSet,TStockMultiPOSingleDataSet,
  TStockInvoiceSingleDataSet, TInvoiceItemsInfoDataSet, TPOModItemsInfoDataSet,
  TPOOrderedScreenDataSet, TPONewItemsInfoDataSet, TStockSupplierSingleDataSet,
  TStockSupplierContactSingleDataSet, TSupplierContactInfoDataSet,
  TPOInfoDataSet, TPOItemsInfoDataSet, TIngredientsPhysCountScreenDataSet,
  TTransferIngredientsInfoDataSet, TSupplierItemsScreenDataSet]);

UntillApp.RegTicDataSets.RegisterDataSets([TStockBalanceTicketDataSet,
  TStockInvoicesTicketDataSet, TStockTurnoverTicketDataSet,
  TPurchaseOrderTicketDataSet, TStockAdjustmentTicketDataSet,
  TStockInvoicesItemTicketDataSet, TStockAdjustmentItemTicketDataSet,
  TPurchaseOrderItemTicketDataSet, TPhysicalCountTicketDataSet,
  TStockSupplierTicketDataSet, TRecipeCostTicketDataSet, TReorderTicketDataSet,
  TSalesAndCostTicketDataSet, TStockIngredientTicketDataSet,
  TStockInitCountTicketDataSet, TRecipeItemsTicketDataSet,
  TCountSheetTicketDataSet, TIngredentsSoldTicketDataSet,
  TSalesAndCostIngredentsTicketDataSet, TStockTurnoverSummaryTicketDataSet,
  TInitInvoiceItemsTicketDataSet, TTurnoverHistoryTicketDataSet,
  TStockArticlesTicketDataSet, TIngReNormalTicketDataSet ]);

TStockReportsContext.Create(UntillApp.RegTicDataSets);

end.
