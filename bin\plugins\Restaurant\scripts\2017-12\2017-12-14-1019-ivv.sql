CREATE OR ALTER VIEW VIEW_TURNOVER_INCL_ROOM
AS
SELECT * FROM RDB$DATABASE; -- full view definition moved to 2022-11-30-1800-kvn-remove-ib_udf.sql
commit;
grant all on VIEW_TURNOVER_INCL_ROOM to untilluser;
commit;


CREATE OR ALTER VIEW VIEW_TURNOVER_EXCL_ROOM
AS
SELECT * FROM RDB$DATABASE; -- full view definition moved to 2022-11-30-1800-kvn-remove-ib_udf.sql
commit;
grant all on VIEW_TURNOVER_EXCL_ROOM to untilluser;
commit;

SET TERM ^ ;

create or alter procedure GET_END_OF_DAY_TURNOVER2
as
begin
  -- full procedure definition moved to 2022-11-30-1800-kvn-remove-ib_udf.sql
end^

SET TERM ; ^
GRANT EXECUTE ON PROCEDURE GET_END_OF_DAY_TURNOVER2 TO UNTILLUSER;
