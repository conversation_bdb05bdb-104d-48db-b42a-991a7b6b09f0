unit TestIvvU ;

interface
uses BLRMainU, BLAlgGlobalU, POSAlgU, DateUtils;

procedure TestIvv(s :String);

implementation

uses SysUtils, Classes, Dialogs, BLRPrintU, profilerU, BLRBillU,UntillPosU;

procedure TestIvv(s :String);
var msg :TBLMsgAliasTicket;
    man : TMixedOrderManager;
    tempBill : TBLRBill;
begin
  if Sametext(s,'1') then begin
    msg := TBLMsgAliasTicket.Create;
    msg.Name := 'bill';
    msg.NewName := 'bill1';
  end else if Sametext(s,'2') then begin
    man := nil;
    tempBill := nil;
    try
      man := TMixedOrderManager.Create;
      tempBill := TBLRBill.Create(nil, Upos.UntillDB);
      tempBill.SetClients(blr.ActiveBill.id_clients);
      tempBill.SetCourses(nil, blr.ActiveBill.id_courses);
      tempBill.NumberOfCovers := blr.ActiveBill.NumberOfCovers;
      tempBill.order_name := blr.ActiveBill.order_name;
      tempBill.CurrentChairNumber := blr.ActiveBill.CurrentChairNumber;
      tempBill.CurrentChairName := blr.ActiveBill.CurrentChairName;
      tempBill.group_vat_level := blr.ActiveBill.group_vat_level;
      tempBill.SalesArea:= blr.ActiveBill.SalesArea;
      tempBill.id_bill := blr.ActiveBill.id_bill;

      blr.ActiveBill.items.last;
      while not blr.ActiveBill.items.bof do begin
        if blr.ActiveBill.items.untill_record_line_id.asInteger=1 then begin
          blr.ActiveBill.items.next;
          break;
        end;
        blr.ActiveBill.items.prior;
      end;
      while not blr.ActiveBill.items.eof do begin
        if blr.ActiveBill.items.rowbeg.asInteger=1 then
          tempBill.items.AppendRow(blr.ActiveBill.items);
        blr.ActiveBill.items.next;
      end;

      Blr.ActiveBill.LoadFromActiveBill(nil, 0, 'a', 0);
    finally
      FreeAndNil(tempBill);
      FreeAndNil(man);
    end;
  end;
end;

end.
