alter table ACCOUNTS_PAYMENTS add total decimal(17,4);
commit;
DROP TRIGGER ACC_PITEMS_INS_UPD_TRIG;

SET TERM ^ ;

CREATE OR ALTER TRIGGER ACC_PAYMENTS_INS_TRIG FOR ACCOUNTS_PAYMENTS
ACTIVE AFTER INSERT POSITION 0
as
declare id_accounts bigint;
declare common_acc integer;
declare cnt integer;
declare clnt bigint;
begin
  if ((deleting) or (updating)) then exit;

  select common_accounts,id_accounts from clients where id = new.id_clients  into :common_acc, :id_accounts;
  if ((:common_acc<>0) and (:id_accounts>0)) then begin
    select count(*) from account_balance_common where id_accounts = :id_accounts into :cnt;
    
    if (:cnt = 0) then
      insert into account_balance_common (id_accounts, amount) values (:id_accounts, -new.total);
    else
      update account_balance_common set amount=amount- new.total where id_accounts = :id_accounts;
   end else begin
    select count(*) from account_balance where id_clients = new.id_clients into :cnt;

    if (:cnt = 0) then
      insert into account_balance (id_clients, amount) values (:clnt, -new.total);
    else
      update account_balance set amount=amount - new.total where id_clients = new.id_clients;
   end
end
^

SET TERM ; ^
commit;

