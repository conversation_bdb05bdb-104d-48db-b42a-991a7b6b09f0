SET TERM ^ ;

create or alter procedure GETORDERITEMOSIG (
    ID_ORDER_ITEM bigint,
    ID_ORDERS bigint)
returns (
    OSIG varchar(1024))
as
declare variable IDX integer;
declare variable ROWBEG integer;
declare variable KIND char(1);
declare variable ID_ARTICLES varchar(20);
declare variable TEXT varchar(50);
begin
  idx = 0;
  osig = '';
  for
  select order_item.rowbeg, cast(order_item.kind as char(1)), 
         cast(coalesce(order_item.id_articles,0) as varchar(20)), order_item.text
  from order_item where order_item.id_orders=:ID_ORDERS and order_item.id >= :id_order_item
  into
       :rowbeg, :kind, :id_articles, :text
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || kind;
    if (id_articles='0' and :text<>'') then begin
      osig = osig || text;
    end else begin
      osig = osig || id_articles;
    end
    idx = idx + 1;
  end
  suspend;
end^

SET TERM ; ^

commit;
