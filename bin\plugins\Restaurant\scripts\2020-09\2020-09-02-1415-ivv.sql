SET TERM ^ ;

create or alter procedure UPDATE_POS_DEP_POSITION(id_sa bigint)
as
declare variable ID_DEP bigint;
declare variable APOS integer;
begin
  if (id_sa=0 ) then exit;
  delete from dep_position_ex where ID_SALES_AREA=:id_sa;
        apos = 0;
        for
            SELECT department.id from department
            join department_AVAILABLE on department_AVAILABLE.ID_department=department.id and department_AVAILABLE.id_sales_area=:id_sa and department_AVAILABLE.IS_ACTIVE=1
            where department.is_active=1
            order by department.dep_number into :id_dep do begin
                insert into dep_position_ex(id_sales_area, pos, ID_department)
                    values(:id_sa, :apos, :ID_dep);
                apos = apos + 1;
            end
end
^

create or alter procedure UPDATE_POS_DEP_POSITION_ALL
as
declare variable id_sa bigint;
declare variable ID_DEP bigint;
declare variable APOS integer;
begin
  delete from dep_position_ex;
  for
    SELECT sales_area.id from sales_area where sales_area.is_active=1 into :id_sa do begin
        apos = 0;
        for
            SELECT department.id from department
            join department_AVAILABLE on department_AVAILABLE.ID_department=department.id and department_AVAILABLE.id_sales_area=:id_sa and department_AVAILABLE.IS_ACTIVE=1
            where department.is_active=1 
            order by department.dep_number into :id_dep do begin
                insert into dep_position_ex(id_sales_area, pos, ID_department)
                    values(:id_sa, :apos, :ID_dep);
                apos = apos + 1;
            end
    end
end
^
SET TERM ; ^

grant execute on procedure UPDATE_POS_DEP_POSITION to untilluser;
grant execute on procedure UPDATE_POS_DEP_POSITION_all to untilluser;
