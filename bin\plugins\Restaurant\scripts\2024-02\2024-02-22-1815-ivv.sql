CREATE OR ALTER VIEW VIEW_TURNOVER_INCL_ROOM(
    ARTICLE_ID,
    ARTICLE_NAME,
    ARTICLE_NUMBER,
    DEPARTMENT_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_NUMBER,
    GROUP_ID,
    GROUP_NAME,
    GROUP_GR_NUMBER,
    SALES_AREA_ID,
    SALES_AREA_NAME,
    SALES_AREA_NUMBER,
    PRICE,
    VAT,
    QUANTITY,
    VAT_SIGN,
    VAT_PERCENT,
    CLOSE_DATETIME)
AS
select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sum(sa.quantity) quantity,
      oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef = 1.00
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, coalesce(parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sa.quantity, oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef < 1.00
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, sa.quantity,
          coalesce(sa.parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
        sum(sign(pbi.quantity) * pbi.price) amount,sum(sign(pbi.quantity) * pbi.vat) vat,sum(pbi.quantity),pbi.vat_sign, pbi.vat_percent,pbill.pdatetime close_datetime
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null  and (bill.close_datetime is not null)
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles a on a.id = pbi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        left outer join sales_area s on s.id = pbill.id_sales_area
        group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
           pbi.vat_sign, pbi.vat_percent,pbill.pdatetime ,bill.id;

CREATE OR ALTER VIEW VIEW_TURNOVER_EXCL_ROOM(
    ARTICLE_ID,
    ARTICLE_NAME,
    ARTICLE_NUMBER,
    DEPARTMENT_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_NUMBER,
    GROUP_ID,
    GROUP_NAME,
    GROUP_GR_NUMBER,
    SALES_AREA_ID,
    SALES_AREA_NAME,
    SALES_AREA_NUMBER,
    PRICE,
    VAT,
    QUANTITY,
    VAT_SIGN,
    VAT_PERCENT,
    CLOSE_DATETIME)
AS
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sum(sa.quantity) quantity,
      oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef = 1.00 and not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime,bill.id, coalesce(parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sa.quantity, oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef < 1.00 and not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, sa.quantity,
          coalesce(sa.parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
        sum(sign(pbi.quantity) * pbi.price) amount,sum(sign(pbi.quantity) * pbi.vat) vat,sum(pbi.quantity),pbi.vat_sign, pbi.vat_percent,pbill.pdatetime close_datetime
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null  and (bill.close_datetime is not null)
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles a on a.id = pbi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        left outer join sales_area s on s.id = pbill.id_sales_area
        where not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
        group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
           pbi.vat_sign, pbi.vat_percent,pbill.pdatetime ,bill.id;

COMMIT;
