create table eft_config_selections (
    id u_id,
    name varchar(100),
    id_payments u_id,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint eft_config_selections_pk primary key (id),
    constraint eft_config_selections_fk1 foreign key (id_payments) references payments(id)
);
commit;
grant all on eft_config_selections to untilluser;
commit;
execute procedure register_sync_table_ex('eft_config_selections', 'b', 1);
commit;
execute procedure register_bo_table('eft_config_selections', '', '');
commit;


create table eft_config_sel_items (
    id u_id,
    id_eft_config_selections u_id, 
    name varchar(100),
    eft_params varchar(1024),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint eft_config_sel_items_pk primary key (id),
    constraint eft_config_sel_items_fk1 foreign key (id_eft_config_selections) references eft_config_selections(id)
);
commit;
grant all on eft_config_sel_items to untilluser;
commit;
execute procedure register_sync_table_ex('eft_config_sel_items', 'b', 1);
commit;
execute procedure register_bo_table('eft_config_sel_items', 'id_eft_config_selections', 'eft_config_selections');
commit;

create table eft_config_sel_avail (
    id u_id,
    id_eft_config_selections u_id, 
    id_computers bigint,
    id_falcon_terminals bigint, 
    id_ordermans bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint eft_config_sel_avail_pk primary key (id),
    constraint eft_config_sel_avail_fk1 foreign key (id_eft_config_selections) references eft_config_selections(id),
    constraint eft_config_sel_avail_fk2 foreign key (id_computers) references computers(id),
    constraint eft_config_sel_avail_fk3 foreign key (id_falcon_terminals) references falcon_terminals(id),
    constraint eft_config_sel_avail_fk4 foreign key (id_ordermans) references ordermans(id)
);
commit;
grant all on eft_config_sel_avail to untilluser;
commit;
execute procedure register_sync_table_ex('eft_config_sel_avail', 'b', 1);
commit;
execute procedure register_bo_table('eft_config_sel_avail', 'id_eft_config_selections', 'eft_config_selections');
commit;
