CREATE OR ALTER VIEW OI_ONHOLD(
    ID_ORDER_ITEM,
    ONHOLD,
    datetime,
    end_datetime)
AS
select order_item.id id_order_item, coalesce(onhold,0) onhold, order_item_onhold.datetime, end_datetime
from order_item 
join order_item_onhold on order_item_onhold.id_order_item=order_item.id and
order_item_onhold.datetime= (select Max(datetime) from order_item_onhold oh where oh.id_order_item =order_item.id);
commit;
grant all on OI_ONHOLD to untilluser;
commit;
