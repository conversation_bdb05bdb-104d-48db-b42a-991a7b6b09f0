unit NeedClientByNameParamsFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ActionParamsFram, UntillSelectBoxU, StdCtrls, TntCompatibilityU,
  ExtCtrls, UntillPanelU, TicketEntityManager, UntillDBU, BOUntillDBU,
  ClientEntityManager, UTF8U;

type
  TNeedClientByNameParamsFrame = class(TActionParamsFrame)
    UntillPanel1: TUntillPanel;
    lblName: TTntLabel;
    usbClient: TUntillSelectBox;
    procedure usbClientButtonClick(Sender: TObject);
  private
    { Private declarations }
    emClient : TClientEntityManager;
  public
    procedure SetParameters(Parameters: array of Variant); override;
    procedure GetParameters(var Parameters: array of Variant); override;
    procedure TranslateStrings; override;

    constructor Create(AOwner: TComponent; AActionObject:TObject; ANameManager:TObject; AUntillDB:TBOCustomUntillDB); override;
    destructor Destroy; override;
  end;

var
  NeedClientByNameParamsFrame: TNeedClientByNameParamsFrame;

implementation
uses RestaurantPluginU, DataControlsU;

{$R *.dfm}
{ TNeedClientByNameParamsFrame }

constructor TNeedClientByNameParamsFrame.Create(AOwner: TComponent;
  AActionObject, ANameManager: TObject; AUntillDB: TBOCustomUntillDB);
begin
  inherited;
  emClient := TClientEntityManager.Create(Self,UntillDB);
end;

destructor TNeedClientByNameParamsFrame.Destroy;
begin
  FreeAndNil(emClient);
  inherited;
end;

procedure TNeedClientByNameParamsFrame.GetParameters(var Parameters: array of Variant);
begin
  inherited;
  Parameters[0]:=usbClient.text;
end;

procedure TNeedClientByNameParamsFrame.SetParameters(Parameters: array of Variant);
var q: IIBSQL;
begin
  inherited;
  if length(Parameters)>0 then begin
    q := UntillDB.GetIIbSql;
    q.SetText('select ID from clients where name = :name and coalesce(client_type,0)=0');
    q.q.ParamByName('name').asString := UTF8_Encode(Parameters[0]);
    q.ExecQuery;
    if not q.Eof then
      FillUsb(usbClient, StrToInt64Def(q.q.fields[0].asstring,0), emClient, 'name');
    q.Close;
  end;
end;

procedure TNeedClientByNameParamsFrame.TranslateStrings;
begin
  inherited;
  lblName.Caption:=Plugin.TranslateLabel('NeedClientByNameParamsFram','Client name');
end;

procedure TNeedClientByNameParamsFrame.usbClientButtonClick(
  Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emClient.ShowModal(usbClient.Value);
  if res>0 then with usbClient do begin
    Value:=res;
    Text:=emClient.GetWideStringById('name',res);
  end;
  usbClient.SetFocus;
end;

end.
