unit ColorFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ActionParamsFram, StdCtrls, TntCompatibilityU,UntillEquU,
  ExtCtrls, UntillPanelU, DialogsU, UntillComboBoxU, IBSQL,
  UntillDBU, UntillCheckBoxU, ColorComboU, UntillScreenU, UntillGraphicElementsU;

type
  TColorFrame = class(TActionParamsFrame)
    pnlColor: TUntillPanel;
    lblColor: TTntLabel;
    cbColor: TTntColorComboBox;
    pnlRMColor: TUntillPanel;
    lblRMColor: TTntLabel;
    cbRMColor: TTntColorRMComboBox;
    procedure cbRMColorExit(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure GetParameters(var Parameters: array of Variant); override;
    procedure SetParameters(Parameters: array of Variant); override;
    procedure CanSave(Sender: TObject); override;
    procedure TranslateStrings; override;
    destructor Destroy; override;
  end;

var
  ColorFrame: TColorFrame;

implementation
uses NameManagerU, InterpretatorU, PluginU;
{$R *.dfm}

{ TTestActionFrame }

procedure TColorFrame.CanSave(Sender: TObject);
begin
  inherited;
end;

procedure TColorFrame.cbRMColorExit(Sender: TObject);
begin
  inherited;
  plugin.SaveCustomColors( cbRMColor );
end;

destructor TColorFrame.Destroy;
begin
  inherited;
end;

procedure TColorFrame.GetParameters(var Parameters: array of Variant);
begin
  inherited;
  Parameters[0] := WideString(ColorToString(cbColor.Selected))
end;

procedure TColorFrame.SetParameters(Parameters: array of Variant);
begin
  inherited;
  try
    if Parameters[0] = '' then
      cbColor.Selected := 0
    else
      cbColor.Selected := StringToColor(Parameters[0]);
  except
    cbColor.Selected := 0;
  end;
end;

procedure TColorFrame.TranslateStrings;
var El     : TObject;
begin
  inherited;
  El := self.ActionObject;
  if not (El is TUntillGraphicElement) then exit;
  pnlColor.Visible := True;
  pnlRMColor.Visible := False;
  lblColor.Caption := Plugin.TranslateLabel('ColorFram','Color');
  lblRMColor.Caption := Plugin.TranslateLabel('ColorFram','Color');
  plugin.GetCustomColors;
  plugin.InitCustomColors( TUntillGraphicElement(el).GetDBGuid, cbColor );
end;

end.
