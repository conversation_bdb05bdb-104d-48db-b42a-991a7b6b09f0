create table art_free_opt_defaults (
    id u_id,
    item_number int,
    ID_ARTICLES_FREE_OPTIONS bigint,
    id_option_article bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint art_free_opt_defaults_pk primary key (id),
	constraint art_free_opt_defaults_fk1 foreign key (ID_ARTICLES_FREE_OPTIONS) references ARTICLES_FREE_OPTIONS(id),
	constraint art_free_opt_defaults_fk2 foreign key (id_option_article) references option_article(id)
);
commit;
grant all on art_free_opt_defaults to untilluser;
commit;
execute procedure register_sync_table_ex('art_free_opt_defaults', 'b', 1);
commit;
execute procedure register_bo_table('art_free_opt_defaults', 'ID_ARTICLES_FREE_OPTIONS', 'ARTICLES_FREE_OPTIONS');
commit;
execute procedure register_bo_table('art_free_opt_defaults', 'id_option_article', 'option_article');
commit;





