#pragma description Untill Database Tasks Extended Structures with Examples
#pragma author Augment Agent  
#pragma version 1.0

// Расширенные структуры для задач базы данных Untill
// Включает дополнительные типы данных и вспомогательные структуры

#include "untill_db_tasks_structures.hexpat"

// ========== Дополнительные базовые типы ==========
using Word = u16;
using Cardinal = u32;
using Int64 = s64;
using Single = float;
using Double = double;

// ========== Структуры для работы с потоками ==========
// Заголовок потока с размером
struct StreamHeader {
    Integer size;
    u8 data[size];
} [[format("format_stream")]];

fn format_stream(StreamHeader stream) {
    return std::format("Stream: {} bytes", stream.size);
};

// ========== Константы версий потоков ==========
// Из исходного кода найдены следующие константы версий:
enum StreamVersions : SmallInt {
    TOOLTASK_STREAM_VERSION = 2,
    TOOLCLEARTASK_STREAM_VERSION = 1,
    COMPACT_STREAM_VERSION = 4,  // Из CompactDBTaskParamsFrame
    RESYNC_STREAM_VERSION = 4,   // Из ResyncTaskParamFram
    BUILD_OMAN_STREAM_VERSION = 1 // Из BuildOmanResTaskParamsFram
};

// ========== Расширенная структура TCompactDBTask ==========
// Полная структура с всеми возможными полями
struct TCompactDBTaskParamsExtended {
    StreamVersions version;
    Boolean onlyBackup;
    Boolean repair;
    
    if (version >= 1) {
        Integer days;
    }
    
    if (version >= 2) {
        Boolean dontReboot;
    }
    
    if (version >= 3) {
        WideString backupPath;
    }
    
    if (version >= 4) {
        Boolean skipAggr;
        WideString sysdbaPwd;
    }
} [[format("format_compact_extended")]];

fn format_compact_extended(TCompactDBTaskParamsExtended params) {
    str result = std::format("CompactDB Extended v{}\n", params.version);
    result += std::format("  Only Backup: {}\n", params.onlyBackup ? "Yes" : "No");
    result += std::format("  Repair: {}\n", params.repair ? "Yes" : "No");
    
    if (params.version >= 1) {
        result += std::format("  Days: {}\n", params.days);
    }
    
    if (params.version >= 2) {
        result += std::format("  Don't Reboot: {}\n", params.dontReboot ? "Yes" : "No");
    }
    
    if (params.version >= 3) {
        result += std::format("  Backup Path: {}\n", params.backupPath.data);
    }
    
    if (params.version >= 4) {
        result += std::format("  Skip Aggregates: {}\n", params.skipAggr ? "Yes" : "No");
    }
    
    return result;
};

// ========== Структура для TTool4DBTask ==========
// Задача инструментов базы данных
struct TTool4DBTaskParams {
    Integer mode;
    // Дополнительные параметры могут следовать в зависимости от режима
} [[format("format_tool4_task")]];

fn format_tool4_task(TTool4DBTaskParams params) {
    return std::format("Tool4DB: mode={}", params.mode);
};

// ========== Структура для TQueryDBTask ==========
// Задача выполнения запросов
struct TQueryDBTaskParams {
    SmallInt version;
    WideString query;
    // Могут быть дополнительные параметры
} [[format("format_query_task")]];

fn format_query_task(TQueryDBTaskParams params) {
    return std::format("QueryDB v{}: {}", params.version, params.query.data);
};

// ========== Универсальный парсер задач ==========
// Структура для автоматического определения и разбора любой задачи
struct UniversalDBTaskParser {
    SmallInt version;
    
    // Определяем тип задачи по версии и следующим байтам
    if (version >= 1 && version <= 10) {
        // Пытаемся определить тип по паттерну данных
        u8 type_detector[4];
        
        // Возвращаемся к началу после версии
        $ = $ - 4;
        
        // Выбираем подходящую структуру
        match (version) {
            (1...4): TCompactDBTaskParamsExtended compactTask;
            (1...3): TExportDBTaskParams exportTask;
            (1...2): TEndOfDayTaskParams eodTask;
            (1): TAggregatesRecalculationTaskParams aggregatesTask;
        }
    }
} [[format("format_universal_parser")]];

fn format_universal_parser(UniversalDBTaskParser parser) {
    return std::format("Universal Parser v{}", parser.version);
};

// ========== Структуры для отладки ==========
// Hex dump для неизвестных данных
struct HexDump {
    u8 data[64]; // Первые 64 байта для анализа
} [[format("format_hex_dump")]];

fn format_hex_dump(HexDump dump) {
    str result = "Hex Dump:\n";
    for (u32 i = 0, i < 64, i += 16) {
        result += std::format("{:04X}: ", i);
        for (u32 j = 0, j < 16 && (i + j) < 64, j += 1) {
            result += std::format("{:02X} ", dump.data[i + j]);
        }
        result += "\n";
    }
    return result;
};

// ========== Примеры использования ==========
// Раскомментируйте нужную структуру для анализа ваших данных:

// Для анализа параметров задачи компактирования:
// TCompactDBTaskParamsExtended compactTaskData @ 0x00;

// Для анализа параметров задачи экспорта:
// TExportDBTaskParams exportTaskData @ 0x00;

// Для анализа параметров задачи закрытия дня:
// TEndOfDayTaskParams eodTaskData @ 0x00;

// Для универсального анализа:
// UniversalDBTaskParser universalData @ 0x00;

// Для отладки неизвестных данных:
// HexDump debugData @ 0x00;

// По умолчанию используем универсальный парсер
UniversalDBTaskParser mainData @ 0x00;
