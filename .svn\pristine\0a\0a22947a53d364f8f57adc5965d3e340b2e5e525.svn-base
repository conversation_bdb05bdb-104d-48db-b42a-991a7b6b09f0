[Class]
Name=TBLRMsgSetKNumbers
TimeStamp=23.03.2017 14:54
Interval=0.000247256939474028;30.12.1899 0:00:21
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=4
bill=231
deposit=1
order=1414
tr=271
[Start]
**************************************************
TimeStamp=20.04.2017 11:47
Interval=0;30.12.1899
[Class]
Name=TBLMessageKeyboardString
TimeStamp=20.04.2017 11:47
Interval=1.28819447127171E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=20.04.2017 11:47
Interval=1.04513892438263E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=20.04.2017 11:47
Interval=2.54629412665963E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:47
Interval=7.26852158550173E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:47
Interval=4.40971780335531E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=0
[Class]
Name=TBLRMsgArticle
TimeStamp=20.04.2017 11:47
Interval=1.21643533930182E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000139
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=20.04.2017 11:47
Interval=1.22800920507871E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 10     <USER> <GROUP>           16,00     1,60   2,78   13,22 21,0%   5,00  8,22    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgSplitBill
TimeStamp=20.04.2017 11:47
Interval=1.52662032633089E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
OpenType=potSimple
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:47
Interval=8.71528027346358E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
s=2
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=20.04.2017 11:47
Interval=4.47916681878269E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=20.04.2017 11:47
Interval=7.80092523200437E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
StateData=
>>>TBillsHandlingPayment
<?xml version="1.0"?>
<driverCall xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><request><BillClosingRequest type="NS1:BillClosingRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-billshandling</posId><bill type="NS1:Bill"><billNumber type="NS2:string">231</billNumber><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><openTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</openTime><orderItems type="NS3:Array" NS3:arrayType="NS1:OrderItem[1]"><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">2</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">1.6</singlePrice><singleVatAmount type="NS2:decimal">0.2777</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">3.2</totalPrice><totalQuantity type="NS2:int">2</totalQuantity><totalVatAmount type="NS2:decimal">0.2777</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></orderItems><paymentItems type="NS3:Array" NS3:arrayType="NS1:PaymentItem[1]"><item type="NS1:PaymentItem"><amount type="NS2:decimal">3.2</amount><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><customerAmount type="NS2:decimal">3.2</customerAmount><paymentId type="NS2:long">5000001501</paymentId><paymentKind type="NS2:int">0</paymentKind><paymentName type="NS2:string">Cash</paymentName><paymentNumber type="NS2:int">1</paymentNumber><paymentThirdPartyId type="NS2:string"></paymentThirdPartyId></item></paymentItems><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a</tablePart><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">371</transactionId><transactionNumber type="NS2:string">271</transactionNumber><waiterId type="NS2:long">5000000102</waiterId><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></bill><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><partialBill type="NS2:boolean">true</partialBill></BillClosingRequest></request></driverCall>

<<<TBillsHandlingPayment
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 2      <USER> <GROUP>            3,20     1,60   0,56    2,64 21,0%   1,00  1,64    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        3,20        3,20        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Start]
**************************************************
TimeStamp=20.04.2017 11:48
Interval=0.000666909720166586;30.12.1899 0:00:57
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:48
Interval=9.15509735932574E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
s=2
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=20.04.2017 11:48
Interval=4.98842564411461E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLRMsgGetEqualPartNumber
TimeStamp=20.04.2017 11:48
Interval=5.9027734096162E-6;30.12.1899
Exception=
StateGUID={141AB2AD-3DC7-4883-B9BD-4050590D2097}
StateName=TBLSGetNumberValue
FieldCount=0
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:48
Interval=6.84028054820374E-6;30.12.1899
Exception=
StateGUID={141AB2AD-3DC7-4883-B9BD-4050590D2097}
StateName=TBLSGetNumberValue
FieldCount=1
s=3
[Class]
Name=TBLMessageOk
TimeStamp=20.04.2017 11:48
Interval=4.29397914558649E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=0
[Class]
Name=TBLRMsgSplitBillEqual
TimeStamp=20.04.2017 11:48
Interval=0;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=3
PartCount=3
PartsID=0
SplitType=potSimple
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=20.04.2017 11:48
Interval=6.37731864117086E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=1
TransferType=attSimple
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=20.04.2017 11:48
Interval=2.18981440411881E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
StateData=
>>>TBillsHandlingPayment
<?xml version="1.0"?>
<driverCall xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><request><BillClosingRequest type="NS1:BillClosingRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-billshandling</posId><bill type="NS1:Bill"><billNumber type="NS2:string">232</billNumber><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><openTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</openTime><orderItems type="NS3:Array" NS3:arrayType="NS1:OrderItem[1]"><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">2</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">0.54</singlePrice><singleVatAmount type="NS2:decimal">0.0937</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">1.08</totalPrice><totalQuantity type="NS2:int">2</totalQuantity><totalVatAmount type="NS2:decimal">0.0937</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></orderItems><paymentItems type="NS3:Array" NS3:arrayType="NS1:PaymentItem[1]"><item type="NS1:PaymentItem"><amount type="NS2:decimal">1.08</amount><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><customerAmount type="NS2:decimal">1.08</customerAmount><paymentId type="NS2:long">5000001501</paymentId><paymentKind type="NS2:int">0</paymentKind><paymentName type="NS2:string">Cash</paymentName><paymentNumber type="NS2:int">1</paymentNumber><paymentThirdPartyId type="NS2:string"></paymentThirdPartyId></item></paymentItems><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a</tablePart><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">371</transactionId><transactionNumber type="NS2:string">271</transactionNumber><waiterId type="NS2:long">5000000102</waiterId><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></bill><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><partialBill type="NS2:boolean">true</partialBill></BillClosingRequest></request></driverCall>

<<<TBillsHandlingPayment
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 0,68   Coca Cola            1,08     1,60   0,19    0,89 21,0%   0,11  0,78    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        1,08        1,08        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:49
Interval=0.000469317128590774;30.12.1899 0:00:40
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
s=3
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=20.04.2017 11:49
Interval=1.22800920507871E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=20.04.2017 11:49
Interval=5.91435673413798E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=1
TransferType=attSimple
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=20.04.2017 11:49
Interval=1.49537008837797E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
StateData=
>>>TBillsHandlingPayment
<?xml version="1.0"?>
<driverCall xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><request><BillClosingRequest type="NS1:BillClosingRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-billshandling</posId><bill type="NS1:Bill"><billNumber type="NS2:string">233</billNumber><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><openTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</openTime><orderItems type="NS3:Array" NS3:arrayType="NS1:OrderItem[2]"><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">2</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">0.54</singlePrice><singleVatAmount type="NS2:decimal">0.0937</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">1.08</totalPrice><totalQuantity type="NS2:int">2</totalQuantity><totalVatAmount type="NS2:decimal">0.0937</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">3</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">1.6</singlePrice><singleVatAmount type="NS2:decimal">0.2777</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">4.8</totalPrice><totalQuantity type="NS2:int">3</totalQuantity><totalVatAmount type="NS2:decimal">0.2777</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></orderItems><paymentItems type="NS3:Array" NS3:arrayType="NS1:PaymentItem[1]"><item type="NS1:PaymentItem"><amount type="NS2:decimal">5.88</amount><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><customerAmount type="NS2:decimal">5.88</customerAmount><paymentId type="NS2:long">5000001501</paymentId><paymentKind type="NS2:int">0</paymentKind><paymentName type="NS2:string">Cash</paymentName><paymentNumber type="NS2:int">1</paymentNumber><paymentThirdPartyId type="NS2:string"></paymentThirdPartyId></item></paymentItems><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a</tablePart><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">371</transactionId><transactionNumber type="NS2:string">271</transactionNumber><waiterId type="NS2:long">5000000102</waiterId><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></bill><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><partialBill type="NS2:boolean">true</partialBill></BillClosingRequest></request></driverCall>

<<<TBillsHandlingPayment
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 3      <USER> <GROUP>            4,80     1,60   0,83    3,97 21,0%   1,50  2,47    
 0,68   Coca Cola            1,08     1,60   0,19    0,89 21,0%   0,11  0,78    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        5,88        5,88        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageCancel
TimeStamp=20.04.2017 11:49
Interval=0.000541631947271526;30.12.1899 0:00:46
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=20.04.2017 11:49
Interval=6.37731136521325E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=20.04.2017 11:49
Interval=2.31484591495246E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=20.04.2017 11:49
Interval=7.08333391230553E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLMessageInput
TimeStamp=20.04.2017 11:49
Interval=2.03935196623206E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
s=2
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=20.04.2017 11:49
Interval=7.3958290158771E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageOk
TimeStamp=20.04.2017 11:49
Interval=1.67013931786641E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>TBillsHandlingPayment
<?xml version="1.0"?>
<driverCall xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><request><BillClosingRequest type="NS1:BillClosingRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-billshandling</posId><bill type="NS1:Bill"><billNumber type="NS2:string">234</billNumber><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><openTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</openTime><orderItems type="NS3:Array" NS3:arrayType="NS1:OrderItem[1]"><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">5</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">0.4</singlePrice><singleVatAmount type="NS2:decimal">0.0694</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">2</totalPrice><totalQuantity type="NS2:int">5</totalQuantity><totalVatAmount type="NS2:decimal">0.0694</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></orderItems><paymentItems type="NS3:Array" NS3:arrayType="NS1:PaymentItem[1]"><item type="NS1:PaymentItem"><amount type="NS2:decimal">2</amount><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><customerAmount type="NS2:decimal">2</customerAmount><paymentId type="NS2:long">5000001501</paymentId><paymentKind type="NS2:int">0</paymentKind><paymentName type="NS2:string">Cash</paymentName><paymentNumber type="NS2:int">1</paymentNumber><paymentThirdPartyId type="NS2:string"></paymentThirdPartyId></item></paymentItems><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a</tablePart><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">371</transactionId><transactionNumber type="NS2:string">271</transactionNumber><waiterId type="NS2:long">5000000102</waiterId><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></bill><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><partialBill type="NS2:boolean">true</partialBill></BillClosingRequest></request></driverCall>

<<<TBillsHandlingPayment
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 0,75   Coca Cola            1,20     1,60   0,21    0,99 21,0%   0,38  0,62    
 0,50   Coca Cola            0,80     1,60   0,14    0,66 21,0%   0,08  0,58    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        2,00        2,00        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=20.04.2017 11:50
Interval=0.000348564812156837;30.12.1899 0:00:30
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TBillsHandlingPayment
<?xml version="1.0"?>
<driverCall xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><request><BillClosingRequest type="NS1:BillClosingRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-billshandling</posId><bill type="NS1:Bill"><billNumber type="NS2:string">235</billNumber><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><openTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</openTime><orderItems type="NS3:Array" NS3:arrayType="NS1:OrderItem[1]"><item type="NS1:OrderItem"><articleHqId type="NS2:string"></articleHqId><articleId type="NS2:long">5000000139</articleId><articleName type="NS2:string">Coca Cola</articleName><articleNumber type="NS2:int">1</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentId type="NS2:long">5000000120</departmentId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAllowed type="NS2:boolean">true</discountAllowed><discountAmount type="NS2:decimal">0</discountAmount><groupId type="NS2:long">5000000091</groupId><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><menu type="NS2:boolean">false</menu><prepaidItemId type="NS2:string"></prepaidItemId><quantity type="NS2:int">5</quantity><signature type="NS2:string">a4afb022366d06a98c4198e6ada285bc</signature><singlePrice type="NS2:decimal">0.768</singlePrice><singleVatAmount type="NS2:decimal">0.1333</singleVatAmount><sizeModifierId type="NS2:long">0</sizeModifierId><sizeModifierName type="NS2:string"></sizeModifierName><totalPrice type="NS2:decimal">3.84</totalPrice><totalQuantity type="NS2:int">5</totalQuantity><totalVatAmount type="NS2:decimal">0.1333</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></orderItems><paymentItems type="NS3:Array" NS3:arrayType="NS1:PaymentItem[1]"><item type="NS1:PaymentItem"><amount type="NS2:decimal">3.84</amount><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><customerAmount type="NS2:decimal">3.84</customerAmount><paymentId type="NS2:long">5000001501</paymentId><paymentKind type="NS2:int">0</paymentKind><paymentName type="NS2:string">Cash</paymentName><paymentNumber type="NS2:int">1</paymentNumber><paymentThirdPartyId type="NS2:string"></paymentThirdPartyId></item></paymentItems><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a</tablePart><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">371</transactionId><transactionNumber type="NS2:string">271</transactionNumber><waiterId type="NS2:long">5000000102</waiterId><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></bill><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><partialBill type="NS2:boolean">true</partialBill></BillClosingRequest></request></driverCall>

<<<TBillsHandlingPayment
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 1,44   Coca Cola            2,30     1,60   0,40    1,90 21,0%   0,72  1,18    
 0,96   Coca Cola            1,54     1,60   0,27    1,27 21,0%   0,16  1,12    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        2,00        2,00        0,00                                        
Cash        3,84        3,84        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=20.04.2017 11:51
Interval=0.000449050923634786;30.12.1899 0:00:38
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=20.04.2017 11:51
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=20.04.2017 11:51
Interval=2.21412046812475E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
