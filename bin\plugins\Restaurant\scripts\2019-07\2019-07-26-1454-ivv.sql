create table article_sm_price_periods(
    id u_id,
    id_ARTICLE_PRICE_PERIODS 	bigint,
    id_size_modifier_item	bigint,
    price			decimal(17,4),	
    is_active 			smallint,
    IS_ACTIVE_MODIFIED 		timestamp,
    IS_ACTIVE_MODIFIER 		varchar(30),
    constraint article_sm_price_periods_pk primary key (id),
    constraint article_sm_price_periods_fk1 foreign key (id_ARTICLE_PRICE_PERIODS) references ARTICLE_PRICE_PERIODS(id),
    constraint article_sm_price_periods_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on article_sm_price_periods to untilluser;
commit;
execute procedure register_sync_table_ex('article_sm_price_periods', 'b', 1);
commit;
execute procedure register_bo_table('article_sm_price_periods', 'id_ARTICLE_PRICE_PERIODS', 'ARTICLE_PRICE_PERIODS');
commit;
execute procedure register_bo_table('article_sm_price_periods', 'id_size_modifier_item', 'size_modifier_item');
commit;
