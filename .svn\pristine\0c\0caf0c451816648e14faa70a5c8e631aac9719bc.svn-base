unit BLRClientU;

interface
uses classes, SysUtils, BLAlgU, BLInputNumberU, UrlNotifierU,UntillPluginU,BLRAlgU,
     StrUtils, UntillDBU, BLSysU, NamedVarsU, ClassesU, RestaurantPluginU,CommonU,
     Dialogs, UntillDataSetsU, Generics.Collections;

const URL_CLIENT_CARDS : string = 'URL_CLIENT_CARDS';
const CLIENT_CODE_LENGTH : Integer = 50;
type

  TBLRMsgCityMessage = class(TBLMessageID);
  TBLRMsgEmailInvoice = class(TBLMessage);
  TBLRMsgCardMessage = class(TBLMessageID)
  private
    Fctype: integer;
    procedure Setctype(const Value: integer);
  published
    property ctype: integer read Fctype write Setctype; //0 - card, 1 - smartcard
  end;
  TBLRMsgClientCardName= class(TBLMessageID);
  TBLRMsgNeedAddClientCard = class(TBLMessage);
  TBLRMsgNeedModifyClientCard= class(TBLMessage);

  TClientCard = class
    card_id       : Int64;
    card_type     : Integer;
    id_card_name  : Int64;
    card_number   : string;
  private
    function Equal( acc : TClientCard ): boolean;
  end;

  TClientCards = class(TObjectList<TClientCard>)
  private
    function AddNotExist( acc :TClientCard ) : boolean;
  end;

  TBLRModifyClient = class ( TBLAlgStep, IURLListener )
  private
    fid_card : Int64;
    fcard_type: Integer; //0 - card; 1 - smartcard
    fcards        : TClientCards;
    procedure ClearClientInfo;
    procedure DeleteClientCard;
    procedure SaveCardData;
  protected
    bNewClient   : Boolean;
    OlbClientId  : String;
    bInitialized : Boolean;
  public
    property cards  : TClientCards read fcards;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;

    procedure OnChildFinished(child: TBLAlgStep); override;
    procedure OnInput(s: WideString); override;

    procedure OnCancel; override;
    procedure OnOK; override;
    procedure OnUrlEvent(url: String; ue: TUrlEvent);

    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep; NewClient :Boolean); reintroduce;
    destructor Destroy(); override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override;
    procedure ProcessTagReaderString(msg: TBLMessageTagReaderString); override;
  end;

  TBLRAddCard = class ( TBLAlgStep )
  private
     fcard_id      : Int64;
     fcard_number  : string;
     fid_card_name : Int64;
     fcardtype     : integer;
  public
    property card_number  : string read fcard_number;
    property id_card_name : Int64 read fid_card_name;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure OnInput(s: WideString); override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep; aid_card : Int64); reintroduce;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override;
    procedure ProcessTagReaderString(msg: TBLMessageTagReaderString); override;
    function GetParentModifyClient : TBLRModifyClient;
    procedure OnOK; override;
  end;

  TBLRModifyClientCard = class (TBLRModifyClient)
  public
    class function GetStaticGuid: string; override;
    procedure OnChildFinished(child: TBLAlgStep); override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
  end;

  TBLRSelectClient = class ( TBLAlgStep )
  private
    OldClientId : String;
    CardPriceId: Int64;
    FZeroClientOnCancel : boolean;
    procedure DoApplyFilter;
    procedure RefreshButtons;
    procedure AssignClient;
    procedure SetClientByName(clientName : WideString);
    procedure SaveClientInvoiceEmail(email : string);
  public
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    procedure OnChildFinished(child: TBLAlgStep); override;

    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep; aZeroClientOnCancel : boolean = true); reintroduce;
    procedure OnInput(s: WideString); override;
    procedure OnCancel; override;
    procedure OnOK; override;
    procedure OnUndo; override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override;
    procedure ProcessTagReaderString(msg: TBLMessageTagReaderString); override;
  end;

  TBLRSelectClientParam = class(TBLRSelectRestItem)
  protected
    class function DatasetParamClass : TUntillDbItemsListDatasetParamClass; override;
  public
    class function GetStaticGuid: string; override;
    function    GetItemName(aid_art : Int64): String; override;
    function    GetHint: widestring; override;
    function    CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure OnInput(s: WideString); override;
    procedure   ProcessMessage(msg: TBLMessage); override;
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep); override;
  end;

function DoSendInvoice(aemail: string) : Int64;
function IsClientDateField(aFieldName : string) : boolean;
function IsClientTimeField(aFieldName : string) : boolean;
function GetCardNumber(aid_card : Int64) : string;
function GetCardName(aid_card_name : Int64) : string;

implementation
uses BLRMainU, BlrBillTypesU, BLRPrintU, UntillPosU, POSAlgU, UntillAppU,
  ClientDataProviderU, AccessControlU, DateUtils, SystemDataProviderU,
  UntillLogsU, ClientsU, BLRAlgPaymentU, RestaurantSingleDSU, BLRInvoiceU,
  BLRAlgSplitBillU, RestaurantDataSetsU, RestaurantDatasetParamsU, ZipCodeU,
  BLRTakeAwayU, ClientEmailU, CleanCashU, LegalTicketsU, InvoicePrintToolU,
  UBLU, UBLProxyU, ExtraFieldsU, DataControlsU, BLRBillUtilsU, KernelCashDatasetsU;

procedure SendUpdateCientCards;
var ue : TURLEvent;
begin
  ue := TURLEvent.Create(0,'', uetUpdate);
  try
    Upos.un.SendEvent(URL_CLIENT_CARDS, ue);
  finally
    FreeAndnil(ue);
  end;
end;

function GetCardName(aid_card_name : Int64) : string;
var iq : IIBSQL;
begin
  result := '';
  if aid_card_name=0 then exit;

  iq := upos.Untilldb.GetPreparedIIbSql('select CARD_NAME from CLIENT_CARD_NAMES where id=:id');
  iq.q.Params[0].asInt64 := aid_card_name;
  iq.ExecQuery;
  if not iq.Eof then
    result := iq.fields[0].asString;
end;

function GetCardNumber(aid_card : Int64) : string;
var iq : IIBSQL;
begin
  result := '';
  if aid_card=0 then exit;

  iq := upos.Untilldb.GetPreparedIIbSql('select card_number from client_cards where id=:id');
  iq.q.Params[0].asInt64 := aid_card;
  iq.ExecQuery;
  if not iq.Eof then
    result := iq.fields[0].asString;
end;
{ TBLRModifyClient }

function TBLRModifyClient.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := True;
  if msg = TBLMessageDelete then exit;
  if msg = TBLRMsgNeedModifyClientCard then exit;
  if msg = TBLRMsgNeedAddClientCard then exit;
  if msg = TBLRMsgCardMessage then exit;
  if msg = TBLRMsgCityMessage then exit;
  if msg = TBLRMsgSA then exit;
  if msg = TBLRMsgNeedSA then exit;
  if msg = TBLMessageInput then Exit;
  if msg = TBLMessageBackSpace then exit;
  if msg = TBLMessageClear then exit;
  if msg = TBLMessageUndo then exit;
  if msg = TBLMessageOK then Exit;
  if msg = TBLMessageCancel then Exit;
  if msg = TBLSMsgChangeTimeRange then Exit;
  if msg = TBLRMsgPaymentMode then Exit;
  if msg = TBLMsgCountry then Exit;

  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRModifyClient.Create(ABla: TBLAlg; AParent: TBLAlgStep; NewClient :Boolean);
var
  evCheck  : TPMAnalyseChangeFlags;
begin
  fid_card := 0;
  fcard_type := 0;
  ClientDataProvider.CheckLicensed;
  inherited Create(ABla, AParent);
  fcards := TClientCards.Create;
  bInitialized := False;
  bNewClient := NewClient;
  OlbClientId := ClientDataProvider.ClientID;
  evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
  try
    UPos.Un.SendEvent(FOCUS_OFF, evCheck);
  finally
    evCheck.Free;
  end;
  upos.un.RegisterListener(URL_POS_GENERAL, Self);
end;

destructor TBLRModifyClient.Destroy;
begin
  upos.un.UnRegisterListener(URL_POS_GENERAL, Self);
  FreeAndNil(fcards);
  inherited;
end;

function TBLRModifyClient.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Modify client', 'State hint');
end;

class function TBLRModifyClient.GetStaticGuid: string;
begin
  result := '{18AEE40E-9C89-4dcd-BEEA-B4244C3D5029}';
end;

function IsClientDateField(aFieldName: string): boolean;
var id : Int64;
begin
  id := StrToInt64Def(RightStr(aFieldName, Length(aFieldName) - 9),0);

  result :=  SameText(aFieldName, 'last_account_date')
    or SameText(aFieldName, 'date_creation')
    or SameText(aFieldName, 'date_expiration')
    or SameText(aFieldName, 'birthday')
    or ((pos('extrafld_', aFieldName) > 0) and (GetCacheExtraFieldType( id ) in [edtDate]));
end;

function IsClientTimeField(aFieldName: string): boolean;
var id : Int64;
begin
  id := StrToInt64Def(RightStr(aFieldName, Length(aFieldName) - 9),0);
  result := ((pos('extrafld_', aFieldName) > 0) and (GetCacheExtraFieldType(  id ) in [edtTime]));
end;

procedure TBLRModifyClient.OnCancel;
begin
  ClearClientInfo;
  inherited;
end;

procedure TBLRModifyClient.ClearClientInfo;
begin
  ClientDataProvider.ClientID := OlbClientId;
  BLAlgU.ActiveDatasetName := EmptyStr;
  BLAlgU.ActiveFieldName  := EmptyStr;
  ClientInfoProvider.CancelClient;
  Untillapp.AnalyseChangeFlags;
end;

procedure TBLRModifyClient.OnChildFinished(child: TBLAlgStep);
begin
  inherited;
  Self.FreeOnLastChild := true;

  if ( child is TBLRAddCard ) then begin
    Self.FreeOnLastChild := false;
    exit;
  end;
  if ( child is TBLSShowMessage ) then begin
      if TBLSShowMessage(child).Result = blOK then
        ClientInfoProvider.SaveClient(classname='TBLRModifyClient')
      else begin
        ClearClientInfo;
        BLAlgU.ActiveDatasetName := 'TClientSingleDataset';
        BLAlgU.ActiveFieldName   := 'card';
        bNewClient := true;
        posalg.SendInput(' ');
      end;
      Self.FreeOnLastChild := false;
    exit;
  end;

  if ( child is TBLRGetDepositAmount) then begin
    Self.FreeOnLastChild := false;
    exit;
  end;

  if ( child is TBLRSelectSA ) then begin
    Self.FreeOnLastChild := false;
    exit;
  end;

  if ( child is TBLSCalendar )  then begin
    if (child.Result = blOk) then begin
      if IsClientDateField( ActiveFieldName ) then
        ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, DateToStr(TBLSCalendar(child).SelectedDate ))
    end;
    Self.FreeOnLastChild := false;
    exit;
  end;

end;

procedure TBLRModifyClient.OnInput(s: WideString);
var
  ss : WideString;
  c1: Currency;
  i1: Integer;
  s1: String;
begin
  if upos.UserId = GUEST_DEFAULT_USER_ID then exit;
  if not SameText(BLAlgU.ActiveDatasetName,TClientSingleDataset.ClassName) then exit;
  if SameText(BLAlgU.ActiveFieldName,EmptyStr) then exit;
  ss := ClientInfoProvider.GetFieldValue(BLAlgU.ActiveFieldName);
  if SameText(BLAlgU.ActiveFieldName, 'limit') or
    SameText(BLAlgU.ActiveFieldName, 'deposit') then begin
    c1 := StrToCurrDef(ss, 0);
    i1 := Trunc(c1);
    if (SameText(s, '-')) then begin
      ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, IntToStr(-i1));
    end else begin
      s1 := IntToStr(i1);
      if (StrToIntDef(s, MAXINT)<>MAXINT) and (StrToIntDef(s1 + s, MAXINT)<>MAXINT) then
        ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, s1 + s);
    end;
  end else if IsClientDateField( ActiveFieldName ) then
        // nothing
  else if IsClientTimeField( ActiveFieldName ) then
        // nothing
  else begin
    if SameText(BLAlgU.ActiveFieldName, 'card') then begin
      if POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength>0 then
        if Length(ss + s)> POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength then
          plugin.RaisePosException('Card number length cannot exceed  %d symbols',[POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength], 'TBLRModifyClient.ProcessKeybordString(');
    end;
    if SameText(BLAlgU.ActiveFieldName, 'code') then begin
        if Length(ss + s)> CLIENT_CODE_LENGTH then
          plugin.RaisePosException('Code length cannot exceed  %d symbols',[CLIENT_CODE_LENGTH], 'TBLRModifyClient.ProcessKeybordString(');
    end;
    ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, ss + s);
  end;
end;

procedure TBLRModifyClient.OnOK;
begin
  ClientInfoProvider.SaveClient(self.classtype = TBLRModifyClient);
  if StrToInt64Def(ClientDataProvider.ClientID,0) > 0 then begin // for new clients
    SaveCardData;
    SendUpdateCientCards;
  end;
  inherited;
end;

procedure TBLRModifyClient.OnUrlEvent(url: String; ue: TUrlEvent);
var msgClick : TBLMsgClickLabel;
    evCheck  : TPMAnalyseChangeFlags;
begin
  if (ue is TPMAnalyseChangeFlags) and not bInitialized then begin
    bInitialized := true;
    msgClick := TBLMsgClickLabel.Create;
    msgClick.DatasetName := TClientSingleDataSet.GetName;
    if self.classname = TBLRModifyClient.Classname then
      msgClick.FieldName   := 'name'
    else
      msgClick.FieldName   := 'card';
    msgClick.InCheck     := False;
    PosAlg.SendMessage(msgClick);
    evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
    try
      UPos.Un.SendEvent(FOCUS_ON, evCheck);
    finally
      evCheck.Free;
    end;
  end;
end;

procedure TBLRModifyClient.ProcessKeybordString(msg: TBLMessageKeyboardString);
var cid : Int64;
    cm  : TBLRMsgClient;
    s   : String;
    evCheck : TPMAnalyseChangeFlags;
begin
  if (SameText(BLAlgU.ActiveDatasetName,'TClientSingleDataset') and SameText(BLAlgU.ActiveFieldName,'card')) then begin
    if upos.UserId = GUEST_DEFAULT_USER_ID then exit;

    if bNewClient then begin
      cid:=GetClientByCard(upos.UntillDB, msg.s);
      if cid > 0 then begin
        TBLRSelectClient.Create(Self.bla, Self);
        cm:=TBLRMsgClient.Create;
        cm.id:=cid;
        posalg.SendMessage(cm);
        posalg.SendOK;
      end else begin
        // DSP Stade de Mons new client cards https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=335454
        if POSRestaurantSettings.Settings.BlockNewClient then exit;

        if POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength>0 then
          if Length(msg.s)> POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength then
             plugin.RaisePosException('Card number length cannot exceed  %d symbols',[POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength], 'TBLRModifyClient.ProcessKeybordString(');

        ClientInfoProvider.SetFieldValue('date_creation', DateTimeToStr(upos.GetPosNow));
        ClientInfoProvider.SetFieldValue('date_expiration', DateTimeToStr(IncYear(upos.GetPosNow,POSRestaurantSettings.Settings.ExtraSettings.CardYears)));
        ClientInfoProvider.SetFieldValue('card', msg.s);
        s := plugin.Translate('RestaurantPluginU','This is new card do you want to create?');
        TBLSShowMessage.Create(posalg, Self, s, mtConfirmation);
      end;
      bNewClient := false;
    end else
      ClientInfoProvider.SetFieldValue('card', msg.s);

    evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
    try
      UPos.Un.SendEvent(FOCUS_OFF, evCheck);
    finally
      evCheck.Free;
    end;
    BLAlgU.ActiveDatasetName :='TClientSingleDataset';
    BLAlgU.ActiveFieldName   :='deposit';
    evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
    try
      UPos.Un.SendEvent(FOCUS_ON, evCheck);
    finally
      evCheck.Free;
    end;
  end else
    inherited;
end;

procedure TBLRModifyClient.DeleteClientCard;
var q   : IIBSQL;
    wt  : IWTran;
    i   : Integer;
    cc  : TClientCard;
begin
  if fid_card = 0 then exit;
  wt := upos.UntillDB.getWTran;

  if fcard_type = 0 then
    q := wt.GetPreparedIIbSql('update client_cards set is_active=0, is_active_modifier=:user, is_active_modified=:dt where id=:id ')
  else
    q := wt.GetPreparedIIbSql('update CLIENT_SMARTCARD_UIDS set is_active=0, is_active_modifier=:user, is_active_modified=:dt where id=:id ');
  q.q.Params[2].AsInt64 := fid_card;
  q.q.Params[0].AsString := LeftStr(upos.UserNameInfo.Username, 30);
  q.q.Params[1].AsDateTime := upos.GetPOSNow;
  q.ExecQuery;

  for i := 0 to Pred(cards.Count) do begin
    cc := cards[i];
    if (cc.id_card_name = fid_card) or (cc.card_id = fid_card) then begin
      cards.Delete(i);
      SendUpdateCientCards;
      exit;
    end;
  end;

  wt.Commit;
  SendUpdateCientCards;
end;

procedure TBLRModifyClient.SaveCardData;
var q, qSel   : IIBSQL;
    wt  : IWTran;
    qq  : TIBSQLU;
    i   : Integer;
    cc  : TClientCard;
begin
  if fcards.Count = 0 then exit;

  wt := upos.UntillDB.getWTran;
  for i := 0 to pred(fcards.count) do begin
    cc := fcards.Items[i];
    if cc.card_type = 1 then begin // smartcard
      qq := wt.GetTempSql;
      qq.SQL.Text := 'insert into client_smartcard_uids (id_clients, smartcard_uid, is_active) values (:id_clients, :smartcard_uid, 1)';
      qq.Params[0].AsString := ClientDataProvider.ClientID;
      qq.Params[1].AsString := cc.card_number;
      qq.ExecQuery;
    end else begin // normal card
      qSel := wt.GetPreparedIIbSql('select clients.name '
        + ' from client_cards '
        + ' join clients on clients.id=client_cards.id_clients and clients.is_active=1'
        + ' where upper(client_cards.card_number)=upper(:card_number) and client_cards.is_active=1');
      qSel.q.ParamByName('card_number').AsString := cc.card_number;
      qSel.ExecQuery;
      if not qSel.eof then begin
        plugin.RaisePosException('Client "%s" with same card number already exists',[qSel.fields[0].asString]);
        exit;
      end;

      if cc.card_id > 0 then  begin
        q := wt.GetPreparedIIbSql('update client_cards '
          + ' set ID_CLIENT_CARDS_NAME=:ID_CLIENT_CARDS_NAME, '
          + ' card_number=:card_number where id=:id ');
        q.q.ParamByName('id').AsInt64 := cc.card_id;
        q.q.ParamByName('card_number').AsString := cc.card_number;
        if cc.id_card_name = 0 then
          q.q.ParamByName('ID_CLIENT_CARDS_NAME').Clear
        else
          q.q.ParamByName('ID_CLIENT_CARDS_NAME').AsInt64 := cc.id_card_name;
        q.ExecQuery;
      end else begin
        if trim(cc.card_number)='' then exit;

        q := wt.GetPreparedIIbSql('insert into client_cards(item_number, ID_CLIENTS, card_number, ID_CLIENT_CARDS_NAME, is_active) '
           + ' values(:item_number, :ID_CLIENTS, :card_number, :ID_CLIENT_CARDS_NAME, 1) ');
        q.q.ParamByName('item_number').AsInt64 := GetFirstAvailNumberDb(upos.UntillDB, 'client_cards', 'item_number',
          'id_clients=' + ClientDataProvider.ClientID );
        q.q.ParamByName('ID_CLIENTS').AsString  := ClientDataProvider.ClientID;
        q.q.ParamByName('card_number').AsString := cc.card_number;
        if cc.id_card_name = 0 then
          q.q.ParamByName('ID_CLIENT_CARDS_NAME').Clear
        else
          q.q.ParamByName('ID_CLIENT_CARDS_NAME').AsInt64 := cc.id_card_name;
        q.ExecQuery;
      end;
    end; // normal card
  end;
  wt.Commit;
end;


procedure TBLRModifyClient.ProcessMessage(msg: TBLMessage);
var ss : WideString;
    c1: Currency;
    i1: Integer;
    s1: String;
    ue : TPMAnalyseChangeDatasetFlags;
    dt : TDatetime;
    id : Int64;
    msgPayment : TBLRMsgPaymentMode;
    DepositValue : Currency;
    strDT : string;
begin
  inherited;

  if msg is TBLMessageDelete then begin
    DeleteClientCard;
    exit;
  end;
  if msg is TBLRMsgNeedModifyClientCard then begin
    if fcard_type=0 then
      TBLRAddCard.Create(Self.bla, Self, fid_card);
    exit;
  end;
  if msg is TBLRMsgNeedAddClientCard then begin
    TBLRAddCard.Create(Self.bla, Self, 0);
    exit;
  end;
  if msg is TBLRMsgCardMessage then begin
    fid_card := TBLRMsgCardMessage(msg).id;
    fcard_type := TBLRMsgCardMessage(msg).ctype;
    exit;
  end;
  if msg is TBLRMsgSA then begin
    ClientInfoProvider.SetFieldValue('SAID', TBLRMsgSA(msg).id);
    exit;
  end;
  if msg is TBLRMsgCityMessage then begin
    ClientInfoProvider.SetFieldValue('city', ZipCodeU.GetCityById(upos.UntillDB, TBLMsgCountry(msg).Id));
    exit;
  end;

  if msg is TBLMsgCountry then begin
    ClientInfoProvider.SetFieldValue('country', IntToStr(TBLMsgCountry(msg).Id));
    exit;
  end;
  if msg is TBLRMsgPaymentMode then begin
    if upos.UserId = GUEST_DEFAULT_USER_ID then exit;
    if (ClientInfoProvider.deposit<>0) then begin
      DepositValue := ClientInfoProvider.deposit;
      if POSRestaurantSettings.Settings.ExtraSettings.DepositLimit>0 then
        if DepositValue > POSRestaurantSettings.Settings.ExtraSettings.DepositLimit then
          plugin.RaisePosException('Deposit value cannot exceed %f',[POSRestaurantSettings.Settings.ExtraSettings.DepositLimit],'TBLRModifyClient.ProcessMessage');

      ClientInfoProvider.SaveClient(self.classname='TBLRModifyClient');
      id:=StrToInt64Def(ClientDataProvider.ClientID, 0);
      if (id>0)and(not IsOnInvoiceClient(upos.UntillDB, id)) then begin
        TBLRGetDepositAmount.Create(Self.bla, Self);
        posalg.SendInput(CurrToStr(DepositValue));
        msgPayment := TBLRMsgPaymentMode.Create;
        msgPayment.id_payments := TBLRMsgPaymentMode(msg).id_payments;
        posalg.SendMessage(msgPayment);
      end;
    end;
    exit;
  end;
  if msg is TBLSMsgChangeTimeRange then begin
    if IsClientDateField( ActiveFieldName )
      or IsClientTimeField( ActiveFieldName ) then
    with TBLSMsgChangeTimeRange(msg) do begin
      strDT := ClientInfoProvider.GetFieldValue(BLAlgU.ActiveFieldName);
      if strDT = '' then
        strDT := '0';
      dt:=SysDateTimeToLocal(StrToDateTime(strDT));
      ChangeTimeRange(Params, upos.GetPosTimeInfoFunc(), dt);
      ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName,Datetimetostr(LocalDateTimeToSys(dt)));
      ue := TPMAnalyseChangeDatasetFlags.Create(0, 'CalendarDateTime', uetUpdate);
      try
        UPos.Un.SendEvent(URL_POS_GENERAL, ue);
      finally
        FreeAndnil(ue);
      end;
      exit;
    end;
  end;
  if (msg is TBLRMsgNeedSA) then begin
    SADataProvider.SAID := '0';
    TBLRSelectSA.Create(Self.bla, Self, true);
    exit;
  end;
  if msg is TBLMessageClear then begin
    fid_card := 0;
    fcard_type := 0;
    if not SameText(BLAlgU.ActiveDatasetName,'TClientSingleDataset') then exit;
    ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, EmptyStr);
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if msg is TBLMessageBackSpace then begin
    if IsClientDateField( ActiveFieldName ) or IsClientTimeField( ActiveFieldName ) then
      exit;
    ss := ClientInfoProvider.GetFieldValue(ActiveFieldName);
    if Length(ss) > 0 then begin
      if SameText(BLAlgU.ActiveFieldName, 'limit') then begin
        c1 := StrToCurrDef(ss, 0);
        i1 := Trunc(c1);
        s1 := IntToStr(i1);
        delete(s1,Length(s1) ,1);
        if (StrToIntDef(s1, MAXINT)<>MAXINT) then
          ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, s1)
        else
          ClientInfoProvider.SetFieldValue(BLAlgU.ActiveFieldName, '0')
      end else begin
        delete(ss,Length(ss) ,1);
        ClientInfoProvider.SetFieldValue(ActiveFieldName, ss);
      end;
    end;
    exit;
  end;

end;

procedure TBLRModifyClient.ProcessTagReaderString(
  msg: TBLMessageTagReaderString);
var cid: Int64;
    cm : TBLRMsgClient;
    s  : String;
    evCheck : TPMAnalyseChangeFlags;
begin
  if upos.UserId = GUEST_DEFAULT_USER_ID then exit;
  UntillApp.ClearChangeFlags;
  cid:=GetClientByCard(upos.UntillDB, msg.s);
  if bNewClient then begin
    if cid > 0 then begin
      TBLRSelectClient.Create(Self.bla, Self);
      cm:=TBLRMsgClient.Create;
      cm.id:=cid;
      posalg.SendMessage(cm);
      posalg.SendOK;
    end else begin
      // DSP Stade de Mons new client cards https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=335454
      if POSRestaurantSettings.Settings.BlockNewClient then exit;

      if POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength>0 then
        if Length(msg.s)> POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength then
          plugin.RaisePosException('Card number length cannot exceed  %d symbols',[POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength], 'TBLRModifyClient.ProcessKeybordString(');
      ClientInfoProvider.SetFieldValue('date_ creation', DateTimeToStr(upos.GetPosNow));
      ClientInfoProvider.SetFieldValue('date_expiration', DateTimeToStr(IncYear(upos.GetPosNow,POSRestaurantSettings.Settings.ExtraSettings.CardYears)));
      ClientInfoProvider.SetFieldValue('card', msg.s);
      s := plugin.Translate('RestaurantPluginU','This is new card do you want to create?');
      TBLSShowMessage.Create(posalg, Self, s, mtConfirmation);
    end;
  end else
    ClientInfoProvider.SetFieldValue('card', msg.s);

    evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
    try
      UPos.Un.SendEvent(FOCUS_OFF, evCheck);
    finally
      evCheck.Free;
    end;
    BLAlgU.ActiveDatasetName :='TClientSingleDataset';
    BLAlgU.ActiveFieldName   :='deposit';
    evCheck := TPMAnalyseChangeFlags.Create(0, '', uetUpdate);
    try
      UPos.Un.SendEvent(FOCUS_ON, evCheck);
    finally
      evCheck.Free;
    end;
  bNewClient := false;
  UntillApp.AnalyseChangeFlags;
end;

{ TBLRModifyClientCard }

function TBLRModifyClientCard.CanAcceptMessage(
  msg: TBLMessageClass): boolean;
begin
  Result := True;
  if msg = TBLRMsgNeedClient then Exit;
  Result := inherited CanAcceptMessage(msg);
end;

function TBLRModifyClientCard.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Modify client card', 'State hint');
end;

class function TBLRModifyClientCard.GetStaticGuid: string;
begin
  result := '{29F34BE4-A3A1-4115-8344-537A5ECBFD8A}';
end;

procedure TBLRModifyClientCard.OnChildFinished(child: TBLAlgStep);
begin
  inherited;
  if child is TBLRLogin then begin
    FreeOnLastChild := false;
    exit;
  end;
  if child is TBLRSelectClient then begin
    FreeOnLastChild := false;
    RefreshClientInfo;
    exit;
  end;
end;

procedure TBLRModifyClientCard.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgNeedClient then begin
    TBLRSelectClient.Create(Self.bla, Self);
    exit;
  end;
  inherited;
end;

{ TBLRSelectClient }

function TBLRSelectClient.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := True;
  if msg = TBLRMsgEditClientInfo then Exit;
  if msg = TBLRMsgEmailInvoice then Exit;

  if not GarbageCollectionMode then begin
    if msg = TBLRMsgNewClientInfo then Exit;
    if msg = TBLRMsgDeleteClient then Exit;
  end;
  if msg = TBLRMsgNeedTATimeSlot then exit;
  if msg = TBLRMsgNeedChangeOrderName then Exit;
  if msg = TBLRNeedClientByName then exit;
  if msg = TBLRMsgNeedSA then exit;
  if msg = TBLRMsgNeedMarkInvoice then exit;
  if msg = TBLRMsgClientCard then exit;
  if msg = TBLMessageInput then Exit;
  if msg = TBLMessageOK then Exit;
  if msg = TBLMessageCancel then Exit;
  if msg = TBLMessageBackSpace then exit;
  if msg = TBLRMsgClient then exit;
  if msg = TBLMessageUndo then Exit;
  if msg = TBLMessageClear then exit;
  if msg = TBLRMsgNeedClientDepositOnAccount then exit;
  if msg = TBLRMsgNeedPrintInvoice then exit;
  if msg = TBLRMsgNeedPreviewInvoice then exit;
  if msg = TBLMessageKeyboardString then exit;
  if msg = TBLClientIDSmartCard then exit;
  if msg = TBLSMsgCustomerSmartCard then exit;
  if msg = TBLRMsgNeedCreateTTGCard then exit;

  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRSelectClient.Create(ABla: TBLAlg; AParent: TBLAlgStep;
  aZeroClientOnCancel : boolean = true);
var bNeedLoadBill : boolean;
    SavedClientId : String;
begin

  ClientDataProvider.CheckLicensed;
  bNeedLoadBill := (PosAlg.CurrentStateGUID <> TBLRSplitNote.GetStaticGUID) and
    not ((PosAlg.CurrentStateGUID = TBLRPayment.GetStaticGUID)
        and (TBLRPayment(PosAlg.GetCurrentStep).AfterSplitBill))
    and not ((blr.ActiveBill.DelayType = odltTakeAway)
        and (TAOrderMode(PosRestaurantSettings.Settings.ExtraSettings.TAOrderType) in [taomFirstOrder, taomFree] ))
    and (blr.ActiveBill.delayType <> odltDelay); //Is reload bill is needed
  inherited Create(ABLa, aParent);
  FZeroClientOnCancel := aZeroClientOnCancel;
  OldClientId := ClientDataProvider.ClientID;
  self.FreeOnLastChild:=false;
  if (blr.ActiveBill.delayType = odltDelay) and not blr.ActiveBill.items.IsEmpty then
    plugin.RaisePosException('You already entered some articles, but have not saved the order yet. Client cannot be changed.');
  if bNeedLoadBill then begin
    SavedClientId := ClientDataProvider.ClientID;
    blr.ActiveBill.LoadFromActiveBill(nil,blr.ActiveBill.TableNo, blr.ActiveBill.table_part, blr.ActiveBill.SalesArea);
    if StrToInt64Def(SavedClientId,0)>0 then
      ClientDataProvider.ClientID := SavedClientId ;
  end;
  UntillApp.ClearChangeFlags;
  CurrentInvoice.DiscountValue:=0;
  CurrentInvoice.DiscountPercent:=false;
  UntillApp.AnalyseChangeFlags;
end;

procedure TBLRSelectClient.DoApplyFilter;
begin
  ClientDataProvider.ClientFilter := StrClientFilter;
end;

function TBLRSelectClient.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Select client', 'State hint');
end;

class function TBLRSelectClient.GetStaticGuid: string;
begin
  result := '{5C17A421-E651-4e95-9C20-79A06DF31F70}';
end;

procedure TBLRSelectClient.OnCancel;
begin
  if FZeroClientOnCancel then begin
    if OldClientId = '' then
      ClientDataProvider.ClientID := '0'
    else
      ClientDataProvider.ClientID := OldClientId;
  end;
  inherited;
  StrClientFilter := '';
  DoApplyFilter;
end;

procedure TBLRSelectClient.AssignClient;
var id_client : Int64;
begin

  id_client := StrToInt64Def(ClientDataProvider.ClientID,0);
  if (blr.activebill.DelayType = odltTakeAway) and ( id_client = 0 )
    and (trim(StrClientFilter)<>'') and (trim(blr.activebill.order_name)='') then
    blr.activebill.order_name := trim(StrClientFilter)
  else begin
    blr.ActiveBill.SetClients(id_client);
    if (parent is  TBLRPayment) and (TBLRPayment(parent).AfterSplitBill) then exit;
    if (blr.activebill.delayType <> odltDelay) then begin
      if not BLR.ActiveBill.rest_items.IsEmpty then begin
          blr.ActiveBill.TryToSaveBill(SAVEBILL_AFTERCHANGECLIENT);
          blr.ActiveBill.LoadFromActiveBill(nil,blr.ActiveBill.TableNo, blr.ActiveBill.table_part, blr.ActiveBill.SalesArea);
          if (POSRestaurantSettings.Settings.ExtraSettings.PaymentRecalcPrice) and (parent is TBLRPayment) then exit;
          blr.ActiveBill.TransferAll(blr.TransferResult, false, false, false);
          blr.ActiveBill.RecalcClientPrices(StrToInt64Def(ClientDataProvider.ClientID,0));
          blr.TransferResult.LoadFromActiveBill(nil,ORDER_EMPTY_TABLE, blr.ActiveBill.table_part, blr.ActiveBill.SalesArea);
          PrintRearDisplay(blr.TransferResult);
      end;
    end;
  end;

end;

procedure TBLRSelectClient.OnChildFinished(child: TBLAlgStep);
var newemail : string;
begin
  if (child is TBLRGetClientEmail) then begin
    Self.FreeOnLastChild := False;
    if (child.result in [ blOK ]) then begin
      newemail := trim(TBLRGetClientEmail(child).Email);
      if newemail <> '' then begin
        SaveClientInvoiceEmail( newemail );
        DoSendInvoice(newemail);
      end;
    end;
    exit;
  end;
  if child is TBLRChangeOrderName then begin
    FreeOnLastChild := true;
    exit;
  end;
  if child is TBLSShowMessage then begin
    FreeOnLastChild := true;

    if child.Result <> blOK then exit;
    AssignClient;
  end else if (child is TBLRGetDepositAmount) then
    RefreshButtons;
  inherited;
end;

procedure TBLRSelectClient.OnInput(s: WideString);
begin
  StrClientFilter := StrClientFilter + s;
  DoApplyFilter;
end;

procedure TBLRSelectClient.OnOK;
var s : String;
    bNeedCheckPartial : boolean;
    id_client : Int64;
begin
  id_client := StrToInt64Def(ClientDataProvider.ClientID,0);
  if (parent is TBLRAlgTAOrder) then begin
    if (id_client = 0)  and (trim(blr.ActiveBill.order_name)='') then
     plugin.RaisePosException('Please define client');
  end;
  if (parent is TBLREditOrder) or (parent is TBLRPayment) then begin
    bNeedCheckPartial := not (POSRestaurantSettings.Settings.ExtraSettings.PaymentRecalcPrice) or (parent is TBLREditOrder);
    if bNeedCheckPartial and (blr.ActiveBill.NotPaid<>blr.ActiveBill.SCTotal) then begin
       plugin.RaisePosException('Bill %s partially paid. Client cannot be changed', [blr.ActiveBill.pnumber.ToString]);
    end;

    blr.ActiveBill.DataChangeEventLock;
    try
      if bNeedCheckPartial then begin
        if blr.ActiveBill.GetHasDiscountItems(id_client, false) then begin
          s := Plugin.Translate('BLRAlgU','Bill has discounted items. Continue change client and cancel discounts?');
          TBLSShowMessage.Create(posalg, Self, s, mtConfirmation);
          exit;
        end;
      end;
      AssignClient;
    finally
      blr.ActiveBill.DataChangeEventRelease;
    end;
  end;
  inherited;
  StrClientFilter := '';
  DoApplyFilter;
end;

procedure TBLRSelectClient.OnUndo;
begin
  StrClientFilter := '';
  DoApplyFilter;
end;

procedure TBLRSelectClient.ProcessKeybordString(msg: TBLMessageKeyboardString);
var cid: Int64;
    cm: TBLRMsgClient;
begin
  UntillApp.ClearChangeFlags;

  cid:=GetClientByCard(upos.UntillDB, msg.s);
  if cid > 0 then begin
    cm:=TBLRMsgClient.Create;
    cm.id:=cid;
    posalg.SendMessage(cm);
  end else
    inherited;

  UntillApp.AnalyseChangeFlags;
end;

function DoSendInvoice(aemail : string) : Int64;
var lastInvSys: TDateTime;
    clinfo    :  TInt64WS;
    cc        : TCleanCashBase;
    idLayout  : Int64;
    id_clnt, id_sa   : Int64;
    mailer    : TUBLMailer;
    uc        : TUBLConnection;
    sa_email  : string;
begin
  result := -1;
  id_clnt := StrToInt64Def(ClientDataProvider.ClientId,0);
  if id_clnt=0 then exit;

  sa_email := aemail;
  if IsSharedAccountPOS(id_clnt, id_sa) then begin
    if not isSharedAccountPartial(upos.UntillDB, id_sa) then
      plugin.RaisePosException('Client is linked to Shared Account, emailing invoice is impossible.');
  end;
  sa_email := aemail;

  if length(trim(sa_email)) < 3 then
    plugin.RaisePosException('Client email not defined.');

  lastInvSys := GetClientLastInvoiceSys(upos.UntillDB, id_clnt, imAuto);
  clinfo:=TInt64WS.Create(id_clnt, GetClientName(id_clnt));
  if not assigned(clinfo) then exit;

  cc := CleanCashU.GetCleanCashDevice(upos.UntillDB, upos.UserId);

  if assigned(cc) and (pltInvoice in cc.PrintLegalTickets) then
    idLayout := TLegalTicketInvoice.GetId
  else
    idLayout:= GetInvoiceLayout(upos.UntillDB, TUntillPlugin(UntillApp.GetPluginByName(APP_NAME)).GetSetting('common','ComputerName',ThisComputerName));

  if idLayout=0 then exit;

  uc := GetUBL(upos.UntillDB);
  if uc.Soap.mail_checkMailerConfiguration(uc.SessionID) = false then begin
    plugin.RaisePosException('Mail service is not configured');
  end;

  mailer := TUBLMailer.Create(TInvoiceMode.imAuto);
  try

    result := mailer.HandleInvoice(upos.UntillDB, id_clnt, upos.UserId, upos.GetPosNow,
    lastInvSys, upos.GetPosNow, 0,False, 1, 0, sa_email);

  finally
    FreeAndNil(mailer);
  end;

end;

procedure TBLRSelectClient.ProcessMessage(msg: TBLMessage);
var id: int64;
    pd: Currency;
    cm: TBLRMsgClient;
    c1, c2: Currency;
    p1: Int64;
begin
  if msg is TBLRMsgEmailInvoice then begin
    if ClientDataProvider.ClientId='' then exit;
    if trim(ClientInfoProvider.INVOICE_EMAIL)='' then
      TBLRGetClientEmail.Create(bla, self, ClientInfoProvider.GetFieldValue('invoice_email'), nil)
    else
      DoSendInvoice(ClientInfoProvider.INVOICE_EMAIL);
    exit;
  end;
  if msg is TBLRMsgNeedTATimeSlot then begin
    self.free;
    exit;
  end;
  if msg is TBLRMsgNeedChangeOrderName then begin
    TBLRChangeOrderName.Create(Self.bla, Self);
    exit;
  end;
	if msg is TBLRNeedClientByName then begin
    SetClientByName(TBLRNeedClientByName(msg).Name);
    exit;
  end;
	if msg is TBLRMsgNeedCreateTTGCard then begin
    if ClientDataProvider.ClientID = '0' then exit;
  	TBLRCreateTTGCard.Create(Self.bla, Self);
    exit;
  end;
  if (msg is TBLRMsgNeedSA) then begin
    SADataProvider.SAID := '0';
    TBLRSelectSA.Create(Self.bla, Self, false);
    exit;
  end;
  if (msg is TBLRMsgNeedMarkInvoice) then begin
    TBLRMarkInvoice.Create(Self.bla, Self);
    RefreshButtons;
    exit;
  end;

  if (msg is TBLRMsgEditClientInfo) or (msg is TBLRMsgNewClientInfo) or (msg is TBLRMsgClientCard) then begin
    if msg is TBLRMsgEditClientInfo then begin
      if ClientDataProvider.ClientID = '0' then exit;
    end;
    if msg is TBLRMsgClientCard then
      TBLRModifyClientCard.Create(Self.bla, Self, false)
    else begin
      TBLRModifyClient.Create(Self.bla, Self, (msg is TBLRMsgNewClientInfo));
    end;
    RefreshClientInfo;
    if msg is TBLRMsgNewClientInfo then ClientDataProvider.ClientID := '0';
    exit;
  end;
  if (msg is TBLRMsgDeleteClient) then begin
    if ClientDataProvider.ClientID <> '0' then begin
      ClientInfoProvider.DeleteClient;
    end;
    exit;
  end;

  if msg is TBLMessageBackSpace then begin
    if length(StrClientFilter) > 0 then delete(StrClientFilter, Length(StrClientFilter),1);
    DoApplyFilter;
    exit;
  end;
  if msg is TBLRMsgClient then begin
    ClientDataProvider.ClientID := IntToStr((msg as TBLRMsgClient).id);
    pd:=GetClientLastDiscountPercent(upos.UntillDB, (msg as TBLRMsgClient).id);
    if pd > 0 then begin
      UntillApp.ClearChangeFlags;
      CurrentInvoice.DiscountValue:=pd;
      CurrentInvoice.DiscountPercent:=true;
      UntillApp.AnalyseChangeFlags;
    end else begin
      UntillApp.ClearChangeFlags;
      CurrentInvoice.DiscountValue:=0;
      CurrentInvoice.DiscountPercent:=false;
      UntillApp.AnalyseChangeFlags;
    end;
    exit;
  end;

  if (msg is TBLSMsgCustomerSmartCard) and (self.parent is TBLREditOrder) then begin
    if (msg as TBLSMsgCustomerSmartCard).CardInfo.ClientId <> 0 then begin
      cm:=TBLRMsgClient.Create;
      cm.id := (msg as TBLSMsgCustomerSmartCard).CardInfo.ClientId;
      PosAlg.SendMessage(cm);
      if (PosAlg.GetCurrentStep is TBLRSelectClient) then
        (PosAlg.GetCurrentStep as TBLRSelectClient).CardPriceId := (msg as TBLSMsgCustomerSmartCard).CardInfo.CardPriceId;
      PosAlg.SendOK;
    end else if ((msg as TBLSMsgCustomerSmartCard).CardInfo.CardPriceId <> 0) then begin
      ChangeActiveBillPrice((msg as TBLSMsgCustomerSmartCard).CardInfo.CardPriceId, c1, c2, p1);
    end else
      Plugin.RaiseError(Plugin.TranslatePOS('BLRAlgU', 'Card is not linked to a client or price'));
  end;

  if msg is  TBLClientIDSmartCard then begin
    cm := TBLRMsgClient.Create;
    cm.id := (msg as TBLClientIDSmartCard).ClientId;
    posalg.SendMessage(cm);
    posalg.SendOK;
  end;

  if msg is TBLMessageClear then begin
    ClientDataProvider.ClientID := '0';
    TClientDataSetParam.Clear(upos.NamedParams);
    exit;
  end;
  if msg is TBLRMsgNeedClientDepositOnAccount then begin
    id:=StrToInt64Def(ClientDataProvider.ClientID, 0);
    if (id>0)and(not IsOnInvoiceClient(upos.UntillDB, id)) then begin
      TBLRGetDepositAmount.Create(Self.bla, Self);
    end;
    exit;
  end;

  if msg is TBLRMsgNeedPrintInvoice then begin
    BLRAlgPaymentU.DoPrintInvoice(TBLRMsgNeedSomthWithCustomLayout(msg).CustomLayout, []);
    exit;
  end;
  if msg is TBLRMsgNeedPreviewInvoice then begin
    BLRAlgPaymentU.DoPrintInvoice(TBLRMsgNeedSomthWithCustomLayout(msg).CustomLayout, [PrintPreview]);
    exit;
  end;

  inherited;
end;

procedure TBLRSelectClient.SaveClientInvoiceEmail(email : string);
var wt   : IWTran;
    qUpd : IIBSQL;
    id_client : Int64;
begin
  id_client := StrToInt64Def(ClientDataProvider.ClientID,0);
  if id_client = 0 then exit;

  wt := upos.UntillDB.getWTran;
  qUpd := wt.GetPreparedIIbSql('update clients set INVOICE_EMAIL=:INVOICE_EMAIL where id=:id');
  qUpd.q.ParamByName('INVOICE_EMAIL').AsString := lowercase(Email);
  qUpd.q.ParamByName('id').AsInt64 := id_client;
  qUpd.ExecQuery;
  wt.commit;
end;

procedure TBLRSelectClient.ProcessTagReaderString(msg: TBLMessageTagReaderString);
var cid: Int64;
    cm: TBLRMsgClient;
begin
  UntillApp.ClearChangeFlags;

  cid:=GetClientByCard(upos.UntillDB, msg.s);
  if cid > 0 then begin
    cm:=TBLRMsgClient.Create;
    cm.id:=cid;
    posalg.SendMessage(cm);
  end else
    inherited;

  UntillApp.AnalyseChangeFlags;
end;

procedure TBLRSelectClient.RefreshButtons;
var ue : TPMAnalyseChangeDatasetFlags;
begin
  ue := TPMAnalyseChangeDatasetFlags.Create(0, 'refresh', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, ue);
  finally
    FreeAndnil(ue);
  end;
end;

procedure TBLRSelectClient.SetClientByName(clientName: WideString);
var iq : IIBSQL;
  msgClient : TBLRMsgClient;
begin
  iq := upos.Untilldb.GetPreparedIIbSql('select * from clients where upper(name)=upper(:name) and is_active=1');
  iq.q.Params[0].asString := clientName;
  iq.ExecQuery;
  if not iq.Eof then begin
    msgClient := TBLRMsgClient.Create;
    msgClient.id := StrToInt64Def(iq.q.Fields[0].asString,0);
    posalg.SendMessage(msgClient);
  end;
end;

{ TBLRSelectClientParam }

function TBLRSelectClientParam.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgClient then Exit;
  if msg = TBLMessageInput then Exit;
  if msg = TBLMessageBackSpace then exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRSelectClientParam.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  ClientDataProvider.ClientFilter := '';
  SendUpdateDep;
end;

class function TBLRSelectClientParam.DatasetParamClass: TUntillDbItemsListDatasetParamClass;
begin
  result := TClientDataSetParam;
end;

function TBLRSelectClientParam.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Choose clients', 'State hint');
end;

function TBLRSelectClientParam.GetItemName(aid_art: Int64): String;
begin
   result := GetClientName(aid_art);
end;

class function TBLRSelectClientParam.GetStaticGuid: string;
begin
  result := '5e5124be-0d59-464a-aa20-fef5bbdc2dec';
end;

procedure TBLRSelectClientParam.OnInput(s: WideString);
begin
  StrClientFilter := StrClientFilter + s;
  ClientDataProvider.ClientFilter := StrClientFilter;
end;

procedure TBLRSelectClientParam.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgClient then begin
    inherited;
    ClientDataProvider.ClientID := IntToStr((msg as TBLRMsgClient).id);
    exit;
  end;
  if msg is TBLMessageBackSpace then begin
    if length(StrClientFilter) > 0 then delete(StrClientFilter, Length(StrClientFilter),1);
    ClientDataProvider.ClientFilter := StrClientFilter;
    exit;
  end;
  inherited;
end;

{ TBLRAddCard }

function TBLRAddCard.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := True;
  if msg = TBLRMsgClientCardName then exit;
  if msg = TBLMessageInput then exit;
  if msg = TBLMessageBackSpace then exit;
  if msg = TBLSMsgUnknownSmartCard then exit;
  if msg = TBLClientIDSmartCard then exit;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRAddCard.Create(ABla: TBLAlg; AParent: TBLAlgStep; aid_card : Int64);
begin
  inherited Create(ABla, AParent);
  fid_card_name := 0;
  fcard_id      := aid_card;
  fcardtype := 0;
  fcard_number  := '';
  if fcard_id > 0 then
    fcard_number := GetCardNumber(fcard_id);
end;

function TBLRAddCard.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Add client card', 'State hint');
end;

function TBLRAddCard.GetParentModifyClient: TBLRModifyClient;
begin
  result := TBLRModifyClient(parent);
end;

class function TBLRAddCard.GetStaticGuid: string;
begin
  result :='33b738f7-a858-4fa3-9fd4-920059bd62c7'
end;

procedure TBLRAddCard.OnInput(s: WideString);
begin
 if POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength>0 then
   if Length(card_number + s)> POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength then
      plugin.RaisePosException('Card number length cannot exceed  %d symbols',[POSRestaurantSettings.Settings.ExtraSettings.MaxCardLength], 'TBLRModifyClient.ProcessKeybordString(');
  fcard_number := fcard_number + s;
end;

procedure TBLRAddCard.OnOK;
var cc  : TClientCard;
begin

    cc  := TClientCard.Create;
    cc.card_id       := fcard_id;
    cc.card_type     := fcardtype;
    cc.id_card_name  := fid_card_name;
    cc.card_number   := fcard_number;
    if not TBLRModifyClient(parent).fcards.AddNotExist(cc) then
      FreeAndNil( cc );
    if StrToInt64Def(ClientDataProvider.ClientID,0) > 0 then
      TBLRModifyClient(parent).SaveCardData;
    SendUpdateCientCards;
    inherited;
end;

procedure TBLRAddCard.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLClientIDSmartCard then begin
    TBLSShowMessage.CreateSafe(Self.bla, Self, plugin.TranslatePOS('BLRAlgU', 'Smart card is already linked to client', 'Add client cards error message'), mtError);
    exit;
  end;
  if msg is TBLSMsgUnknownSmartCard then begin
    fcard_number := TBLSMsgUnknownSmartCard(msg).SmartCardUid;
    fcardtype := 1;
    OnOk;
    exit;
  end;
  if msg is TBLRMsgClientCardName then begin
    fid_card_name := TBLRMsgClientCardName(msg).id;
    exit;
  end;
  if msg is TBLMessageBackSpace then begin
    if Length(fcard_number) > 0 then
      delete(fcard_number,Length(fcard_number) ,1);
    exit;
  end;
  inherited;
end;

procedure TBLRAddCard.ProcessKeybordString(msg: TBLMessageKeyboardString);
begin
  inherited;
  fcard_number := msg.s;
end;

procedure TBLRAddCard.ProcessTagReaderString(msg: TBLMessageTagReaderString);
begin
  inherited;
  UntillApp.ClearChangeFlags;
  fcard_number := msg.s;
  UntillApp.AnalyseChangeFlags;
end;

{ TBLRMsgCardMessage }

procedure TBLRMsgCardMessage.Setctype(const Value: integer);
begin
  Fctype := Value;
end;

{ TClientCards }

function TClientCards.AddNotExist(acc: TClientCard) : boolean;
var i : Integer;
   cc : TClientCard;
begin
  result := true;
  for i := 0 to Pred(count) do begin
    cc := items[i];
    if cc.Equal(acc) then begin
      result := false;
      exit;
    end;
  end;
  Add(acc);
end;

{ TClientCard }

function TClientCard.Equal(acc: TClientCard): boolean;
begin
  result := true;
  if self.card_id = acc.card_id then exit;
  if self.card_type = acc.card_type then exit;
  if self.id_card_name = acc.id_card_name then exit;
  if self.card_number = acc.card_number then exit;
  result := false;
end;

initialization
  RegisterClass(TBLRMsgCityMessage);
  RegisterClass(TBLRMsgEmailInvoice);
  RegisterClass(TBLRMsgCardMessage);
  RegisterClass(TBLRMsgNeedAddClientCard);
  RegisterClass(TBLRMsgNeedModifyClientCard);
  RegisterClass(TBLRMsgClientCardName);
end.
