unit PivotsU;

interface

uses SyncObjs, Generics.Collections, ClassesU, UntillDBU, Classes, Types;

const
  UNTILL_INVOKE_RECALCULATE_AGGREGATES = 'RecalculateAggregates';
  UNTILL_INVOKE_REMOVE_AGGREGATES = 'RemoveAggregates';
  AGGREGATES_VERSION = 14;

type
  TPivotState = (READY, NO_PIVOTS, OBSOLETE, IGNORED);
  TPivotStates = set of TPivotState;

{
  compactdb "c:\products\untill\bin\db\demo.fdb"
  invoke Restaurant /db="regression.fdb" /params="RemoveAggregates;masterkey"
}

procedure RecalculateAggregates(dbName, sysdbaPwd: string);
procedure RemoveAggregates(dbName, sysdbaPwd: string);
function PivotsReady(db: TUntillDB; var NotReadyReason: string): Boolean;
function PivotsState(db: TUntillDB): TPivotStates; overload;
function PivotsState(dbName, sysdbaPwd: string): TPivotStates; overload;

implementation

uses SysUtils, UntillLogsU, DBStartStopU, BOUntillDBU, CommonU, TaskUtilsU, IOUtils, Forms, KernelSettingsU,
  AppTerminationU, ThreadObserverU, Diagnostics;

function ReadScript(name: string): string;
var
  path: string;
begin
{$IFNDEF UNTILL_RELEASE}
  path := '..\plugins\Restaurant\src\sql\';
{$ELSE}
  path := ExtractFilePath(Application.ExeName) + 'plugins\Restaurant\runtime\';
{$ENDIF}
  result := TFile.ReadAllText(path + name + '.sql');
end;

procedure ExecScript(wt: IWTran; name: string);
begin
  try
    wt.GetPreparedIIbSql(ReadScript(name)).ExecQuery;
  except
    on e: exception do begin
      e.Message := 'Failed on script: ' + name + CRLF + e.Message;
      raise;
    end;
  end;
end;

procedure DoRemoveAggregates(db: TUntillDB); overload;
var
  wt: IWTran;
begin
  wt := db.getWTran();
  wt.GetPreparedIIbSql('execute procedure remove_aggregates').ExecQuery;
  wt.commit;
end;

procedure DoRecalculateAggregates(dbName, sysdbaPwd: string);
var
  db: TUntillDB;
  wt: IWTran;
  sw: TStopwatch;
begin
  DBStartStopU.StopDatabase(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd, 'Recalculate Aggregates');
  try
    db := TUntillDB.Create(nil);
    try
      db.RegisterOnServer := false;
      db.Connect(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd, tdbAny, CommonU.GetUntillSrvPort);

      sw := TStopwatch.StartNew;

      DoRemoveAggregates(db);

      wt := db.getWTran();

      // create tables
      ExecScript(wt, 'CreateTablePivotSA');
      ExecScript(wt, 'CreateTablePivotPayments');
      wt.GetPreparedIIbSql('grant all on pivot_payments to untilluser;').ExecQuery;
      wt.GetPreparedIIbSql('grant all on pivot_sa to untilluser;').ExecQuery;

      // prevent object PIVOT_SA is in use error
      wt.commit;
      wt := db.getWTran();

      // views
      ExecScript(wt, 'CreateViewSaAggrView');
      ExecScript(wt, 'CreateViewPbillPaymentsAggrView');
      wt.GetPreparedIIbSql('grant all on sa_aggr_view to untilluser').ExecQuery;
      wt.GetPreparedIIbSql('grant all on pbill_payments_aggr_view to untilluser').ExecQuery;

      wt.commit;
      wt := db.getWTran();

      // compute aggregates
      ExecScript(wt, 'InsertIntoPivotSA');
      ExecScript(wt, 'InsertIntoPivotPayments');

      // pivots_version table
      wt.GetPreparedIIbSql('create table pivots_version(version smallint);').ExecQuery;
      wt.GetPreparedIIbSql('grant all on pivots_version to untilluser').ExecQuery;

      wt.commit;
      wt := db.getWTran();

      // triggers
      ExecScript(wt, 'CreateTriggerPBillPaymentsBookp');
      ExecScript(wt, 'CreateTriggerPbillHotelPaymentsInfo');
      ExecScript(wt, 'CreateTriggerPBillCardPaymentsInfo');
      ExecScript(wt, 'CreateTriggerVoucherPBillPayments');
      ExecScript(wt, 'CreateTriggerVouchers');
      ExecScript(wt, 'CreateTriggerSoldArticles');
      ExecScript(wt, 'CreateTriggerPBillPayments');
      ExecScript(wt, 'CreateTriggerPBillItem');
      ExecScript(wt, 'CreateTriggerNegPBill');
      ExecScript(wt, 'CreateTriggerDepositCardPaymentsInfo');
      ExecScript(wt, 'CreateTriggerFiscalNumbers');
      ExecScript(wt, 'CreateTriggerFiscalNumbersEx');

      // indices
      wt.GetPreparedIIbSql('create index pivot_payments_idx1 on pivot_payments (id_pbill_payments);').ExecQuery;
      wt.GetPreparedIIbSql('create index pivot_payments_idx2 on pivot_payments (pdatetime);').ExecQuery;
      wt.GetPreparedIIbSql('create index pivot_payments_idx3 on pivot_payments (id_vouchers);').ExecQuery;
      // for trigger deposit_card_payments_info_aggr
      wt.GetPreparedIIbSql('create index pivot_payments_idx4 on pivot_payments (id_pbill);').ExecQuery;
      // for trigger neg_pbill_aggr
      wt.GetPreparedIIbSql('create index pivot_sa_idx1 on pivot_sa(datetime);').ExecQuery;
      wt.GetPreparedIIbSql('create index pivot_sa_idx2 on pivot_sa(id_pbill);').ExecQuery;

      // pivots ready
      with wt.GetPreparedIIbSql('insert into pivots_version (version) values (:version);') do begin
        q.Params[0].AsInteger := AGGREGATES_VERSION;
        ExecQuery;
      end;

      wt.commit;

      TraceLog.WriteString(dbName + ': aggregates recalculated in ' + IntToStr(sw.ElapsedMilliseconds) + 'ms');
    finally
      FreeAndNil(db);
    end;
  finally
    DBStartStopU.StartDatabase(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd);
  end;
end;

procedure DoRemoveAggregates(dbName, sysdbaPwd: string); overload;
var
  db: TUntillDB;
begin
  DBStartStopU.StopDatabase(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd, 'Remove Aggregates');
  try
    db := TUntillDB.Create(nil);
    try
      db.RegisterOnServer := false;
      db.Connect(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd, tdbAny, CommonU.GetUntillSrvPort);
      DoRemoveAggregates(db);
      TraceLog.WriteString(dbName + ': aggregates removed');
    finally
      FreeAndNil(db);
    end;
  finally
    DBStartStopU.StartDatabase(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd);
  end
end;

procedure RemoveAggregates(dbName, sysdbaPwd: string);
begin
  ThreadObserver.SetLongOperationFlag(MAIN_THREAD_NAME);
  try
    DoRemoveAggregates(dbName, sysdbaPwd);
  finally
    ThreadObserver.ClearLongOperationFlag(MAIN_THREAD_NAME);
  end;
end;

procedure RecalculateAggregates(dbName, sysdbaPwd: string);
begin
  ThreadObserver.SetLongOperationFlag(MAIN_THREAD_NAME);
  try
    DoRecalculateAggregates(dbName, sysdbaPwd);
  finally
    ThreadObserver.ClearLongOperationFlag(MAIN_THREAD_NAME);
  end;
end;

function PivotsState(dbName, sysdbaPwd: string): TPivotStates; overload;
var
  db: TUntillDB;
begin
  db := TUntillDB.Create(nil);
  try
    db.RegisterOnServer := false;
    db.Connect(CommonU.GetDBPath(dbName), 'SYSDBA', sysdbaPwd, tdbAny, CommonU.GetUntillSrvPort);
    result := PivotsState(db);
  finally
    FreeAndNil(db);
  end;
end;

function PivotsIgnored(db: TUntillDB): Boolean;
var
  iq: IIBSQL;
begin
  result := true;
  iq := db.GetPreparedIIbSql('select ignore_pivots from settings');
  iq.ExecQuery;
  if not iq.Eof then
    result := iq.FieldByName('ignore_pivots').AsInteger = 1;
end;

function PivotsState(db: TUntillDB): TPivotStates;
var
  iq: IIBSQL;
begin
  result := [];
  if PivotsIgnored(db) then
    result := [IGNORED];
  iq := db.GetPreparedIIbSql('select 1 from rdb$relations where rdb$relation_name = ''PIVOTS_VERSION''');
  iq.ExecQuery;
  if not iq.Eof then begin
    iq := db.GetPreparedIIbSql('select first 1 * from pivots_version');
    iq.ExecQuery;
    if iq.Eof or (iq.FieldByName('version').AsInteger <> AGGREGATES_VERSION) then
      result := result + [OBSOLETE]
  end else
    result := result + [NO_PIVOTS];

  if result = [] then
    result := [READY];
end;

function PivotsReady(db: TUntillDB; var NotReadyReason: string): Boolean;
var
  ps: TPivotState;
  tmp: string;
begin
  NotReadyReason := '';
  for ps in PivotsState(db) do begin
    tmp := '';
    case ps of
      READY: Exit(true);
      IGNORED: tmp := 'ignored';
      NO_PIVOTS: tmp := 'no aggregates';
      OBSOLETE: tmp := 'need recalculate';
    end;
    NotReadyReason := NotReadyReason + tmp + ', ';
  end;

  NotReadyReason := copy(NotReadyReason, 0, Length(NotReadyReason) - 2);
  result := false;
end;

end.
