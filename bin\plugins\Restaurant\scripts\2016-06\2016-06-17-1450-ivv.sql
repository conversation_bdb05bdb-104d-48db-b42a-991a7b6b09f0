create table savepoints_articles (
    id u_id,
    id_sp_turnover bigint,
    id_articles  bigint,
    quantity     integer,
    constraint sp_articles_pk primary key (id),
    constraint sp_articles_fk1 foreign key (id_sp_turnover) references SAVEPOINT_TURNOVER(id),
    constraint sp_articles_fk2 foreign key (id_articles) references articles(id)
);
commit;
grant all on savepoints_articles to untilluser;
commit;
execute procedure register_sync_table_ex('savepoints_articles', 'p', 1);
commit;
