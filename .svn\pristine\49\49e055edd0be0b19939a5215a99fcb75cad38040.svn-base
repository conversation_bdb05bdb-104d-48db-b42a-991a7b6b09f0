unit BLRVouchersU;

interface
uses Classes, BLAlgU, BLRAlgU, UntillDBU, PosAlgU, DB, RestaurantPluginU,
  BLRMainU, SysUtils, CommonU , UntillPOSU, ClassesU, PSPU, Math, NamedVarsU,
  StrUtils, UntillAppU, UrlNotifierU, UntillDateTimeU, Dialogs, BLSysU,
  UBLProxyU, PaymentsU, BLRBillU, Generics.Collections, CashdroU, CashmachineDriverU;

const VOUCHER_BARCODE_LENGTH_EAN    : Integer = 13;
      VOUCHER_BARCODE_LENGTH_CODE39 : Integer = 9;
      QR_BARCODE_LENGTH_CODE        : Integer = 35;
      MAX_MULTI_VOUCHER : Integer = 1000;
      URL_REINIT_VOUCHER_NUMBER : String = 'URL_REINIT_VOUCHER_NUMBER';
      NUMERIC_STATE_VOUCHERS_VALUE: String = 'New vouchers value';
      NAMED_PARAM_VOUCHER_ID : string  = 'NAMED_PARAM_VOUCHER_ID';
      NAMED_PARAM_VOUCHER_ID_REFUND : string  = 'NAMED_PARAM_VOUCHER_ID_REFUND';
type

  TInitPaymentVoucherType = (ipvtAdjust, ipvtDelete, ipvtRepay);
  TUntillVoucherType = (uvtRegular = 0, uvtDP = 1);

  TBLRConfirmDeleteVouchers    = class ( TBLSShowMessage );
  TVoucherListEvent = class(TUrlEvent);
  TVoucerPaidType = (vchtPaid, vchtNotPaid);

  TVCHPaymentData= class
  private
    id_payments : Int64;
    price       : currency;
    EFTParams   : string;
    id_pbill_payment : Int64;
  public
    constructor Create(aid_payments : Int64; aprice : Currency;
      aEFTParams : String; aid_pbill_payment : Int64);
  end;

  TDSVoucherData = class
  private
    fid      : Int64;
    fbarcode : String;
    famount  : Currency;
    fvalue   : Currency;
  public
    property id      : Int64 read fid write fid;
    property barcode : String read fbarcode;
    property amount  : Currency read famount;
    property value  : Currency read fvalue;
    constructor Create(aid : Int64; abarcode : string;
      aamount, avalue : Currency);
  end;

  TVoucherShortData  = class
  public
    id      : Int64;
    barcode : string;
    constructor Create(abarcode : string; aid : Int64);
  end;

  TVoucherShortDataList = class(TObjectList<TVoucherShortData>)
  private
    function BarCodeExists( abarcode : string) : boolean;
    function GetItems(Index: Integer): TVoucherShortData;
  public
    property Items[Index: Integer]: TVoucherShortData read GetItems;
  end;

  TDSVoucherDataList = class(TList)
  private
    function GetItems(Index: Integer): TDSVoucherData;
  public
    procedure Clear; override;
    property Items[Index: Integer]: TDSVoucherData read GetItems;
  end;

  TBLRMsgSwitchVouchersType = class (TBLMessage);
  TBLRMsgNeedProlongVoucher = class (TBLMessage);
  TBLRMsgNeedDeleteVoucher  = class (TBLMessage);
  TBLRMsgNeedRepayVoucher   = class (TBLMessage);
  TBLRMsgSendVoucherValue   = class (TBLMessage)
  private
    Fvalue : Currency;
  published
    property value : Currency read FValue write FValue;
  end;

  TBLRMsgNeedEnterNewValue = class (TBLMessage);
  TBLRMsgNeedAdjustVoucher = class (TBLMessage);

  TBLRMsgSendVoucher = class (TBLMessageID)
  private
    Fbarcode : String;
  published
    property barcode : String read fbarcode write fbarcode;
  end;

  TVoucherState = ( vchsSpent, vchsActive, vchsInit, vchsBlocked );
  TVoucherType  = (vctPredefined, vctUntill, vctManual);
  TVoucherMode  = (vcmBarCode, vcmAmount);
  TMultiVoucherMode  = (mvcmQuantity, mvcmAmount);
  TVoucherKind  = (vckCreate, vckPay, vckProlong);

  TVoucherData = record
    barcode          : String;
    amount           : Currency;
    datetime         : TDatetime;
    id_pbill_payment : Int64;
    RES_NAME         : string;
    RES_TABLENO      : Integer;
  end;

  TVoucherPaymentJournalData = record
    amount  : Currency;
    barcode : String;
    enddate : IUntillDatetime;
    ipname  : String;
  end;

  TBLRConfirmMultiVoucherSave  = class ( TBLSShowMessage )
  private
    fid_payments : Int64;
    FEftParams: String;
  public
    constructor Create(ABla: TBLAlg; AParent:TBLAlgStep; MessageBody :
      WideString; MessageType : TMsgDlgType; aid_payments : Int64 = 0; AEftParams: String=''); reintroduce;
  	property EftParams: String read FEftParams;
  end;

  TBLRBaseVoucher    = class (TBLAlgStep)
  private
    StrInvalidBarcodeException : String;
    FId_voucher : Int64;
    FBarCode    : String;
    FAmount     : String;
    FRestAmount : Currency;
    FStartDate  : TDatetime;
    FEndDate    : TDatetime;
  protected
    procedure LoadVoucher(barcode : string); virtual;
  public
    property  id_voucher : Int64 read fid_voucher;
    property  Amount     : String read FAmount;
    property  RestAmount : Currency read FRestAmount;
    property  BarCode    : String read FBarCode;
    property  StartDate  : TDatetime read FStartDate;
    property  EndDate    : TDatetime read FEndDate;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override; // Scan barcode
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep); override;
  end;

  TBLRGetVoucher  = class (TBLRBaseVoucher, IURLListener)
  private
    FNeedProcessPayment : Boolean;
    FVoucherAmountFilter: Currency;
    FMode       : TVoucherMode;
    FKind       : TVoucherKind;
    FBarCodeFilter : String;
    fid_payments: Int64;
    beforeProlongType : TVoucherKind;
    function IsBarcodeExists(_barcode: String): boolean;
    procedure UpdateVoucherList;
    procedure DoProlongVoucher(bc: String);
    procedure InitNewVoucher( avchKind : TVoucherKind );
  protected
    procedure OnChildFinished(child: TBLAlgStep); override;
    procedure LoadVoucher(barcode : string); override;
  public
    procedure OnUrlEvent(url: string; ue: TUrlEvent);
    class function GetStaticGuid: string; override;
    property NeedProcessPayment : Boolean read FNeedProcessPayment;
    property id_payments : Int64 read fid_payments write fid_payments;
    property Mode    : TVoucherMode read FMode;
    property BarCodeFilter : String read FBarCodeFilter;
    property Kind       : TVoucherKind read FKind;
    property VoucherAmountFilter: Currency read FVoucherAmountFilter;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep;
      vchKind: TVoucherKind  = vckCreate); reintroduce;
    destructor Destroy; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    procedure OnInput(s: widestring); override;
    procedure OnOK; override;
    procedure OnUndo; override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override; // Scan barcode
  end;

  TBLRShowVoucherValue    = class (TBLRBaseVoucher)
  public
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override; // Scan barcode
  end;

  TBLRShowVoucherBalance = class (TBLAlgStep)
  private
    FVoucherID: Int64;
    FBalance: Currency;
  public
    constructor Create (ABla: TBLAlg; AParent: TBLAlgStep; AVoucherID: Int64; ABalance: Currency); reintroduce;
    class function GetStaticGuid: string; override;
    function GetHint: widestring; override;
  	property VoucherID: Int64 read FVoucherID;
    property Balance: Currency read FBalance;
  end;

  TBLRCreateMultiVoucher  = class (TBLAlgStep)
  private
    strConfirmVouchers : String;
    Fdt : TDatetime;
    FmMode  : TMultiVoucherMode;
    FQty    : String;
    FAmount : String;
    fvcrList: TStringList;
    function GetAmount : Currency;
    function GetQty    : Integer;
    procedure SaveMultiVoucher( aid_payments : Int64; EftParams: String );
    procedure ReinitVoucherNumber;
  protected
    procedure OnChildFinished(child: TBLAlgStep); override;
  public
    property mMode : TMultiVoucherMode read FmMode;
    property dt   : TDatetime read Fdt;
    property Amount : Currency read GetAmount;
    property Quantity : Integer read GetQty;
    property vcrList: TStringList read fvcrList;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    procedure OnInput(s: widestring); override;
    procedure OnOK; override;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep); override;
    destructor Destroy; override;
  end;

  TVoucherUpdateInfo = record
    id      : Int64;
    spend   : Currency;
    amount  : Currency;
  end;

  TBLRVoucherBunch  = class (TBLAlgStep)
  private
    fShowVouchers  : Boolean;
    FBarCodeFilter : String;
    FVoucherAmountFilter : Currency;
    FvcList : TVoucherShortDataList;
    FCurIdx : Integer;
    procedure DeleteVoucherFromList(aIdx: Integer); overload;
    procedure UpdateVoucherAmount(wt : IWTran;
      abarcode : string; aAmount : Currency);
    procedure Init;
    function ItemIndexValid(aidx: Integer): boolean;
    procedure DoUpdateVoucherAmount(wt : IWtran;
      avcInfo : TVoucherUpdateInfo; aAmount : Currency);
    procedure PutVoucherAdjustment(wt : IWTran; aid: Int64; prev_amount,
      new_amount: Currency; vch_state: Integer; id_pbill_cancel : Int64 =0;
      id_pbill_result: Int64 = 0);
    procedure UpdateVoucherRefund(wt: IWTran; id_vouchers, id_pbill: Int64);
  public
    property CurIdx : Integer read FCurIdx write FCurIdx;
    property vcList : TVoucherShortDataList read FvcList;
    property VoucherAmountFilter : Currency read FVoucherAmountFilter;
    property BarCodeFilter : String read FBarCodeFilter;
    function  GetTotal: Currency; virtual;
    procedure AddVoucherToList(abc: String; aID : Int64);
    procedure OnInput(s: widestring); override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    procedure ProcessKeybordString(msg: TBLMessageKeyboardString); override; // Scan barcode
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep; bShowVouchers : boolean); reintroduce; virtual;
    destructor Destroy; override;
  end;

  TBLRPayVoucher  = class (TBLRVoucherBunch)
  private
    fnewvalue     : currency;
    fAfterAdjust  : boolean;
    FPayType       : TInitPaymentVoucherType;
    fid_payments  : Int64;
    procedure PayVoucherList(aEFTParams: String);
    function CancelVoucherPayments(wt: IWTran; itemBarCode: String) : Int64;
    procedure ProcessPbillVoucherPayment(wt: IWTran; aid_pbill,
      aid_vouchers: Int64; apayments: TVCHPaymentData);
  protected
    procedure OnChildFinished(child: TBLAlgStep); override;
  public
    property PayType       : TInitPaymentVoucherType read FPayType Write FPayType;
    property AfterAdjust  : boolean read fAfterAdjust write fAfterAdjust;
    property newvalue    : currency read fnewvalue;
    property id_payments : Int64 read fid_payments;
    class function GetStaticGuid: string; override;
    function  GetTotal: Currency; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep;
      bShowVouchers : boolean; anewvalue : currency); reintroduce;
    procedure OnOK; override;
  end;

  TBLRAdjustVoucher = class (TBLRVoucherBunch)
  private
    fnewValue       : Currency;
    FCurVoucherType : TVoucherState;
    procedure AssignNewValues;
    procedure InitPayment(aPayType :TInitPaymentVoucherType);
    procedure DeleteVouchers;
    procedure DoAssignValues;
  protected
    procedure OnChildFinished(child: TBLAlgStep); override;
  public
    property CurVoucherType : TVoucherState read FCurVoucherType;
    property newValue : Currency read fnewValue;
    class function GetStaticGuid: string; override;
    function  GetHint: widestring; override;
    function  CanAcceptMessage(msg: TBLMessageClass): boolean; override;
    procedure ProcessMessage(msg: TBLMessage); override;
    procedure OnOK; override;
    constructor Create (ABla: TBLAlg; AParent:TBLAlgStep; aVoucherType : TVoucherState); reintroduce;
  end;

function SaveVoucher(wt : IWTran;
  aid_pbill : Int64;
  avoucherData: TVoucherData;
  aid_payments : Int64; aPrint : boolean;
  aid_clients : Int64;
  aEFTParams: String;
  AskEmail    : boolean;
  vch_type    : TUntillVoucherType) : Int64;
function GetVoucherStateName( astate : Integer) : string;
function GetVoucherValue(voucher_id : Int64) : Currency;
function GetVoucherActualAmount( wt : IWTran; aid_voucher: Int64 ): Currency;
function GetVoucherBarcode(voucher_id : Int64) : String;
function GetVoucherEndDate(start_date : TDatetime) : TDatetime;
function GetVoucherED(id: Int64) : TDatetime;
procedure BlockVoucher(wt : IWTran; aid_vouchers : Int64);
procedure GetDSVoucherList( astate:TVoucherState;
  aDSVoucherList : TDSVoucherDataList;
  aNeedShowSpent : boolean;
  aIgnoreFilters : boolean  = false );
procedure PrintDPTicket( aid_voucher : Int64 = 0);
procedure PrintDPBill(id_voucher: Int64; storno : boolean);
procedure PrintVoucherTicket( aid_voucher : Int64 = 0);
function GetVoucherByPBill(wt : IWTran; aid_pbill : Int64) : Int64;
procedure DeleteReturnVoucherID;
procedure SetReturnVoucherID(aid_pbill : Int64);
function DefaultBarcodeLength : Integer;
function GetActiveVoucherID(abarcode: String; VCH_TYPE : Integer = 0 ): Int64;

implementation
uses BLRAlgPaymentU, CurrencyU, PSP_VeriFone, PSP_VicU,
  RegClassesU, BLRPrintU, ClientDepositsU, PrinterCommU, PrintUtilsU, TicketProcU,
  PosPrintThreadU, DateUtils, JournalLogsU, BLRAlgSplitBillU, ClientsU,
  RestaurantJLogU, BarCodesU, SystemDataProviderU, NumbersU, NumberPostPonerU,
  BLRClientU, ClientDataProviderU, UBLU, FiscalDataU, DevicesU,
  CustomBillHandlersU, CommonStringsU, ClientEmailU;

function DefaultBarcodeLength : Integer;
begin
  result := QR_BARCODE_LENGTH_CODE;
  if (POSRestaurantSettings.Settings.ExtraSettings.VoucherType=Ord(vctUntill)) then begin
    if POSRestaurantSettings.Settings.ExtraSettings.VoucherBarcodeType=0 then
      result := VOUCHER_BARCODE_LENGTH_EAN
    else
      result := VOUCHER_BARCODE_LENGTH_CODE39
  end;
end;

function GetPaymentData(ip : IUntillPayment;
  TotalPrice, TotalCurrencyPrice: Currency; aEFTParams: String): TPaymentData;
begin
  result.Reopen := False;
  result.Payment := ip;
  result.ReopenedPbillPaymentId := 0;
  result.ReopenedPbillId := 0;
  result.Amount := TotalPrice;
  result.CurrencyAmount := TotalCurrencyPrice;
  result.EftPaymentData.EFTData := aEFTParams;
  result.ReopenTipsAmount := 0;
  result.ClientId := 0;
  result.AccountType := 0;
  result.PayerName := '';
  result.FullPayment := true;
end;

procedure ProcessCashdroPayment(wt: IWTran; PData: TPaymentData; vpNumber : string);
var
  Payment: IUntillPayment;
  conf: TCashdroDeviceConfiguration;
  ctx: TCashdroPaymentContext;
  dev: TCashdroLink;
  cmd: TCashMachineDriver;
begin
  Payment := GetPayment(upos.Untilldb, PData.Payment.id);
  if Payment.driver_id <> '' then begin
    conf := upos.CashMachineInterfaces.GetConfig(Payment.driver_id);
    cmd := TCashMachineDriver.Create();
    try
      cmd.DriverId := conf.Params.ValByName[conf.PARAM_DRIVER_ID];
      cmd.DriverParams := conf.Params.ValByName[conf.PARAM_DRIVER_PARAMS];
      if conf.Params.GetIndexOf(conf.PARAM_RECEIPT_GUID) > -1 then
        cmd.ReceiptGuid := conf.Params.ValByName[conf.PARAM_RECEIPT_GUID];
      cmd.UntillDb := upos.untillDb;

      if PData.Reopen then
        cmd.PosReturn(PData.CurrencyAmount, vpNumber)
      else
        cmd.PosPayment(PData.CurrencyAmount, vpNumber);

      case cmd.Result of
        trCancelled:
          Plugin.RaisePosTranslatedException(StrOperationCancelled)
        else begin
          if cmd.ReceiptGuid <> '' then
            PrintCashmachineReceipt(cmd.GetTransactionDataVars, cmd.ReceiptGuid,
                NewJDetails(jeCashmachineReceipt, TRJDSubtype.CASHMACHINE_RECEIPT_PRINTED, upos.UserId));
          if cmd.Result = trOk then
            cmd.SavePaymentInfo(wt, 0, PData.VoucherPbillPaymentId, 0)
          else
            Plugin.RaisePosTranslatedException(StrOperationDeclined)
        end;
      end;
    finally
      cmd.Free;
    end;
  end else if (Payment.psp_model >= 0) then begin
    assert( not PData.Reopen, 'Reopen not allowed for Cashdro payments');
    conf := TCashdroUtils.GetConfigForCurrentPC(Payment.psp_model, true);
    try
      ctx := TCashdroPaymentContext.Create;
      try
        ctx.Bill  := nil;
        ctx.conf  := conf;
        ctx.PData := PData;
        ctx.Tran  := wt;
        dev := TCashdroUtils.CreateLink( conf );
        try
          dev.Execute(ctx);
        finally
          dev.Free;
        end;
      finally
        FreeAndNil(ctx);
      end;
    finally
      FreeAndNil(conf);
    end;
  end;
end;

function GetActiveVoucherID(abarcode: String; VCH_TYPE : Integer = 0 ): Int64;
var iq : IIBSQL;
begin
  result := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select id from vouchers '
    + ' where is_active=1 and vch_barcode=:vch_barcode and state=:state and coalesce(VCH_TYPE, 0) = :VCH_TYPE');
  iq.q.ParamByName('vch_barcode').AsString  := abarcode;
  iq.q.ParamByName('VCH_TYPE').AsInteger    := VCH_TYPE;
  iq.q.ParamByName('state').AsInteger       := ord( vchsActive );
  iq.ExecQuery;
  if iq.eof then exit;
  result := StrToInt64Def(iq.q.fields[0].asString,0);
end;

function GetVoucherID(_barcode: String): Int64;
var iq : IIBSQL;
begin
  result := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select first 1 id '
    + ' from vouchers '
    + ' where vch_barcode=:vch_barcode'
    + ' order by VCH_DT desc');
  iq.q.ParamByName('vch_barcode').AsString     := _barcode;
  iq.ExecQuery;
  if iq.eof then exit;
  result := StrToInt64Def(iq.q.fields[0].asString,0);
end;

function CreateDriverRequest(db: TUntillDB; DriverId: String; items: TDictionary<String, Currency>; ip: IUntillPayment; amount: Currency): VoucherBillClosingRequest;
var vcha: ArrayOfVoucher;
    pmi: ArrayOfPaymentItem;
    i: integer;
    code: String;
    ci: TCurrencyInfo;
begin

  result := nil;
  if ip=nil then exit;

  SetLength(vcha, items.count);
  SetLength(pmi, 1);
  i:=0;
  for code in items.Keys do begin
    vcha[i]:= Voucher.Create;
    vcha[i].id := code;
    vcha[i].amount := CurrencyToXSDecimal(items.Items[code]);
    inc(i);
  end;
  pmi[0] := PaymentItem.Create;
  pmi[0].paymentId := ip.id;

  ci := CurrencyFromPosPayment(db, ip, DriverId);
  pmi[0].currencyCharCode := ci.CharCode;
  pmi[0].currencyDigitalCode := ci.DigCode;
  pmi[0].paymentKind := ip.kind;
  pmi[0].paymentName := ip.name;
  pmi[0].paymentNumber := ip.number;
  pmi[0].amount := CurrencyToXSDecimal(amount);

  result:=VoucherBillClosingRequest.Create;
  result.Vouchers := vcha;
  result.payments := pmi;
end;


procedure BlockVoucher(wt : IWTran; aid_vouchers : Int64);
var qUpd : IIBSQL;
begin
  if aid_vouchers<=0 then exit;
  assert(assigned(wt));

  qUpd := wt.GetPreparedIIbSql ('update vouchers set state =:vchsBlocked where id=:id');
  qUpd.q.ParamByName('id').AsInt64 := aid_vouchers;
  qUpd.q.ParamByName('vchsBlocked').AsInteger := Ord(vchsBlocked);
  qUpd.ExecQuery;

end;

procedure SetReturnVoucherID(aid_pbill : Int64);
var id_vouchers : Int64;
begin
  upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  id_vouchers := GetVoucherByPBill(nil, aid_pbill);
  if id_vouchers<=0 then exit;

  if id_vouchers > 0 then
    upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID] := TInt64.Create( id_vouchers );
end;

procedure DeleteReturnVoucherID;
begin
  upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
end;

function GetVoucherByPBill(wt : IWTran; aid_pbill : Int64) : Int64;
var qSel : IIBSQL;
begin
  result := 0;
  if aid_pbill<=0 then exit;

  if wt=nil then
    wt := upos.UntillDB.getWTran;
  qSel := wt.GetPreparedIIbSql('select distinct vpp.id_vouchers '
    + ' from VOUCHER_PBILL_PAYMENTS vpp'
    + ' join PBILL_PAYMENTS on PBILL_PAYMENTS.id=vpp.ID_PBILL_PAYMENTS'
    + ' where PBILL_PAYMENTS.id_pbill=:id_pbill'
    );
  qSel.q.ParamByName('id_pbill').AsInt64 := aid_pbill;
  qSel.ExecQuery;
  if qSel.eof then exit;

  result := StrToInt64Def(qSel.FieldByName('id_vouchers').asString,0);
end;

function GetVoucherStateName( astate : Integer) : string;
begin
  result := plugin.Translate('BLRVouchersU', 'Not paid');
  if astate = ord(vchsActive) then
    result := plugin.Translate('BLRVouchersU', 'Paid');
  if astate = ord(vchsSpent) then
    result := plugin.Translate('BLRVouchersU', 'Spent');
  if astate = ord(vchsBlocked) then
    result := plugin.Translate('BLRVouchersU', 'Blocked');
end;

function GetLastBarcodeFromRange : Integer;
var qSel   : IIBSQL;
    strSQL : string;
    newNum : Integer;
begin
  result := 0;
  with POSRestaurantSettings.Settings.ExtraSettings do begin
    if (VoucherNumFrom >= VoucherNumTo) or (VoucherNumFrom = 0) then exit; // the range is not defined

      strSQL := 'select first 1 VCH_BARCODE from vouchers'
        + ' where is_active=1 and '
        + ' CASE '
          + ' WHEN VCH_BARCODE SIMILAR TO ''[0-9]+'' THEN CAST(VCH_BARCODE AS bigint) '
          + ' ELSE 0 '
        + ' END between :VoucherNumFrom and :VoucherNumTo'
      + ' order by case when VCH_BARCODE similar to ''[0-9]+'' then cast(VCH_BARCODE as bigint) else 0 end desc, id desc';

    qSel := upos.UntillDB.GetPreparedIIbSql( strSQL );
    qSel.q.ParamByName('VoucherNumFrom').AsInteger:= VoucherNumFrom;
    qSel.q.ParamByName('VoucherNumTo').AsInteger  := VoucherNumTo;
    qSel.ExecQuery;
    if qSel.eof then begin
      result := VoucherNumFrom;
      exit;
    end;
    newNum := qSel.fields[0].asInteger + 1;

    if newNum > VoucherNumTo then
      plugin.RaisePosException('Voucher range %d to %d is used up completely. Set a new range to create vouchers',
        [VoucherNumFrom, VoucherNumTo]);

    result := newNum;
  end;
end;

procedure CheckNumberRange( aQty : Integer );
var NewMaxNumber : Integer;
    LastNum      : Integer;
begin
  with POSRestaurantSettings.Settings.ExtraSettings do begin
  if (VoucherType <> Ord(vctPredefined)) or (VoucherNumFrom = 0) then exit;
  LastNum := GetLastBarcodeFromRange;

  if LastNum = 0 then
    plugin.RaisePosException( 'Voucher range %d to %d is exceeded, last number is %d. Set a new range or reduce the number of multi-vouchers',
      [VoucherNumFrom, VoucherNumTo, VoucherNumTo] );

  NewMaxNumber := LastNum + aQty;

  if NewMaxNumber > POSRestaurantSettings.Settings.ExtraSettings.VoucherNumTo then
    plugin.RaisePosException( 'Voucher range %d to %d is exceeded, last number is %d. Set a new range or reduce the number of multi-vouchers',
      [VoucherNumFrom, VoucherNumTo, NewMaxNumber] );

  end;
end;

procedure CheckVoucherPayment( aip : IUntillPayment );
begin
  if (aip.kind=PAYMENT_ACCOUNT) and (StrToInt64Def(ClientDataProvider.ClientID,0)=0) then
    plugin.RaisePosException('Client not defined');
  if aip.kind=PAYMENT_ROOM then
    plugin.RaisePosException('Voucher cannot be paid by Hash payment');
  if aip.kind=PAYMENT_SC then
    plugin.RaisePosException('Voucher cannot be paid by Service charge payment');
  if aip.kind=PAYMENT_DISCOUNT then
    plugin.RaisePosException('Voucher cannot be paid by Discount payment');
  if aip.kind=PAYMENT_VOUCHER then
    plugin.RaisePosException('Voucher cannot be paid by Voucher payment');
end;

procedure SaveJournalLog( aJournalData : TVoucherPaymentJournalData );
begin
  JournalLog.Log(upos.UntillDB, NewJDetails(jeMisc, TRJDSubtype.VOUCHER_ISSUED, upos.UserId)
      .AddData(TRJDetails.KEY_BARCODE, trim(aJournalData.BarCode))
      .AddData(TRJDetails.KEY_AMOUNT, CurrToStr(aJournalData.amount, FmtSettings))
      .AddData(TRJDetails.KEY_PAYMENT_MODE, aJournalData.ipname)
      .AddData(TRJDetails.KEY_EXPIRATION_DATE, aJournalData.EndDate.Format('DD.MM.YYYY'))
      );
end;

function IsVoucherField(FieldName: String): Boolean;
var i: integer;
begin
  result := false;
  for i:=0 to Length(DEPOSIT_CARD_PAYMENTS_INFO_FIELDS)-1 do
    if SameText(DEPOSIT_CARD_PAYMENTS_INFO_FIELDS[i], FieldName) then begin
      result := true;
      break;
    end;
end;

function GetVoucherBarcode(voucher_id : Int64) : String;
var qSel : IIBSQL;
begin
  result := '';
  qSel := upos.UntillDB.GetPreparedIIbSql('select VCH_BARCODE from vouchers where id=:id '
    + ' and is_active=1');
  qSel.q.ParamByName('id').AsInt64 := voucher_id;
  qSel.ExecQuery;
  if qSel.eof then exit;
  result := qSel.q.fields[0].asString;
end;

function GetVoucherValue(voucher_id : Int64) :Currency;
var qSel : IIBSQL;
begin
  result := 0;
  qSel := upos.UntillDB.GetPreparedIIbSql('select VCH_AMOUNT from vouchers '
    + ' where id=:id and state=:state and is_active=1');
  qSel.q.ParamByName('id').AsInt64 := voucher_id;
  qSel.q.ParamByName('state').AsInteger := Ord( vchsActive );
  qSel.ExecQuery;
  if qSel.eof then exit;
  result := qSel.q.fields[0].asCurrency;
end;

function GetVoucherActualAmount( wt : IWTran; aid_voucher : Int64) : Currency;
var qSel : IIBSQL;
    bNeedCommit : boolean;
begin
  result := 0;
  if aid_voucher<=0  then exit;

  bNeedCommit := false;
  if not assigned(wt) then begin
    wt := upos.UntillDB.getWTran;
    bNeedCommit := true;
  end;

  qSel := wt.GetPreparedIIbSql('select ORIG_VCH_AMOUNT - coalesce(sum(pbill_payments.price),0) '
    + ' from vouchers '
    + ' left outer join voucher_payments on voucher_payments.ID_VOUCHERS = vouchers.id'
    + ' left outer join pbill_payments on voucher_payments.ID_PBILL_PAYMENTS = pbill_payments.id'
    + ' where vouchers.id=:id'
    + ' group by ORIG_VCH_AMOUNT');
  qSel.q.ParamByName('id').AsInt64 := aid_voucher;
  qSel.ExecQuery;
  if qSel.eof then exit;

  result := qSel.fields[0].asCurrency;

  if bNeedCommit then wt.commit;

end;

procedure SendUpdateVoucherMsg;
var e: TVoucherListEvent;
begin
  e:=TVoucherListEvent.Create(0, '', uetUpdate);
  try
    upos.un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure ProcessCardPayment(wt : IWTran; anv : INamedVars;
  aPaymentID : Int64; aAmount : Currency; aNumber : String; EFTParams: String);
var orderid: String;
    psp: TCustomPSP;
    cinfo: TCurrencyInfo;

begin
  assert(assigned(wt));

  orderid := aNumber;
  cinfo:=upos.MainCurrency;

  psp := PspCreateDevice(upos.UntillDB, aPaymentId, upos.GetUserID);
  if psp<>nil then begin
    try
      try

        if Length(EFTParams) > 0 then
          psp.PspParams := EFTParams;

        psp.DoRequest(Abs(SimpleRoundTo(aAmount, -cinfo.Round)),
               0,
               cinfo,
               upos.GetPOSNow,
               0,
               orderid,
               pspPayment);

        psp.SavePaymentData(anv, TSaveDataOptions.Create(False, False));
        HandleEFTReceipt(psp);
      except
        on e: exception do begin
          CheckDeclinedTransaction(psp, e);
          raise;
        end;
      end;
    finally
      freeandnil(psp);
    end;
  end;
end;

procedure ProcessAccountPayments( wt : IWTran; aid_acc, aid_clients: Int64;
  aBarcode : String; aprice : Currency );
var qIns : IIBSQL;
begin
  if aid_acc=0 then exit;

  CheckClientBalance(wt, aid_clients);

  qIns := wt.GetPreparedIIbSql('insert into accounts_payment_items '
    + ' ( id_account_payments, rowbeg, '
    + ' quantity, vat, single_price,  vat_percent, '
    + ' original_price, original_total, text) '
    + ' values ( :id_account_payments, 1, '
    + ' 1, 0, :single_price, 0, :original_price, '
    + ' :original_total, :text)');

    qIns.q.ParamByName('id_account_payments').asInt64 := aid_acc;
    qIns.q.ParamByName('text').asString               := aBarcode;
    qIns.q.ParamByName('single_price').asCurrency     := aprice;
    qIns.q.ParamByName('original_price').asCurrency   := aprice;
    qIns.q.ParamByName('original_total').asCurrency   := aprice;
    qIns.ExecQuery;
end;

function ProcessAccountPaymentBill(wt: IWTran;
  aid_clients : Int64;
  aTotal : Currency;
  aAccountType : Integer;
  aVpNumber : Integer; aVpSuffix : String) : Int64;

var q : IIBSQL;
    id_acc: Int64;
begin
  if (aid_clients <= 0)  then
    Plugin.RaisePosException('Client not defined');

  CheckClientExpired( upos.UntillDB, aid_clients );

  // *************** SAVE HISTORY ***************
  q := wt.GetPreparedIIbSql( 'insert into accounts_payments (id, datetime, tr_datetime, id_clients, '
    +' id_untill_users, tableno, tablepart, number, name, suffix, bill_number, parts_count, '
    + ' part_index, discount, id_sales_area, total, account_type, payer_name, id_pbill_payments) '
    + ' values(:id, :datetime, :tr_datetime, :id_clients, :id_untill_users, :tableno, '
    + ' :tablepart, :number, :name, :suffix, :bill_number, :parts_count, :part_index, '
    + ' :discount, :id_sales_area, :total, :account_type, :payer_name, :id_pbill_payments)');
  id_acc := wt.GetID;
  q.q.ParamByName('id').asInt64 := id_acc;
  q.q.ParamByName('id_sales_area').Clear;
  q.q.ParamByName('tr_datetime').AsDatetime := upos.GetPosNow;
  q.q.ParamByName('datetime').AsDatetime    := upos.GetPOSNow;
  q.q.ParamByName('id_pbill_payments').Clear;
  q.q.ParamByName('id_clients').asInt64 := aid_clients;
  q.q.ParamByName('id_untill_users').asInt64 := upos.GetUserID;
  q.q.ParamByName('tableno').Clear;
  q.q.ParamByName('tablepart').Clear;
  q.q.ParamByName('number').asInteger := aVpNumber;
  q.q.ParamByName('name').Clear;
  q.q.ParamByName('suffix').asString := aVpSuffix;
  q.q.ParamByName('parts_count').asInteger := 1;
  q.q.ParamByName('part_index').asInteger := 1;
  q.q.ParamByName('discount').Clear;
  q.q.ParamByName('total').AsCurrency := aTotal;
  q.q.ParamByName('account_type').AsInteger := aAccountType;
  q.q.ParamByName('payer_name').Clear;
  q.q.ParamByName('payer_name').AsString   := GetClientName(aid_clients);
  q.q.ParamByName('bill_number').asInteger := aVpNumber;
  q.q.ExecQuery;

  result := id_acc;

end;

function GetVoucherED(id: Int64) : TDatetime;
var iq : IIBSQL;
begin
  result := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select end_date from vouchers where id=:id');
  iq.q.Params[0].AsInt64 := id;
  iq.ExecQuery;
  if iq.eof then exit;
  result := iq.q.fields[0].asDatetime;
end;

function GetVoucherEndDate(start_date : TDatetime) : TDatetime;
var vperiod, tperiod : Integer;
begin
  vperiod := POSRestaurantSettings.Settings.ExtraSettings.VoucherValidPeriod;
  tperiod := POSRestaurantSettings.Settings.ExtraSettings.VoucherPeriodType;
  if vperiod<=0 then
    vperiod := 100;
  if tperiod<0 then
    tperiod := 0;

  if tperiod=0 then
    result := IncDay(start_date, vperiod)
  else if tperiod=1 then
    result := IncWeek(start_date, vperiod)
  else if tperiod=2 then
    result := IncMonth(start_date, vperiod)
  else
    result := IncYear(start_date, vperiod)
end;

procedure DoPrintVoucherTicket( aidPrinter, aidLayout : Int64 );
var ticket: TMemoryStream;
    script: TPrintScript;
    task:TPrintTask;
    plain, ticketGuid, cf: String;
begin
  if (aidPrinter = 0) or (aidLayout = 0) then exit;

  UPos.SetTicketsLang(GetLangOfService(upos.UntillDB));
  ticket:=LoadTicket(upos.UntillDB, aidLayout, ticketGuid, cf);
  try
    script := TPrintScriptFactory.MakeScript(ticket, upos.GetUserId, upos.UntillDB, upos.NamedParams, nil, plain);
    try
      if (assigned(posAlg))and(posAlg.bWriteStateData) then begin
        posAlg.WriteStateData(POSALG_WDSOURCE_POSTICKET
        , 'Printer=' + GetPrinterName(upos.UntillDB, aidPrinter)
        + ': TicketId=' + IntToStr(aidLayout)
        + chr(13) + chr(10) + plain);
      end;

      task:=TPrintTask.Create(nil);
      task.PrinterId      := aidPrinter;
      task.TicketGUID     := ticketGuid;
      task.Caption:=Plugin.Translate('ClientsU', 'Voucher print task');
      task.PosId := TUntillPos.GetPCPosId(False);
      task.AddPrintScript(script);
      PosPrintThread.AddTaskToQueue(task);
    finally
      FreeAndNil(script);
    end;
  finally
    FreeAndNil(ticket);
  end;
end;

procedure PrintDPTicket( aid_voucher : Int64 = 0);
var idPrinter, idLayout : int64;
begin
  idPrinter := POSRestaurantSettings.Settings.DPPrinter;
  idLayout  := POSRestaurantSettings.Settings.DPLayout;

  upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  try
    if aid_voucher > 0 then
      upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID] := TInt64.Create( aid_voucher );
    DoPrintVoucherTicket( idPrinter, idLayout );
  finally
    upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  end;
end;

procedure PrintVoucherTicket( aid_voucher : Int64 = 0);
var idPrinter, idLayout : int64;
begin
  idPrinter := POSRestaurantSettings.Settings.VoucherPrinter;
  idLayout  := POSRestaurantSettings.Settings.VoucherLayout;

  upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  try
    if aid_voucher > 0 then
      upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID] := TInt64.Create( aid_voucher );
    DoPrintVoucherTicket( idPrinter, idLayout );
  finally
    upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  end;
end;

procedure PrintVoucherTicketBunch;
var idPrinter, idLayout : int64;
begin
  idPrinter := POSRestaurantSettings.Settings.VoucherPrinter;
  idLayout  := POSRestaurantSettings.Settings.MultiVoucherLayout;

  DoPrintVoucherTicket( idPrinter, idLayout );

end;

function DoSaveAccountData(wt : IWtran; Amount : Currency; VpNumber : Integer; VpSuffix : string) : Int64;
var iqAcc : IIBSQL;
    acc_type  : Integer;
    id_client : Int64;
begin
  id_client := StrToInt64Def(ClientDataProvider.ClientID,0);
  iqAcc := upos.UntillDB.GetPreparedIIbSql('select ACCOUNT_TYPE from ACCOUNTS '
    + ' join clients on clients.id_accounts=accounts.id'
    + ' where clients.id=:id_clients');
  iqAcc.q.Params[0].AsInt64 := id_client;
  iqAcc.ExecQuery;
  acc_type := 0;
  if not iqAcc.Eof then
    acc_type := iqAcc.Fields[0].asInteger;

  result := ProcessAccountPaymentBill(wt, id_client, Amount, acc_type, VpNumber, VpSuffix);
end;

procedure PrintDPBill(id_voucher: Int64; storno : boolean);
var idPrinter, idLayout : int64;
begin
  if storno then
    upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID_REFUND] := TInt64.Create(id_voucher)
  else
    upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID] := TInt64.Create(id_voucher);
  try
    idPrinter := POSRestaurantSettings.Settings.BillPrinter;
    idLayout  := POSRestaurantSettings.Settings.DPBillLayout;

    DoPrintVoucherTicket( idPrinter, idLayout );
  finally
  if storno then
    upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID_REFUND)
  else
    upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  end;
end;

procedure PrintVoucherBill();
var idPrinter, idLayout : int64;
begin
  idPrinter := POSRestaurantSettings.Settings.BillPrinter;
  idLayout  := POSRestaurantSettings.Settings.VoucherBillLayout;

  DoPrintVoucherTicket( idPrinter, idLayout );
end;

procedure ProcessPbillVoucherPayments(
  wt : IWTran;
  aid_pbill         : Int64;
  aid_vouchers      : Int64;
  apayments_list    : TObjectList<TVCHPaymentData>;
  aid_acc : Int64;
  barcode: String;
  aEFTParams : String;
  vpNum      : string);
var ip    : IUntillPayment;
    pd    : TVCHPaymentData;
    nv    : INamedVars;
    qIns  : IIBSQL;
    pdata         : TPaymentData;
    id    : Int64;
begin
  if apayments_list.count = 0 then exit;
  if aid_vouchers = 0 then exit;
  if aid_pbill = 0 then exit;

  for pd in apayments_list do begin

    ip := GetPayment(upos.UntillDB, pd.id_payments);
    if assigned(ip) then begin
        id := wt.getID;
        qIns := wt.GetPreparedIIbSql('insert into voucher_pbill_payments'
        + ' (id, id_voucher_pbill, price, id_vouchers, id_payments, id_pbill_payments)'
        + ' values (:id, :id_voucher_pbill, :price, :id_vouchers, :id_payments, :id_pbill_payments)');
        qIns.q.ParamByName('id').asInt64  := id;
        qIns.q.ParamByName('id_voucher_pbill').asInt64  := aid_pbill;
        qIns.q.ParamByName('price').asCurrency          := pd.price;
        qIns.q.ParamByName('id_vouchers').asInt64       := aid_vouchers;
        qIns.q.ParamByName('id_payments').asInt64       := pd.id_payments;
        if pd.id_pbill_payment = 0 then
          qIns.q.ParamByName('id_pbill_payments').Clear
        else
          qIns.q.ParamByName('id_pbill_payments').asInt64 := pd.id_pbill_payment;
        qIns.ExecQuery;

        if (ip.kind = PAYMENT_CARD) then begin
          nv := newNv;
        end else if (ip.kind = PAYMENT_ACCOUNT) then begin
          if aid_acc<=0 then exit;
          ProcessAccountPayments( wt, aid_acc, StrToInt64Def(ClientDataProvider.ClientID,0), barcode, pd.price );
        end else if (ip.kind = PAYMENT_CASHDRO )then begin
          pdata := GetPaymentData(ip, pd.price, pd.price, aEFTParams);
          pdata.VoucherPbillPaymentId := id;
          pdata.PbillPaymentId        := 0;
        	ProcessCashdroPayment(WT, pdata, vpNum);
        end;
    end;
  end;

end;

function CreateVoucherPBill( wt : IWtran) : Int64;
var qIns    : IIBSQL;
    vchNumber : TNumber;
    npp     : TNumberPostPoner;
begin
  assert(assigned(wt));

  vchNumber := ReserveNumber(upos.UntillDB, VCH_BILL_NUMBER_NAME, upos.UntillDB.HandHeldId, 0, upos.ServerOne);

  npp := TNumberPostPoner.Create([thAfterRollback]);
  npp.vchnumber := vchNumber;
  npp.db := upos.UntillDB;
  (wt as IWTranRegisterHook).RegisterHook(npp);

  result := wt.GetID;
  qIns := wt.GetPreparedIIbSql('insert into voucher_pbill'
    + '(id, number, FAILUREDNUMBER, SUFFIX, pdatetime, id_untill_users, pcname, id_clients)'
    + 'values(:id, :number, :FAILUREDNUMBER, :SUFFIX, :pdatetime, :id_untill_users, :pcname, :id_clients)');
  qIns.q.ParamByName('id').AsInt64                := result;
  qIns.q.ParamByName('number').AsInteger          := vchNumber.Number;
  qIns.q.ParamByName('FAILUREDNUMBER').AsInteger  := vchNumber.FailuredNumber;
  qIns.q.ParamByName('SUFFIX').AsString           := vchNumber.suffix;
  qIns.q.ParamByName('pdatetime').AsDatetime      := upos.GetPOSNow;
  qIns.q.ParamByName('id_untill_users').AsInt64   := upos.GetUserID;
  qIns.q.ParamByName('pcname').asString           := upos.POSComputerName;
  if StrToInt64Def(ClientDataProvider.ClientID,0) = 0  then
    qIns.q.ParamByName('id_clients').Clear
  else
    qIns.q.ParamByName('id_clients').asInt64 := StrToInt64Def(ClientDataProvider.ClientID,0);
  qIns.ExecQuery;

end;

procedure GetVoucherPBillNumber(wt : IWTran; aid_pbill : Int64;
  var vpNumber : Integer; var vpSuffix : string);
var qSel : IIBSQL;
begin
  assert(assigned(wt));
  vpNumber := 0;
  vpSuffix := '';
  if aid_pbill <= 0 then exit;

  qSel := wt.GetPreparedIIbSql('select number, suffix from VOUCHER_PBILL where id=:id');
  qSel.q.ParamByName('id').AsInt64 := aid_pbill;
  qSel.ExecQuery;
  if qSel.eof then exit;

  vpNumber := qSel.FieldByName('number').asInteger;
  vpSuffix := qSel.FieldByName('suffix').asString;
end;

function SaveVoucher(
  wt            : IWTran;
  aid_pbill     : Int64;
  avoucherData   : TVoucherData;
  aid_payments  : Int64;
  aPrint        : boolean;
  aid_clients   : Int64;
  aEFTParams    : String;
  AskEmail      : boolean;
  vch_type      : TUntillVoucherType) : Int64;
var iq : IIBSQL;
    ip : IUntillPayment;
    id_vouchers : Int64;
    dt : TDatetime;
    endDate: IUntillDateTime;
    vState : TVoucherState;
    ipname : String;
    jd : TVoucherPaymentJournalData;
    nv : INamedVars;
    payments_list : TObjectList<TVCHPaymentData>;
    bc : String;
    vpNumber : Integer;
    vpSuffix : String;
    id_acc   : Int64;
    bNeedCommit :boolean;
    req: VoucherBillClosingRequest;
    items: TDictionary<String, Currency>;
begin
  result := 0;

  items:=TDictionary<String, Currency>.Create;
  try
    if avoucherData.amount <= 0 then
      plugin.RaisePosException('Please enter correct Voucher amount');

    bc     := trim(avoucherData.BarCode);
    ipname := '';
    if aid_payments > 0 then begin // save Voucher fully paid
      vState := vchsActive;
      ip := GetPayment(upos.UntillDB, aid_payments);
      ipname := ip.name;

      CheckVoucherPayment( ip );
    end else // not paid yet
      vState := vchsInit;

    if wt = nil then begin
      bNeedCommit := true;
      wt := upos.UntillDB.getWTran;
    end else
      bNeedCommit := false;
    iq := wt.GetPreparedIIbSql('select count(*) from vouchers '
      + ' where vch_barcode=:bc and is_active=1 '
      + ' and state in (:vchsActive, :vchsInit) and coalesce(VCH_TYPE, 0) = :vt');
    iq.q.ParamByName('bc').AsString := bc;
    iq.q.ParamByName('vchsActive').AsInteger  := Ord( vchsActive ); // Active or Init
    iq.q.ParamByName('vchsInit').AsInteger    := Ord( vchsInit ); // Active or Init
    iq.q.ParamByName('vt').AsInteger          := Ord(vch_type);
    iq.ExecQuery;
    if iq.q.Fields[0].AsInteger>0 then exit;

    id_vouchers := wt.GetID;
    iq := wt.GetPreparedIIbSql('insert into '
     + ' vouchers(id, vch_amount, vch_barcode, vch_dt, vch_pc_name, id_untill_users, id_payments, is_active, '
     + ' state, end_date, orig_vch_amount, vch_type, RES_TABLENO, VCH_RES_STATUS, RES_NAME)'
     + ' values(:id, :vch_amount, :vch_barcode, :vch_dt, :vch_pc_name, :id_untill_users, :id_payments, 1, '
     + ' :state, :end_date, :vch_amount, :vch_type, :RES_TABLENO, :VCH_RES_STATUS, :RES_NAME)'
     );
    dt := avoucherData.datetime;
    endDate := GetUntillDateTime(GetVoucherEndDate(dt));

    iq.q.ParamByName('id').AsInt64               := id_vouchers;
    iq.q.ParamByName('vch_type').AsInteger             := Ord(vch_type);
    iq.q.ParamByName('vch_amount').AsCurrency    := avoucherData.Amount;
    iq.q.ParamByName('vch_barcode').AsString     := bc;
    iq.q.ParamByName('vch_dt').AsDatetime        := dt;
    iq.q.ParamByName('vch_pc_name').AsString     := upos.POSComputerName;
    iq.q.ParamByName('id_untill_users').AsInt64  := upos.GetUserID;
    if aid_payments > 0 then
      iq.q.ParamByName('id_payments').AsInt64      := aid_payments
    else
      iq.q.ParamByName('id_payments').Clear;
    iq.q.ParamByName('end_date').AsDatetime      := endDate.AsDateTime;
    iq.q.ParamByName('state').AsInteger          := Ord( vState );
    iq.q.ParamByName('RES_TABLENO').AsInteger    := avoucherData.RES_TABLENO;
    iq.q.ParamByName('VCH_RES_STATUS').AsInteger := 0;
    iq.q.ParamByName('RES_NAME').AsString        := avoucherData.RES_NAME;
    iq.ExecQuery;

    if vState = vchsActive then begin
      if aid_pbill=0 then // Need create VoucherP_BILL
        aid_pbill := CreateVoucherPBIll(wt);
      payments_list := TObjectList<TVCHPaymentData>.Create;
      payments_list.Add(TVCHPaymentData.Create(aid_payments,
        avoucherData.Amount, aEFTParams, avoucherData.id_pbill_payment));
      try
        id_acc := 0;
        GetVoucherPBillNumber(wt, aid_pbill, vpNumber, vpSuffix);
        if (ip.kind = PAYMENT_ACCOUNT) then
          id_acc := DoSaveAccountData(wt, avoucherData.Amount, VpNumber, VpSuffix);
          ProcessPbillVoucherPayments( wt, aid_pbill, id_vouchers, payments_list, id_acc, bc, aEFTParams, IntToStr(vpNumber) + vpSuffix );
      finally
        FreeAndNil( payments_list );
      end;
      if (ip.kind = PAYMENT_CARD) and (avoucherData.id_pbill_payment = 0 { not paid already in pbill_payments} )then begin
        nv := newNv;
        ProcessCardPayment(wt, nv, aid_payments, avoucherData.Amount, IntToStr(id_vouchers), aEFTParams);
      end;
    end;

    if assigned(ip )then begin
      PosFiscalData.clear;
      items.add(avoucherData.BarCode, avoucherData.Amount);
      upos.CustomBillHandlers.Execute(procedure(cfg: TCustomBillsHandlerInterfaceConfiguration) begin
        req:=CreateDriverRequest(upos.UntillDB, cfg.DriverId,  items, ip, avoucherData.Amount);
        DoCustomBillsHandlingCall(upos.UntillDB, wt, cfg, req, FISCAL_OP_VOUCHER_BILL_CLOSING);
      end);
    end;

    if bNeedCommit then
      wt.commit;

    result := id_vouchers;

    jd.amount  := avoucherData.Amount;
    jd.ipname  := ipname;
    jd.endDate := endDate;
    jd.barcode := avoucherData.BarCode;
    SaveJournalLog( jd );

    if aPrint then begin
      if vch_type = uvtDP then begin
        PrintDPTicket( id_vouchers );
        PrintDPBill(id_vouchers, false);
      end else begin
        PrintVoucherTicket;
        PrintVoucherBill;
      end;
    end;
  finally
    FreeAndNil(items);
  end;
end;

{ TBLRGetVoucher }

function TBLRGetVoucher.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgNeedProlongVoucher then Exit;
  if msg = TBLRMsgNeedAdjustVoucher then Exit;
  if msg = TBLRMsgSendVoucherValue then Exit;
  if FKind = vckCreate then begin
    if msg = TBLSMsgChangeTimeRange  then Exit;
    if (POSRestaurantSettings.Settings.ExtraSettings.VoucherType=Ord(vctManual))
       and (FMode = vcmBarcode) then begin
      if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
      if msg = TBLMessageBackSpace then Exit;   // removes symbol from value or barcode
      if msg = TBLMessageClear then Exit;       // removes all symbols from value or barcode
      if msg = TBLMessageOK then Exit;       // adds symbol to value or barcode
    end else if FMode = vcmAmount then begin
      if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
      if msg = TBLMessageBackSpace then Exit;   // removes symbol from value or barcode
      if msg = TBLMessageClear then Exit;       // removes all symbols from value or barcode
      if msg = TBLRMsgPaymentMode then Exit;    // Link voucher to Payment and saves voucher in DB
      if msg = TBLRMsgNeedClient then Exit;   // client
      if msg = TBLMessageOK then Exit;
    end else begin
      if POSRestaurantSettings.Settings.ExtraSettings.VoucherNumFrom=0 then begin
        if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
        if msg = TBLMessageKeyboardString then exit; // Scan barcode
      end;
      if msg = TBLMessageOK then Exit;
    end;
  end else if FKind = vckPay then begin
    if msg = TBLMessageUndo  then Exit;
    if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
    if msg = TBLMessageBackSpace then Exit;   // removes symbol from value or barcode
    if msg = TBLMessageClear then Exit;       // removes all symbols from value or barcode
    if msg = TBLMessageOK then Exit;       // adds symbol to value or barcode
    if msg = TBLRMsgSendVoucher then Exit;
    if msg = TBLMessageKeyboardString then exit; // Scan barcode
  end else if FKind = vckProlong then begin
    if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
    if msg = TBLMessageBackSpace then Exit;   // removes symbol from value or barcode
    if msg = TBLMessageClear then Exit;       // removes all symbols from value or barcode
    if msg = TBLMessageOK then Exit;       // adds symbol to value or barcode
    if msg = TBLRMsgSendVoucher then Exit;
  end;
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRGetVoucher.Create(ABla: TBLAlg; AParent: TBLAlgStep;
  vchKind: TVoucherKind  = vckCreate);
begin
  inherited Create(ABla, AParent);
  ClientDataProvider.Clear;
  FNeedProcessPayment := false;
  FStartDate  := upos.GetPOSNow;
  InitNewVoucher( vchKind );
  SendUpdateVoucherMsg;
  if assigned(Upos_) then
    upos.un.RegisterListener(URL_REINIT_VOUCHER_NUMBER, Self);
end;

procedure TBLRGetVoucher.InitNewVoucher ( avchKind : TVoucherKind );
var manualNum : Integer;
begin
  FId_voucher := 0;
  FAmount     := '';
  FRestAmount := 0;
  FBarCode    := '';
  FBarCodeFilter := '';
  FVoucherAmountFilter := 0;
  beforeProlongType := vckCreate;
  FMode       := vcmAmount;
  FKind       := avchKind;
  with POSRestaurantSettings.Settings.ExtraSettings do begin
    if FKind = vckCreate then begin
      if (VoucherType=Ord(vctPredefined))
        or (VoucherType=Ord(vctManual)) then begin
          FMode := vcmBarCode;
          if VoucherType = Ord(vctPredefined) then begin
            manualNum := GetLastBarcodeFromRange;
            if manualNum > 0 then
              FBarCode := IntToStr(manualNum);
          end;
        end
      else
        FBarCode := GenerateBarCode(DefaultBarcodeLength);
    end else begin
      FMode := vcmBarCode;
    end;
  end;
end;

function TBLRGetVoucher.GetHint: widestring;
begin
  if FMode = vcmBarCode then
    Result := plugin.TranslatePOS('BLRAlgU', 'Enter voucher barcode', 'State hint')
  else
    Result := plugin.TranslatePOS('BLRAlgU', 'Enter voucher value', 'State hint');
end;

class function TBLRGetVoucher.GetStaticGuid: string;
begin
  result := 'b76c3898-8ecf-4bdd-892a-2519ab72f769';
end;

procedure TBLRGetVoucher.OnChildFinished(child: TBLAlgStep);
begin
  if child is TBLRSelectClient then begin
    self.FreeOnLastChild := false;
    exit;
  end;
  if child is TBLRAdjustVoucher then begin
    self.FreeOnLastChild := false;
    exit;
  end;
  if child is TBLRGetClientEmail then begin
    DoSendInvoice( ClientInfoProvider.invoice_email );
    self.FreeOnLastChild := false;
    exit;
  end;

  inherited;
  if IsEftCfgSelectionCompletionHandled(Self, Child) then
  	exit;
end;

procedure TBLRGetVoucher.OnInput(s: widestring);
var ss : String;
begin
  if FKind = vckCreate then begin
    if FMode=vcmBarCode then begin
      if Length(FBarCode) < DefaultBarcodeLength then
        if (CharInSet(s[1], ['a'..'z'])) or (CharInSet(s[1], ['A'..'Z'])) or (CharInSet(s[1], ['0'..'9'])) then
          FBarCode := FBarCode + s;
    end else begin
      if (Trim(s) = '.')  then s := FormatSettings.DecimalSeparator;
      ss := FAmount + s;
      if IsFloat(ss) then begin
        if StrToFloat(ss) < MAX_QUANTITY then
          FAmount := ss;
      end else
        FAmount := '0';
    end;
  end else begin
    FBarcodeFilter := FBarcodeFilter + s;
    UpdateVoucherList;
  end;
end;

procedure TBLRGetVoucher.UpdateVoucherList;
begin
  LoadVoucher(FBarcode);
  SendUpdateVoucherMsg;
end;

procedure TBLRGetVoucher.OnOK;
var vd : TVoucherData;
begin
  if (FKind = vckCreate) and (FMode=vcmBarCode) then begin
    if trim(FBarcode)='' then
      Plugin.RaisePosException('Barcode is empty','TBLRGetVoucher.ProcessKeybordString');
    if IsBarcodeExists(FBarcode) then
      Plugin.RaisePosException('Barcode already exists','TBLRGetVoucher.ProcessKeybordString');
    UntillApp.ClearChangeFlags;
    FMode := vcmAmount;
    UntillApp.AnalyseChangeFlags;
  end else begin
  // User clicks OK, but did not Pay it yet
    if (FKind = vckCreate) and (FMode=vcmAmount) and (fid_voucher=0) then begin
      vd.barcode  := FBarCode;
      vd.amount   := StrToCurrDef(FAmount,0);
      vd.datetime := FStartDate;
      vd.id_pbill_payment := 0;
      if SaveVoucher(nil, 0, vd, 0, true, 0, '', false, uvtRegular) = 0 then
        plugin.RaisePosException('Voucher with Barcode already exists');
      InitNewVoucher( FKind );
    end else
      inherited;
  end;
end;

procedure TBLRGetVoucher.OnUndo;
var id_payment : Int64;
begin
  if not assigned(blr.ActiveBill) then exit;
  if blr.ActiveBill.pbill_payments.CurRow < 0 then exit;

  blr.ActiveBill.pbill_payments.First;
  blr.ActiveBill.pbill_payments.MoveBy(blr.ActiveBill.pbill_payments.CurRow);
  id_payment := StrToInt64Def(blr.ActiveBill.pbill_payments.id_payments.AsString,0);
  if (GetPaymentKind(upos.UntillDB, id_payment) <> PAYMENT_VOUCHER) then exit;
  if blr.ActiveBill.pbill_payments.saved.asBoolean then exit;
  blr.ActiveBill.pbill_payments.Delete;
end;

procedure TBLRGetVoucher.OnUrlEvent(url: string; ue: TUrlEvent);
begin
  inherited;
  if url=URL_REINIT_VOUCHER_NUMBER then
    InitNewVoucher( FKind );
end;

procedure TBLRGetVoucher.ProcessKeybordString(msg: TBLMessageKeyboardString);
begin
  inherited;
  if FMode = vcmAmount then exit;
  UntillApp.ClearChangeFlags;
  FBarCode := TBLMessageKeyboardString(msg).s;
  if length(trim(FBarCode))=0 then
    Plugin.RaisePosException(StrInvalidBarcodeException,'TBLRGetVoucher.ProcessKeybordString');
  if FKind = vckCreate then begin
    if IsBarcodeExists(FBarCode) then
      Plugin.RaisePosException('Scanned barcode already exists','TBLRGetVoucher.ProcessKeybordString');
    FMode := vcmAmount;
  end else if FKind = vckPay then begin
    LoadVoucher(FBarCode);
    if (fid_voucher>0) and (FRestAmount>0) then begin
      FNeedProcessPayment := true;
      Posalg.SendOK;
    end;
  end else begin
    UpdateVoucherList;
    LoadVoucher(FBarCode);
  end;
  UntillApp.AnalyseChangeFlags;
end;

function TBLRGetVoucher.IsBarcodeExists(_barcode : String) : boolean;
var iq : IIBSQL;
begin
  iq := upos.UntillDB.GetPreparedIIbSql('select count(*) from vouchers '
    + ' where is_active=1 and vch_barcode=:vch_barcoderest '
    + ' and state<>:spent and coalesce(VCH_TYPE, 0) = 0');
  iq.q.ParamByName('vch_barcoderest').AsString := _barcode;
  iq.q.ParamByName('spent').AsInteger := Ord( vchsSpent );
  iq.ExecQuery;

  result := iq.q.fields[0].asInteger>0;
end;

procedure TBLRGetVoucher.LoadVoucher(barcode: string);
begin
  inherited;
  if fBarCode='' then exit;

  fid_voucher := GetActiveVoucherID(fBarCode);
  if blr.ActiveBill.IsVoucherUsed(fid_voucher, PAYMENT_VOUCHER) then
    Plugin.RaisePosException('Voucher is already reserved to pay','TBLRGetVoucher.ProcessKeybordString');
end;

destructor TBLRGetVoucher.Destroy;
begin
  if assigned(Upos_) then
    upos.un.UnRegisterListener(URL_REINIT_VOUCHER_NUMBER, Self);
  inherited;
end;

procedure TBLRGetVoucher.DoProlongVoucher(bc : String);
var iq, iqq : IIBSQL;
    oldEndDate, start_date, newEndDate : TDatetime;
    wt : IWTran;
    id : Int64;
begin
  iq := upos.UntillDB.GetPreparedIIbSql('select * from vouchers '
    + ' where vch_barcode=:barcode and is_active=1 and state=:state'
    + ' and coalesce(VCH_TYPE, 0) = 0');
  iq.q.ParamByName('barcode').AsString     := bc;
  iq.q.ParamByName('state').AsInteger := ord( vchsActive );
  iq.ExecQuery;
  if iq.eof then exit;

  id := StrToInt64Def(iq.q.fieldByName('id').AsString,0);
  start_date := upos.GetPosNow;
  newEndDate := GetVoucherEndDate(start_date);
  oldEndDate := iq.q.fieldByName('END_DATE').AsDatetime;

  wt := upos.UntillDB.getWTran;
  iq := wt.GetPreparedIIbSql ('update vouchers set END_DATE =:END_DATE where id=:id');
  iq.q.ParamByName('id').AsInt64 := id;
  iq.q.ParamByName('end_date').AsDatetime := newEndDate;
  iq.ExecQuery;

  iqq := wt.GetPreparedIIbSql('insert into voucher_prolong(id_vouchers, id_untill_users, '
    + ' prolong_dt, prev_dt, new_dt) '
    + ' values(:id_vouchers, :id_untill_users, :prolong_dt, :prev_dt, :new_dt)');
  iqq.q.ParamByName('id_vouchers').AsInt64 := id;
  iqq.q.ParamByName('id_untill_users').AsInt64 := upos.GetUserID;
  iqq.q.ParamByName('prolong_dt').AsDatetime := upos.GetPOSNow;
  iqq.q.ParamByName('prev_dt').AsDatetime := oldEndDate;
  iqq.q.ParamByName('new_dt').AsDatetime := newEndDate;
  iqq.ExecQuery;

  wt.commit;

end;

procedure TBLRGetVoucher.ProcessMessage(msg: TBLMessage);
var vd   : TVoucherData;
	pmsg: TBLRMsgPaymentMode;
  EFTParams: String;
begin
  inherited;
  if msg is TBLRMsgNeedClient then begin
    ClientDataProvider.ClientID := '0';
    TBLRSelectClient.Create(Self.bla, Self);
    exit;
  end;
  if msg is TBLRMsgNeedAdjustVoucher then begin
    TBLRAdjustVoucher.Create(bla, self, vchsActive);
    exit;
  end;
  if msg is TBLSMsgChangeTimeRange then begin
    UntillApp.ClearChangeFlags;
    with TBLSMsgChangeTimeRange(msg) do begin
      if Params.bChangeFromDate then
        ChangeTimeRange(Params, upos.GetPosTimeInfoFunc(), FStartDate);
    end;
    UntillApp.AnalyseChangeFlags;
    exit;
  end;
  if FKind = vckCreate then begin
    if msg is TBLRMsgNeedProlongVoucher then begin
      beforeProlongType := FKind;
      FKind := vckProlong;
      UpdateVoucherList;
    end else begin
      if FMode = vcmAmount then begin
        if msg is TBLMessageBackSpace then
          delete(FAmount, Length(FAmount) ,1)
        else if msg is TBLMessageClear then
          FAmount := '0'
        else if msg is TBLRMsgPaymentMode then begin    // Link voucher to Payment and saves voucher in DB

          if FKind = vckCreate then begin
          	pmsg := TBLRMsgPaymentMode(msg);

            if not IsEftParamsSpecified(Self.bla, Self, pmsg, 0, EFTParams) then
              exit;

            vd.barcode  := FBarCode;
            vd.amount   := StrToCurrDef(FAmount,0);
            vd.datetime := FStartDate;
            vd.id_pbill_payment := 0;
            fid_payments:= TBLRMsgPaymentMode(msg).id_payments;
            if SaveVoucher(nil, 0, vd, fid_payments, true, 0, EFTParams, false, uvtRegular) = 0 then
              plugin.RaisePosException('Voucher with Barcode already exists');

            if (GetPaymentKind(upos.UntillDB, fid_payments) = PAYMENT_ACCOUNT) and TBLRMsgPaymentMode(msg).EmailInvoice then begin
              if (ClientInfoProvider.INVOICE_EMAIL <> '') then
                DoSendInvoice( ClientInfoProvider.invoice_email );
            end;
            ClientDataProvider.Clear;

            Self.Free;
            exit;
          end;
        end;
      end else if FMode = vcmBarCode then begin
        if msg is TBLMessageBackSpace then
          delete(FBarcode, Length(FBarcode) ,1)
      end else begin
        if msg is TBLMessageBackSpace then
          delete(FBarcodeFilter, Length(FBarcodeFilter) ,1)
        else if msg is TBLMessageClear then
          FBarcodeFilter := '';
      end;
    end;
    exit;
  end else if FKind = vckPay then begin
    if msg is TBLRMsgNeedProlongVoucher then begin
      beforeProlongType := FKind;
      FKind := vckProlong;
      UpdateVoucherList;
    end else if msg is TBLMessageBackSpace then begin
      if Length(FBarcodeFilter)>0 then
        delete(FBarcodeFilter, Length(FBarcodeFilter) ,1);
      UpdateVoucherList;
    end else if msg is TBLRMsgSendVoucher then begin
      LoadVoucher(TBLRMsgSendVoucher(msg).barcode);
      FNeedProcessPayment := true;
      Posalg.SendOK;
      exit;
    end else if msg is TBLMessageClear then begin
      FBarcodeFilter       := '';
      FVoucherAmountFilter := 0;
      UpdateVoucherList;
    end else if msg is TBLRMsgSendVoucherValue then begin
      FVoucherAmountFilter := TBLRMsgSendVoucherValue(msg).value;
      UpdateVoucherList;
    end;
    exit;
  end else if FKind = vckProlong then begin
    if msg is TBLMessageBackSpace then
      delete(FBarcodeFilter, Length(FBarcodeFilter) ,1)
    else if msg is TBLMessageClear then
      FBarcodeFilter := ''
    else if msg is TBLRMsgSendVoucher then
      DoProlongVoucher(TBLRMsgSendVoucher(msg).barcode)
    else if msg is TBLRMsgNeedProlongVoucher then
      FKind := beforeProlongType
    else if msg is TBLMessageClear then begin
      FVoucherAmountFilter := 0;
    end else if msg is TBLRMsgSendVoucherValue then begin
      FVoucherAmountFilter := TBLRMsgSendVoucherValue(msg).value;
    end;
    UpdateVoucherList;
    exit;
  end;
end;

{ TBLRPayVoucher }

function TBLRPayVoucher.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLRMsgNeedClient then Exit;   // client
  if msg = TBLRMsgPaymentMode then Exit;   // payment
  Result := inherited CanAcceptMessage(msg);
end;


constructor TBLRPayVoucher.Create(ABla: TBLAlg; AParent: TBLAlgStep;
  bShowVouchers: boolean; anewvalue : currency);
begin
  inherited Create(ABla, AParent, bShowVouchers);
  ClientDataProvider.Clear;
  fnewvalue := anewvalue;
  fAfterAdjust := false;
end;

function TBLRPayVoucher.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Vouchers payment', 'State hint')
end;

class function TBLRPayVoucher.GetStaticGuid: string;
begin
  result := 'dd4e5651-e2ee-4aaf-a1b1-1a5d392c70e7';
end;

function TBLRPayVoucher.GetTotal: Currency;
var iq : IIBSQL;
    i  : Integer;
    sqlStr : string;
begin
  result := 0;
  if FvcList.Count=0 then exit;

  for i := 0 to Pred(FvcList.Count) do begin
    if fAfterAdjust then
      sqlStr := 'select ' + CurrToStr(fnewvalue) + ' - coalesce(sum(vpp.price),0) rest'
    else
      sqlStr := 'select ORIG_VCH_AMOUNT - coalesce(sum(vpp.price),0) rest';
    sqlStr := sqlStr  + ' from vouchers '
      + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
      + ' where vch_barcode=:barcode and is_active=1 and coalesce(VCH_TYPE, 0) = 0'
      + ' group by ORIG_VCH_AMOUNT';
    iq := upos.UntillDB.GetPreparedIIbSql( sqlStr );
    iq.q.paramByname('barcode').asString :=FvcList[i].barcode;
    iq.ExecQuery;
    if not iq.eof then
      result := result + iq.q.FieldByName('rest').asCurrency;
  end;
end;

procedure TBLRPayVoucher.OnChildFinished(child: TBLAlgStep);
begin
  if child is TBLRSelectClient then begin
    FreeOnLastChild := false;
    exit;
  end;

  inherited;
  if IsEftCfgSelectionCompletionHandled(Self, Child) then
  	exit;
end;

procedure TBLRPayVoucher.OnOK;
begin
  if (PayType = ipvtDelete) and (GetTotal<>0)  then
    plugin.RaisePosException('Please choose payment type');
  inherited;
end;

procedure TBLRPayVoucher.ProcessMessage(msg: TBLMessage);
var pmsg: TBLRMsgPaymentMode;
		EFTParams: String;
begin
  if msg is TBLRMsgNeedClient then begin
    ClientDataProvider.ClientID := '0';
    TBLRSelectClient.Create(Self.bla, Self);
    exit;
  end;
  try
    if msg is TBLRMsgPaymentMode then begin   // payment of voucher list
    	pmsg := TBLRMsgPaymentMode(msg);
      fid_payments := pmsg.id_payments;

      if not IsEftParamsSpecified(Self.bla, Self, pmsg, 0, EFTParams) then
        exit;

      PayVoucherList(EFTParams);
      if AfterAdjust and (vcList.Count=0) then
        self.Free;
      exit;
    end;
  finally
    SendUpdateVoucherMsg;
  end;
  inherited;
end;

procedure DeleteVocuhersInList( awt : IWTran; avcList : TVoucherShortDataList );
var i   : Integer;
    q, qSel : IIBSQL;
    qUpdAdj : IIBSQL;
    id      : Int64;
begin
  assert(assigned(awt));
  if avcList.count=0 then exit;

  for i := Pred( avcList.count ) downto 0 do begin

    qSel := awt.GetPreparedIIbSql('select * from vouchers'
     + ' where VCH_BARCODE=:barcode and is_active=1');
    qSel.q.ParamByName('barcode').AsString := avcList[ i ].barcode;
    qSel.ExecQuery;
    if qSel.eof then exit;

    id := StrToInt64Def(qSel.fields[0].asString,0);

    q := awt.GetPreparedIIbSql('update vouchers '
      + ' set is_active=0, IS_ACTIVE_MODIFIED=:dt '
      + ' where id=:id');
    q.q.ParamByName('dt').AsDatetime := upos.GetPOSNow;
    q.q.ParamByName('id').asInt64 := id;
    q.ExecQuery;

    qUpdAdj := awt.GetPreparedIIbSql('insert into voucher_adjustments'
    + ' (id_vouchers, id_untill_users, adjustment_dt, prev_amount, '
    + ' new_amount, voucher_state, pcname) '
    + ' select id, :id_untill_users, :adjustment_dt, VCH_AMOUNT, '
    + ' VCH_AMOUNT, state, :pcname'
    + ' from vouchers where id=:id');
    qUpdAdj.q.ParamByName('id').AsInt64      := id;
    qUpdAdj.q.ParamByName('id_untill_users').AsInt64  := upos.GetUserID;
    qUpdAdj.q.ParamByName('adjustment_dt').AsDatetime := upos.GetPOSNow;
    qUpdAdj.q.ParamByName('pcname').AsString          := upos.POSComputerName;
    qUpdAdj.ExecQuery;

  end;
end;

procedure TBLRPayVoucher.ProcessPbillVoucherPayment(
  wt : IWTran;
  aid_pbill         : Int64;
  aid_vouchers      : Int64;
  apayments         : TVCHPaymentData);
var ip        : IUntillPayment;
    qIns      : IIBSQL;
    id_acc    : Int64;
    vpNumber  : Integer;
    vpSuffix  : string;
begin
  if not assigned(apayments) then exit;
  if aid_vouchers = 0 then exit;
  if aid_pbill = 0 then exit;

  ip := GetPayment(upos.UntillDB, apayments.id_payments);
  if assigned(ip) then begin
    qIns := wt.GetPreparedIIbSql('insert into voucher_pbill_payments'
    + ' (id_voucher_pbill, price, id_vouchers, id_payments)'
    + ' values (:id_voucher_pbill, :price, :id_vouchers, :id_payments)');
    qIns.q.ParamByName('id_voucher_pbill').asInt64  := aid_pbill;
    qIns.q.ParamByName('price').asCurrency          := apayments.price;
    qIns.q.ParamByName('id_vouchers').asInt64       := aid_vouchers;
    qIns.q.ParamByName('id_payments').asInt64       := apayments.id_payments;
    qIns.ExecQuery;

    if (ip.kind = PAYMENT_ACCOUNT) then begin
      GetVoucherPBillNumber(wt, aid_pbill, vpNumber, vpSuffix);
      id_acc := ProcessAccountPaymentBill(wt,
            StrToInt64Def(ClientDataProvider.ClientID,0),
            apayments.price, 0, VpNumber, VpSuffix);
      ProcessAccountPayments( wt, id_acc, StrToInt64Def(ClientDataProvider.ClientID,0),
        GetVoucherBarcode(aid_vouchers), apayments.price );
    end;
  end;
end;

function TBLRPayVoucher.CancelVoucherPayments(wt : IWTran;
  itemBarCode : String) : Int64;
var sqlStr : string;
    qSel   : IIBSQL;
    amount : currency;
    id_payments, id_vouchers, id_pbill  : Int64;
    pb : TVCHPaymentData;
    ip : IUntillPayment;
begin
  result := 0;
  assert(assigned(wt));
  if trim(itemBarCode)='' then exit;
  sqlStr := 'select VOUCHERS.id, -sum(vpp.price) rest, vpp.ID_PAYMENTS, vp.id_clients '
    + ' from VOUCHERS '
    + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
    + ' left outer join voucher_pbill vp on vpp.ID_VOUCHER_PBILL=vp.id '
    + ' where VCH_BARCODE=:barcode and is_active=1 and coalesce(VCH_TYPE, 0) = 0'
    + ' group by VOUCHERS.id,ID_PAYMENTS, vp.id_clients'
    + ' having sum(vpp.price)<>0';
  qSel := wt.GetPreparedIIbSql(sqlStr);
  qSel.q.ParamByName('barcode').asString := itemBarCode;
  qSel.ExecQuery;
  if qSel.eof then exit;

  id_pbill := CreateVoucherPBIll(wt);
  while not qSel.eof do begin
    amount      := qSel.FieldByName('rest').asCurrency;
    id_vouchers := StrToInt64Def(qSel.FieldByName('id').asString,0);
    id_payments := StrToInt64Def(qSel.FieldByName('ID_PAYMENTS').asString,0);
    pb := TVCHPaymentData.Create(id_payments, amount, '', 0);
    ClientDataProvider.ClientID := qSel.FieldByName('id_clients').asString;
    ProcessPbillVoucherPayment(wt, id_pbill, id_vouchers, pb);
    ip := GetPayment(upos.UntillDB, id_payments);
    if (ip.kind = PAYMENT_CARD) then
      ProcessCardPayment(wt, newNV, id_payments, amount, itemBarCode, '');
   qSel.next;
  end;
  result := id_pbill;
end;

procedure TBLRPayVoucher.PayVoucherList(aEFTParams: String);
var ip :IUntillPayment;
    ipname : String;
    jd : TVoucherPaymentJournalData;
    qSel : IIBSQL;
    qUpd : IIBSQL;
    wt   : IWTran;
    i    : Integer;
    curDT : TDatetime;
    billNumber : String;
    itemAmount, totalAmount : Currency;
    id_vouchers : Int64;
    endDate : IUntillDatetime;
    nv : INamedVars;
    payments_list : TObjectList<TVCHPaymentData>;
    id_pbill : Int64;
    id_acc   : Int64;
    vpNumber : Integer;
    vpSuffix : string;
    vcTotal  : Currency;
    req: VoucherBillClosingRequest;
    items: TDictionary<String, Currency>;
    vval : Currency;
    delvcList : TVoucherShortDataList;
    sqlStr : string;
    id_pbill_cancel : Int64;
    newamount : Currency;
    vch_type  : TUntillVoucherType;
begin
  if fid_payments <= 0 then exit;
  ip := GetPayment(upos.UntillDB, fid_payments);
  items:=TDictionary<String, Currency>.Create;
  delvcList := TVoucherShortDataList.Create;
  try

    wt := upos.UntillDB.getWTran;
    curDT := upos.GetPosNow;
    id_pbill := 0;
    id_acc   := 0;
    if not (PayType in [ipvtDelete])  then begin
      CheckVoucherPayment( ip );
      ipname := ip.name;

      totalAmount     := 0;
      billNumber := '';

      id_pbill := CreateVoucherPBIll(wt);
      id_acc  := 0;

      GetVoucherPBillNumber(wt, id_pbill, vpNumber, vpSuffix);

      if (ip.kind = PAYMENT_ACCOUNT) then begin
        vcTotal := 0;
        for i := 0 to Pred( vcList.count ) do begin
          qSel := wt.GetPreparedIIbSql('select ' + CurrToStr(fnewvalue) + ' - coalesce(sum(vpp.price),0) '
            + ' from VOUCHERS '
            + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
            + ' where VCH_BARCODE=:barcode and is_active=1 and coalesce(VCH_TYPE, 0) = 0'
            + ' group by VCH_AMOUNT' );
          qSel.q.ParamByName('barcode').asString := vcList[ i ].barcode;
          qSel.ExecQuery;
          vcTotal := vcTotal + qSel.fields[0].asCurrency;
        end;

        id_acc := ProcessAccountPaymentBill(wt,
          StrToInt64Def(ClientDataProvider.ClientID,0),
          vcTotal, 0, VpNumber, VpSuffix);
      end;
    end;

    vch_type    := uvtRegular;
    id_vouchers := 0;
    for i := Pred( vcList.count ) downto 0 do begin
      id_pbill_cancel := 0;
      if billNumber='' then billNumber := vcList[ i ].barcode;

      if PayType in [ipvtDelete]  then begin
        CancelVoucherPayments(wt, vcList[ i ].barcode);
        // Just delete the voucher
        if not delvcList.BarCodeExists ( vcList[ i ].barcode ) then
          delvcList.Add( TVoucherShortData.Create(vcList[ i ].barcode, vcList[ i ].id));
      end else begin
        if PayType in [ipvtRepay] then
          id_pbill_cancel := CancelVoucherPayments(wt, vcList[ i ].barcode);

        if fAfterAdjust then
          sqlStr := 'select VOUCHERS.id, sum(vpp.price) rest,'
        else
          sqlStr := 'select VOUCHERS.id, (ORIG_VCH_AMOUNT - coalesce(sum(vpp.price),0)) rest, ';
        sqlStr := sqlStr  + ' END_DATE, ORIG_VCH_AMOUNT, STATE vch_state, VCH_TYPE '
          + ' from VOUCHERS '
          + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
          + ' where VCH_BARCODE=:barcode and is_active=1 '
          + ' group by VOUCHERS.id, ORIG_VCH_AMOUNT, END_DATE, VOUCHERS.state, VCH_TYPE';
        qSel := wt.GetPreparedIIbSql(sqlStr);
        qSel.q.ParamByName('barcode').asString := vcList[ i ].barcode;
        qSel.ExecQuery;

        if not qSel.eof then begin
          id_vouchers := StrToInt64Def( qSel.FieldByName('id').asString,0 );
          if fAfterAdjust then
            itemAmount  := fnewvalue - qSel.FieldByName('rest').asCurrency
          else
            itemAmount  := qSel.FieldByName('rest').asCurrency;
          if itemAmount<>0 then begin
            endDate     := GetUntillDateTime( qSel.FieldByName('END_DATE').asDatetime );

            newamount := qSel.FieldByName('ORIG_VCH_AMOUNT').asCurrency + itemAmount;
            if fAfterAdjust then
              UpdateVoucherAmount(wt, vcList[ i ].barcode, newamount);
            qUpd := wt.GetPreparedIIbSql('update VOUCHERS '
              + ' set id_payments=:id_payments, state=:state, vch_dt_payment =:dt '
              + ' where id=:id');
            qUpd.q.ParamByName('STATE').asInteger     := Ord( vchsActive );
            qUpd.q.ParamByName('dt').asDatetime       := curDT;
            qUpd.q.ParamByName('id_payments').asInt64 := ip.id;
            qUpd.q.ParamByName('id').asInt64          := id_vouchers;
            qUpd.ExecQuery;

            payments_list := TObjectList<TVCHPaymentData>.Create;
            payments_list.Add(TVCHPaymentData.Create(ip.id, itemAmount, aEFTParams, 0));
            try
              if (ip.kind = PAYMENT_ACCOUNT) then
                DoSaveAccountData(wt, itemAmount, VpNumber, VpSuffix);
              ProcessPbillVoucherPayments(wt, id_pbill, id_vouchers, payments_list, id_acc, vcList[ i ].barcode, aEFTParams, IntToStr(vpNumber) + vpSuffix);
              if (newamount = 0) and (qSel.FieldByName('VCH_TYPE').asInteger = 1) then
                vch_type := uvtDP;
                UpdateVoucherRefund(wt, id_vouchers, id_pbill);
            finally
              FreeAndNil( payments_list );
            end;

            jd.amount  := itemAmount;
            jd.ipname  := ipname;
            jd.endDate := endDate;
            jd.barcode := vcList[ i ].barcode;
            SaveJournalLog( jd );

            totalAmount := totalAmount + itemAmount;
            items.TryGetValue(vcList[ i ].barcode, vval);
            vval := vval + itemAmount;
            items.AddOrSetValue(vcList[ i ].barcode, vval);
            if qSel.FieldByName('ORIG_VCH_AMOUNT').asCurrency+itemAmount = fnewvalue then
              if not delvcList.BarCodeExists(vcList[ i ].barcode) then
                delvcList.Add( TVoucherShortData.Create(vcList[ i ].barcode, vcList[ i ].id) );
          end;
          if (id_pbill_cancel > 0) and (id_pbill > 0) then begin
            PutVoucherAdjustment(wt, id_vouchers, itemAmount, itemAmount,
              qSel.FieldByName('VCH_STATE').asInteger, id_pbill_cancel, id_pbill);
          end;
        end;
      end;
    end;
    if (ip.kind = PAYMENT_CARD) then begin
      nv := newNV;
      ProcessCardPayment(wt, nv, fid_payments, totalAmount, billNumber, aEFTParams);
    end;

    PosFiscalData.clear;
    upos.CustomBillHandlers.Execute(procedure(cfg: TCustomBillsHandlerInterfaceConfiguration) begin
      req:=CreateDriverRequest(upos.UntillDB, cfg.DriverId, items, ip, totalAmount);
      DoCustomBillsHandlingCall(upos.UntillDB, wt, cfg, req, FISCAL_OP_VOUCHER_BILL_CLOSING);
    end);

    if PayType = ipvtDelete then
      DeleteVocuhersInList( wt, delvcList );

    wt.commit;

    if vcList.Count >0 then begin
      // Print all saved vouchers
      if vch_type = uvtDP then begin
        PrintDPBill(id_vouchers, true);
      end else begin
        PrintVoucherTicketBunch;
        // Print vouchers bill
        PrintVoucherBill;
      end;
    end;
    vcList.Clear;

  finally
    items.Free;
    delvcList.Free;
    ClientDataProvider.Clear;
  end;
end;

{ TBLRCreateMultiVoucher }

function TBLRCreateMultiVoucher.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  result := true;
  if mMode = mvcmAmount then begin
    if msg = TBLRMsgPaymentMode then Exit;  // Pay all vouchers
  end;
  if msg = TBLMessageCancel then Exit;  // Closes screen
  if msg = TBLMessageOK then Exit;      // Save vouchers
  if msg = TBLMessageInput then Exit;       // enter voucher quantity or voucher value
  if msg = TBLMessageBackSpace then Exit;   // corrects voucher quantity or voucher value
  if msg = TBLMessageClear then Exit;       // clears voucher quantity or voucher value
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRCreateMultiVoucher.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  FmMode  := mvcmQuantity;
  FQty    := '';
  FAmount := '';
  fvcrList := TStringList.Create;
  strConfirmVouchers := plugin.TranslatePOS('BLRAlgStockBalanceU',
        'You are going to create %s vouchers with amount %s. Are you sure?')
end;

destructor TBLRCreateMultiVoucher.Destroy;
begin
  FreeAndNil( fvcrList );
  inherited;
end;

function TBLRCreateMultiVoucher.GetAmount: Currency;
begin
  result := StrToCurrDef(FAmount,0);
end;

function TBLRCreateMultiVoucher.GetHint: widestring;
begin
  if mMode= mvcmQuantity then
    Result := plugin.TranslatePOS('BLRAlgU', 'Enter quantity of multi vouchers', 'State hint')
  else
    Result := plugin.TranslatePOS('BLRAlgU', 'Enter amount that will apply to each voucher', 'State hint')
end;

function TBLRCreateMultiVoucher.GetQty: Integer;
begin
  result := StrToIntDef(FQty,0);
end;

class function TBLRCreateMultiVoucher.GetStaticGuid: string;
begin
  result := 'f5a07198-83c7-4fdf-995a-ca6307c0f144';
end;

procedure TBLRCreateMultiVoucher.OnChildFinished(child: TBLAlgStep);
var
		cmvs: TBLRConfirmMultiVoucherSave;
begin
  if IsEftCfgSelectionCompletionHandled(Self, Child) then
  	exit;

  if child is TBLRConfirmMultiVoucherSave then begin
  	cmvs := TBLRConfirmMultiVoucherSave(child);
    FreeOnLastChild := false;
    if child.Result=blOK then begin
      SaveMultiVoucher( cmvs.fid_payments, cmvs.EftParams ) ;
      FreeOnLastChild := true;
    end;
    exit;
  end;

  inherited;
end;

procedure TBLRCreateMultiVoucher.OnInput(s: widestring);
var ss : String;
    msgErr : String;
begin
  if FmMode = mvcmQuantity then begin
    if IsNumeric( s ) then begin
      if StrToIntDef(FQty + s,0) > MAX_MULTI_VOUCHER then begin
        msgErr := WideFormat(strConfirmVouchers, [IntToStr(MAX_MULTI_VOUCHER)]);
        Plugin.RaisePosException(msgErr);
      end;
      CheckNumberRange( StrToIntDef(FQty + s,0) );
      FQty := FQty + s;
    end;
  end else begin
    if Trim(s) = '.' then s := FormatSettings.DecimalSeparator;
    ss := FAmount + s;
    if IsFloat(ss) then begin
      if StrToFloat(ss) < MAX_QUANTITY then
        FAmount := ss;
    end else
      FAmount := '0';
  end;
end;

procedure TBLRCreateMultiVoucher.OnOK;
var s : String;
begin
  if (FmMode=mvcmQuantity) then begin
    if StrToIntDef(FQty,0) <= 0 then
      Plugin.RaisePosException('Please enter quantity of vouchers','TBLRGetVoucher.ProcessKeybordString');
    FmMode := mvcmAmount;
  end else begin
    if StrToCurrDef(FAmount,0) <= 0 then
      Plugin.RaisePosException('Please enter voucher amount','TBLRGetVoucher.ProcessKeybordString');
    s := WideFormat(plugin.TranslatePOS('BLRAlgStockBalanceU',
      'You are going to create %s vouchers with amount %s. Are you sure?'), [FQty, FAmount]);
    TBLRConfirmMultiVoucherSave.Create(bla, self, s, mtConfirmation);
  end;
end;

procedure TBLRCreateMultiVoucher.SaveMultiVoucher( aid_payments : Int64; EftParams: String );
var i    : Integer;
    vd   : TVoucherData;
    barcode : String;
    cntErr : Integer;
    newid  : Int64;
    qty    : Integer;
    value  : Currency;
    blrPay : TBLRPayVoucher;
    manualNum : Integer;
begin

  fdt := upos.GetPOSNow;

  qty := StrToIntDef( FQty,0 );
  if qty <= 0 then exit;
  if qty > MAX_MULTI_VOUCHER then exit;

  value := StrToCurrDef( FAmount,0 );
  if value = 0 then exit;

  for i := 1 to qty do begin
    with POSRestaurantSettings.Settings.ExtraSettings do begin
    if (VoucherType = Ord(vctPredefined)) and (VoucherNumFrom > 0) then begin
      manualNum := GetLastBarcodeFromRange;
      if manualNum = 0 then begin
        plugin.RaisePosException('Voucher number range is exceeded. Set a new range or reduce the number of multi-vouchers');
        exit;
      end;
      barcode := IntToStr(manualNum);
    end else
      barcode := GenerateBarCode(DefaultBarcodeLength);
    end;
    vd.barcode  := barcode;
    vd.amount   := value;
    vd.datetime := dt;
    vd.id_pbill_payment := 0;
    cntErr := 0;
    newid  := 0;
    while true do begin // bar code may exist already
      newid  := SaveVoucher(nil, 0, vd, 0, false, 0, '', false, uvtRegular);
      if newid = 0 then begin // barcode was already used
        Inc(cntErr);
        if cntErr > 100 then  // probably all numbers are used already
          plugin.RaisePosException('Voucher bunch cannot be saved: all numbers are used');
        barcode    := GenerateBarCode(DefaultBarcodeLength); // Generate another barcode
        vd.barcode := barcode;
      end else
        break;
    end;
    if newid > 0 then
      vcrList.Add( vd.barcode );
  end;

  // Pay all them if id_payment is defined
  if (aid_payments > 0) and (vcrList.count > 0) then begin
    blrPay := TBLRPayVoucher.Create(PosAlg, nil, true, 0);
    blrPay.fid_payments := aid_payments;
    for i := 0 to Pred( vcrList.count ) do
      blrPay.AddVoucherToList( vcrList[i],0 );
    blrPay.PayVoucherList(EftParams);
    blrPay.Free;
  end;

  ReinitVoucherNumber;
end;

procedure TBLRCreateMultiVoucher.ReinitVoucherNumber;
var ev : TurlEvent;
begin
  ev := TurlEvent.Create(0, '', uetUpdate);
  try
    Upos.un.SendEvent(URL_REINIT_VOUCHER_NUMBER, ev);
  finally
    FreeAndNil( ev );
  end;
end;

procedure TBLRCreateMultiVoucher.ProcessMessage(msg: TBLMessage);
var s : String;
		pm: TBLRMsgPaymentMode;
    EFTParams: String;
begin
  if FmMode = mvcmQuantity then begin
    if msg is TBLMessageBackSpace then begin
      if length(FQty) > 0 then delete(FQty, Length(FQty) ,1);
      exit;
    end else if msg is TBLMessageClear then begin
      FQty := '';
      exit;
    end;
  end else if FmMode = mvcmAmount then begin
    if msg is TBLMessageBackSpace then begin
      if length(FAmount) > 0 then delete(FAmount, Length(FAmount) ,1);
      exit;
    end else if msg is TBLMessageClear then begin
      FAmount := '';
      exit;
    end else if msg is TBLRMsgPaymentMode then begin    // Link voucher to Payment and saves voucher in DB

    	pm := TBLRMsgPaymentMode(msg);

      if not IsEftParamsSpecified(Self.bla, Self, pm, 0, EFTParams) then
        exit;

      if StrToCurrDef(FAmount,0) <= 0 then
        Plugin.RaisePosException('Please enter voucher amount','TBLRGetVoucher.ProcessKeybordString');
      s := WideFormat(strConfirmVouchers, [FQty, FAmount]);

      TBLRConfirmMultiVoucherSave.Create(bla, self, s, mtConfirmation, TBLRMsgPaymentMode(msg).id_payments, EFTParams);
      exit;
    end;
  end;
  inherited;
end;

{ TBLRConfirmMultiVoucherSave }

constructor TBLRConfirmMultiVoucherSave.Create(ABla: TBLAlg;
  AParent: TBLAlgStep; MessageBody: WideString; MessageType: TMsgDlgType;
  aid_payments: Int64 = 0; AEftParams: String='');
begin
  inherited Create(ABla, AParent, MessageBody, MessageType);
  fid_payments := aid_payments;
  FEftParams := AEftParams;
end;

procedure GetDSVoucherList(
  astate:TVoucherState;
  aDSVoucherList : TDSVoucherDataList;
  aNeedShowSpent : boolean;
  aIgnoreFilters : boolean  = false);
var iq : IIBSQL;
    strSQL : String;
    bc : String;
    ABill : TBlrBill;
    vchAmount : Currency;
    bNeedShow : boolean;
    NeedValueFilterParam : boolean;
    id_v : Int64;
    dtAdded : boolean;
begin

  if not assigned(aDSVoucherList) then exit;

  if (RunMode in [armNormal, armPos]) and (Upos.StartScreen='') then exit;
  if not (posalg.GetCurrentStep is TBLRGetVoucher)
    and not (posalg.GetCurrentStep is TBLRVoucherBunch) then exit;

  if (posalg.GetCurrentStep is TBLRVoucherBunch)
    and not TBLRVoucherBunch(posalg.GetCurrentStep).fShowVouchers then begin
      aDSVoucherList.Clear;
      exit;
  end;

  vchAmount := 0;
  ABill     := nil;
  dtAdded   := false;
  if posalg.IsStepInStack(TBLRSplitNote.GetStaticGuid)  then
    ABill := Blr.TransferOriginal
  else if posalg.IsStepInStack(TBLREditOrder.GetStaticGuid) then
    ABill := Blr.ActiveBill;
  strSQL := 'select first 200 (orig_vch_amount - coalesce(sum(pbill_payments.price),0)) price, '
    + ' vouchers.id, vouchers.VCH_BARCODE, vouchers.vch_amount, vouchers.orig_vch_amount '
    + ' from vouchers '
    + ' left outer join voucher_payments on voucher_payments.ID_VOUCHERS = vouchers.id'
    + ' left outer join pbill_payments on voucher_payments.ID_PBILL_PAYMENTS = pbill_payments.id'
    + ' where is_active=1 and state=:state and coalesce(VCH_TYPE, 0) = 0' ;
  if (posalg.GetCurrentStep is TBLRGetVoucher) then begin
    vchAmount := TBLRGetVoucher(posalg.GetCurrentStep).VoucherAmountFilter;
    dtAdded := true;
    if TBLRGetVoucher(posalg.GetCurrentStep).Kind=vckProlong then
      strSQL := strSQL +  ' and coalesce(end_date, CURRENT_TIMESTAMP) < :dt'
    else
      strSQL := strSQL +  ' and coalesce(end_date, CURRENT_TIMESTAMP) >= :dt ';
    bc := trim(TBLRGetVoucher(posalg.GetCurrentStep).FBarcodeFilter);
  end else if (posalg.GetCurrentStep is TBLRVoucherBunch) then begin
    dtAdded := true;
    strSQL := strSQL +  ' and coalesce(end_date, CURRENT_TIMESTAMP) >= :dt ';
    bc := trim(TBLRVoucherBunch(posalg.GetCurrentStep).FBarcodeFilter);
    vchAmount := TBLRVoucherBunch(posalg.GetCurrentStep).VoucherAmountFilter;
  end;
  if (bc<>'') and (not aIgnoreFilters) then
    strSQL := strSQL + ' and upper(VCH_BARCODE) like upper(''' + bc +'%'')';

  NeedValueFilterParam := false;
  if (vchAmount > 0) and (not aIgnoreFilters) then begin
    strSQL := strSQL + ' and vouchers.vch_amount  = :vchAmount';
    NeedValueFilterParam := true;
  end;
  strSQL := strSQL + ' group by vouchers.VCH_BARCODE, vouchers.orig_vch_amount, vouchers.vch_amount, vouchers.id, vouchers.vch_amount';
//  strSQL := strSQL + ' order by vouchers.VCH_BARCODE';
  iq := upos.UntillDB.GetPreparedIIbSql(strSQL);
  if dtAdded then
     iq.q.ParamByName('dt').asDatetime := upos.GetPOSNow;
  if NeedValueFilterParam then
    iq.q.ParamByName('vchAmount').asCurrency := vchAmount;
  iq.q.ParamByName('state').asInteger := Ord(astate);
  iq.ExecQuery;

  while not iq.eof do begin
    id_v := StrToInt64Def(iq.q.fieldByName('id').asString,0);
    bNeedShow := true;
    if assigned(ABill) then
      bNeedShow := not ABill.IsVoucherUsed(id_v, PAYMENT_VOUCHER)
    else if not aNeedShowSpent then
      bNeedShow := iq.q.fieldByName('price').asCurrency=iq.q.fieldByName('ORIG_VCH_AMOUNT').asCurrency ;
    if (posalg.GetCurrentStep is TBLRVoucherBunch) then
        if TBLRVoucherBunch(posalg.GetCurrentStep).vcList.BarCodeExists( iq.q.fieldByName('VCH_BARCODE').asString ) then
          bNeedShow := false;

        if bNeedShow then aDSVoucherList.add( TDSVoucherData.Create(
            id_v,
            iq.q.fieldByName('VCH_BARCODE').asString,
            iq.q.fieldByName('price').asCurrency,
            iq.q.fieldByName('orig_vch_amount').asCurrency) );
    iq.next;
  end;
end;

{ TDSVoucherData }

constructor TDSVoucherData.Create(aid: Int64; abarcode: string;
  aamount, avalue: Currency);
begin
  fid      := aid;
  fbarcode := abarcode;
  famount  := aamount;
  fvalue   := avalue;
end;

{ TDSVoucherDataList }

procedure TDSVoucherDataList.Clear;
var i : Integer;
begin
  for i := Pred(count) downto 0 do GetItems(i).Free;
  inherited;
end;

function TDSVoucherDataList.GetItems(Index: Integer): TDSVoucherData;
begin
  result := TDSVoucherData( inherited Items[Index]);
end;

{ TBLRVoucherBunch }

procedure TBLRVoucherBunch.AddVoucherToList(abc: String; aID : Int64);
begin
  if trim( abc ) = '' then exit;
  if FvcList.BarCodeExists( abc ) then exit;

  if (aid = 0 ) then
    aid := GetVoucherID(abc);

  FvcList.Add( TVoucherShortData.Create( abc, aid ));
  FCurIdx := FvcList.Count-1;
end;

procedure TBLRVoucherBunch.UpdateVoucherRefund(wt : IWTran;
  id_vouchers, id_pbill: Int64);
var qUpd : IIBSQL;
begin
  assert(assigned(wt));
  if id_vouchers <= 0 then exit;
  if id_pbill    <= 0 then exit;

  qUpd := wt.GetPreparedIIbSql('update VOUCHERS set refund=1 where id=:id');
  qUpd.q.params[0].AsInt64 := id_vouchers;
  qUpd.ExecQuery;

  qUpd := wt.GetPreparedIIbSql('update VOUCHER_PBILL set refund=1 where id=:id');
  qUpd.q.params[0].AsInt64 := id_pbill;
  qUpd.ExecQuery;
end;

procedure TBLRVoucherBunch.UpdateVoucherAmount(wt : IWTran; abarcode: string;
  aAmount: Currency);
var qSel : IIBSQL;
    vch_state : Integer;
    vcInfo    : TVoucherUpdateInfo;
begin
  assert(assigned(wt));
  qSel := wt.GetPreparedIIbSql('select vouchers.id, vch_amount, vouchers.state,'
    + ' coalesce(sum(pbill_payments.price),0) rest '
    + ' from vouchers '
    + ' left outer join voucher_payments on voucher_payments.ID_VOUCHERS = vouchers.id'
    + ' left outer join pbill_payments on voucher_payments.ID_PBILL_PAYMENTS = pbill_payments.id'
    + ' where vch_barcode=:barcode and is_active=1 '
    + ' group by vouchers.id, vouchers.vch_amount, vouchers.state');
  qSel.q.ParamByName('barcode').AsString := abarcode;
  qSel.ExecQuery;

  if not qSel.eof then begin
    vcInfo.id     := StrToInt64Def(qSel.q.Fields[0].asString,0);
    vcInfo.amount := qSel.q.Fields[1].asCurrency;
    vcInfo.spend  := qSel.q.Fields[3].asCurrency;
    vch_state:= qSel.q.Fields[2].asInteger;
    if vcInfo.id > 0 then begin

      DoUpdateVoucherAmount(wt, vcInfo, aAmount);
      PutVoucherAdjustment(wt, vcInfo.id, vcInfo.amount, aAmount, vch_state);

    end;
  end;
end;

procedure TBLRVoucherBunch.PutVoucherAdjustment(wt : IWTran; aid : Int64;
  prev_amount, new_amount : Currency; vch_state : Integer;
  id_pbill_cancel: Int64 = 0; id_pbill_result : Int64 = 0);
var qUpdAdj : IIBSQL;
begin
  assert( assigned(wt) );
  qUpdAdj := wt.GetPreparedIIbSql('insert into voucher_adjustments'
    + ' (id_vouchers, id_untill_users, adjustment_dt, prev_amount, '
    + ' new_amount, voucher_state, pcname, id_voucher_pbill_cancel, id_voucher_pbill_result) '
    + ' values(:id_vouchers, :id_untill_users, :adjustment_dt, :prev_amount, '
    + ' :new_amount, :voucher_state, :pcname, :id_voucher_pbill_cancel, :id_voucher_pbill_result)');
  qUpdAdj.q.ParamByName('id_vouchers').AsInt64      := aid;
  qUpdAdj.q.ParamByName('id_untill_users').AsInt64  := upos.GetUserID;
  qUpdAdj.q.ParamByName('adjustment_dt').AsDatetime := upos.GetPOSNow;
  qUpdAdj.q.ParamByName('prev_amount').AsCurrency   := prev_amount;
  qUpdAdj.q.ParamByName('new_amount').AsCurrency    := new_amount;
  qUpdAdj.q.ParamByName('voucher_state').AsInteger  := vch_state;
  qUpdAdj.q.ParamByName('pcname').AsString          := upos.POSComputerName;
  if (id_pbill_cancel>0) and (id_pbill_result>0)then begin
    qUpdAdj.q.ParamByName('id_voucher_pbill_cancel').AsInt64 := id_pbill_cancel;
    qUpdAdj.q.ParamByName('id_voucher_pbill_result').AsInt64 := id_pbill_result;
  end else begin
    qUpdAdj.q.ParamByName('id_voucher_pbill_cancel').Clear;
    qUpdAdj.q.ParamByName('id_voucher_pbill_result').Clear;
  end;
  qUpdAdj.ExecQuery;
end;

procedure TBLRVoucherBunch.DoUpdateVoucherAmount(wt : IWtran;
  avcInfo : TVoucherUpdateInfo; aAmount : Currency);
var qUpd: IIBSQL;
begin
  assert( assigned(wt) );
  qUpd := wt.GetPreparedIIbSql ('update vouchers '
    + ' set vch_amount=:vch_amount, orig_vch_amount=:vch_amount, STATE=:STATE'
    + ' where id=:id');
  qUpd.q.ParamByName('id').AsInt64            := avcInfo.id;
  qUpd.q.ParamByName('vch_amount').AsCurrency := aAmount;
  if (avcInfo.spend > aAmount) then
    aAmount := avcInfo.spend;
  if avcInfo.amount <> aAmount then
    qUpd.q.ParamByName('STATE').AsInteger     := Ord( vchsInit );
  if (aAmount = 0) then
    qUpd.q.ParamByName('STATE').AsInteger     := Ord( vchsSpent );
  qUpd.ExecQuery;
end;

function TBLRVoucherBunch.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  Result := true;
  if msg = TBLMessageInput then Exit;       // adds symbol to value or barcode
  if msg = TBLMessageBackSpace then Exit;   // removes symbol from value or barcode
  if msg = TBLMessageClear then Exit;       // clears voucher quantity or voucher value
  if msg = TBLRMsgSendVoucherValue then Exit; // Filter by value
  if msg = TBLMessageCancel then Exit;   // Closes screen
  if msg = TBLMessageOK then Exit;       // Closes screen
  if msg = TBLRMsgSendVoucher then Exit; // put voucher to payment list
  if msg = TBLMessageDelete then Exit;   // remove voucher from payment list
  Result := inherited CanAcceptMessage(msg);
end;

constructor TBLRVoucherBunch.Create(ABla: TBLAlg; AParent: TBLAlgStep; bShowVouchers : boolean);
begin
  inherited Create(ABla, AParent);
  ClientDataProvider.Clear;
  FCurIdx := 0;
  fShowVouchers := bShowVouchers;
  Init;
end;

procedure TBLRVoucherBunch.Init;
begin
  FvcList := TVoucherShortDataList.Create;
  FVoucherAmountFilter := 0;
  FBarcodeFilter := '';
  SendUpdateVoucherMsg;
end;

procedure TBLRVoucherBunch.DeleteVoucherFromList(aIdx: Integer);
begin
  if not ItemIndexValid( aidx ) then exit;
  FvcList.delete( aidx );
  FCurIdx := 0;
end;

function TBLRVoucherBunch.ItemIndexValid( aidx : Integer ) : boolean;
begin
  result := false;
  if (aidx < 0) then exit;
  if (aidx > Pred(FvcList.count)) then exit;
  result := true;
end;

destructor TBLRVoucherBunch.Destroy;
begin
  FreeAndNil( FvcList );
  inherited;
end;

function TBLRVoucherBunch.GetTotal: Currency;
var iq : IIBSQL;
    i  : Integer;
    sqlStr : string;
begin
  result := 0;
  if FvcList.Count=0 then exit;

  for i := 0 to Pred(FvcList.Count) do begin
    sqlStr := 'select ORIG_VCH_AMOUNT - coalesce(sum(vpp.price),0) rest';
    sqlStr := sqlStr  + ' from vouchers '
      + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
      + ' where vch_barcode=:barcode and is_active=1 and coalesce(VCH_TYPE, 0) = 0'
      + ' group by ORIG_VCH_AMOUNT';
    iq := upos.UntillDB.GetPreparedIIbSql( sqlStr );
    iq.q.paramByname('barcode').asString :=FvcList[i].barcode;
    iq.ExecQuery;
    if not iq.eof then
      result := result + iq.q.FieldByName('rest').asCurrency;
  end;
end;

procedure TBLRVoucherBunch.OnInput(s: widestring);
begin
  if Length(FBarcodeFilter) < 9 then
    if (CharInSet(s[1], ['a'..'z'])) or (CharInSet(s[1], ['A'..'Z'])) or (CharInSet(s[1], ['0'..'9'])) then begin
      FBarcodeFilter := FBarcodeFilter + s;
      SendUpdateVoucherMsg;
  end;
end;

procedure TBLRVoucherBunch.ProcessKeybordString(msg: TBLMessageKeyboardString);
var s : string;
begin
  s := TBLMessageKeyboardString(msg).s;
  if Length(FBarcodeFilter) < 9 then
    if (CharInSet(s[1], ['a'..'z'])) or (CharInSet(s[1], ['A'..'Z'])) or (CharInSet(s[1], ['0'..'9'])) then begin
      FBarcodeFilter := FBarcodeFilter + s;
      SendUpdateVoucherMsg;
  end;
end;

procedure TBLRVoucherBunch.ProcessMessage(msg: TBLMessage);
begin
  try
    if msg is TBLMessageBackSpace then begin
      if Length(FBarcodeFilter)>0 then begin
        delete(FBarcodeFilter, Length(FBarcodeFilter) ,1);
        SendUpdateVoucherMsg;
      end;
      exit;
    end else if msg is TBLMessageClear then begin
      FVoucherAmountFilter := 0;
      FBarcodeFilter := '';
      SendUpdateVoucherMsg;
      exit;
    end else if msg is TBLRMsgSendVoucherValue then begin
      FVoucherAmountFilter := TBLRMsgSendVoucherValue(msg).value;
      SendUpdateVoucherMsg;
      exit;
    end else if msg is TBLRMsgSendVoucher then begin // put voucher to payment list
      AddVoucherToList( TBLRMsgSendVoucher(msg).barcode, TBLRMsgSendVoucher(msg).id );
      exit;
    end else if msg is TBLMessageDelete then begin   // remove voucher from payment list
      DeleteVoucherFromList( FCurIdx );
      exit;
    end else if msg is TBLMessageDelete then begin   // remove voucher from payment list
      DeleteVoucherFromList( FCurIdx );
      exit;
    end;
  finally
    SendUpdateVoucherMsg;
  end;
  inherited;
end;

{ TBLRAdjustVoucher }

function TBLRAdjustVoucher.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  result := true;
  if msg = TBLRMsgSwitchVouchersType then exit;
  if msg = TBLRMsgNeedDeleteVoucher then exit;
  if msg = TBLRMsgNeedRepayVoucher then exit;
  if msg = TBLRMsgNeedEnterNewValue then exit;
  result := inherited CanAcceptMessage(msg);
  SendUpdateVoucherMsg;
end;

constructor TBLRAdjustVoucher.Create(ABla: TBLAlg; AParent: TBLAlgStep; aVoucherType : TVoucherState);
begin
  inherited Create( ABla, AParent, true );
  ClientDataProvider.Clear;
  SystemDataProvider.NumericValue := '0';
  fnewValue       := 0;
  FCurVoucherType := aVoucherType;
  SendUpdateVoucherMsg;
end;

procedure TBLRAdjustVoucher.DeleteVouchers;
var wt : IWTran;
begin
  if FCurVoucherType = vchsActive then begin
    fnewValue := 0;
    InitPayment(ipvtDelete);
  end else begin
    wt := upos.UntillDB.getWTran;
    DeleteVocuhersInList(wt, vclist);
    wt.commit;
  end;
end;

function TBLRAdjustVoucher.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Adjust voucher values', 'State hint')
end;

class function TBLRAdjustVoucher.GetStaticGuid: string;
begin
  result := '6bc09bac-86d9-4715-aba7-5417039901f5'
end;

procedure TBLRAdjustVoucher.OnChildFinished(child: TBLAlgStep);
begin
  if child is TBLRConfirmDeleteVouchers then begin
    FreeOnLastChild := false;
    if child.Result = blOK then begin
      DeleteVouchers;
      SendUpdateVoucherMsg;
    end;
    exit;
  end;
  if child is TBLSGetNumberValue then begin
    FreeOnLastChild := false;
    if child.Result = blOK then begin
      fnewValue := StrToCurrDef(SystemDataProvider.NumericValue,0);
      if FCurVoucherType = vchsActive then
        InitPayment(ipvtAdjust);
    end;
    exit;
  end;
  if child is TBLRPayVoucher then begin
    FreeOnLastChild := true;
    SendUpdateVoucherMsg;
  end;
  inherited;
end;

procedure TBLRAdjustVoucher.InitPayment(aPayType :TInitPaymentVoucherType);
var blrPay  : TBLRPayVoucher;
    sqlStr  : string;
    qSel    : IIBSQL;
    id_payments : Int64;
    pmsg : TBLRMsgPaymentMode;
begin
  if FvcList.count = 0 then
    plugin.RaisePosException('Please choose Voucher', 'BLRVouchersU');

  if FvcList.count > 1 then
    plugin.RaisePosException('Only one Voucher can take part in operation');

  id_payments := 0;
  if aPayType = ipvtDelete then begin
    sqlStr := 'select vpp.ID_PAYMENTS, VOUCHERS.id '
      + ' from VOUCHERS '
      + ' left outer join voucher_pbill_payments vpp on vpp.ID_VOUCHERS=VOUCHERS.id '
      + ' where VCH_BARCODE=:barcode and is_active=1 and coalesce(VCH_TYPE, 0) = 0'
      + ' group by VOUCHERS.id, VCH_AMOUNT, END_DATE, vpp.ID_PAYMENTS, VOUCHERS.id';
    qSel := upos.UntillDB.GetPreparedIIbSql(sqlStr);
    qSel.q.ParamByName('barcode').asString := FvcList[0].barcode;
    qSel.ExecQuery;
    if qSel.eof then exit;

    id_payments := StrToInt64Def(qSel.Fields[0].asString,0);
  end;

  blrPay := TBLRPayVoucher.Create(bla, self, false, fnewvalue);
  blrPay.fAfterAdjust := true;
  if aPayType = ipvtRepay then
    blrPay.fAfterAdjust := false;
  blrPay.PayType   := aPayType;

  blrPay.AddVoucherToList( FvcList[0].barcode, FvcList[0].id );

  if (aPayType = ipvtDelete) and (id_payments > 0) then begin
    pmsg := TBLRMsgPaymentMode.Create;
    pmsg.id_payments := id_payments;
    pmsg.paymentname := PaymentsU.GetPaymentName(upos.UntillDB, id_payments);
    posalg.SendMessage( pmsg );
  end;
end;

procedure TBLRAdjustVoucher.OnOK;
begin
  AssignNewValues;
  fnewValue := 0;
  FvcList.Clear;
  SendUpdateVoucherMsg;
end;

procedure TBLRAdjustVoucher.DoAssignValues;
var wt        : IWTran;
    i         : Integer;
    bc        : string;
begin
  wt := upos.UntillDB.getWTran;
  for i := 0 to Pred(FvcList.Count) do begin
    bc := FvcList[i].barcode;
    UpdateVoucherAmount(wt, bc, fnewValue);
  end;
  wt.Commit;
end;

procedure TBLRAdjustVoucher.AssignNewValues;
begin
  if fnewValue = 0 then exit;

  DoAssignValues;

  SendUpdateVoucherMsg;
end;

procedure TBLRAdjustVoucher.ProcessMessage(msg: TBLMessage);
begin
  if msg is TBLRMsgNeedRepayVoucher then begin
    InitPayment(ipvtRepay);
    SendUpdateVoucherMsg;
    exit;
  end else if msg is TBLRMsgNeedDeleteVoucher then begin
    if FvcList.count = 0 then
      plugin.RaisePosException('Please choose Voucher', 'BLRVouchersU');
    TBLRConfirmDeleteVouchers.Create(bla, self,
      plugin.Translate('BLRVouchersU', 'You are going to delete selected vouchers. Are you sure?' ),
      mtConfirmation);
    exit;
  end else if msg is TBLRMsgSwitchVouchersType then begin
    if FCurVoucherType = vchsInit then
      FCurVoucherType := vchsActive
    else
      FCurVoucherType := vchsInit;
    fnewValue := 0;
    FvcList.Clear;
    SendUpdateVoucherMsg;
    exit;
  end else if msg is TBLRMsgNeedEnterNewValue then begin
    if FvcList.count = 0 then
      plugin.RaisePosException('Please choose Voucher', 'BLRVouchersU');
    TBLSGetNumberValue.Create(Self.bla, Self, true,
      plugin.TranslatePOS('BLRVouchersU', 'New voucher''s value'),
      NUMERIC_STATE_VOUCHERS_VALUE, fnewValue);
  end else if msg is TBLRMsgSendVoucher then begin // put voucher to payment list
    if FCurVoucherType = vchsActive then
      FvcList.Clear;
    AddVoucherToList( TBLRMsgSendVoucher(msg).barcode, TBLRMsgSendVoucher(msg).id );
    SendUpdateVoucherMsg;
    exit;
  end;
  inherited;
end;

{ TBLRShowVoucherValue }

function TBLRShowVoucherValue.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Show voucher value', 'State hint')
end;

class function TBLRShowVoucherValue.GetStaticGuid: string;
begin
  result :='9148ac21-0d55-47ec-a928-52100e16ec3f';
end;

procedure TBLRShowVoucherValue.ProcessKeybordString(
  msg: TBLMessageKeyboardString);
begin
  inherited;
  FBarCode := TBLMessageKeyboardString(msg).s;
  LoadVoucher(FBarCode);
end;

{ TBLRShowVoucherBalance }

constructor TBLRShowVoucherBalance.Create (ABla: TBLAlg; AParent: TBLAlgStep; AVoucherID: Int64; ABalance: Currency);
begin
  inherited Create(ABla, AParent);
  FVoucherID := AVoucherID;
  FBalance := ABalance;
  upos.NamedParams.RemoveParam(NAMED_PARAM_VOUCHER_ID);
  upos.NamedParams.Params[NAMED_PARAM_VOUCHER_ID] := TInt64.Create(AVoucherID);
end;

function TBLRShowVoucherBalance.GetHint: widestring;
begin
  Result := plugin.TranslatePOS('BLRAlgU', 'Show voucher balance', 'State hint')
end;

class function TBLRShowVoucherBalance.GetStaticGuid: string;
begin
  result := '{33CA9CA9-E886-4D08-8110-61B74B689717}';
end;

{ TBLRBaseVoucher }

function TBLRBaseVoucher.CanAcceptMessage(msg: TBLMessageClass): boolean;
begin
  result := true;
  if msg = TBLMessageCancel then Exit;      // Closes screen
  result := false;
end;

constructor TBLRBaseVoucher.Create(ABla: TBLAlg; AParent: TBLAlgStep);
begin
  inherited;
  FId_voucher := 0;
  FBarCode    := '';
  FAmount     := '';
  FRestAmount := 0;
  FStartDate  := 0;
  FEndDate    := 0;
  StrInvalidBarcodeException := plugin.TranslatePOS('BLRVouchersU', 'Please scan valid barcode')
end;

procedure TBLRBaseVoucher.LoadVoucher(barcode: string);
var qSel : IIBSQL;
begin
  FAmount     := '0';
  FRestAmount := 0;
  qSel := upos.UntillDB.GetPreparedIIbSql('select (orig_vch_amount - coalesce(sum(pbill_payments.price),0)) price, '
    + ' VCH_BARCODE, ORIG_VCH_AMOUNT, vouchers.VCH_DT, END_DATE '
    + ' from vouchers '
    + ' left outer join voucher_payments on voucher_payments.ID_VOUCHERS = vouchers.id'
    + ' left outer join pbill_payments on voucher_payments.ID_PBILL_PAYMENTS = pbill_payments.id'
    + ' where vch_barcode=:barcode and is_active=1 AND STATE > 0 and coalesce(VCH_TYPE, 0) = 0'
    + ' group by VCH_BARCODE, ORIG_VCH_AMOUNT, vouchers.VCH_DT, END_DATE');
  qSel.q.Params[0].AsString := barcode;
  qSel.ExecQuery;
  if qSel.eof then exit;

  FBarcode    := qSel.q.fieldByName('VCH_BARCODE').asString;
  FAmount     := qSel.q.fieldByName('ORIG_VCH_AMOUNT').asString;
  FRestAmount := qSel.q.fieldByName('price').asCurrency;
  FStartDate  := qSel.q.fieldByName('VCH_DT').asDatetime;
  FEndDate    := qSel.q.fieldByName('END_DATE').asDatetime;

end;

procedure TBLRBaseVoucher.ProcessKeybordString(msg: TBLMessageKeyboardString);
begin
  UntillApp.ClearChangeFlags;
  FBarCode := TBLMessageKeyboardString(msg).s;
  if length(trim(FBarCode)) = 0 then
    Plugin.RaisePosException(StrInvalidBarcodeException,'TBLRGetVoucher.ProcessKeybordString');
  LoadVoucher(FBarCode);
  UntillApp.AnalyseChangeFlags;
  inherited;
end;

{ TVCHPaymentData }

constructor TVCHPaymentData.Create(aid_payments: Int64; aprice: Currency;
  aEFTParams : String; aid_pbill_payment : Int64);
begin
  id_payments      := aid_payments;
  price            := aprice;
  EFTParams        := aEFTParams;
  id_pbill_payment := aid_pbill_payment;
end;

{ TVoucherShortData }

constructor TVoucherShortData.Create(abarcode: string; aid: Int64);
begin
  barcode := abarcode;
  id      := aid;
end;

{ TVoucherShortDataList }

function TVoucherShortDataList.BarCodeExists(abarcode: string): boolean;
var i : Integer;
begin
  result := false;
  for i := 0 to Pred(count) do begin
    if SameText(items[i].barcode, abarcode) then begin
      result := true;
      exit;
    end;
  end;
end;

function TVoucherShortDataList.GetItems(Index: Integer): TVoucherShortData;
begin
  result := TVoucherShortData( inherited Items[Index]);
end;

initialization
  RegisterClass(TBLRMsgSendVoucher);
  RegisterClass(TBLRMsgNeedProlongVoucher);
  RegisterClass(TBLRMsgSendVoucherValue);
  RegisterClass(TBLRMsgNeedEnterNewValue);
  RegisterClass(TBLRMsgNeedAdjustVoucher);
  RegisterClass(TBLRMsgSwitchVouchersType);
  RegisterClass(TBLRMsgNeedDeleteVoucher);
  RegisterClass(TBLRMsgNeedRepayVoucher);

end.
