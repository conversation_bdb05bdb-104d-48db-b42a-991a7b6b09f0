unit PrintBillParamsFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ActionParamsFram, UntillSelectBoxU, StdCtrls, TntStdCtrls,
  ExtCtrls, TntExtCtrls, UntillPanelU, TicketEntityManager, UntillDBU,
  UntillCheckBoxU;

type
  TPrintBillParamsFrame = class(TNeedPaymentFram)
    UntillPanel1: TUntillPanel;
    chkDefault: TUntillCheckBox;
    TntLabel1: TTntLabel;
    usbPrinter: TUntillSelectBox;
    TntLabel2: TTntLabel;
    usbTicket: TUntillSelectBox;
    procedure usbLayoutButtonClick(Sender: TObject);
    procedure chkDefaultClick(Sender: TObject);
  private
    { Private declarations }
    emTickets: TTicketEntityManager;
  public
    procedure TranslateStrings; override;

    constructor Create(AOwner: TComponent; AActionObject: TObject;  ANameManager: TObject; AUntillDB: TUntillDb); override;
    destructor Destroy; override;
    procedure SetParameters(Parameters: array of Variant); override;
    procedure GetParameters(var Parameters: array of Variant); override;
    
    { Public declarations }

  end;

implementation
uses DataControlsU, RestaurantPluginU, NeedPaymentFram;

{$R *.dfm}

{ TPrintBillParamsFrame }

constructor TPrintBillParamsFrame.Create(AOwner: TComponent; AActionObject: TObject;  ANameManager: TObject; AUntillDB: TUntillDb);
begin
  inherited;
  emTickets:=TTicketEntityManager.Create(Self, AUntillDB);
end;

destructor TPrintBillParamsFrame.Destroy;
begin
  FreeAndNil(emTickets);
  inherited;
end;

procedure TPrintBillParamsFrame.GetParameters(var Parameters: array of Variant);
begin
  inherited;
  Parameters[0]:=usbLayout.Text;
end;

procedure TPrintBillParamsFrame.SetParameters(Parameters: array of Variant);
var iq: IIBSQL;
begin
  inherited;
  if Length(Parameters)<1 then exit;
  iq:=UntillDB.GetIIbSql;
  iq.SetText('select id from tickets where name = :name');
  iq.q.ParamByName('name').asstring:=UTF8_Encode(Parameters[0]);
  iq.ExecQuery();
  if not iq.Eof then
    FillUsb(usbLayout, iq.FieldByName('id').AsInt64, emTickets, 'name');
end;

procedure TPrintBillParamsFrame.TranslateStrings;
begin
  inherited;
  lblLayout.Caption:=Plugin.TranslateLabel('PrintBillParamsFram','Custom layout');
end;

procedure TPrintBillParamsFrame.usbLayoutButtonClick(Sender: TObject);
begin
  UsbButton(usbLayout, emTickets);
end;

procedure TPrintBillParamsFrame.chkDefaultClick(Sender: TObject);
begin
  inherited;
  usbPrinter.Enabled := ( not chkDefault.Checked);
  usbTicket.Enabled  := usbPrinter.Enabled;
  if not usbTicket.enabled then
  begin
    usbPrinter.Text := '';
    usbTicket.Text := '';
  end;
end;

end.
