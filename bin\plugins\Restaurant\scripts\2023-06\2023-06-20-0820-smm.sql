set term !!;
create or alter procedure get_first_available_caller
(id_group bigint, after_code varchar(50)) 
returns (id_caller bigint, code varchar(50), barcode varchar(50))
as 
declare variable ordering_mode integer;
declare variable number_distr integer;
declare variable c varchar(100);
declare variable o varchar(30);
declare variable x varchar(100);
declare variable stmt varchar(300);
begin    
    select ordering_mode, number_distr from caller_groups where id=:id_group into :ordering_mode, :number_distr;
    if (:ordering_mode = 1) then begin
        o = ' order by cast(code as numeric)';
    end else begin
        o = ' order by code';
    end
    if (coalesce(:after_code, '') != '') then begin
        c = ' and code > :after_code';
    end else begin
        c = '';
    end
    if (:number_distr = 1) then begin
        x = ' and not exists(select id_caller from caller_locks lock where lock.id_caller=callers.id and (current_timestamp - lock.lock_ts < 120))';
    end else begin
        x = '';
    end
    stmt = 'select first 1 id, code, barcode from callers where id_caller_groups='|| :id_group || ' and is_active=1 and id not in (' ||
        ' select r.id_callers from CAL_ORDERS r, callers c, bill b, orders o' ||
        ' where r.ID_ORDERS = o.id and r.inactive is null and o.ID_BILL = b.id and c.id = r.ID_CALLERS' ||
        ' and ((r.CLOSE_DATETIME is null) or (b.CLOSE_DATETIME is null)) ' || x ||
        ' )' || c || o;
    execute statement stmt into :id_caller, :code, :barcode;

    if (:number_distr = 1 and coalesce(:id_caller,0)!=0) then begin
        insert into caller_locks(id_caller, lock_ts) values (:id_caller, current_timestamp);
    end

    suspend;
end
!!
commit
!!
create or alter procedure get_daily_available_caller
(id_group bigint, d1 timestamp, d2 timestamp) 
returns (id_caller bigint, code varchar(50), barcode varchar(50))
as 
declare variable ccode varchar(50);
begin
    select first 1 code from cal_orders co inner join callers c on c.id = co.id_callers
          where co.open_datetime between :d1 and :d2
          and c.id_caller_groups = :id_group
          and coalesce(inactive, 0)=0
          order by co.open_datetime desc
          into :ccode;
    if (:ccode != null) then begin
        select id_caller, code, barcode from get_first_available_caller(:id_group, :ccode) into :id_caller, :code, :barcode;
        if (coalesce(:id_caller,0)!=0) then begin
            exit;
        end
    end
    select id_caller, code, barcode from get_first_available_caller(:id_group, null) into :id_caller, :code, :barcode;
    suspend;
end
!!
commit
!!
grant execute on procedure get_daily_available_caller to untilluser
!!
grant execute on procedure get_first_available_caller to untilluser
!!
commit
!!
create or alter trigger cal_orders_ins_trigger for cal_orders
active after insert position 0
as
begin
    delete from caller_locks where id_caller=new.id_callers;
end
!!
commit
!!


set term ;!!
