create table report_group_tickets (
    id u_id,
    id_report_groups bigint,
    id_tickets bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint rpgt_pk primary key (id),
    constraint rpgt_fk1 foreign key (id_report_groups) references report_groups(id),
    constraint rpgt_fk2 foreign key (id_tickets) references tickets(id)
);
commit;
grant all on report_group_tickets to untilluser;
commit;
execute procedure register_sync_table_ex('report_group_tickets', 'b', 1);
commit;
execute procedure register_bo_table('report_group_tickets', 'id_report_groups', 'report_groups');
commit;


