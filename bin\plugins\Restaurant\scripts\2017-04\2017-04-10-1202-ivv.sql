SET TERM !! ;

create or alter procedure FIX_POS_ARTICLE_POSITIONS
as
declare variable ID_DEP bigint;
declare variable ID_SA bigint;
declare variable ID_ART bigint;
declare variable APOS integer;
declare variable ART_CNT integer;
declare variable ID_ART_DEP bigint;
begin
  for
    SELECT sales_area.id from sales_area where sales_area.is_active=1 into :id_sa do begin
    for
        SELECT department.id from department where department.is_active=1 into :id_dep do begin
        apos = 0;
        for
            SELECT article_position_ex.id_articles
            from article_position_ex
            join articles on articles.id = article_position_ex.id_articles and articles.is_active=1
            where article_position_ex.id_sales_area = :id_sa and article_position_ex.id_department=:id_dep
            order by article_position_ex.pos into :id_art do begin

              select count(*) from DEPARTMENT_ARTICLES
                where id_articles=:id_art and id_department=:id_dep and is_active=1 into :art_cnt;
              select id_departament from articles where id = :id_art into id_art_dep;
              if (id_art_dep <> id_dep AND art_cnt=0) then begin
                delete from article_position_ex
                where article_position_ex.ID_articles = :id_art and article_position_ex.id_department=:id_dep
                    and article_position_ex.id_sales_area= :id_sa;
              end else begin
                update article_position_ex set pos = :apos
                where article_position_ex.ID_articles = :id_art and article_position_ex.id_department=:id_dep and article_position_ex.id_sales_area= :id_sa;
                apos = apos + 1;
              end
            end
        end
    end
end
!!
commit
!!
grant execute on procedure FIX_POS_ARTICLE_POSITIONS to untilluser
!!
commit
!!
set term ;!!
