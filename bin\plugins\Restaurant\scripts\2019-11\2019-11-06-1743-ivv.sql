create table article_sm_barcodes(
    id u_id,
    id_ARTICLE_barcodes 	bigint,
    id_size_modifier_item	bigint,
    barcode			varchar(50),	
    is_active 			smallint,
    IS_ACTIVE_MODIFIED 		timestamp,
    IS_ACTIVE_MODIFIER 		varchar(30),
    constraint article_sm_barcodes_pk primary key (id),
    constraint article_sm_barcodes_fk1 foreign key (id_ARTICLE_barcodes) references ARTICLE_barcodes(id),
    constraint article_sm_barcodes_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on article_sm_barcodes to untilluser;
commit;
execute procedure register_sync_table_ex('article_sm_barcodes', 'b', 1);
commit;
execute procedure register_bo_table('article_sm_barcodes', 'id_ARTICLE_barcodes', 'ARTICLE_barcodes');
commit;
execute procedure register_bo_table('article_sm_barcodes', 'id_size_modifier_item', 'size_modifier_item');
commit;

