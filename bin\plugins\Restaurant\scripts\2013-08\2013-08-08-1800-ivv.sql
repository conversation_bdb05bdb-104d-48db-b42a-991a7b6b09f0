create table article_ks_notify (
    id u_id,
    ID_articles bigint,
    ID_KS bigint,
    ID_ks_PURPOSE bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint aksn_pk primary key (id),
	constraint aksn_fk_art foreign key (ID_articles) references articles(id),
	constraint aksn_fk_ks foreign key (ID_KS) references kitchen_screens(id),
	constraint aksn_fk_ksp foreign key (ID_ks_PURPOSE) references kitchen_screen_purposes(id)
);
commit;
grant all on article_ks_notify to untilluser;
commit;
execute procedure register_sync_table_ex('article_ks_notify', 'b', 1);
commit;
execute procedure register_bo_table('article_ks_notify', '', '');
commit;


