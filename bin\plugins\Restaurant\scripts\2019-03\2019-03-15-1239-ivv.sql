create table allergens (
	id 			u_id,
	number 			integer,
	ml_name 		blob,
	picture 		blob,
	is_active 		smallint,
        IS_ACTIVE_MODIFIED 	timestamp,
        IS_ACTIVE_MODIFIER 	varchar(30),
	constraint allergens_pk primary key (id)
);
commit;
grant all on allergens to untilluser;
commit;
execute procedure register_sync_table_ex('allergens', 'b', 1);
commit;
execute procedure register_bo_table('allergens', '', '');
commit;

