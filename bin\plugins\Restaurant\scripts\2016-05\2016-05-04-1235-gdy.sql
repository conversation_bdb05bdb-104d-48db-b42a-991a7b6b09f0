DELETE FROM AKS_ITEM_ENTRIES aks
    WHERE NOT EXISTS (
        SELECT * FROM order_item oi WHERE oi.ID = aks.ID_ORDER_ITEM OR oi.ID =
            aks.ID_ORDER_ITEM_REMOVED
    ) OR NOT EXISTS (
        SELECT * FROM ARTICLES a WHERE a.ID = aks.ID_ARTICLES OR a.ID =
            aks.ID_MENU_ARTICLES
    ) OR NOT EXISTS (
        SELECT * FROM BILL b WHERE b.ID = aks.ID_BILL
    ) OR NOT EXISTS (
        SELECT * FROM MENU_ITEM mi WHERE mi.ID = aks.ID_MENU_ITEM OR mi.ID =
            aks.ID_MENU_ITEM_REMOVED
    );
COMMIT;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_1 FOREIGN KEY (ID_ORDER_ITEM) REFERENCES ORDER_ITEM (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_2 FOREIGN KEY (ID_ARTICLES) REFERENCES ARTICLES (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_3 FOREIGN KEY (ID_ORDER_ITEM_REMOVED) REFERENCES ORDER_ITEM (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_4 FOREIGN KEY (ID_BILL) REFERENCES BILL (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_5 FOREIGN KEY (ID_MENU_ITEM) REFERENCES MENU_ITEM (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_6 FOREIGN KEY (ID_MENU_ARTICLES) REFERENCES ARTICLES (ID) ON DELETE CASCADE;
ALTER TABLE AKS_ITEM_ENTRIES ADD CONSTRAINT FK_AKS_ITEM_ENTRIES_7 FOREIGN KEY (ID_MENU_ITEM_REMOVED) REFERENCES MENU_ITEM (ID) ON DELETE CASCADE;

COMMIT;
