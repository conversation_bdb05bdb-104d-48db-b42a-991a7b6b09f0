alter table discount_reasons add guid varchar(36);
commit;
CREATE INDEX idx_discount_reasons_guid ON discount_reasons(guid);
commit;
update discount_reasons set guid=uuid_to_char(gen_uuid());
commit;
alter table discount_reasons add constraint discount_reasons_guid unique(guid);
commit;
set term !! ;
EXECUTE BLOCK AS BEGIN
  IF (SUBSTRING(RDB$GET_CONTEXT('SYSTEM', 'ENGINE_VERSION') FROM 1 FOR 2) = '2.') THEN
    EXECUTE STATEMENT 'UPDATE RDB$RELATION_FIELDS SET RDB$NULL_FLAG = 1 WHERE RDB$FIELD_NAME = ''GUID'' AND RDB$RELATION_NAME = ''DISCOUNT_REASONS''';
  ELSE
    EXECUTE STATEMENT 'ALTER TABLE DISCOUNT_REASONS ALTER COLUMN GUID SET NOT NULL';
END
!!
COMMIT
!!
create or alter trigger discount_reasons_guid_trigger for discount_reasons
active before insert
as
begin
    if (new.guid is null) then begin
      new.guid = uuid_to_char(gen_uuid());
    end                                            
end
!!
commit
!!
set term ; !!

