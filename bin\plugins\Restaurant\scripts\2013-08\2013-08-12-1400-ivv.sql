create table ks_routing (
    id u_id,
    id_ks_from bigint,
    id_ks_to bigint,
	id_untill_users bigint,
	rout_datetime timestamp,
    constraint ksr_routing_pk primary key (id),
	constraint ksr_fk_ks1 foreign key (id_ks_from) references kitchen_screens(id),
	constraint ksr_fk_ks2 foreign key (id_ks_to) references kitchen_screens(id),
	constraint ksr_fk_uu foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on ks_routing to untilluser;
commit;
execute procedure register_sync_table_ex('ks_routing', 'p', 1);
commit;

create table ks_routing_courses (
    id u_id,
    id_ks_routing bigint,
    id_courses bigint,
    constraint ksrc_pk primary key (id),
	constraint ksrc_fk_ksr foreign key (id_ks_routing) references ks_routing(id),
	constraint ksrc_fk_courses foreign key (id_courses) references courses(id)
);
commit;
grant all on ks_routing_courses to untilluser;
commit;
execute procedure register_sync_table_ex('ks_routing_courses', 'p', 1);
commit;
