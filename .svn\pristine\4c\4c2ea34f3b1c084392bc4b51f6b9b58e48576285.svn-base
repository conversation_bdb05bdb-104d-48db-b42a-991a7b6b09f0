unit PromoMassModifyFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, MassModifyFram, UntillDBU, EntityManagerU, ArticleAvailEmbEntityManager,
  ArticleNotifyEmbEntityManager, CourseEntityManager, DepartmentEntityManager,
  PromotionEntityManager, CommissionEntityManager, SizeModifierEntityManager,
  CurrencyEntityManager, CommonU, ArticleOptionEmbEntityManager,
  PropFontDlg, ArticlePriceEmbEntityManager, UntillEquU,
  ColorComboU, UntillSpinEditU, StdCtrls, UntillRealEdit, TntCompatibilityU,
  UntillComboBoxU, UntillCheckBoxU, ExtCtrls, ArticleFreeOptionEmbEntityManager,
  UntillSelectBoxU, ComCtrls, RestaurantCommonStringsU,
  UntillPageControl, ArticleModifyPriceEmbEntityManager, CheckLst,
  UntillRadioButtonU, VDGroupsEntityManager, UntillCheckListBoxU,
  BOUntillDBU, OptionEntityManager, UTF8U, ButtonPropSelectFram,
  ArticleKSNotifyEmbEntityManager, SupplierEntityManager, PUAEntityManager;

type
  TPromoMassModifyFrame = class(TMassModifyFrame)
    pcMain: TUntillPageControl;
    tsGeneral: TTntTabSheet;
    lblNewDepartment: TTntLabel;
    usbNewDepartment: TUntillSelectBox;
    chbChangeDepartment: TUntillCheckBox;
    chbAddAvailable: TUntillCheckBox;
    pnlNewAvailable: TTntPanel;
    tsAppearance: TTntTabSheet;
    cmbNewPCColor: TTntColorComboBox;
    cmbHHTColor: TTntColorComboBox;
    cmbOManColor: TTntColorComboBox;
    usbNewPCFont: TUntillSelectBox;
    chbChangePCAppearance: TUntillCheckBox;
    lblNewAvailable: TBlockHeading;
    cmbPCAppGroup: TUntillComboBox;
    chbChangePCColor: TCheckBox;
    chbChangePCFont: TCheckBox;
    chbChangeHHTAppearance: TUntillCheckBox;
    usbHHTFont: TUntillSelectBox;
    cmbHHTAppGroup: TUntillComboBox;
    chbChangeHHTColor: TCheckBox;
    chbChangeHHTFont: TCheckBox;
    chbChangeOManAppearance: TUntillCheckBox;
    usbOManFont: TUntillSelectBox;
    cmbOManAppGroup: TUntillComboBox;
    chbChangeOManColor: TCheckBox;
    chbChangeOManFont: TCheckBox;
    chbChangePCAltFont: TCheckBox;
    usbNewPCAltFont: TUntillSelectBox;
    chbChangeOManAltFont: TCheckBox;
    usbOManAltFont: TUntillSelectBox;
    usbHHTAltFont: TUntillSelectBox;
    chbChangeHHTAltFont: TCheckBox;
    chbRemoveAvailable: TUntillCheckBox;
    chbChangeAvailable: TUntillCheckBox;
    lblHHTUseDefault: TTntLabel;
    lblOManUseDefault: TTntLabel;
    chbHHTDefaultColor: TUntillCheckBox;
    chbHHTDefaultFont: TUntillCheckBox;
    chbHHTDefaultAltFont: TUntillCheckBox;
    chbOManDefaultColor: TUntillCheckBox;
    chbOManDefaultFont: TUntillCheckBox;
    chbOManDefaultAltFont: TUntillCheckBox;
    procedure CheckBoxRefresh(Sender:TObject);
    procedure usbNewDepartmentButtonClick(Sender: TObject);
    procedure usbNewPCFontButtonClick(Sender: TObject);
    procedure usbHHTFontButtonClick(Sender: TObject);
    procedure usbOManFontButtonClick(Sender: TObject);
    procedure chbChangePCFontClick(Sender: TObject);
    procedure chbChangePCAltFontClick(Sender: TObject);
    procedure usbNewPCAltFontButtonClick(Sender: TObject);
    procedure usbHHTAltFontButtonClick(Sender: TObject);
    procedure usbOManAltFontButtonClick(Sender: TObject);
    procedure chbChangePCColorClick(Sender: TObject);
    procedure cmbNewPCColorExit(Sender: TObject);
    procedure cmbHHTColorExit(Sender: TObject);
    procedure cmbOManColorExit(Sender: TObject);
  private
    fdbguid : string;
    procedure FillDeletePrices;
    procedure FillScreenGroups(cmb: TUntillComboBox; st: TBtnType);
  public
    emDepartment : TDepartmentEntityManager;
    emArticleAvail : TArticleAvailEmbEntityManager;

    NewPCFont: TFont;
    NewHHTFont: TFont;
    NewOManFont : TFont;
    NewPCAltFont: TFont;
    NewHHTAltFont: TFont;
    NewOManAltFont : TFont;

    procedure TranslateStrings; override;
    procedure Initialize(UntillDB: TBOCustomUntillDB; AManager: TEntityManager); override;
    function  NothingToUpdate:Boolean;

    destructor Destroy; override;
  end;

var
  PromoMassModifyFrame: TPromoMassModifyFrame;

implementation

uses RestaurantPluginU, UntillFram, ClassesU, DataControlsU, FixedIdU,
  PermsProviderU, RegClassesU;

{$R *.dfm}

{ TMassModifyFrame1 }

procedure TPromoMassModifyFrame.chbChangePCColorClick(Sender: TObject);
begin
  inherited;
  cmbNewPCColor.Enabled := chbChangePCColor.checked;
end;

procedure TPromoMassModifyFrame.chbChangePCAltFontClick(Sender: TObject);
begin
  inherited;
  usbNewPCAltFont.enabled := chbChangePCAltFont.checked;
end;

procedure TPromoMassModifyFrame.chbChangePCFontClick(Sender: TObject);
begin
  inherited;
  usbNewPCFont.enabled := chbChangePCFont.Checked;
end;

procedure TPromoMassModifyFrame.CheckBoxRefresh(Sender: TObject);
var b:boolean;
begin
  if chbChangeDepartment.Checked then b:=true else b:=false;
  lblNewDepartment.Enabled:=b;
  usbNewDepartment.Enabled:=b;

  if chbAddAvailable.Checked or chbRemoveAvailable.Checked or chbChangeAvailable.checked then
    b := true
  else
    b := false;
  if Sender = chbAddAvailable then begin
    if chbAddAvailable.checked then begin
      lblNewAvailable.Caption:=Plugin.TranslateLabel('ArticleMassModifyingDlg','New available','Label caption');
      if chbRemoveAvailable.Checked then chbRemoveAvailable.Checked := false;
      if chbChangeAvailable.Checked then chbChangeAvailable.checked := false;
    end
  end else if Sender = chbRemoveAvailable then begin
    if chbRemoveAvailable.checked then begin
      lblNewAvailable.Caption:=Plugin.TranslateLabel('ArticleMassModifyingDlg','Remove available','Label caption');
      if chbAddAvailable.Checked then
        chbAddAvailable.checked := false;
      if chbChangeAvailable.Checked then
        chbChangeAvailable.checked := false;
    end;
  end else if Sender = chbChangeAvailable then begin
    if chbChangeAvailable.checked then begin
      lblNewAvailable.Caption:=Plugin.TranslateLabel('ArticleMassModifyingDlg','Replace available','Label caption');
      if chbAddAvailable.Checked then
        chbAddAvailable.checked := false;
      if chbRemoveAvailable.Checked then
        chbRemoveAvailable.checked := false;
    end;
  end;
  lblNewAvailable.Enabled:=b;
  RecursiveChangeEnabled(pnlNewAvailable, b);

  if chbChangePCAppearance.Checked then
    b := true
  else
    b := false;
  cmbPCAppGroup.Enabled := b;
  cmbNewPCColor.Enabled := b;
  chbChangePCColor.Enabled := b;
  usbNewPCFont.Enabled := b;
  chbChangePCFont.Enabled := b;
  usbNewPCAltFont.Enabled := b;
  chbChangePCAltFont.Enabled := b;

  if chbChangeHHTAppearance.Checked then
    b := true
  else
    b := false;
  chbChangeHHTColor.Enabled := b;
  chbChangeHHTFont.Enabled := b;
  chbChangeHHTAltFont.Enabled := b;
  chbHHTDefaultColor.Enabled := b and chbChangeHHTColor.Checked;
  chbHHTDefaultFont.Enabled := b and chbChangeHHTFont.Checked;
  chbHHTDefaultAltFont.Enabled := b and chbChangeHHTAltFont.Checked;
  cmbHHTAppGroup.Enabled := b;
  cmbHHTColor.Enabled := chbChangeHHTColor.Checked and not chbHHTDefaultColor.Checked;
  usbHHTFont.Enabled := chbChangeHHTFont.Checked and not chbHHTDefaultFont.Checked;
  usbHHTAltFont.Enabled := chbChangeHHTAltFont.Checked and not chbHHTDefaultAltFont.Checked;

  if chbChangeOManAppearance.Checked then b:=true else b:=false;
  chbChangeOManColor.Enabled := b;
  chbChangeOManFont.Enabled := b;
  chbChangeOManAltFont.Enabled := b;
  chbOManDefaultColor.Enabled := b and chbChangeOManColor.Checked;
  chbOManDefaultFont.Enabled := b and chbChangeOManFont.Checked;
  chbOManDefaultAltFont.Enabled := b and chbChangeOManAltFont.Checked;
  cmbOManAppGroup.Enabled := b;
  cmbOManColor.Enabled := chbChangeOManColor.Checked and not chbOManDefaultColor.Checked;
  usbOManFont.Enabled := chbChangeOManFont.Checked and not chbOManDefaultFont.Checked;
  usbOManAltFont.Enabled := chbChangeOManAltFont.Checked and not chbOManDefaultAltFont.Checked;
end;

procedure TPromoMassModifyFrame.cmbHHTColorExit(Sender: TObject);
begin
  inherited;
  plugin.SaveCustomColors( cmbHHTColor );
end;

procedure TPromoMassModifyFrame.cmbNewPCColorExit(Sender: TObject);
begin
  inherited;
  plugin.SaveCustomColors( cmbNewPCColor );
end;

procedure TPromoMassModifyFrame.cmbOManColorExit(Sender: TObject);
begin
  inherited;
  plugin.SaveCustomColors( cmbOManColor );
end;

destructor TPromoMassModifyFrame.Destroy;
begin
  FreeAndNil(emDepartment);
  FreeAndNil(emArticleAvail);
  FreeAndNil(NewPCFont);
  FreeAndNil(NewHHTFont);
  FreeAndNil(NewOManFont);
  FreeAndNil(NewPCAltFont);
  FreeAndNil(NewHHTAltFont);
  FreeAndNil(NewOManAltFont);

  cmbPCAppGroup.ClearWithObjects;
  cmbHHTAppGroup.ClearWithObjects;
  cmbOManAppGroup.ClearWithObjects;
  inherited;
end;

procedure TPromoMassModifyFrame.Initialize(UntillDB: TBOCustomUntillDB; AManager: TEntityManager);
begin
  inherited;
  fdbguid := UntillDB.GetGuid;
  NewPCFont:=TFont.Create;
  NewPCFont.Assign(Self.Font);
  NewHHTFont:=TFont.Create;
  NewHHTFont.Assign(Self.Font);
  NewOManFont:=TFont.Create;
  NewOManFont.Assign(Self.Font);
  NewPCAltFont:=TFont.Create;
  NewPCAltFont.Assign(Self.Font);
  NewHHTAltFont:=TFont.Create;
  NewHHTAltFont.Assign(Self.Font);
  NewOManAltFont:=TFont.Create;
  NewOManAltFont.Assign(Self.Font);
  emDepartment := TDepartmentEntityManager.Create(Self,UntillDB);

  emArticleAvail := TArticleAvailEmbEntityManager.Create(Self, AManager, -1);
  emArticleAvail.CreateListFrame(pnlNewAvailable);

  pcMain.ActivePageIndex:=0;
  CheckBoxRefresh(Self);
  FillDeletePrices;
  FillScreenGroups(cmbPCAppGroup, btnPC);
  FillScreenGroups(cmbHHTAppGroup, btnHHT);
  FillScreenGroups(cmbOManAppGroup, btnOMan);
  plugin.GetCustomColors;
  plugin.InitCustomColors( fdbguid, cmbNewPCColor);
  plugin.InitCustomColors( fdbguid, cmbHHTColor);
  plugin.InitCustomColors( fdbguid, cmbOManColor);
end;

procedure TPromoMassModifyFrame.TranslateStrings;
begin
  inherited;
  Caption:=Plugin.Translate('ArticleMassModifyingDlg','Mass modifying','Form caption');
  lblNewAvailable.Caption:=Plugin.TranslateLabel('ArticleMassModifyingDlg','New available','Label caption');
  lblHHTUseDefault.Caption := Plugin.TranslateLabel('ArticleEntityManager', 'Use default', 'Label caption');
  lblOManUseDefault.Caption := Plugin.TranslateLabel('ArticleEntityManager', 'Use default', 'Label caption');

  chbChangePCColor.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change color','Checkbox caption');
  chbChangePCFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change font','Checkbox caption');
  chbChangeHHTColor.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change color','Checkbox caption');
  chbChangeHHTFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change font','Checkbox caption');

  chbChangeOManColor.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change color','Checkbox caption');
  chbChangeOManFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change font','Checkbox caption');
  chbChangePCAltFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change alt. font','Checkbox caption');
  chbChangeHHTAltFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change alt. font','Checkbox caption');
  chbChangeOManAltFont.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change alt. font','Checkbox caption');

  chbChangeDepartment.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change department','Checkbox caption');
  chbAddAvailable.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Add available','Checkbox caption');
  chbChangeAvailable.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Replace available','Checkbox caption');
  chbChangePCAppearance.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change PC appearance','Checkbox caption');
  chbChangeHHTAppearance.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change HHT appearance','Checkbox caption');
  chbChangeOManAppearance.Caption:=Plugin.Translate('ArticleMassModifyingDlg','Change Orderman appearance','Checkbox caption');

  tsGeneral.Caption:=Plugin.Translate('ArticleMassModifyingDlg','&1 General','Page caption');
  tsAppearance.Caption:=Plugin.Translate('ArticleMassModifyingDlg','&4 Appearance','Page caption');

end;

procedure TPromoMassModifyFrame.usbNewDepartmentButtonClick(Sender: TObject);
var
  res:Int64;
begin
  inherited;
  with usbNewDepartment, emDepartment do begin
    res:=ShowModal(Value);
    if res>0 then begin
      Value:=res;
      Text := GetWideStringById('name',res);
    end;
    SetFocus;
  end;
end;

procedure TPromoMassModifyFrame.usbOManAltFontButtonClick(
  Sender: TObject);
var TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewOManAltFont);
    if PropFontDlg.Execute(chbChangeOManAppearance.Caption, fdbguid, TempFont) then begin
      usbOManAltFont.Text := TempFont.Name;
      NewOManAltFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

procedure TPromoMassModifyFrame.usbOManFontButtonClick(Sender: TObject);
var TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewOManFont);
    if PropFontDlg.Execute(chbChangeOManAppearance.Caption, fdbguid, TempFont) then begin
      usbOManFont.Text := TempFont.Name;
      NewOManFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

procedure TPromoMassModifyFrame.usbNewPCAltFontButtonClick(Sender: TObject);
var  TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewPCAltFont);
    if PropFontDlg.Execute(chbChangePCAppearance.Caption, fdbguid, TempFont) then begin
      usbNewPCAltFont.Text := TempFont.Name;
      NewPCAltFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

procedure TPromoMassModifyFrame.usbNewPCFontButtonClick(Sender: TObject);
var TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewPCFont);
    if PropFontDlg.Execute(chbChangePCAppearance.Caption, fdbguid, TempFont) then begin
      usbNewPCFont.Text := TempFont.Name;
      NewPCFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

function TPromoMassModifyFrame.NothingToUpdate: Boolean;
begin
  result := true;
  if chbChangeDepartment.Checked
    or chbAddAvailable.Checked
    or chbChangeAvailable.Checked
    or chbRemoveAvailable.Checked
    or chbChangePCAppearance.Checked
    or chbChangeHHTAppearance.Checked
    or chbChangeOManAppearance.Checked
    or chbChangeHHTColor.Checked
    or chbChangeHHTFont.Checked
    or chbChangeHHTAltFont.Checked
    or chbChangeOManColor.Checked
    or chbChangeOManFont.Checked
    or chbChangeOManAltFont.Checked
    or chbHHTDefaultColor.Checked
    or chbHHTDefaultFont.Checked
    or chbHHTDefaultAltFont.Checked
    or chbOManDefaultColor.Checked
    or chbOManDefaultFont.Checked
    or chbOManDefaultAltFont.Checked
  then
    result := false;
end;

procedure TPromoMassModifyFrame.FillDeletePrices;
begin
end;

procedure TPromoMassModifyFrame.FillScreenGroups(cmb: TUntillComboBox; st: TBtnType);
var q : IIBSQL;
    i: integer;
begin
  q := FUntillDB.GetPreparedIIbSql('select * from screen_groups where screen_type=:type and is_active=1');
  if st = btnPC then
    q.q.ParamByName('type').AsInteger:=0
  else if st = btnHHT then
    q.q.ParamByName('type').AsInteger:=1
  else
    q.q.ParamByName('type').AsInteger:=2;
  q.ExecQuery;
  while not q.Eof do begin
    cmb.AddItem(q.q.fieldByName('name').asString,TInt64.Create(StrToInt64Def(q.q.fieldByName('id').asString,0)) );
    q.Next;
  end;
  for I := 0 to cmb.items.count do
    if (TInt64(cmb.items.Objects[i]).Value=ID_SCREEN_GROUPS_PC)
       or (TInt64(cmb.items.Objects[i]).Value=ID_SCREEN_GROUPS_FALCON)
       or (TInt64(cmb.items.Objects[i]).Value=ID_SCREEN_GROUPS_ORDERMAN)
    then begin
      cmb.ItemIndex := I;
      break;
    end;
end;

procedure TPromoMassModifyFrame.usbHHTAltFontButtonClick(Sender: TObject);
var TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewHHTAltFont);
    if PropFontDlg.Execute(chbChangeHHTAppearance.Caption, fdbguid, TempFont) then begin
      usbHHTAltFont.Text := TempFont.Name;
      NewHHTAltFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

procedure TPromoMassModifyFrame.usbHHTFontButtonClick(Sender: TObject);
var TempFont: TFont;
begin
  inherited;
  TempFont := TFont.Create;
  try
    TempFont.Assign(NewHHTFont);
    if PropFontDlg.Execute(chbChangePCAppearance.Caption, fdbguid,TempFont) then begin
      usbHHTFont.Text := TempFont.Name;
      NewHHTFont.Assign(TempFont);
    end;
  finally
    TempFont.Free;
  end;
end;

end.
