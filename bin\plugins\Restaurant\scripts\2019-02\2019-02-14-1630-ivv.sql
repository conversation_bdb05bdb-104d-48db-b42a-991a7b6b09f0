create table voucher_pbill(
    id u_id,
    number integer,
    FAILUREDNUMBER integer,
    SUFFIX varchar(3),
    pdatetime timestamp,
    id_untill_users bigint,
    pcname varchar(50),
    constraint voucher_pbill_pk primary key (id),
    constraint voucher_pbill_fk1 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on voucher_pbill to untilluser;
commit;
execute procedure register_sync_table_ex('voucher_pbill', 'p', 1);
commit;

create table voucher_pbill_payments (
    id u_id,
    id_voucher_pbill bigint,
    price decimal(17,4),
    id_vouchers bigint,
    id_payments bigint,
    constraint vchpbp_pk primary key (id),
    constraint vchpbp_fk1 foreign key (id_voucher_pbill) references voucher_pbill(id),
    constraint vchpbp_fk2 foreign key (id_vouchers) references vouchers(id),
    constraint vchpbp_fk3 foreign key (id_payments) references payments(id)
);
commit;
grant all on voucher_pbill_payments to untilluser;
commit;
execute procedure register_sync_table_ex('voucher_pbill_payments', 'p', 1);
commit;
