create table inv_loc_period_normal (
    id u_id,
    ID_PERIODS bigint,
    ID_INVENTORY_ITEM_LOCATIONS bigint,
    normal_value decimal(17,4),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint inv_loc_period_normal_pk primary key (id),
	constraint inv_loc_period_normal_fk1 foreign key (ID_PERIODS) references PERIODS(id),
	constraint inv_loc_period_normal_fk2 foreign key (ID_INVENTORY_ITEM_LOCATIONS) references INVENTORY_ITEM_LOCATIONS(id)
);
commit;
grant all on inv_loc_period_normal to untilluser;
commit;
execute procedure register_sync_table_ex('inv_loc_period_normal', 'b', 1);
commit;
execute procedure register_bo_table('inv_loc_period_normal', 'ID_PERIODS', 'PERIODS');
commit;
execute procedure register_bo_table('inv_loc_period_normal', 'ID_INVENTORY_ITEM_LOCATIONS', 'INVENTORY_ITEM_LOCATIONS');
commit;

