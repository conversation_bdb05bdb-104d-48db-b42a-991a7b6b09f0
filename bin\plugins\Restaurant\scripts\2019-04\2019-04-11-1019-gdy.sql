set term !! ;
CREATE OR ALTER procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint,
    ID_ORDERS bigint)
returns (
    OSIG varchar(1024))
as
declare variable ID_COURSES varchar(20);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable CHAIR_NUMBER integer;
declare variable ROWBEG integer;
declare variable TEXT varchar(20);
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select order_item.rowbeg, cast(order_item.kind as char(1)), 
         cast(coalesce(order_item.id_articles, 0) as varchar(20)), order_item.text,
         cast(coalesce(order_item.ID_COURSES, 0) as varchar(20)), order_item.CHAIR_NUMBER
  from order_item where order_item.id_orders=:ID_ORDERS and order_item.id >= :id_order_item
  into
       :rowbeg, :kind, :id_articles, :text, :id_courses, :chair_number
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || '/' || kind || '/' || chair_number;
    if (id_articles='0') then begin
      osig = osig || '/' || text;
    end else begin
      osig = osig || '/' || id_articles;
    end
    if (id_courses <> '0') then
        osig = osig || '/' || id_courses;
    osig = osig || '|';
    idx = idx + 1;
  end
  suspend;
end
!!

CREATE OR ALTER procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable CHAIR_NUMBER integer;
declare variable ID_COURSES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
declare variable ID_BILL bigint;
declare variable ID_MENU bigint;
begin
  idx = 0;
  osig = '';
  SELECT first 1 o.id_bill, mi.ID_MENU, oi.CHAIR_NUMBER
    from MENU_ITEM mi
    join order_item oi on mi.id_menu = oi.id_menu
    join orders o on o.ID = oi.ID_ORDERS
    where mi.id = :ID_MENU_ITEM
    into :id_bill, :id_menu, :chair_number;

  for
  select  menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text,
         cast(coalesce(menu_item.ID_COURSES, 0) as varchar(20))
         from menu_item
    join order_item oi on menu_item.id_menu = oi.id_menu
    join orders o on o.ID = oi.ID_ORDERS
  where menu_item.ID_MENU = :id_menu and o.ID_BILL = :id_bill and menu_item.id >= :ID_MENU_ITEM
    and OI.QUANTITY > 0

  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text, :id_courses
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || 'mi' || '/' || kind || '/' || CHAIR_NUMBER;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || '/' || id_articles;
    end
    if (id_courses <> '0') then
        osig = osig || '/' || id_courses;
    osig = osig || '|';
    idx = idx + 1;
  end
  suspend;
end
!!

commit
!!

set term ; !!