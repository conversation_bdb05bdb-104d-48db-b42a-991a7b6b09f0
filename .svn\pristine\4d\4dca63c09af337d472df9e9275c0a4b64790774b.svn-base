; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define JRE_INSTALLATION_PATH "C:/tools/untill/"
#define JRE_FILE "bellsoft-jre8u402+7-windows-i586-full.zip"
#define JRE_FOLDER "jre8u402-full"
#define JRE_URI_RESERVE "https://dev.untill.com/files/soft/"
#define JRE_URI "https://github.com/bell-sw/Liberica/releases/download/8u402%2B7/"



[Setup]
AppName={code:GetAppName|"1"}
AppVerName={code:GetAppName|"1"} {code:GetVersion|"1"}
AppPublisher=unTill
AppPublisherURL=https://www.untill.com
AppSupportURL=https://www.untill.com
AppUpdatesURL=https://www.untill.com
DefaultDirName={code:GetDestination|"1"}\{code:GetDirName|"1"}
DefaultGroupName={code:GetDirName|"1"}
OutputDir=.
OutputBaseFilename=mysetup
WizardImageFile=WizUntill.bmp
Compression=lzma2/ultra
SolidCompression=yes
UsePreviousLanguage=no
VersionInfoDescription=unTill
VersionInfoProductName=unTill
DisableWelcomePage=no
DisableDirPage=no
DisableProgramGroupPage=no
SetupLogging=yes
CloseApplications=no

[Tasks]
; NOTE: The following entry contains English phrases ("Create a desktop icon" and "Additional icons"). You are free to translate them into another language if required.
Name: "desktopicon"; Description: "Create a &desktop icon"; GroupDescription: "Additional icons:"
; NOTE: The following entry contains English phrases ("Create a Quick Launch icon" and "Additional icons"). You are free to translate them into another language if required.
Name: "quicklaunchicon"; Description: "Create a &Quick Launch icon"; GroupDescription: "Additional icons:"; Flags: unchecked

[Dirs]
Name: "{app}\scripts"
Name: "{app}\plugins\Restaurant\scripts"
Name: "{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}"

[InstallDelete]
Type: filesandordirs; Name: "{app}\tomcat"
Type: filesandordirs; Name: "{app}\jre"

Type: filesandordirs; Name: "{app}\plugins\drivers\vmax-fiscal-printer-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\samport-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\hs3-hotel-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\ingenico-telium-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\zapper-billshandler-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\magneds-compound-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\baxi-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\jsonsender-billshandler-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\itesso-compound-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\payplaza-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\digicash-compound-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\six-payment-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\oracle-xmlpos-compound-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\pepper-eft-driver"
Type: filesandordirs; Name: "{app}\plugins\drivers\mews"
Type: filesandordirs; Name: "{app}\plugins\drivers\uareu"
Type: filesandordirs; Name: "{app}\plugins\drivers\epay-zvt"
Type: filesandordirs; Name: "{app}\plugins\drivers\adyen"
Type: filesandordirs; Name: "{app}\plugins\drivers\payconiq"
Type: filesandordirs; Name: "{app}\plugins\drivers\resengo"
Type: filesandordirs; Name: "{app}\plugins\drivers\wigroup"
Type: filesandordirs; Name: "{app}\plugins\drivers\worldline"
Type: filesandordirs; Name: "{app}\plugins\drivers\intersolve-ok"
Type: filesandordirs; Name: "{app}\plugins\drivers\gastroguide"
Type: filesandordirs; Name: "{app}\plugins\drivers\celona"
Type: filesandordirs; Name: "{app}\plugins\drivers\eftpos"
Type: filesandordirs; Name: "{app}\plugins\drivers\discoversystems"
Type: filesandordirs; Name: "{app}\plugins\drivers\opi-its"
Type: filesandordirs; Name: "{app}\plugins\drivers\smarti"
Type: filesandordirs; Name: "{app}\plugins\drivers\vanduijnen-smarttap"
Type: filesandordirs; Name: "{app}\plugins\drivers\paymentsense"
Type: filesandordirs; Name: "{app}\plugins\drivers\jtech-pager"
Type: filesandordirs; Name: "{app}\plugins\drivers\efsta"
Type: filesandordirs; Name: "{app}\plugins\drivers\datecs-dude"
Type: filesandordirs; Name: "{app}\plugins\drivers\datecs-fp-bulgaria"
Type: filesandordirs; Name: "{app}\plugins\drivers\hugin-fpu"
Type: filesandordirs; Name: "{app}\plugins\drivers\eftpos-nz"
Type: filesandordirs; Name: "{app}\plugins\drivers\onlinewerkrooster"
Type: filesandordirs; Name: "{app}\plugins\drivers\splitt"
Type: filesandordirs; Name: "{app}\plugins\drivers\loyaltek-transhandler"
Type: filesandordirs; Name: "{app}\plugins\drivers\icg-cashdro-web"
Type: filesandordirs; Name: "{app}\plugins\drivers\glory-money-machine"
Type: filesandordirs; Name: "{app}\plugins\drivers\hoist-hotsoft"
Type: filesandordirs; Name: "{app}\plugins\drivers\protel"
Type: filesandordirs; Name: "{app}\plugins\drivers\synergy-pf-550-mk"
Type: filesandordirs; Name: "{app}\plugins\drivers\adyen-pos"
Type: filesandordirs; Name: "{app}\plugins\drivers\sevenrooms"
Type: filesandordirs; Name: "{app}\plugins\drivers\cashmatic"
Type: filesandordirs; Name: "{app}\plugins\drivers\takeawaycom"
Type: filesandordirs; Name: "{app}\plugins\drivers\efsta-austria"
Type: filesandordirs; Name: "{app}\plugins\drivers\dinetime"
Type: filesandordirs; Name: "{app}\plugins\drivers\netresto"
Type: filesandordirs; Name: "{app}\plugins\drivers\docmsign-fp-greece"
Type: filesandordirs; Name: "{app}\plugins\drivers\rbsrmcp-fp-greece"
Type: filesandordirs; Name: "{app}\plugins\drivers\piggy-giftcards"
Type: filesandordirs; Name: "{app}\plugins\drivers\clock-pms"
Type: filesandordirs; Name: "{app}\plugins\drivers\piggy-loyalty"
Type: filesandordirs; Name: "{app}\plugins\drivers\guestline"
Type: filesandordirs; Name: "{app}\plugins\drivers\clover"
Type: filesandordirs; Name: "{app}\plugins\drivers\payplaza-easypos"
Type: filesandordirs; Name: "{app}\plugins\drivers\epson-fp-curacao"
Type: filesandordirs; Name: "{app}\plugins\drivers\ccv-mapi"
Type: filesandordirs; Name: "{app}\plugins\drivers\sa97-fp-lithuania"
Type: filesandordirs; Name: "{app}\plugins\drivers\jcc-verifone-eft"
Type: filesandordirs; Name: "{app}\plugins\drivers\adoria"
Type: filesandordirs; Name: "{app}\plugins\drivers\apaleo-hotel"
Type: filesandordirs; Name: "{app}\plugins\drivers\xafax-netpay-eft"
Type: filesandordirs; Name: "{app}\plugins\drivers\asa-hotel"
Type: filesandordirs; Name: "{app}\plugins\drivers\piggy-vouchers"
Type: filesandordirs; Name: "{app}\plugins\drivers\retail-force"
Type: filesandordirs; Name: "{app}\plugins\drivers\pax-s5"
Type: filesandordirs; Name: "{app}\plugins\drivers\loyyo-cards"
Type: filesandordirs; Name: "{app}\plugins\drivers\t-flex-cash-machine"
Type: filesandordirs; Name: "{app}\plugins\drivers\shiji-hotel"
Type: filesandordirs; Name: "{app}\plugins\drivers\klearly"
Type: filesandordirs; Name: "{app}\plugins\drivers\sentoo"
Type: filesandordirs; Name: "{app}\plugins\drivers\zenchef"
Type: filesandordirs; Name: "{app}\plugins\drivers\viva-wallet-hht"
Type: filesandordirs; Name: "{app}\plugins\drivers\viva-wallet"

Type: filesandordirs; Name: "{app}\plugins\Restaurant\runtime"


[Files]
; NOTE: Don't use "Flags: ignoreversion" on any shared system files
Source: "..\build\bin\StopAll.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\dlcheck.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\unTill.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\Untill_debug.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace skipifsourcedoesntexist
Source: "..\build\bin\Untillsrv.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\DBUpgrade.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\UntillWD.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\untillsqlsrv.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\servicemgr.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\instsvc.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\instsql.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace uninsrestartdelete
Source: "..\build\bin\UntillKernel.bpl"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\*.lng"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\build\bin\LangEdit.exe"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\images\*.*"; DestDir: "{app}\images"; Flags: ignoreversion
Source: "..\build\bin\*.bpl"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\*.dll"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\libssl32.dll"; DestDir: "{app}"; DestName: "ssleay32.dll"; Flags: ignoreversion restartreplace
Source: "..\build\bin\SaxComm8.ocx"; DestDir: "{sys}"; Flags: restartreplace regserver
Source: "..\build\bin\VmaxOCX_CW.ocx"; DestDir: "{sys}"; Flags: restartreplace regserver
Source: "..\build\bin\syslt.belgium.dat"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\syslt.sweden.dat"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\syslt.gdpdu.dat"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\syslt.sysrep.dat"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\syslt.france.dat"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "{src}\syslt.dat"; DestDir: "{app}"; Check: IsCustomSysLT; Flags: ignoreversion external
Source: "..\build\scripts\UntillKernel\sql\*.*"; DestDir: "{app}\sql"; Flags: ignoreversion recursesubdirs
Source: "..\build\scripts\UntillKernel\scripts\*.*"; DestDir: "{app}\scripts"; Flags: ignoreversion recursesubdirs skipifsourcedoesntexist createallsubdirs
Source: "..\build\bin\untilludf.dll"; DestDir: "{code:GetFirebirdDir}\UDF"; Flags: ignoreversion uninsneveruninstall restartreplace; Check: IsUdfFilesDiffer
Source: "..\build\bin\midas.dll"; DestDir: "{cf}\unTill\dll"; Flags: sharedfile uninsneveruninstall
Source: "..\build\DelphiBPLs\*.bpl"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\ICGCashDroDll\*.*"; DestDir: "{app}\ICGCashDroDll"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "7za.exe"; DestDir: "{tmp}"; Flags: ignoreversion
Source: "..\build\bin\checkports.dll"; Flags: dontcopy
Source: "..\build\bin\curl-7.81.0-win32-mingw\bin\curl.exe"; Flags: dontcopy
Source: "..\build\bin\gdpdu-*.dtd"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\utils\external-ip-provider.exe"; Flags: dontcopy
Source: "fopcfg.xml"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "ServiceMgrTask.xml"; DestDir: "{tmp}"


; ********************************* PLUGINS ******************************************
; MainGraphicsElements
Source: "..\build\bin\plugins\maingraphicelements\MainGraphicElements.bpl"; DestDir: "{app}\plugins\maingraphicelements"; Flags: ignoreversion restartreplace
Source: "..\build\bin\plugins\maingraphicelements\images\*.*"; DestDir: "{app}\plugins\maingraphicelements\images"; Flags: ignoreversion recursesubdirs
Source: "..\build\bin\plugins\maingraphicelements\*.lng"; DestDir: "{app}\plugins\maingraphicelements"; Flags: ignoreversion

; Restaurant
Source: "..\build\bin\plugins\Restaurant\Restaurant.bpl"; DestDir: "{app}\plugins\Restaurant"; Flags: ignoreversion restartreplace
Source: "..\build\bin\plugins\Restaurant\images\*.*"; DestDir: "{app}\plugins\Restaurant\images"; Flags: ignoreversion recursesubdirs
Source: "..\build\scripts\Restaurant\sql\*.*"; DestDir: "{app}\plugins\Restaurant\sql"; Flags: ignoreversion recursesubdirs
Source: "..\build\scripts\Restaurant\scripts\*.*"; DestDir: "{app}\plugins\Restaurant\scripts"; Flags: ignoreversion recursesubdirs skipifsourcedoesntexist createallsubdirs
Source: "..\build\bin\plugins\Restaurant\*.lng"; DestDir: "{app}\plugins\Restaurant"; Flags: ignoreversion

; Drivers
Source: "..\build\bin\plugins\drivers\vmax-fiscal-printer-driver\*"; DestDir: "{app}\plugins\drivers\vmax-fiscal-printer-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\samport-eft-driver\*"; DestDir: "{app}\plugins\drivers\samport-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\hs3-hotel-driver\*"; DestDir: "{app}\plugins\drivers\hs3-hotel-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\ingenico-telium-eft-driver\*"; DestDir: "{app}\plugins\drivers\ingenico-telium-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\magneds-compound-driver\*"; DestDir: "{app}\plugins\drivers\magneds-compound-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\baxi-eft-driver\*"; DestDir: "{app}\plugins\drivers\baxi-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\jsonsender-billshandler-driver\*"; DestDir: "{app}\plugins\drivers\jsonsender-billshandler-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\itesso-compound-driver\*"; DestDir: "{app}\plugins\drivers\itesso-compound-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\payplaza-eft-driver\*"; DestDir: "{app}\plugins\drivers\payplaza-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\digicash-compound-driver\*"; DestDir: "{app}\plugins\drivers\digicash-compound-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\six-payment-eft-driver\*"; DestDir: "{app}\plugins\drivers\six-payment-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\oracle-xmlpos-compound-driver\*"; DestDir: "{app}\plugins\drivers\oracle-xmlpos-compound-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\pepper-eft-driver\*"; DestDir: "{app}\plugins\drivers\pepper-eft-driver"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\mews\*"; DestDir: "{app}\plugins\drivers\mews"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\uareu\*"; DestDir: "{app}\plugins\drivers\uareu"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\epay-zvt\*"; DestDir: "{app}\plugins\drivers\epay-zvt"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\adyen\*"; DestDir: "{app}\plugins\drivers\adyen"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\payconiq\*"; DestDir: "{app}\plugins\drivers\payconiq"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\resengo\*"; DestDir: "{app}\plugins\drivers\resengo"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\wigroup\*"; DestDir: "{app}\plugins\drivers\wigroup"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\worldline\*"; DestDir: "{app}\plugins\drivers\worldline"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\intersolve-ok\*"; DestDir: "{app}\plugins\drivers\intersolve-ok"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\gastroguide\*"; DestDir: "{app}\plugins\drivers\gastroguide"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\celona\*"; DestDir: "{app}\plugins\drivers\celona"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\eftpos\*"; DestDir: "{app}\plugins\drivers\eftpos"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\discoversystems\*"; DestDir: "{app}\plugins\drivers\discoversystems"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\opi-its\*"; DestDir: "{app}\plugins\drivers\opi-its"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\smarti\*"; DestDir: "{app}\plugins\drivers\smarti"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\vanduijnen-smarttap\*"; DestDir: "{app}\plugins\drivers\vanduijnen-smarttap"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\paymentsense\*"; DestDir: "{app}\plugins\drivers\paymentsense"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\jtech-pager\*"; DestDir: "{app}\plugins\drivers\jtech-pager"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\efsta\*"; DestDir: "{app}\plugins\drivers\efsta"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\datecs-dude\*"; DestDir: "{app}\plugins\drivers\datecs-dude"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\datecs-fp-bulgaria\*"; DestDir: "{app}\plugins\drivers\datecs-fp-bulgaria"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\hugin-fpu\*"; DestDir: "{app}\plugins\drivers\hugin-fpu"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\eftpos-nz\*"; DestDir: "{app}\plugins\drivers\eftpos-nz"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\onlinewerkrooster\*"; DestDir: "{app}\plugins\drivers\onlinewerkrooster"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\splitt\*"; DestDir: "{app}\plugins\drivers\splitt"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\loyaltek-transhandler\*"; DestDir: "{app}\plugins\drivers\loyaltek-transhandler"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\icg-cashdro-web\*"; DestDir: "{app}\plugins\drivers\icg-cashdro-web"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\glory-money-machine\*"; DestDir: "{app}\plugins\drivers\glory-money-machine"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\hoist-hotsoft\*"; DestDir: "{app}\plugins\drivers\hoist-hotsoft"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\protel\*"; DestDir: "{app}\plugins\drivers\protel"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\synergy-pf-550-mk\*"; DestDir: "{app}\plugins\drivers\synergy-pf-550-mk"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\adyen-pos\*"; DestDir: "{app}\plugins\drivers\adyen-pos"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\sevenrooms\*"; DestDir: "{app}\plugins\drivers\sevenrooms"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\cashmatic\*"; DestDir: "{app}\plugins\drivers\cashmatic"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\takeawaycom\*"; DestDir: "{app}\plugins\drivers\takeawaycom"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\efsta-austria\*"; DestDir: "{app}\plugins\drivers\efsta-austria"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\dinetime\*"; DestDir: "{app}\plugins\drivers\dinetime"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\netresto\*"; DestDir: "{app}\plugins\drivers\netresto"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\docmsign-fp-greece\*"; DestDir: "{app}\plugins\drivers\docmsign-fp-greece"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\rbsrmcp-fp-greece\*"; DestDir: "{app}\plugins\drivers\rbsrmcp-fp-greece"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\piggy-giftcards\*"; DestDir: "{app}\plugins\drivers\piggy-giftcards"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\clock-pms\*"; DestDir: "{app}\plugins\drivers\clock-pms"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\piggy-loyalty\*"; DestDir: "{app}\plugins\drivers\piggy-loyalty"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\guestline\*"; DestDir: "{app}\plugins\drivers\guestline"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\clover\*"; DestDir: "{app}\plugins\drivers\clover"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\payplaza-easypos\*"; DestDir: "{app}\plugins\drivers\payplaza-easypos"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\epson-fp-curacao\*"; DestDir: "{app}\plugins\drivers\epson-fp-curacao"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\ccv-mapi\*"; DestDir: "{app}\plugins\drivers\ccv-mapi"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\sa97-fp-lithuania\*"; DestDir: "{app}\plugins\drivers\sa97-fp-lithuania"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\jcc-verifone-eft\*"; DestDir: "{app}\plugins\drivers\jcc-verifone-eft"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\adoria\*"; DestDir: "{app}\plugins\drivers\adoria"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\apaleo-hotel\*"; DestDir: "{app}\plugins\drivers\apaleo-hotel"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\xafax-netpay-eft\*"; DestDir: "{app}\plugins\drivers\xafax-netpay-eft"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\asa-hotel\*"; DestDir: "{app}\plugins\drivers\asa-hotel"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\piggy-vouchers\*"; DestDir: "{app}\plugins\drivers\piggy-vouchers"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\retail-force\*"; DestDir: "{app}\plugins\drivers\retail-force"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\pax-s5\*"; DestDir: "{app}\plugins\drivers\pax-s5"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\loyyo-cards\*"; DestDir: "{app}\plugins\drivers\loyyo-cards"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\t-flex-cash-machine\*"; DestDir: "{app}\plugins\drivers\t-flex-cash-machine"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\shiji-hotel\*"; DestDir: "{app}\plugins\drivers\shiji-hotel"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\klearly\*"; DestDir: "{app}\plugins\drivers\klearly"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\sentoo\*"; DestDir: "{app}\plugins\drivers\sentoo"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\zenchef\*"; DestDir: "{app}\plugins\drivers\zenchef"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\viva-wallet-hht\*"; DestDir: "{app}\plugins\drivers\viva-wallet-hht"; Flags: ignoreversion recursesubdirs;
Source: "..\build\bin\plugins\drivers\viva-wallet\*"; DestDir: "{app}\plugins\drivers\viva-wallet"; Flags: ignoreversion recursesubdirs;

; ********************************* DRIVER DLLs ******************************************
Source: "..\build\bin\plugins\drivers\worldline\*.dll"; DestDir: "{sys}"; Flags: ignoreversion restartreplace;

; Terminals
Source: "..\build\bin\terminals\*.*"; DestDir: "{app}\terminals"; Flags: ignoreversion recursesubdirs

; ********************************** TOMCAT ******************************************
Source: "..\build\apache-tomcat-9.0.86\*"; DestDir: "{app}\tomcat"; Flags: ignoreversion recursesubdirs

; *********************************** UBL ********************************************
Source: "..\build\UBL.war"; DestDir: "{tmp}"; Flags: ignoreversion recursesubdirs

; *********************************** JViewer2 *******************************************
Source: "..\build\JViewer2.zip"; DestDir: "{tmp}"; Flags: ignoreversion recursesubdirs

; *********************************** Shield ********************************************
Source: "..\build\UntillShieldWebapp.war"; DestDir: "{tmp}"; Flags: ignoreversion recursesubdirs

; *********************************** Untill Api ********************************************
Source: "..\build\UntillApi.war"; DestDir: "{tmp}"; Flags: ignoreversion recursesubdirs

; ********************************* MAPs *********************************************
Source: "..\build\bin\*.map"; Excludes: "test_untill.map,legalticketsbuilder.map"; DestDir: "{app}"; Flags: ignoreversion restartreplace
Source: "..\build\bin\plugins\MainGraphicElements\MainGraphicElements.map"; DestDir: "{app}\plugins\MainGraphicElements"; Flags: ignoreversion restartreplace
Source: "..\build\bin\plugins\Restaurant\Restaurant.map"; DestDir: "{app}\plugins\Restaurant"; Flags: ignoreversion restartreplace

; ********************************* templates *********************************************
Source: "templates\*"; DestDir: "{app}\templates"; Flags: recursesubdirs onlyifdoesntexist
Source: "help\hcontent.chm"; DestDir: "{app}"; Flags: ignoreversion restartreplace

; ********************************* fonts *********************************************
Source: "..\build\bin\Molot.otf"; DestDir: "{fonts}"; FontInstall: "Molot"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Thin.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Thin"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-ExtraLight.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-ExtraLight"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Light.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Light"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Medium.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Medium"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Regular.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Regular"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-SemiBold.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-SemiBold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Bold.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Bold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-ExtraBold.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-ExtraBold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\Montserrat-Black.otf"; DestDir: "{fonts}"; FontInstall: "Montserrat-Black"; Flags: onlyifdoesntexist uninsneveruninstall

Source: "..\build\bin\static\Inter-Black.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Black"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-Bold.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Bold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-ExtraBold.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-ExtraBold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-ExtraLight.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-ExtraLight"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-Light.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Light"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-Medium.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Medium"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-Regular.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Regular"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-SemiBold.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-SemiBold"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "..\build\bin\static\Inter-Thin.ttf"; DestDir: "{fonts}"; FontInstall: "Inter-Thin"; Flags: onlyifdoesntexist uninsneveruninstall
Source: "AirPosIcons-3.ttf"; DestDir: "{fonts}"; FontInstall: "AirPosIcons-3";

; ********************************* Restaurant runtime scripts *********************************************
Source: "..\plugins\Restaurant\src\sql\*"; DestDir: "{app}\plugins\Restaurant\runtime"; Flags: ignoreversion recursesubdirs

[Registry]
Root: HKCR; Subkey: "lngfile"
Root: HKCR; Subkey: "lngfile\shell"
Root: HKCR; Subkey: "lngfile\shell\open"
Root: HKCR; Subkey: "lngfile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{%22}{app}\LangEdit.exe"" ""%1"""
Root: HKCR; Subkey: ".lng"; Flags: uninsdeletekey
Root: HKCR; Subkey: ".lng"; ValueType: string; ValueName: ""; ValueData: "lngfile"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: none; ValueName: "UNTILLSRV"; Flags: deletevalue
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "UNTILLSCM:{app}"; ValueData: "{app}\servicemgr.exe"; Flags: createvalueifdoesntexist uninsdeletevalue; Check: not IsSchedulerActive;
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: none; ValueName: "UNTILLSCM:{app}"; ValueData: "{app}\servicemgr.exe"; Flags: deletevalue; Check: IsSchedulerActive;


[INI]
Filename: "{app}\Untill.url"; Section: "InternetShortcut"; Key: "URL"; String: "https://www.untill.com"
Filename: "{app}\JViewer2.url"; Section: "InternetShortcut"; Key: "URL"; String: "http://localhost:{code:JServerPortValue}/jv2/"
Filename: "{app}\Untill.ini"; Section: "common"; Key: "port"; String: "{code:UntillSrvPortValue}"
Filename: "{app}\Untill.ini"; Section: "common"; Key: "shieldallow"; String: "{code:ShieldAllowValue}"
Filename: "{app}\Untill.ini"; Section: "common"; Key: "shieldport"; String: "{code:ShieldPortValue}"
Filename: "{app}\Untill.ini"; Section: "common"; Key: "shieldipaddress"; String: "{code:ShieldIpAddressValue}"
Filename: "{app}\Untill.ini"; Section: "build"; Key: "version"; String: "{code:GetVersion|'1'}"
Filename: "{app}\Untill.ini"; Section: "build"; Key: "dateStr"; String: "{code:GetBuildDate|'1'}"
Filename: "{app}\Untill.ini"; Section: "build"; Key: "timestamp"; String: "{code:GetCurrentTime}"

[Icons]
Name: "{group}\{code:GetAppName|'1'}"; Filename: "{app}\unTill.exe"; WorkingDir: "{app}"; IconFilename:"{code:GetIconFileName|'1'}"
Name: "{group}\Lang Editor"; Filename: "{app}\LangEdit.exe"; WorkingDir: "{app}"
; NOTE: The following entry contains an English phrase ("on the Web"). You are free to translate it into another language if required.
Name: "{group}\Journal Viewer 2"; Filename: "{app}\JViewer2.url"; IconFilename: "{app}\images\jviewer.ico"
Name: "{group}\{code:GetAppName|'1'} on the Web"; Filename: "{app}\Untill.url"; IconFilename: "{code:GetIconFileName|'1'}"
; NOTE: The following entry contains an English phrase ("Uninstall"). You are free to translate it into another language if required.
Name: "{group}\Uninstall {code:GetAppName|'1'}"; Filename: "{uninstallexe}"
Name: "{group}\Upgrade Database"; Filename: "{app}\DBUpgrade.exe"; WorkingDir: "{app}"; IconFilename: "{code:GetIconFileName|'1'}"
Name: "{commondesktop}\{code:GetAppName|'1'}"; Filename: "{app}\unTill.exe"; WorkingDir: "{app}"; Tasks: desktopicon; IconFilename: "{code:GetIconFileName|'1'}"
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{code:GetAppName|'1'}"; WorkingDir: "{app}"; Filename: "{app}\unTill.exe"; Tasks: quicklaunchicon; IconFilename: "{code:GetIconFileName|'1'}"

[Run]
Filename: "{tmp}\7za.exe"; WorkingDir: "{#JRE_INSTALLATION_PATH}"; Flags: runhidden; Parameters: "x ""{tmp}\{#JRE_FILE}"" -y -aoa"; Check: IsJreNotInstalled
Filename: "{tmp}\7za.exe"; WorkingDir: "{app}"; Flags: runhidden; Parameters: "x ""{tmp}\UBL.war"" -otomcat\webapps\UBL -y -aoa"
Filename: "{tmp}\7za.exe"; WorkingDir: "{app}"; Flags: runhidden; Parameters: "x ""{tmp}\JViewer2.zip"" -otomcat\webapps\jv2 -y -aoa"
Filename: "{tmp}\7za.exe"; WorkingDir: "{app}"; Flags: runhidden; Parameters: "x ""{tmp}\UntillApi.war"" -otomcat\webapps\api -y -aoa"
Filename: "{tmp}\7za.exe"; WorkingDir: "{app}"; Flags: runhidden; Parameters: "x ""{tmp}\UntillShieldWebapp.war"" -otomcat\webapps-ssl\shield\ROOT -y -aoa"
Filename: "schtasks.exe"; Flags: runhidden; Parameters: "/create /tn ""unTillServiceManager {code:GetCodedDir}"" /f /XML ""{tmp}/ServiceMgrTask.xml"""; Check: IsSchedulerActive and SetServiceMgrXmlAttrs;
;DBUpgrade called in CurStepCahnged.postInstall function
;FileName: "{app}\DBUpgrade.exe"; Check: BeforeDbUpgrade
Filename: "{app}\unTill.exe"; Description: "Launch {code:GetAppName|'1'}"; Flags: nowait postinstall skipifsilent; Check: RestartNotPlanned

[UninstallRun]
Filename: "{app}\StopAll.exe"; RunOnceId: "StopAll"
Filename: "{app}\instsvc.exe"; Parameters: "/remove"; RunOnceId: "UnregSvc1"
Filename: "{app}\instsql.exe"; Parameters: "/remove"; RunOnceId: "UnregSvc2"
Filename: "{app}\tomcat\bin\ubl_unreg.bat"; Flags: runhidden; RunOnceId: "UnregTomcat"
Filename: "schtasks.exe"; Flags: runhidden; Parameters: "/delete /tn ""unTillServiceManager {code:GetCodedDir}"" /f" ;Check: IsSchedulerActive;

[UninstallDelete]
Type: files; Name: "{app}\Untill.url"
Type: files; Name: "{app}\JViewer.url"
Type: files; Name: "{app}\JViewer2.url"

[InstallDelete]
Type: filesandordirs; Name: "{app}\sql"
Type: filesandordirs; Name: "{app}\scripts"
Type: filesandordirs; Name: "{app}\plugins\Restaurant\sql"
Type: filesandordirs; Name: "{app}\plugins\Restaurant\scripts"
Type: files; Name: "{app}\add_new_article.mp4"
Type: files; Name: "{app}\cashdeclartation.mp4"
Type: files; Name: "{app}\createnewuser.mp4"
Type: files; Name: "{app}\new_pricelevel.mp4"
Type: files; Name: "{app}\newagegroup.mp4"
Type: files; Name: "{userdesktop}\{code:GetAppName|'1'}.lnk"

#include "InnoDownloadPlugin\idp.iss"

#include "firebird.iss"

[code]

// Create prepare section and restart system before installation. Backup unTill
#include "prepare_restart.iss"

function checkPortWithConnectingTimeout(startPort: word; endPort : word; timeout: UINT): boolean;
external 'checkPortWithConnectingTimeout@files:CheckPorts.dll stdcall delayload';

var
	UName : String;
	Port:   Integer;
	Light:  Boolean;

    ShieldAllow: TNewCheckListBox;
	ShieldIpAddress: String;
	ShieldIpAddressEdit: TNewEdit;
	ShieldIpAddressText: TNewStaticText;
	ShieldPort: Integer;
    ShieldPortEdit: TNewEdit;
    ShieldPortText: TNewStaticText;
    ShieldProgressText: TNewStaticText;
    ShieldProgressBar: TNewProgressBar;

	InputUntillSrvPortPage : TInputQueryWizardPage;

	CheckUDFFailed: Boolean;
	bRestartNeeded : Boolean;
	DownloadingJrePage : TOutputProgressWizardPage;

const
  TOMCAT_MAJOR_VERSION = 9;

  QuitMessageReboot = 'The installation of a prerequisite program was not completed. You will need to restart your computer to complete that installation.'#13#13'After restarting your computer, Setup will continue next time an administrator logs in.';
  QuitMessageError = 'Error. Cannot continue.';
  StartupBackupFailed = 'Startup section store failed';
  SetupImpossible = 'Setup impossible. Impossible to stop current instance';
  RestartShortcutFailed = 'Create RunOnce section for restart failed';
  CreateBackupFailed = 'Create unTill backup failed';
  PARAM_DONOT_FORCE_RESTART = '/donotforcerestart';
  PARAM_NO_TASK = '/notask';

#include "..\ver\version.inc"

procedure MsgBoxSilent(msg: String);
begin
  SetSetupStatus(SS_FAILED, msg);

  if not WizardSilent then
    MsgBox(msg, mbError, MB_OK);
end;

function IsCustomSysLT: boolean;
begin
  result := FileExists(ExpandConstant('{src}\syslt.dat'));
end;

procedure LoadDefaultPort;
var i:integer;
    filename: String;
    param: String;
    value : integer;
begin
  Port:=3060;
  // Read silent port value
  for i:=0 to ParamCount do begin
    param:=ParamStr(i);
    if lowercase(Copy(param, 1, 9))='/loadinf=' then begin
      FileName:=RemoveQuotes(Copy(param, 10, Length(param) - 9));
      value := GetIniInt('Setup','UntillSrvPort', 3060, 1, 65535, FileName);
      if value <> 3060 then
      begin
        Port:=value;
        exit;
      end;
    end;
  end;
  // read port value from destination folder
  filename:=ExpandConstant('{app}')+'\untill.ini';
  if FileExists(filename) then begin
    Port:=GetIniInt('common','port', 3060, 1, 65535, filename);
  end;
end;

procedure SetDefaultPort;
var i:integer;
    filename: String;
    param: String;
begin
  Port:=3060;
  // Read silent port value
  for i:=0 to ParamCount do begin
    param:=ParamStr(i);
    if lowercase(Copy(param, 1, 9))='/loadinf=' then begin
      FileName:=RemoveQuotes(Copy(param, 10, Length(param) - 9));
      Port:=GetIniInt('Setup','UntillSrvPort', 3060, 1, 65535, FileName);
      exit;
    end;
  end;
  // read port value from destination folder
  filename:=ExpandConstant('{app}')+'\untill.ini';
  if FileExists(filename) then begin
    Port:=GetIniInt('common','port', 3060, 1, 65535, filename);
  end;
  InputUntillSrvPortPage.Values[0] := IntToStr(Port);
end;

function CheckDefinitiveLicenses: boolean;
var
  filename: String;
  r: Integer;
begin

  if not DirExists(ExpandConstant('{app}\db')) then begin
    result := true;
    exit;
  end;

  ExtractTemporaryFile('dlcheck.exe');

  filename:=ExpandConstant('{tmp}')+'\dlcheck.exe';
	if (Exec(filename, '"'+ExpandConstant('{app}')+'"', GetCurrentDir, SW_HIDE, ewWaitUntilTerminated, r) = false) then begin
		MsgBoxSilent('Can''t run {tmp}\dlcheck.exe: '+IntToStr(r))
		Result:=false;
		exit;
	end else begin
	  if r = 1 then begin
    	MsgBoxSilent('It is not allowed to install this build by a license in one or more databases in destination folder');
    	Result:=false;
    	exit;
	  end else if r = 2 then begin
    	//disabled by: https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=249883
    	//
      //MsgBox('Error running dlcheck.exe', mbError, MB_OK);
    	//Result:=false;
    	//exit;
	  end;
		Result:=true;
	end;
end;

function IsJreNotInstalled : boolean;
var
  fJre : String;
  sz: Integer;
begin
  Log('Check JRE');
  fJre := ExpandConstant('{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}\bin\java.exe');
  result := not ( FileSize(fJre, sz) and (sz > 0));
end;

function DownloadFile(uri, fname: String):boolean;
var
  ResultCode : Integer;
begin
  Log('Downloading ' + uri)
  ExtractTemporaryFile('curl.exe');
  if Exec(ExpandConstant(ExpandConstant('{tmp}\curl.exe')), '-kL '+uri+' --output '+fname+' -f', '', SW_HIDE,
     ewWaitUntilTerminated, ResultCode) then begin
    if ResultCode = 0 then begin
      result := true
    end
    else begin
      Log('curl return ExitCode: ' + IntToStr(ResultCode));
      result := false;
    end;
  end
  else begin
    Log('curl Failed');
    result := false;
  end;
end;


function DownloadJre : String;
var fname : String;
  fJreCopied: String;
  errCode : Integer;
begin
  result := '';

  fJreCopied := ExpandConstant('{src}\{#JRE_FILE}');
  fname := ExpandConstant('{tmp}\{#JRE_FILE}');
  if FileExists(fJreCopied) then
  begin
    Log('JRE already downloaded. Using ' + fJreCopied);
    if not FileCopy(fJreCopied, fname, false) then begin
      result := 'Cannot copy JRE file';
      exit;
    end;
  end
  else
  begin
    
    DownloadingJrePage.SetProgress(0, 10);
    DownloadingJrePage.Show;
    try
      Log('Downloading JRE from ' + ExpandConstant('{#JRE_URI}{#JRE_FILE}'));
      if not DownloadFile(ExpandConstant('{#JRE_URI}{#JRE_FILE}'), fname) then
      begin
        Log('Downloading JRE from ' + ExpandConstant('{#JRE_URI_RESERVE}{#JRE_FILE}'));
        if not DownloadFile(ExpandConstant('{#JRE_URI_RESERVE}{#JRE_FILE}'), fname) then begin
          result := 'Cannot download JRE.';
          exit;
        end;
      end;
      DownloadingJrePage.SetProgress(8,10);
      FileCopy(fname, fJreCopied, false);
      DownloadingJrePage.SetProgress(10,10);
    finally
      DownloadingJrePage.Hide;
    end;
  end;

//  if not ForceDirectories(ExpandConstant('{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}')) then begin
//    result := 'Cannot create dirrecotry: ' + ExpandConstant('{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}');
//  end;
end;

function GetExternalIpAddress(): String;
var
  filename: String;
  externalIpAddress: String;
  ResultCode: Integer;
  Lines: TArrayOfString;
begin
  ShieldProgressBar.Style:=npbstMarquee;
  ExtractTemporaryFile('external-ip-provider.exe');
  filename := ExpandConstant('{tmp}') + '\external-ip.txt';
  Exec(ExpandConstant('{tmp}') + '\external-ip-provider.exe', filename, '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
  LoadStringsFromFile(filename, Lines);
  if GetArrayLength(Lines) = 1 then
  	externalIpAddress := Lines[0]
  else
  	externalIpAddress := '127.0.0.1';
  StringChangeEx(externalIpAddress, 'Address:  ', '', True);
  Result := externalIpAddress;
  ShieldProgressBar.Style:=npbstNormal;
end;

procedure SetShieldSettings;
var
  filename: String;
begin
  ShieldAllow.Checked[1] := false;
  filename:=ExpandConstant('{app}')+'\untill.ini';
  if FileExists(filename) then begin
    if GetIniString('common','shieldallow','false',filename) = 'true' then begin
      ShieldAllow.Checked[1] := true;
      ShieldIpAddress:=GetIniString('common', 'shieldipaddress', '', filename);
      if Length(ShieldIpAddress) = 0 then begin
        ShieldIpAddress := GetExternalIpAddress;
      end;
      ShieldPort:=GetIniInt('common','shieldport', 443, 1, 65535, filename);
      ShieldIpAddressEdit.Text := ShieldIpAddress;
      ShieldPortEdit.Text := IntToStr(ShieldPort);
    end;
  end;
  ShieldIpAddressText.Enabled := ShieldAllow.Checked[1];
  ShieldIpAddressEdit.Enabled := ShieldAllow.Checked[1];

  ShieldPortText.Enabled := ShieldAllow.Checked[1];
  ShieldPortEdit.Enabled := ShieldAllow.Checked[1];
end;

function InitializeSetup(): Boolean;
var str : String;
begin
  InitSetup();
	result:=true;
	CheckUDFFailed := False;

  if not Restarted and (ExpandConstant('{param:prepare_restart|0}') <> '2') then begin

    Result := not IsRestartSectionAlreadyExists();
    if not Result then
      MsgBoxSilent(QuitMessageReboot);
  end else
    Result := True;

  if Result then  begin
    str := InitSetup();
    result := ('' = str);
    if not result then begin
      MsgBoxSilent(str);

    end;
  end;

end;

function CheckIsLight: Boolean;
var srcexe: String;
begin
  srcexe := ExtractFileName(ExpandConstant('{srcexe}'));
  result := Pos('apostill', srcexe) > 0;
end;

procedure OnClickHandler(Sender: TObject);
var
  flag: boolean;
  filename: string;
begin
  flag := ShieldAllow.Checked[2];
  ShieldIpAddressText.Enabled := flag;
  ShieldIpAddressEdit.Enabled := flag;
  ShieldPortText.Enabled := flag;
  ShieldPortEdit.Enabled := flag;
  ShieldProgressText.Enabled := flag;
  ShieldProgressBar.Enabled := flag;
  if flag then begin
    filename:=ExpandConstant('{app}')+'\untill.ini';
    if FileExists(filename) then begin
      ShieldIpAddress:=GetIniString('common', 'shieldipaddress', '', filename);
      if Length(ShieldIpAddress) = 0 then begin
        ShieldIpAddress := GetExternalIpAddress;
      end;
      ShieldPort:=GetIniInt('common','shieldport', 443, 1, 65535, filename);
      ShieldIpAddressEdit.Text := ShieldIpAddress;
      ShieldPortEdit.Text := IntToStr(ShieldPort);
    end;
  end else begin
    ShieldIpAddressEdit.Text := '';
    ShieldPortEdit.Text := '';
  end;
end;

procedure ConnectionSettingsWidget(InputUntillSrvPortPage : TInputQueryWizardPage);
var
  Bevel: TBevel;
  _Offset: Integer;
  _Width: Integer;
begin
  _Offset := 3;
  _Width := 417;
  Bevel := TBevel.Create(InputUntillSrvPortPage);
  Bevel.Parent := InputUntillSrvPortPage.Surface;
  if Assigned(InputUntillSrvPortPage.Edits[0]) then
    Bevel.Top := InputUntillSrvPortPage.Edits[0].Top + WizardForm.TasksList.MinItemHeight + _Offset
  else
    Bevel.Top := 68;
  Bevel.Left := 0;
  Bevel.Width := _Width;
  Bevel.Height := _Offset;
  Bevel.Shape := bsTopLine;

  ShieldAllow := TNewCheckListBox.Create(InputUntillSrvPortPage);
  ShieldAllow.Top := Bevel.Top + Bevel.Height;
  ShieldAllow.Height := WizardForm.TasksList.MinItemHeight*3 + _Offset;
  ShieldAllow.Width := _Width;
  ShieldAllow.BorderStyle := bsNone;
  ShieldAllow.ParentColor := True;
  ShieldAllow.MinItemHeight := WizardForm.TasksList.MinItemHeight;
  ShieldAllow.ShowLines := False;
  ShieldAllow.WantTabs := True;
  ShieldAllow.Parent := InputUntillSrvPortPage.Surface;
  ShieldAllow.AddGroup('Use HTTPS for TPAPI?', '', 0, nil);
  ShieldAllow.AddRadioButton('Yes', '', 0, False, True, nil);
  ShieldAllow.AddRadioButton('No', '', 0, True, True, nil);
  ShieldAllow.OnClick := @OnClickHandler;

  ShieldIpAddressText := TNewStaticText.Create(InputUntillSrvPortPage);
  ShieldIpAddressText.Parent := InputUntillSrvPortPage.Surface;
  ShieldIpAddressText.Top := ShieldAllow.Top + ShieldAllow.Height;
  ShieldIpAddressText.Caption := 'Public IP address:';

  ShieldIpAddressEdit := TNewEdit.Create(InputUntillSrvPortPage);
  ShieldIpAddressEdit.Top := ShieldIpAddressText.Top + ShieldIpAddressText.Height;
  ShieldIpAddressEdit.Width := _Width;
  ShieldIpAddressEdit.Parent := InputUntillSrvPortPage.Surface;

  ShieldPortText := TNewStaticText.Create(InputUntillSrvPortPage);
  ShieldPortText.Parent := InputUntillSrvPortPage.Surface;
  ShieldPortText.Top := ShieldIpAddressEdit.Top + ShieldIpAddressEdit.Height;
  ShieldPortText.Caption := 'Port:';

  ShieldPortEdit := TNewEdit.Create(InputUntillSrvPortPage);
  ShieldPortEdit.Top := ShieldPortText.Top + ShieldPortText.Height;
  ShieldPortEdit.Width := _Width;
  ShieldPortEdit.Parent := InputUntillSrvPortPage.Surface;

  ShieldProgressText := TNewStaticText.Create(InputUntillSrvPortPage);
  ShieldProgressText.Parent := InputUntillSrvPortPage.Surface;
  ShieldProgressText.Top := ShieldPortEdit.Top + ShieldPortEdit.Height;
  ShieldProgressText.Caption := 'Collecting parameters...';

  ShieldProgressBar := TNewProgressBar.Create(InputUntillSrvPortPage);
  ShieldProgressBar.Parent := InputUntillSrvPortPage.Surface;
  ShieldProgressBar.Top := ShieldProgressText.Top + ShieldProgressText.Height;
  ShieldProgressBar.Width := _Width;
  ShieldProgressBar.State := npbsNormal;
  ShieldProgressBar.Height := 10;
end;

procedure InitializeWizard();
begin
  InputUntillSrvPortPage := CreateInputQueryPage(wpSelectDir, 'Specify UntillSrv Port Number',
    'What TCP/IP port unTill Server should use to operate on?',
    'Note that next two ports will be also used. To continue click Next.');
  InputUntillSrvPortPage.Add('Port:', False);
  DownloadingJrePage := CreateOutputProgressPage('Preparing to Install', 'Downloading JRE...');

  ConnectionSettingsWidget(InputUntillSrvPortPage);

  light := CheckIsLight;

  FirebirdInit;
end;

function GetVersion(Default: String): String;
var sr: String;
begin
  if Release then sr:='R' else sr:='A';
  result:=IntToStr(MajorVersion) + '.' + IntToStr(MinorVersion) + '.' + sr +
    '.' + IntToStr(BuildNumber);
end;

function GetBuildDate(Default: String): String;
begin
  result := BuildDate;
end;

function  IsVistaOrHigher(): Boolean;
var Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  Result := (Version.Major >= 6);
end;

function CheckSetupConditions(): Boolean;
begin
  try
    if not IsWindows8OrNewer then begin
      // support Windows 7 (based on https://github.com/DomGries/InnoDependencyInstaller)
      if not IsMsiProductInstalled('{65E5BD06-6392-3027-8C26-853107D3CF1A}', PackVersionComponents(14, 30, 30704, 0)) then
        RaiseException(FmtMessage(SetupMessage(msgWinVersionTooLowError), ['Visual C++ 2015-2022 Redistributable (x86)', '14.30']));
    end;
    FirebirdCheck;
    Result := True;
  except
    MsgBoxSilent(GetExceptionMessage);
    Result := False;
  end;
end;

function GetUName():String;
begin
	UName:=GenerateUniqueName('','.exe');
	Result:=UName;
end;

procedure SavePortIfNeeded(APort: Integer);
var i:integer;
    filename: String;
    param: String;
begin
//  MsgBox(GetCmdTail, mbInformation, MB_OK);
  for i:=0 to ParamCount do begin
    param:=ParamStr(i);
//    MsgBox(GetCmdTail+#13#10+'CURRENT:'+param, mbInformation, MB_OK);
    if lowercase(Copy(param, 1, 9))='/saveinf=' then begin
      FileName:=RemoveQuotes(Copy(param, 10, Length(param) - 9));
      SetIniInt('Setup','UntillSrvPort', APort, FileName);
    end;
  end;
end;

function checkPortsFree(startPort, endPort: Integer) : boolean;
var
  bInstallSamePort : boolean;
  filename : String;
begin
  filename:=ExpandConstant('{app}')+'\untill.ini';
  if FileExists(filename) then begin
    bInstallSamePort := Port = GetIniInt('common','port', 3060, 1, 65535, filename);
  end
  else
    bInstallSamePort := false;

  if false = bInstallSamePort then begin
      Log('check ports : [' + IntToStr(startPort) + ', ' + IntToStr(endPort) + ')');
      if checkPortWithConnectingTimeout(startPort, endPort, 1000) = false then begin
        MsgBoxSilent('Another application use one of ports from '+IntToStr(startPort)+ ' to ' + IntToStr(endPort) + #13#10+'Please choose another port');
        result := false;
        exit;
      end;
      Log('Ports free');
  end;
  result := true;
end;


function  CheckInstallationsForPort: boolean;
var Names: TArrayOfString;
    i: integer;
    sPath: String;
    nPort: Integer;
begin
  result:=true;
  if RegGetValueNames(HKLM, 'Software\Microsoft\Windows\CurrentVersion\Run', Names) then begin
    for i:=0 to GetArrayLength(Names)-1 do begin
      if (lowercase(Copy(Names[i], 1, 10))='untillsrv:')
      or (lowercase(Copy(Names[i], 1, 10))='untillscm:') then begin
        sPath:=lowercase(Copy(Names[i], 11, Length(Names[i]) - 10));
        if lowercase(sPath) <> lowercase(ExpandConstant('{app}')) then begin
          nPort:=GetIniInt('common', 'port', 3060, 0, 65535, sPath+'\untill.ini');
          if (nPort - Port > -3) and (nPort - Port < 3) then begin
            result:=false;
            MsgBoxSilent('Another installation: ['+sPath+'] is installed on port '+IntToStr(nPort)+#13#10+'Please choose another port');
            exit;
          end;
        end;
      end;
    end;
    // Check that ports is not buzy with another apps
  end else begin
    MsgBoxSilent('Can''t get access to registry');
    result:=false;
    exit;
  end;
  result := checkPortsFree(Port, Port+5);
end;

function SpecifyUntillSrvPort:boolean;
begin
  result:=false;
  Port:=StrToIntDef(InputUntillSrvPortPage.Values[0], 0)
  if Port > 0 then begin
    SavePortIfNeeded(Port);
    result:=CheckInstallationsForPort;
  end;
end;

function UntillSrvPortValue(Param:String): String;
begin
  Result:=IntToStr(Port);
end;

function ShieldPortValue(Param:String): String;
begin
  Result:=IntToStr(ShieldPort);
end;

function ShieldIpAddressValue(Param:String): String;
begin
  Result:=ShieldIpAddress;
end;

function ShieldAllowValue(Param:String): String;
begin
  if ShieldAllow.Checked[1] then
    Result := 'true'
  else
    Result := 'false';
end;

function JServerPortValue(Param:String): String;
begin
  Result:=IntToStr(Port+4);
end;

function IsLight(Param:String): String;
begin
  if Light then
    Result := '1'
  else
    Result := '0';
end;

function GetDestination(Default: String): String;
begin
  if IsVistaOrHigher then
	result := 'C:\'

  else
    result := ExpandConstant('{pf}');
end;

function GetDirName(Default: String): String;
begin
  if not CheckIsLight then
    result := 'unTill'
  else
    result := 'Apostill';
end;

function GetAppName(Default: String): String;
begin
  if not CheckIsLight then
    result := 'unTill'
  else
    result := 'Apostill';
end;

function GetIconFileName(Default: String): String;
begin
  if not CheckIsLight then
    result := ExpandConstant('{app}\images\unTill.ico')
  else
    result := ExpandConstant('{app}\images\apostill.ico');
end;

function StopAll: boolean;
var ResCode:Integer;
  ExecRes:boolean;
  Msg:String;
begin
  result:=false;

  WriteStatusFile(SS_RUNNING, RSS_STOP_ALL);

  ExtractTemporaryFile('StopAll.exe');
  ExecRes:=Exec(ExpandConstant('{tmp}')+'\StopAll.exe', '"'+ExpandConstant('{app}')+'"', ExpandConstant('{tmp}'), SW_HIDE, ewWaitUntilTerminated, ResCode);
  if ExecRes=false then begin
    MsgBoxSilent('Setup was unable to execute "StopAll.exe" application');
    exit;
  end;

  if ResCode>0 then begin
    case ResCode of
      1: Msg:='unTill.exe is running. Please stop unTill application and try again';
      2: Msg:='unTill upgrade is running. Please stop unTill upgrade and try again';
      3: Msg:='Can''t stop untillsrv. Please check if it is not stopped';
      4: Msg:='unTill Language Editor is running. Please stop Language Editor and try again';
      6: Msg:='Can''t connect to untillsrv. Please check if it is not stopped';
      7: Msg:='Can''t stop unTill SQL Server service';
      8: Msg:='Can''t stop unTill Service Manager service';
      11: Msg:='unTill watchdog is running. Please stop unTill watchdog and try again';
      12: Msg:='Can''t stop oman.exe';
      13: Msg:='Can''t stop unTill JServer service';
      31: Msg:='Service manager not running';
    else
      Msg:='"StopAll.exe" execution failed';
    end;
    MsgBoxSilent(Msg);
    exit;
  end;


  RegDeleteValue(HKLM,'Software\Microsoft\Windows\CurrentVersion\Run', 'UNTILLSRV:'+ExpandConstant('{app}'));

  result:=true;

end;

procedure RegisterServices;
var  ResCode:Integer;
	   ExecRes:boolean;
begin
  ExecRes:=Exec(ExpandConstant('{app}')+'\instsvc.exe', '', ExpandConstant('{app}'), SW_HIDE, ewWaitUntilTerminated, ResCode);
  if ExecRes=false then
  	MsgBoxSilent('Setup was unable to execute "instsvc.exe" application. unTill was not completely installed.')
  else if (ResCode>0) then
   	MsgBoxSilent('An error occured while executing "instsvc.exe" application. unTill was not completely installed.');

  ExecRes:=Exec(ExpandConstant('{app}')+'\instsql.exe', '', ExpandConstant('{app}'), SW_HIDE, ewWaitUntilTerminated, ResCode);
  if ExecRes=false then
  	MsgBoxSilent('Setup was unable to execute "instsql.exe" application. unTill was not completely installed.')
	else if (ResCode>0) then
  	MsgBoxSilent('An error occured while executing "instsql.exe" application. unTill was not completely installed.');

  ExecRes:=Exec(ExpandConstant('{sys}\sc.exe'), 'start UntillJSrv'+IntToStr(Port+4), ExpandConstant('{app}'), SW_HIDE, ewWaitUntilTerminated, ResCode);
  if ExecRes=false then
  	MsgBoxSilent('Setup was unable to start Tomcat. unTill was not completely installed.')
	else if (ResCode>0) then
  	MsgBoxSilent('An error occured while starting Tomcat. unTill was not completely installed.');
end;

function SaveSSF() : boolean;
begin
  result := true;
  if DirExists(ExpandConstant('{userappdata}\{#BackupFolder}\unTill')) then begin
    Log('Remove old backup');
    DelTree(ExpandConstant('{userappdata}\{#BackupFolder}\unTill\*'), false, true, true);
  end
  else begin
    if not DirExists(ExpandConstant('{userappdata}\{#BackupFolder}')) then
      CreateDir(ExpandConstant('{userappdata}\{#BackupFolder}'));

    if not CreateDir(ExpandConstant('{userappdata}\{#BackupFolder}\unTill')) then begin
      MsgBoxSilent('An error occured while creating backup folder');
      result := false;
    end;
  end;
  if result and FileExists(ExpandConstant('{app}\untill.ssf')) then begin
    Log('Copy SSF');
    result := FileCopy(ExpandConstant('{app}\untill.ssf'), ExpandConstant('{userappdata}\{#BackupFolder}\unTill\untill.ssf'), true);
  end;
end;

procedure RestoreSSF();
begin
  Log('Restore SSF file')
  if not FileCopy(ExpandConstant('{userappdata}\{#BackupFolder}\unTill\untill.ssf'),ExpandConstant('{app}\untill.ssf'), true) then
    Log('Restore SSF file FAILED');

end;

function IsExistsParam(what : String) : Boolean;
var
i: Integer;
param : String;
begin
  for i := 0 to ParamCount do begin
    param := ParamStr(i);
    if Lowercase(param) = what then begin
      result := true;
      Exit;
    end;
  end;
  result := false;
end;

function DoNotForceRestart() : boolean;
begin
  result := IsExistsParam(PARAM_DONOT_FORCE_RESTART);
end;

function PrepareSystemToRestart : String;
begin
  if StoreStartupSection(ExpandConstant('{userappdata}\{#BackupFolder}')) = false then
  begin
    // Cancel installation. Error occured
    result := StartupBackupFailed;
  end
  else
  begin
    if not IsExistsParam(PARAM_NO_TASK) then
    begin
      if CreateRunOnceEntry(['loadinf', ExpandConstant('{param:loadinf}'), 'LOG', ExpandConstant('"{param:LOG}"')]) then begin
        Result := '';
      end
      else begin
        Log('Setup task was not created');
        RestoreStartupSection(ExpandConstant('{userappdata}\{#BackupFolder}'));
        Result := RestartShortcutFailed;
      end;
    end;
  end;
end;

function checkIp24BitBlock(subNetworks: array of Integer): Boolean;
begin
  Result := True;
  if subNetworks[0] = 10 then begin
    Result := False;
  end;
end;

function checkIp20BitBlock(subNetworks: array of Integer): Boolean;
begin
  Result := True;
    if(subNetworks[0] = 172) and ((subNetworks[1] >= 16) and (subNetworks[1] <=32))then begin
      Result := False;
    end;
end;

function checkIp16BitBlock(subNetworks: array of Integer): Boolean;
begin
  Result := True;
  if (subNetworks[0] = 192) and (subNetworks[1] = 168) then begin
    Result := False;
  end;
end;

function checkIpLocalhostBlock(subNetworks: array of Integer): Boolean;
begin
  Result := True;
  if (subNetworks[0] = 127) then begin
    Result := False;
  end;
end;

function checkIpAddress(): Boolean;
var
  subNetworks: array [1..4] of Integer;
  i: Integer;
  position: Integer;
  IpAddress: String;
begin
  Result := True;
  IpAddress := ShieldIpAddressEdit.Text;

  //For test purposes
  if (IpAddress = 'localhost') then begin
    exit;
  end;

  for i:=1 to 4 do begin
    position := Pos('.', IpAddress);
      if(position > 0) then begin
        subNetworks[i]:= StrToInt(Copy(IpAddress, 1, position - 1));
        Delete(IpAddress, 1, position);
      end
      else begin
        subNetworks[i]:= StrToInt(IpAddress);
      end;
  end;

  if not checkIp24BitBlock(subNetworks)
  or not checkIp20BitBlock(subNetworks)
  or not checkIp16BitBlock(subNetworks)
  or not checkIpLocalhostBlock(subNetworks)
  then begin
    MsgBox('IP address should be public. Don''t use private IP ranges '
    + '10.0.0.0 - **************, ********** - **************, *********** - ***************'
    + ' or ********* - ***************.', mbError, MB_OK);
    Result := False;
  end;
  ShieldIpAddress := ShieldIpAddressEdit.Text;
end;

function checkShieldParams(): Boolean;
var
  isSamePort: boolean;
  filename: String;
  oldPort: Integer;
begin
  Result := true;
  isSamePort := false;
  if (ShieldAllow.Checked[1] = true) then begin
    ShieldPort := StrToInt(ShieldPortEdit.Text);
    filename := ExpandConstant('{app}')+'\untill.ini';
    if FileExists(filename) then begin
      oldPort := GetIniInt('common','shieldport', 443, 1, 65535, filename);
      if (oldPort = ShieldPort) then begin
        isSamePort := true;
      end;
    end;
    if not isSamePort then begin      
      if not checkPortWithConnectingTimeout(ShieldPort, ShieldPort+1, 1000) then begin
        MsgBox('Another application use ' + ShieldPortEdit.Text
        + ' port. Please select another one.', mbError, MB_OK);
        Result := false;
        exit;
      end;
    end;
    Result := checkIpAddress();
  end;
end;

function NextButtonClick(CurPage: Integer):boolean;
var bStopped : boolean;
prepRes : String;
begin
  result:=true;
  case CurPage of

  	wpWelcome: begin
  		result:=CheckSetupConditions();
  	end;

    wpSelectDir: begin
      SetDefaultPort;
      SetShieldSettings;
      result:=CheckDefinitiveLicenses();
    end;

    InputUntillSrvPortPage.ID: begin
      result:=SpecifyUntillSrvPort() and checkShieldParams();
    end;

    wpReady: begin
      if not Restarted then begin
        if IsMakeBackup() then begin
          if not SaveSSF() then begin
            result := false;
            Exit;
          end
        end;
        bStopped := StopAll();

        if bStopped then
        else  begin
          if isPrepareRestart then begin
            result:= IsAutologon;
            if IsAutologon then begin
              if DoNotForceRestart and WizardSilent then begin
                prepRes := PrepareSystemToRestart();
                if '' <> prepRes then  begin
                  RestoreSSF();
                  SetSetupStatus(SS_FAILED, prepRes)
                end
                else
                  SetSetupStatus(SS_RESTARTING, '');
                result := false;
              end
              else begin
                bRestartNeeded := true;
                result := true;
              end;
            end
            else begin
              RestoreSSF();
              SetSetupStatus(SS_FAILED, 'Autologon is off and some apps may not be stopped : '+ GetErrorDescription());
              result := false;
            end;
          end
          else
            result := false;
        end;
      end;
  	end;

  end;
end;

procedure RegisterServers;
begin
  RegisterServer(False, ExpandConstant('{cf}')+'\unTill\dll\midas.dll', True);
end;

function BeforeDbUpgrade: Boolean;
var strings, strings2: TStrings;
    r: integer;
    hasGuardian: Boolean;
    WaitStartTime: DWORD;
    ServiceStatus: Integer;
begin
  // 1. Write secret file
	result := false;
  strings := TStringList.Create;
  strings2 := TStringList.Create;
  try
    strings.add('uVersion='+GetVersion(''));
    strings.add('uLight='+IsLight(''));
    strings2.add(GetMd5OfString(strings.Text))
    strings2.SaveToFile(ExpandConstant('{app}\untill.ssf'));
		result := true;
  finally
    strings.Free;
    strings2.Free;
  end;
end;

function NeedRestart(): Boolean;
begin
  result := CheckUDFFailed;
end;

function RestartNotPlanned: boolean;
begin
  result := not CheckUDFFailed;
end;

const TR_LOCALE_LANGID = $01F;

procedure CreateTomcatServiceCmdFiles();
var f: TStringList;
    TomcatHome, TomcatExe, ServiceName, OldServiceName: String;
    LocaleSetter : String;
begin
  f := TStringList.Create();
  try
    TomcatExe := 'tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + '.exe';
    TomcatHome := ExpandConstant('{app}\tomcat')
    ServiceName :=  'UntillJSrv' + IntToStr(Port+4);
    OldServiceName := 'UBLServer' + IntToStr(Port+4);
    LocaleSetter := '';
    Log('Language : ' + IntToStr(GetUILanguage()));
    if (GetUILanguage() and $3FF)  = TR_LOCALE_LANGID then begin
      Log('Turkish locale overrided to ROOT for JServer');
      LocaleSetter := '-Duser.language=;-Duser.region=;';       
    end;

    f.Add('%~d0');
    f.Add('cd %~dp0');
    f.Add('set JRE_HOME=' + ExpandConstant('{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}'));
    f.Add('set JAVA_HOME=');
    f.Add('set CATALINA_HOME=');
    f.Add('echo %date% %time%: >>installservice.log');
    f.Add('call service.bat install ' + ServiceName + ' >>installservice.log 2>&1');
    f.Add('"'+TomcatHome+'\bin\'+TomcatExe+'" //US//'+ServiceName+
        ' ++JvmOptions "-Duntill.home='+ExpandConstant('{app}')+';-Dfile.encoding=UTF-8;-Djava.net.preferIPv4Stack=true;"' + 
        LocaleSetter +
        ' --Startup auto --Description="unTill JServer, port '+IntToStr(Port+4)+'"' +
        ' --DisplayName="unTill JServer '+IntToStr(Port+4)+'"' +
        ' --StdOutput "" --StdError "" --JvmMx 512 --ServiceUser LocalSystem');
    f.SaveToFile(TomcatHome + '\bin\ubl_reg.bat');

    f.Delete(7);
    f.Delete(6);
    f.Add('call service.bat uninstall ' + ServiceName + ' >>installservice.log 2>&1');
    f.Add('call service.bat uninstall ' + OldServiceName + ' >>installservice.log');
    f.Add('exit /b 0');
    f.SaveToFile(TomcatHome + '\bin\ubl_unreg.bat');

    f.Clear;
    f.Add('@START "" "%~dp0tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + 'w.exe" //ES//' + ServiceName);
    f.SaveToFile(TomcatHome + '\bin\tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + 'w.bat');
  finally
    f.Free;
  end;
end;

function StrStartsWith(Source: String; Value: String): Boolean;
var src: String;
begin
  src := Copy(Source, 1, Length(Value));
  result := (Lowercase(src) = Lowercase(Value));
end;

function StrEndsWith(Source: String; Value: String): Boolean;
var src: String;
begin
  src := Copy(Source, Length(Source)-Length(Value)+1, Length(Value));
  result := (Lowercase(src) = Lowercase(Value));
end;

procedure ConfigureTomcatPort;
var i:integer;
    filename: String;
    keystore: String;
    line, s: String;
    strings: TArrayOfString;
begin
  filename:=ExpandConstant('{app}\tomcat\conf\server.xml');
  if not FileExists(filename) then
    RaiseException('Unable to locate server.xml')
  else begin
    if not LoadStringsFromFile(filename, strings) then
      RaiseException('Error reading from server.xml')
    else begin
      for i:=0 to GetArrayLength(strings)-1 do begin
        s := Trim(strings[i]);
        if (StrStartsWith(s, '<Connector port="')) and (StrEndsWith(s, 'protocol="HTTP/1.1"')) then begin
          line := '<Connector port="'+IntToStr(Port+4)+'" protocol="HTTP/1.1"';
          strings[i] := line;
        end;
        //if Trim(strings[i])='<Connector port="8009" protocol="AJP/1.3" redirectPort="8443" />' then begin
        //  line := '<!-- ' + strings[i] + ' -->';
        //  strings[i] := line;
        //end;
        if Trim(strings[i])='<Server port="8005" shutdown="SHUTDOWN">' then begin
          line := '<Server port="-1" shutdown="SHUTDOWN">';
          strings[i] := line;
        end;
        if Trim(strings[i])='<Resource name="UserDatabase" auth="Container"' then begin
          line := '<Resource name="UserDatabase" auth="Container" readonly="true"';
          strings[i] := line;
        end;
        if (StrStartsWith(s, '<Listener className="org.apache.catalina.core.AprLifecycleListener"'))
        and (ShieldAllow.Checked[1] = true) then begin
          line := '<!-- ' + s + ' -->';
          strings[i] := line;
        end;
        if (StrStartsWith(s, '</Server>')) and (ShieldAllow.Checked[1] = true) then begin
          keystore:=ExpandConstant('{app}\keys\shield.jks');
          line :=
             '  <Service name="shield">' + #13#10
          +  '    <Connector port="' + IntToStr(ShieldPort) + '"' + #13#10
          +  '               protocol="HTTP/1.1"' + #13#10
          +  '               SSLEnabled="true"' + #13#10
          +  '               scheme="https"' + #13#10
          +  '               secure="true"' + #13#10
          +  '               clientAuth="false"' + #13#10
          +  '               sslProtocol="TLS"' + #13#10
          +  '               keystoreFile="' + keystore + '"' + #13#10
          +  '               keystoreType="JKS"' + #13#10
          +  '               keystorePass="shield"' + #13#10
          +  '               keyAlias="' + ShieldIpAddress + '"/>"' + #13#10
          +  '    <Engine name="shield" defaultHost="shield">' + #13#10
          +  '      <Host name="shield" appBase="webapps-ssl/shield"/>' + #13#10
          +  '    </Engine>' + #13#10
          +  '  </Service>' + #13#10
          +  '</Server>' + #13#10
          ;
          strings[i] := line;
        end;
      end;
      if not SaveStringsToFile(filename, strings, False) then
        RaiseException('Error updating server.xml');
    end;
  end;
end;

procedure ConfigureTomcatLogs;
var
  i, p, p2: Integer;
  filename: string;
  strings: TArrayOfString;
  line: string;
  tagStarted: Boolean;
begin
  filename := ExpandConstant('{app}\tomcat\conf\server.xml');
  if not FileExists(filename) then RaiseException('Unable to locate server.xml');
  if not LoadStringsFromFile(filename, strings) then RaiseException('Error reading from server.xml');
  tagStarted := False;
  for i := 0 to GetArrayLength(strings) - 1 do begin
    if not tagStarted and (Pos('<Valve className="org.apache.catalina.valves.AccessLogValve"', strings[i]) > 0) then begin
      line := '<!--'#13#10 + strings[i]
      strings[i] := line;
      tagStarted := True;
    end;
    if tagStarted then
      StringChangeEx(strings[i], 'pattern="%h %l %u %t &quot;%r&quot; %s %b"', 'pattern="%h %l %u %t &quot;%r&quot; %s %b ct:%{Content-Type}i"', True);
    if tagStarted and (Pos('/>', strings[i]) > 0) then begin
      line := strings[i] + #13#10'-->';
      strings[i] := line;
      tagStarted := False;
    end;
  end;
  if not SaveStringsToFile(filename, strings, False) then RaiseException('Error updating server.xml');

  filename := ExpandConstant('{app}\tomcat\conf\logging.properties');
  if not FileExists(filename) then RaiseException('Unable to locate logging.properties');
  if not LoadStringsFromFile(filename, strings) then RaiseException('Error reading from logging.properties');
  for i := 0 to GetArrayLength(strings) - 1 do begin
    StringChangeEx(strings[i], 'org.apache.juli.AsyncFileHandler', 'java.util.logging.FileHandler', False);
    p := Pos('java.util.logging.FileHandler.directory', strings[i])
    if p > 0 then begin
      line := Copy(strings[i], 1, p - 1) + 'java.util.logging.FileHandler.formatter = org.apache.juli.OneLineFormatter'#13#10
          + Copy(strings[i], 1, p - 1) + 'java.util.logging.FileHandler.limit = 65536'#13#10
          + Copy(strings[i], 1, p - 1) + 'java.util.logging.FileHandler.append = true'
      strings[i] := line;
    end;
    p := Pos('java.util.logging.FileHandler.prefix', strings[i])
    if p > 0 then begin
      p2 := Pos('=', strings[i]);
      line := Copy(strings[i], 1, p - 1) + 'java.util.logging.FileHandler.pattern = ${catalina.base}/logs/'
          + Trim(Copy(strings[i], p2 + 1, MaxInt)) + '%g.log';
      strings[i] := line;
    end;
    p := Pos('java.util.logging.FileHandler.maxDays', strings[i])
    if p > 0 then begin
      line := Copy(strings[i], 1, p - 1) + 'java.util.logging.FileHandler.count = 8'
      strings[i] := line;
    end;
  end;
  if not SaveStringsToFile(filename, strings, False) then RaiseException('Error updating logging.properties');
end;

procedure InstallTomcatService;
var
  ErrorCode: Integer;
  cmd: String;
begin
  cmd:=ExpandConstant('{app}\tomcat\bin\ubl_reg.bat');
  if not ShellExec('', cmd, '', ExpandConstant('{app}\tomcat\bin'), SW_HIDE, ewWaitUntilTerminated, ErrorCode) then
    MsgBoxSilent('Unable to run: '+cmd);
  if ErrorCode <> 0 then
    MsgBoxSilent('Unable to install service: '+cmd);
end;

procedure UninstallTomcatService;
var
  ErrorCode: Integer;
  cmd: String;
begin
  cmd:=ExpandConstant('{app}\tomcat\bin\ubl_unreg.bat');
  if not ShellExec('', cmd, '', ExpandConstant('{app}\tomcat\bin'), SW_HIDE, ewWaitUntilTerminated, ErrorCode) then
    MsgBoxSilent('Unable to run: '+cmd);
  if ErrorCode <> 0 then begin
    MsgBoxSilent('Unable to uninstall service: '+cmd);
  end;
end;

procedure ConfigureTomcat();
begin
  ConfigureTomcatPort;
  ConfigureTomcatLogs;
  CreateTomcatServiceCmdFiles;
  UninstallTomcatService;
  InstallTomcatService;
end;

procedure CurPageChanged(CurPage: Integer);
begin
  case CurPage of
    wpFinished: begin
      if GetSetupStatus() = SS_OK then begin
        ConfigureTomcat();
        RegisterServers;
        RegisterServices;
      end;
    end;
  end;
end;

// Configure Windows Firewall

function ExecNetsh(Params: string): Boolean;
var
  ErrorCode: Integer;
begin
  Result := ShellExec('', 'netsh', Params, ExpandConstant('{app}'), SW_HIDE, ewWaitUntilTerminated, ErrorCode)
    and (ErrorCode = 0)
end;

function DeleteFirewallRule(AppPath: string): Boolean;
var
  FullQuotedAppPath: string;
  Params: string;
begin
  FullQuotedAppPath := '"' + ExpandConstant('{app}\') + AppPath + '"';
  if IsVistaOrHigher() then begin // Advanced firewall management
    Params := 'advfirewall firewall delete rule name=all program=' + FullQuotedAppPath;
  end else begin // Old firewall management
    Params := 'firewall delete allowedprogram ' + FullQuotedAppPath;
  end;
  Result := ExecNetsh(Params);
end;

function AddFirewallRule(AppPath, RuleName: string): Boolean;
var
  FullQuotedAppPath: string;
  Params: string;
begin
  DeleteFirewallRule(AppPath);
  FullQuotedAppPath := '"' + ExpandConstant('{app}\') + AppPath + '"';
  if IsVistaOrHigher() then begin // Advanced firewall management
    Params := 'advfirewall firewall add rule name=' + RuleName + '_In' +
      ' dir=in program=' + FullQuotedAppPath + ' security=notrequired protocol=any action=allow';
    Result := ExecNetsh(Params);
    Params := 'advfirewall firewall add rule name=' + RuleName + '_Out' +
      ' dir=out program=' + FullQuotedAppPath + ' security=notrequired protocol=any action=allow';
    Result := ExecNetsh(Params) and Result;
  end else begin // Old firewall management
    Params := 'firewall add allowedprogram ' + FullQuotedAppPath + ' ' + RuleName + ' ENABLE';
    Result := ExecNetsh(Params);
  end;
end;

function AddFirewallRuleWithPort(AppPath, RuleName, Port: string): Boolean;
var
  FullQuotedAppPath: string;
  Params: string;
begin
  DeleteFirewallRule(AppPath);
  FullQuotedAppPath := '"' + ExpandConstant('{app}\') + AppPath + '"';
  if IsVistaOrHigher() then begin
    Params := 'advfirewall firewall add rule name=' + RuleName + '_In' +
      ' dir=in program=' + FullQuotedAppPath + ' security=notrequired protocol=TCP action=allow localport=' + Port;
    Result := ExecNetsh(Params);
    Params := 'advfirewall firewall add rule name=' + RuleName + '_Out' +
      ' dir=out security=notrequired protocol=TCP action=allow localport=' + Port;
    Params := 'advfirewall firewall add rule name=' + RuleName + '_Out' +
      ' dir=out program=' + FullQuotedAppPath + ' security=notrequired protocol=TCP action=allow localport=' + Port;
    Result := ExecNetsh(Params) and Result;
  end else begin // Old firewall management
    Params := 'firewall add portopening tcp ' + Port + ' ' + RuleName + ' ENABLE';
    Result := ExecNetsh(Params);
  end;
end;

procedure RegisterUntillInWindowsFirewall;
var
  Res: Boolean;
begin
  Res := AddFirewallRule('unTill.exe', 'unTill' + IntToStr(Port));
  Res := AddFirewallRuleWithPort('Untillsrv.exe', 'UntillSrv' + IntToStr(Port), IntToStr(Port)) and Res;
  Res := AddFirewallRule('untillsqlsrv.exe', 'UntillSQLSrv' + IntToStr(Port + 1)) and Res;
  Res := AddFirewallRule('servicemgr.exe', 'UntillServiceMgr' + IntToStr(Port + 2)) and Res;
  Res := AddFirewallRule('tomcat\bin\tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + '.exe', 'UntillJServer' + IntToStr(Port + 4)) and Res;
  Res := AddFirewallRule('terminals\Orderman\Sol\oman\oman.exe', 'OrdermanSolExe' + IntToStr(Port)) and Res;
  if Length(ShieldPortEdit.Text) > 0 then begin
    Res := AddFirewallRule('tomcat\bin\tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + '.exe', 'UntillShield' + ShieldPortEdit.Text);
  end;
  if not Res then
    MsgBoxSilent('Unable to register rules in Windows Firewall');
end;

procedure UnregisterUntillInWindowsFirewall;
begin
  DeleteFirewallRule('unTill.exe');
  DeleteFirewallRule('Untillsrv.exe');
  DeleteFirewallRule('untillsqlsrv.exe');
  DeleteFirewallRule('servicemgr.exe');
  DeleteFirewallRule('tomcat\bin\tomcat' + IntToStr(TOMCAT_MAJOR_VERSION) + '.exe');
  DeleteFirewallRule('terminals\Orderman\Sol\oman\oman.exe');
end;

procedure RunUntillServiceMgr;
var
  ResultCode: Integer;
begin
  if RestartNotPlanned then begin
    Log('Start servicemgr');
    Exec(ExpandConstant('{app}\servicemgr.exe'), '', '', SW_SHOWNORMAL, ewNoWait, ResultCode);
  end;
end;

procedure WriteDbUpgradedJET(dbName: String);
begin
  SetIniString('jet_upgrade', dbname, GetVersion('1'), ExpandConstant('{app}/dbstate.ini'));
end;

function doDbUpgrade() : boolean;
var
	ResultCode: integer;
  params : String ;
  fileExt : String;
  i : Integer;
  dblist : array of String;
begin
  dblist := ListDBFiles();
  WriteStatusFile(SS_RUNNING, RSS_DBUPGRADE);
  params := '';
  ResultCode := 0;
  if WizardSilent then begin
    for i := 0 to GetArrayLength(dblist) - 1 do begin
      params := '/db ' + dblist[i];
      if not Exec(ExpandConstant('{app}/DBUpgrade.exe'), params, '', SW_SHOW, ewWaitUntilTerminated, ResultCode) then begin
        Log('Failed DBUpgrade ' + params + ': ' + SysErrorMessage(ResultCode));
        break;
      end else if 0 <> ResultCode then begin
        Log('DBUpgrade failed for ' + dblist[i])
        break;
      end
      else
        Log('DB upgraded : ' + dblist[i]);
    end;
  end
  else begin
    if not Exec(ExpandConstant('{app}/DBUpgrade.exe'), params, '', SW_SHOW, ewWaitUntilTerminated, ResultCode) then
      Log('Failed DBUpgrade (no params): ' + SysErrorMessage(ResultCode));
  end;
  result := ResultCode = 0;

  if result and IsUDFReplaced then begin
    if (GetArrayLength(dblist) > 0) then begin
      params := '/db ' + dblist[0] + ' /checkUDF';
      if not Exec(ExpandConstant('{app}/DBUpgrade.exe'), params, '', SW_SHOW, ewWaitUntilTerminated, ResultCode) then
        Log('Failed DBUpgrade ' + params + ': ' + SysErrorMessage(ResultCode))
      else
        if 0 <> ResultCode then begin
          Log('UDF check failed');
          CheckUDFFailed := true;
        end else
          Log('UDF check success');
    end;
  end;

  // Write JET upgrade events
  for i := 0 to GetArrayLength(dblist) - 1 do begin
    WriteDbUpgradedJET(LowerCase(dblist[i]));
  end;

  Log('DbUpgrade returned : ' + IntToStr(ResultCode));
end;

procedure GenerateShieldKeyPair;
var
  ResultCode: Integer;
  success: Boolean;
  filename: String;
  cert: String;
  cmd: String;
begin
  if ShieldAllow.Checked[1] = true then begin
    filename := ExpandConstant('{app}\keys');
    if not FileExists(filename) then begin
      success := CreateDir(filename);
      if not success then begin
        Log('Can''t create folder ' + filename);
      end;
    end;
    filename := ExpandConstant('{app}\keys\shield.jks');
    if not FileExists(filename) then begin
      cmd := 'keytool.exe -genkey -keyalg RSA -keysize 2048 -keystore ' + filename
      + ' -dname "CN=' + ShieldIpAddress + '" -storepass shield -alias ' + ShieldIpAddress + ' -keypass shield -validity 36500';
      Log('Create keystore and keypair: ' + cmd);
      Exec('cmd.exe', '/c ' + cmd, '{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}/bin', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      if ResultCode <> 0 then begin
        Log('Can''t create ' + filename);
      end;
    end;

    cmd := 'keytool.exe -list -alias ' + ShieldIpAddress +' -storetype JKS -keystore ' + filename + ' -storepass shield';
    Log('Check keypair: ' + cmd);
    Exec('cmd.exe', '/c ' + cmd, '{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}/bin', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    if ResultCode <> 0 then begin
      cmd := 'keytool.exe -genkey -keyalg RSA -keysize 2048 -keystore ' + filename
      + ' -dname "CN=' + ShieldIpAddress + '" -storepass shield -alias ' + ShieldIpAddress + ' -keypass shield -validity 36500';
      Log('Create keypair');
      Exec('cmd.exe', '/c ' + cmd, '{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}/bin', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      if ResultCode <> 0 then begin
        Log('Can''t create keypair');
      end;
    end;
    cert:=ExpandConstant('{app}\tomcat\webapps-ssl\shield\ROOT\shield\');
    cmd := 'keytool.exe -exportcert -alias ' + ShieldIpAddress + ' -keypass shield -storetype JKS -keystore '
    + filename + ' -file ' + cert + 'shield.cert -rfc -storepass shield';
    Log('Export cert: ' + cmd);
    Exec('cmd.exe', '/c ' + cmd, '{#JRE_INSTALLATION_PATH}{#JRE_FOLDER}/bin', SW_HIDE, ewWaitUntilTerminated, ResultCode);
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
    bRollbackInstallation : boolean;
    stpStatus, err : String;
begin
  case CurStep of
    ssPostInstall: begin
      FirebirdPostInstall;
      bRollbackInstallation := true;
      if BeforeDbUpgrade() then
        if doDbUpgrade() then
        begin
          Log('DbUpgrade completed');
          bRollbackInstallation := false;
        end;
      if IsMakeBackup then begin
        if bRollbackInstallation then begin
          if IsMakeBackup() then  begin
            Log('Rollback installation');
            rollbackInstallation();
          end;
          err := 'DBUpgrade FAILED';
          stpStatus := SS_FAILED
        end
        else
          stpStatus := SS_OK;
        DeleteBackup();
      end
      else
        stpStatus := SS_OK;
      if Restarted or (ExpandConstant('{param:prepare_restart|0}') = '2') then
      begin
        DropRunOnceEntry();
        RestoreStartupSection(ExpandConstant('{userappdata}\{#BackupFolder}'));
      end;
      RegisterUntillInWindowsFirewall;
      RunUntillServiceMgr; // ensure that will be run after registration in Windows Firewall
      GenerateShieldKeyPair;
      SetSetupStatus(stpStatus, err);
    end;
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  case CurUninstallStep of
    usPostUninstall: begin
      UnregisterUntillInWindowsFirewall;
    end;
  end;
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
var
  prepRes : String;
begin
  if Restarted then
    LoadDefaultPort();

  if IsJreNotInstalled then begin
    result := DownloadJre();
    if '' <> result then
      exit;
  end;

  if isPrepareRestart then begin
    // Try to stop all application
    // if successed no restart needed
    // otherwise disable all startup shortcuts and restart
      if bRestartNeeded then begin
        prepRes := PrepareSystemToRestart();
        if '' = prepRes then  begin
          NeedsRestart := true;
          Result := QuitMessageReboot;

        end
        else begin
          NeedsRestart := false;
          Result := prepRes;
        end;
      end;
  end;

  if Result = '' then
    Result := FirebirdPrepareToInstall(NeedsRestart);

  if not NeedsRestart then begin
    if '' = result then begin
      if not PrepareBackupFolder() then begin
        result := CreateBackupFailed;
      end
      else
        WriteStatusFile(SS_RUNNING, RSS_INSTALL_FILES);
    end;

    if '' <> result then begin
      WriteStatusFile(SS_FAILED, result);
    end;
  end
  else
    NeedsRestart := not DoNotForceRestart();
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  if (PageID = wpFinished) and Restarted then
    CurPageChanged(wpFinished);
  Result := Restarted;
end;

procedure DeinitializeSetup;
begin
  DeinitSetup();
end;

function GetCurrentTime(param: String) : String;
begin
  result := GetDateTimeString('dd/mm/yyyy hh:nn:ss', '-', ':');
end;

function SetServiceMgrXmlAttrs: Boolean;
var
  FileData: AnsiString;
  fname, fd: String;
begin
  fname := ExpandConstant('{tmp}\ServiceMgrTask.xml');
  LoadStringFromFile(fname, FileData);
  fd := String(FileData);
  StringChange(fd, 'XXXXX-APPPATH-XXXXX', ExpandConstant('{app}'));
  SaveStringToFile(fname, fd, False);
  result := True;
end;
