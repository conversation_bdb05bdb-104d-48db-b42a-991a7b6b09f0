SET TERM ^ ;

CREATE OR ALTER procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text
         from menu_item
         join order_item on order_item.id_menu = menu_item.ID_MENU
  where menu_item.id >= :id_menu_item and order_item.QUANTITY > 0
  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || 'mi' || kind;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || '_' || id_articles;
    end
    idx = idx + 1;
  end
  suspend;
end^

SET TERM ; ^

grant execute on procedure GETMENUITEMOSIG to untilluser;

COMMIT;