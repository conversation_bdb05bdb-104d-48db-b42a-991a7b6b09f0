create table rent_period_items (
    id u_id,
    id_rent_periods bigint,
    quantity integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint rent_period_items_pk primary key (id),
    constraint rent_pi_fk1 foreign key (id_rent_periods) references rent_periods
);
commit;
grant all on rent_period_items to untilluser;
commit;
execute procedure register_sync_table_ex('rent_period_items', 'b', 1);
commit;
execute procedure register_bo_table('rent_period_items', 'id_rent_periods', 'rent_periods');
commit;


alter table rent_periods_articles add id_rent_period_items bigint,
  add constraint rent_pa_pi_fk foreign key (id_rent_period_items) references rent_period_items;
commit;
