SET TERM ^ ;

CREATE OR ALTER TRIGGER SOLD_ARTICLES_UPD FOR SOLD_ARTICLES
ACTIVE BEFORE UPDATE OR DELETE POSITION 0
as
declare variable dbser integer;
declare variable logging int;
begin
  /* Trigger text */
  select ser_sync_db from untill_db into :dbser;
  logging = null;
  select logging from untill_con where con_id=current_connection into :logging;
  if(logging is null) then
    logging = 0;

  if ( ( logging <> 2 ) and (updating or deleting) ) then begin
    exception e_untill 'you can not delete or update sold_articles';
  end
end
^

SET TERM ; ^
