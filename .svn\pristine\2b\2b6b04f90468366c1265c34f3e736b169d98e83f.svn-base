unit TicketsMultiEmbEntityManager;

interface
uses
  EntityManagerU, Classes, Windows, UntillDBU, ClassManagerU, PluginU, IBSQL,
  UntillFram, TreeNodesU, TreeFoldersU, SysUtils,Messages,
  CommonStringsU, CommonU, EmbEntityManagerU, EntityFram, Variants,
  DBEntityListFram, TicketsFilterFram;
type
  TTicketsMultiEmbEntityManager = class(TEmbeddedEntityManager)
    private
      ffram          : TTicketsFilterFrame;
      flistview      : TEMListView;
      FParentManager : TEntityManager;
      procedure BeforeRefresh(Sender: TObject);
      procedure OnFilterConfirmed(Sender: TObject);
    public
      constructor Create( AOwner: TComponent;
                        AParentManager: TEntityManager;
                        AParentObjectId:TIdType);override;
      procedure InitializeList(Sender:TEMListView);
      function GetTicketName(id_tickets : Int64) : String;
      class function GetTableName: String; override;
  end;

implementation
uses DataControlsU, Menus, TntCompatibilityU, UntillToolBarU,
  StrUtils, DB, Controls, ClassesU;

{ TTicketsMultiEmbEntityManager }

var
STR_CurrencyName :WideString;

procedure TTicketsMultiEmbEntityManager.BeforeRefresh(Sender: TObject);
var ind: integer;
begin
  assert(assigned(ffram));
  ind:=ListParams.IndexOfCondition('upper(tickets.name) like upper');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if ffram.edtName.Text <> '' then
    ListParams.AddCondition('upper(tickets.name) like upper(''%'+SafeSQLParam(ffram.edtName.Text)+'%'')');
  Plugin.SetInterfaceSetting('TicketMultiFilter','NameFilter',ffram.edtName.Text);
end;

constructor TTicketsMultiEmbEntityManager.Create( AOwner: TComponent;
                        AParentManager: TEntityManager;
                        AParentObjectId:TIdType);
begin
  inherited;
  FParentManager := AParentManager;
  LoadIcon(Plugin.GetImageFileName('tickets.ico'));
  IDFieldName:='id';
  SetListParams(['id','name'],'id');
  ListParams.AddCondition('is_report=1');
  InitializeListProc:=InitializeList;
  Options := Options  + [emoMultipleSelection];
  BeforeRefreshList:=BeforeRefresh;
end;

function TTicketsMultiEmbEntityManager.GetTicketName(id_tickets: Int64): String;
var bm : TBookmark;
begin
  result := '';
  if id_tickets<=0 then exit;
  ClientDataset.DisableControls;
  bm := ClientDataset.GetBookmark;
  try
    ClientDataset.First;
    while not ClientDataset.eof do begin
      if StrToInt64Def(ClientDataset.FieldByName('id').asString,0)=id_tickets then begin
        result := ClientDataset.FieldByName('name').asString;
        exit;
      end;
      ClientDataset.Next;
    end;
  finally
    ClientDataset.EnableControls;
    ClientDataset.GotoBookmark(bm);
    ClientDataset.FreeBookmark(bm);
  end;
end;

class function TTicketsMultiEmbEntityManager.GetTableName: String;
begin
  result:='tickets';
end;

procedure TTicketsMultiEmbEntityManager.InitializeList(Sender: TEMListView);
var fr:TDBEntityListFrame;
begin
  with Sender do begin

    flistview := Sender;
    AddFieldHeader('id',dtString,0,'id',false);
    AddFieldHeader(CommonStringsU.StrNameCaption, dtWideString, 50, 'name', true);
    fr:=TDBEntityListFrame(ListView.Frame);
    if fr.FindComponent('TicketsFilterFrame') = nil then
      ffram:=TTicketsFilterFrame.Create(fr);

    ffram.name := 'TicketsFilterFrame';
    fr.panTop.Height:=ffram.Height;
    ffram.Parent:=fr.panTop;
    ffram.Align:=alClient;
    ffram.OnFilterConfirmed := OnFilterConfirmed;
    ffram.edtName.Text:=Plugin.GetInterfaceSetting('TicketMultiFilter','NameFilter','');
  end;
end;

procedure TTicketsMultiEmbEntityManager.OnFilterConfirmed(Sender: TObject);
begin
  flistview.unselectAll;
  flistview.RefreshList;
end;
end.
