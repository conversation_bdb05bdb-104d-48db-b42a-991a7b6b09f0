[Start]
**************************************************
TimeStamp=24.02.2023 14:34
Interval=0;30.12.1899
[Class]
Name=TBLMessageKeyboardString
TimeStamp=24.02.2023 14:34
Interval=9.87268140306696E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=24.02.2023 14:34
Interval=1.41087948577479E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=221
tableno=221
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=24.02.2023 14:34
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=221
tableno=221
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=24.02.2023 14:34
Interval=4.80902817798778E-5;30.12.1899 0:00:04
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=2
id=5000000122
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=24.02.2023 14:34
Interval=9.06249624677002E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
articleType=atNormal
id=5000000194
ID_ARTICLE_BARCODES=0
id_courses=0
ID_SIZE_MODIFIER_ITEM=0
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgAddSupplement
TimeStamp=24.02.2023 14:34
Interval=1.06134248198941E-5;30.12.1899
Exception=
StateGUID={F554408C-6728-4722-8CA6-E038FAB17615}
StateName=TBLRGetSupplement
FieldCount=0
[Class]
Name=TBLRMsgOption
TimeStamp=24.02.2023 14:34
Interval=1.05324143078178E-5;30.12.1899
Exception=
StateGUID={F554408C-6728-4722-8CA6-E038FAB17615}
StateName=TBLRGetSupplement
FieldCount=3
free_Option=False
id_article=5000000370
id_option=15000033435
[Class]
Name=TBLMessageOk
TimeStamp=24.02.2023 14:34
Interval=7.12962355464697E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgNeedPredefinedDiscount
TimeStamp=24.02.2023 14:34
Interval=1.11458357423544E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=13
Discount_apply=daSingle
Discount_subject=0
Discount_type=4
Free_reason=
FRType=frtFix
id_category=0
id_course=0
id_department=0
id_discount_group=0
id_excl_period=0
id_group=0
id_reason=10000016132
Value=50;18.02.1900
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=24.02.2023 14:34
Interval=1.68402766576037E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>TCleanCashSCB.GetSignature
Signature: TEST:3f9d39889d1e
ManufacturingCode: TestManufCode
VscIdentificationNr: TestVSCID
ControlTimestamp: 02.02.2004 08:45:20
TicketCounter: 10/10 PS
ReceiptTotal: 1.61
<<<TCleanCashSCB.GetSignature
>>>TCleanCashSCB.CalcPLUHash
0001TANGO               00000200 
0001HAMBURGER           00000121 
0001KORTING             00000160 

<<<TCleanCashSCB.CalcPLUHash
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>                <GROUP>,00     1,00   0,15    0,85 21,0%         0,85    
        1     Hamburger                     0,60                                
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=3
id_printer=0
id_ticket=0
ignoreClientQuestion=False
[Class]
Name=TBLRMsgNeedProforma
TimeStamp=24.02.2023 14:34
Interval=1.13426358439028E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TCleanCashSCB.GetSignature
Signature: TEST:3f9d39889d1e
ManufacturingCode: TestManufCode
VscIdentificationNr: TestVSCID
ControlTimestamp: 02.02.2004 08:45:20
TicketCounter: 10/10 PS
ReceiptTotal: 1.61
<<<TCleanCashSCB.GetSignature
>>>TCleanCashSCB.CalcPLUHash
0001TANGO               00000200 
0001HAMBURGER           00000121 
0001KORTING             00000160 

<<<TCleanCashSCB.CalcPLUHash
>>>Pos ticket
Printer=Bar Printer: Ticket=
                                          
[LS:  ]  PRO FORMA TICKET   [RS:  ]
                                          
Table:  221a             [LS: ]02.02.2004 11:45 
Proforma no: 1[RS: ]                   Seat:  1 
FDM Ticket nr: 6                          
Served by:    Peter[RS: ]                       
[LS: -]------------------------------------------[RS: -]
Qty Description[RS: ]             U.P.   Total  
[LS: -]------------------------------------------[RS: -]
 1  Tango[RS: ]                   2,00    2,00  
 1  Hamburger[RS: ]               1,21    1,21  
-1  Korting[RS: ]                        -1,60  
[LS: -]------------------------------------------[RS: -]
                     [LS: ]Subtotal:      3,21  
                     [LS: ]Discount:      1,60  
[LS: -]------------------------------------------[RS: -]
                  [LS: ]Grand Total:      1,61  
[LS: =]==========================================[RS: =]
 VAT%:       VAT:     Excl.:      Gross:  [RS: ]
------------------------------------------[RS: -]
   21%       0,17       0,83        1,00  
    6%       0,03       0,58        0,61  
[LS: =]==========================================[RS: =]
Total:       0,21       1,40        1,61  
                                          
Control data:                             
Fdm: TestManufCode    02.02.2004 08:45:20[RS: ] 
Vsc: TestVSCID[RS: ]        Hash: fafa8024      
Sce: 12345678901234[RS: ]   NetworkID: aabbcc   
Ver.: 100.0.A.12345   PC: test[RS: ]            
Ticket: 10/10 PS[RS: ]                          

<<<Pos ticket
<<<<<EOSD
FieldCount=5
BillsHandling=True
cc_eft=False
id_printer=0
id_ticket=0
IgnorePrint=False
[Class]
Name=TBLRMsgTableNo
TimeStamp=24.02.2023 14:34
Interval=5.68286486668512E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=221
tableno=221
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=24.02.2023 14:34
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=221
tableno=221
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=24.02.2023 14:34
Interval=8.35648097563535E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=3
id_printer=0
id_ticket=0
ignoreClientQuestion=False
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=24.02.2023 14:34
Interval=1.65509845828637E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TCleanCashSCB.GetSignature
Signature: TEST:3f9d39889d1e
ManufacturingCode: TestManufCode
VscIdentificationNr: TestVSCID
ControlTimestamp: 02.02.2004 08:45:20
TicketCounter: 10/10 NS
ReceiptTotal: 1.61
<<<TCleanCashSCB.GetSignature
>>>TCleanCashSCB.CalcPLUHash
0001TANGO               00000200 
0001HAMBURGER           00000121 
0001KORTING             00000160 

<<<TCleanCashSCB.CalcPLUHash
>>>Pos ticket
Printer=Bar Printer: Ticket=
                                          
[LS:  ]     VAT RECEIPT     [RS:  ]
                                          
Table:  221a             [LS: ]02.02.2004 11:45 
Bill nr: 28                      Seat:  1 
FDM Ticket nr: 7                          
Served by:    Peter[RS: ]                       
[LS: -]------------------------------------------[RS: -]
Qty Description[RS: ]             U.P.   Total  
[LS: -]------------------------------------------[RS: -]
 1  Tango[RS: ]                   2,00    2,00  
 1  Hamburger[RS: ]               1,21    1,21  
-1  Korting[RS: ]                        -1,60  
[LS: -]------------------------------------------[RS: -]
                     [LS: ]Subtotal:      3,21  
                     [LS: ]Discount:      1,60  
[LS: -]------------------------------------------[RS: -]
                  [LS: ]Grand Total:      1,61  
[LS: =]==========================================[RS: =]
                          [LS: ]Cash      1,60  
[LS: =]==========================================[RS: =]
 VAT%:       VAT:     Excl.:      Gross:  [RS: ]
------------------------------------------[RS: -]
   21%       0,17       0,83        1,00  
    6%       0,03       0,58        0,61  
[LS: =]==========================================[RS: =]
Total:       0,21       1,40        1,61  
                                          
Control data:                             
Fdm: TestManufCode    02.02.2004 08:45:20[RS: ] 
Vsc: TestVSCID[RS: ]        Hash: fafa8024      
Sce: 12345678901234[RS: ]   NetworkID: aabbcc   
Ver.: 100.0.A.12345   PC: test[RS: ]            
Ticket: 10/10 NS[RS: ]                          
TEST:3f9d39889d1e                         

<<<Pos ticket
<<<<<EOSD
FieldCount=24
AskEmail=False
AsTips=False
AsVoucher=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
DetailText=
EFTData=
EFTParams=
Email=
EmailInvoice=False
ExternalID=
idParts=0
id_payments=5000001501
IgnoreCheckProforma=False
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
ZeroPrice=False
[Class]
Name=TMsgTestSetFreeDrink
TimeStamp=24.02.2023 14:34
Interval=3.55671290890314E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=24.02.2023 14:34
Interval=2.4768516595941E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
