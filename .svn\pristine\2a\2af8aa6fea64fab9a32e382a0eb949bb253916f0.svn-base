inherited NeedPrintKSOrderFrame: TNeedPrintKSOrderFrame
  Width = 304
  Height = 388
  ExplicitWidth = 304
  ExplicitHeight = 388
  object UntillPanel1: TUntillPanel
    Left = 0
    Top = 0
    Width = 304
    Height = 388
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    BorderColor = clBlack
    ExplicitLeft = 3
    ExplicitTop = -3
    ExplicitHeight = 399
    object TntLabel1: TTntLabel
      Left = 18
      Top = 19
      Width = 103
      Height = 21
      AutoSize = False
      Caption = 'Printer:'
      Transparent = True
    end
    object TntLabel2: TTntLabel
      Left = 18
      Top = 49
      Width = 104
      Height = 17
      AutoSize = False
      Caption = 'Layout:'
      Transparent = True
    end
    object usbPrinter: TUntillSelectBox
      Left = 134
      Top = 18
      Width = 153
      Height = 21
      BevelOuter = bvNone
      ReadOnly = True
      RightButton.DisabledImageIndex = 1
      RightButton.HotImageIndex = 2
      RightButton.ImageIndex = 0
      RightButton.Visible = True
      TabOrder = 0
      OnButtonClick = usbPrinterButtonClick
      SelStart = 0
      SelLength = 0
    end
    object usbTicket: TUntillSelectBox
      Left = 134
      Top = 49
      Width = 153
      Height = 21
      BevelOuter = bvNone
      ReadOnly = True
      RightButton.DisabledImageIndex = 1
      RightButton.HotImageIndex = 2
      RightButton.ImageIndex = 0
      RightButton.Visible = True
      TabOrder = 1
      OnButtonClick = usbTicketButtonClick
      SelStart = 0
      SelLength = 0
    end
    object lblMode: TUntillGroupBox
      Left = 16
      Top = 82
      Width = 269
      Height = 111
      Caption = 'Mode'
      TabOrder = 2
      object rbAllTables: TUntillRadioButton
        Left = 16
        Top = 74
        Width = 223
        Height = 17
        Caption = 'All tables in one ticket'
        TabOrder = 0
      end
      object rbPerTable: TUntillRadioButton
        Left = 16
        Top = 48
        Width = 223
        Height = 17
        Caption = 'Per table'
        Checked = True
        TabOrder = 1
        TabStop = True
      end
      object rbPerArticle: TUntillRadioButton
        Left = 16
        Top = 22
        Width = 223
        Height = 17
        Caption = 'Per article'
        TabOrder = 2
      end
    end
    object grpData: TUntillGroupBox
      Left = 16
      Top = 209
      Width = 271
      Height = 168
      Caption = 'Data type'
      TabOrder = 3
      object rbNextCourse: TUntillRadioButton
        Left = 16
        Top = 21
        Width = 290
        Height = 20
        Caption = 'Only next course articles'
        Checked = True
        TabOrder = 0
        TabStop = True
      end
      object rbAll: TUntillRadioButton
        Left = 16
        Top = 48
        Width = 290
        Height = 20
        Caption = 'All articles'
        TabOrder = 1
      end
      object rbActive: TUntillRadioButton
        Left = 16
        Top = 76
        Width = 290
        Height = 20
        Caption = 'Current active articles'
        TabOrder = 2
      end
      object rbPending: TUntillRadioButton
        Left = 16
        Top = 105
        Width = 290
        Height = 20
        Caption = 'Pending articles'
        TabOrder = 3
      end
      object rbSelected: TUntillRadioButton
        Left = 16
        Top = 136
        Width = 290
        Height = 20
        Caption = 'Selected articles'
        TabOrder = 4
      end
    end
  end
end
