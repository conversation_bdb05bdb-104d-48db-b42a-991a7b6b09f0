unit PaymentEntityFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, EntityFram, UntillSelectBoxU, StdCtrls, TntCompatibilityU,
  UntillCheckBoxU, ExtCtrls, ComCtrls,
  UntillComboBoxU, UntillPanelU, UntillSpinEditU,
  BookkeepingEntityManager, CurrencyEntityManager, Generics.Collections, UBLProxyU,
  PaymentBookkeepingEmbEntityManager;

type
  TPaymentEntityFrame = class(TEntityFrame)
    UntillPanel1: TUntillPanel;
    lblKind: TTntLabel;
    cmbKind: TUntillComboBox;
    lblName: TTntLabel;
    edtName: TTntEdit;
    lblNumber: TTntLabel;
    useNumber: TUntillSpinEdit;
    lblPSP: TTntLabel;
    cmbPsp: TUntillComboBox;
    lblBookkeeping: TTntLabel;
    usbBookkeeping: TUntillSelectBox;
    lblCurrency: TTntLabel;
    usbCurrency: TUntillSelectBox;
    lblPspParams: TTntLabel;
    edtPspParams: TTntEdit;
    pnlBookkeepingDetails: TPanel;
    lblBookkeepingDetails: TBlockHeading;
    pnlBookkeepingDetailEntries: TPanel;
    edtExternalId: TTntEdit;
    lblExternalId: TTntLabel;
    chkNotReopen: TUntillCheckBox;
    procedure cmbKindChange(Sender: TObject);
    procedure usbBookkeepingButtonClick(Sender: TObject);
    procedure usbCurrencyButtonClick(Sender: TObject);
    procedure updateDeviceCombo();

  private
    { Private declarations }
  public
    emBookkeeping :   TBookkeepingEntityManager;
    emBookkeepingDetails: TPaymentBookkeepingEmbEntityManager;
    emCurrency :   TCurrencyEntityManager;
    { Public declarations }
    procedure TranslateStrings; override;
    constructor Create(AOwner: TComponent); override;
    destructor  Destroy; override;
  end;

var
  PaymentEntityFrame: TPaymentEntityFrame;

implementation
uses commonU, PluginU, CommonStringsU, Math, PspParamsFram, DevicesU,
  PaymentsU, DataControlsU, EntityManagerU, CurrencyU, SCReaderParamsFram,
  CashdroParamsFram, UBLU, DriverConstU, HotelConceptsParamsFram;
{$R *.dfm}

procedure TPaymentEntityFrame.TranslateStrings;
var oldi:integer;
begin
  inherited;
  if NewEntity then
    Self.Caption:=Plugin.Translate('PaymentEntityManager','New payment method')
  else
    Self.Caption:=Plugin.TranslateLabel('PaymentEntityManager','Payment')+edtName.Text;

  lblName.Caption:=StrNameLabelCaption;
  lblNumber.Caption:=StrNumberLabelCaption;
  chkNotReopen.Caption:=Plugin.Translate('PaymentEntityManager','Do not re-open');
  lblKind.Caption:=Plugin.TranslateLabel('PaymentEntityManager','Kind');
  lblPSP.Caption:=Plugin.TranslateLabel('PaymentEntityManager','EFT');
  lblBookkeeping.Caption:=Plugin.TranslateLabel('PaymentEntityManager','Bookkeeping');
  lblCurrency.Caption:=Plugin.TranslateLabel('PaymentEntityManager','Currency');
  lblBookkeepingDetails.Caption:=Plugin.Translate('PaymentEntityManager','Bookkeeping Details');
  lblExternalId.Caption:=Plugin.Translate('PaymentEntityManager','3rd party Id');

  with cmbKind do begin
    oldi:=ItemIndex;
    Clear;
    Items.Add(Plugin.Translate('PaymentEntityManager','Cash'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Card'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Account'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Room'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Cheque'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Hash'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Service charge'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Discount'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Smart card'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Cash machine'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Newyse on-account'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Gift Card'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Prepaid Card Beverage Control'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Voucher'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Zapper'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Coupon'));
    Items.Add(Plugin.Translate('PaymentEntityManager','Bill deposit'));
    ItemIndex:=max(0, oldi);
  end;

  oldi := cmbPsp.ItemIndex;
  updateDeviceCombo;
  cmbPsp.ItemIndex:=min(cmbPsp.Items.Count-1, oldi);
end;

procedure TPaymentEntityFrame.cmbKindChange(Sender: TObject);
begin
  inherited;
  if cmbKind.ItemIndex = PAYMENT_CASH then begin
    usbCurrency.Enabled:=true;
    lblCurrency.Enabled:=true;
    usbCurrency.Value:=GetMainCurrency(UntillDB).Id;
    usbCurrency.Text:=emCurrency.GetWideStringById('name',usbCurrency.Value);
  end else begin
    usbCurrency.Clear;
    usbCurrency.Enabled:=false;
    lblCurrency.Enabled:=false;
  end;
  updateDeviceCombo();
  RecursiveChangeEnabled(pnlBookkeepingDetails, cmbKind.ItemIndex in [PAYMENT_CARD, PAYMENT_GIFT_CARD]);
end;

procedure TPaymentEntityFrame.usbBookkeepingButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emBookkeeping.ShowModal(usbBookkeeping.Value);
  if res>0 then with usbBookkeeping do begin
    Value:=res;
    Text:=emBookkeeping.GetWideStringById('account',res);
  end;
  usbBookkeeping.SetFocus;
end;

constructor TPaymentEntityFrame.Create(AOwner: TComponent);
begin
  inherited;
  emBookkeeping := TBookkeepingEntityManager.Create(Self, UntillDB);
  emBookkeeping.DialogMode:=true;
  emCurrency :=   TCurrencyEntityManager.Create(Self, UntillDB);
  emCurrency.DialogMode:=true;

  emBookkeepingDetails:=TPaymentBookkeepingEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId);
  emBookkeepingDetails.CreateListFrame(pnlBookkeepingDetailEntries);
end;

destructor TPaymentEntityFrame.Destroy;
begin
  cmbPsp.ClearWithObjects;
  FreeAndNil(emBookkeeping);
  FreeAndNil(emCurrency);
  FreeAndNil(emBookkeepingDetails);
  inherited;
end;


procedure TPaymentEntityFrame.usbCurrencyButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emCurrency.ShowModal(usbCurrency.Value);
  if res>0 then with usbCurrency do begin
    Value:=res;
    Text:=emCurrency.GetWideStringById('name',res);
  end;
  usbCurrency.SetFocus;
end;

procedure TPaymentEntityFrame.updateDeviceCombo;
var oldi:integer;
begin
  cmbPsp.Enabled:=false;
  lblPSP.Enabled:=false;
  cmbPsp.ClearWithObjects;
  lblPspParams.Enabled := False;
  edtPspParams.Enabled := False;
	lblPspParams.Caption := Plugin.TranslateLabel('PaymentEntityManager','EFT Params');
  if (cmbKind.ItemIndex = PAYMENT_CARD) or (cmbKind.ItemIndex = PAYMENT_GIFT_CARD) then begin
    lblPspParams.Enabled := True;
    edtPspParams.Enabled := True;
    cmbPsp.Enabled:=true;
    lblPSP.Enabled:=true;
    lblPSP.Caption := Plugin.TranslateLabel('PaymentEntityManager','EFT');
    TPSPParamsFrame.LoadEFTInterfaces(UntillDB, cmbPsp);
    cmbPsp.Items.InsertObject(0, Plugin.Translate('PaymentEntityManager','Don''t use EFT'), nil);
    cmbPsp.ItemIndex := 0;
  end else if cmbKind.ItemIndex = PAYMENT_VOUCHER then begin
    cmbPsp.Enabled:=false;
    usbCurrency.Enabled:=false;
  end else if cmbKind.ItemIndex = PAYMENT_ZAPPER then begin
    cmbPsp.Enabled:=false;
    usbCurrency.Enabled:=false;
  end else if cmbKind.ItemIndex = PAYMENT_SMARTCARD then begin
    cmbPsp.Enabled:=true;
    lblPSP.Enabled:=true;
    lblPSP.Caption := Plugin.TranslateLabel('PaymentEntityManager','Reader');
    with cmbPsp do begin
      oldi:=ItemIndex;
      Items.Add(SCReaderModelCaption(SC_READER_MODEL_FEIG4030USB_PCSC));
  		Items.Add(SCReaderModelCaption(SC_READER_MODEL_FEIG4030USB_OBID));
      ItemIndex:=max(0, oldi);
    end;
  end else if cmbKind.ItemIndex = PAYMENT_ROOM then begin
    cmbPsp.Enabled:=true;
    lblPSP.Enabled:=true;
    lblPSP.Caption := Plugin.TranslateLabel('PaymentEntityManager','Interface');
    cmbPsp.Items.AddObject(Plugin.Translate('PaymentEntityManager','Auto'), nil);
    THotelConceptsParamsFrame.LoadHotelInterfaces(UntillDB, cmbPsp);
    lblPspParams.Enabled := True;
    edtPspParams.Enabled := True;
    lblPspParams.Caption := Plugin.TranslateLabel('PaymentEntityManager','Params');
  end else if cmbKind.ItemIndex = PAYMENT_CASHDRO then begin
    cmbPsp.Enabled:=true;
    lblPSP.Enabled:=true;
    lblPSP.Caption := Plugin.TranslateLabel('PaymentEntityManager','Device');
    oldi:=cmbPsp.ItemIndex;
    TCashdroDevice.LoadCashMachineDrivers(UntillDb, cmbPsp, True);
    cmbPsp.ItemIndex:=max(0, oldi);
{    with cmbPsp do begin
      Items.Add(CashdroDeviceCaption(CASHDRO_MODEL_ICG));
      ItemIndex:=max(0, oldi);
    end;}
  end;
end;

end.
