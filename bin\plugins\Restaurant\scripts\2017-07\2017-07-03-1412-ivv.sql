create table table_area_computers (
    id u_id,
    id_table_area bigint,
    id_computer bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint table_area_computers_pk primary key (id),
	constraint table_area_computers_fk1 foreign key (id_table_area) references table_area(id),
	constraint table_area_computers_fk2 foreign key (id_computer) references computers(id)
);
commit;
grant all on table_area_computers to untilluser;
commit;
execute procedure register_sync_table_ex('table_area_computers', 'b', 1);
commit;
execute procedure register_bo_table('table_area_computers', 'id_table_area', 'table_area');
commit;
execute procedure register_bo_table('table_area_computers', 'id_computer', 'computers');
commit;

