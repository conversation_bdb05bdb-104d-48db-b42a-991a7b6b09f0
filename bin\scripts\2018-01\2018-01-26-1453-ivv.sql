CREATE TABLE break_reasons  (
	ID U_ID,
    br_number integer,
    br_name varchar(50),
    br_NAME_DATA blob,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint break_reasons_pk primary key (id)
);
commit;

execute procedure register_sync_table_ex('break_reasons', 'b', 1);
execute procedure register_bo_table('break_reasons', '', '');
commit;

CREATE TABLE user_breaks (
	ID U_ID,
    id_untill_user  bigint,
    ub_start  		timestamp,
    id_break_reason bigint,
    ub_end 			timestamp,
    ub_comments 	varchar(250),
    id_user_end_action 	bigint,
    constraint user_breaks_pk primary key (id),
	constraint user_breaks_fk1 foreign key (id_untill_user) references untill_users(id),
	constraint user_breaks_fk2 foreign key (id_break_reason) references break_reasons(id),
	constraint user_breaks_fk3 foreign key (id_user_end_action) references untill_users(id)
);
commit;

execute procedure register_sync_table_ex('user_breaks', 'p', 1);
commit;

