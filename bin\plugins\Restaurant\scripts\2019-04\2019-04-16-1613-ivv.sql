create table bill_chairs (
    id u_id,
    id_bill bigint,
    chair_number integer,	
    id_chair_allergens bigint,
    text varchar(50),
    constraint bill_chairs_pk primary key (id),
    constraint bill_chairs_fk1 foreign key (id_bill) references bill(id),
    constraint bill_chairs_fk2 foreign key (id_chair_allergens) references allergens(id)
);
commit;
grant all on bill_chairs to untilluser;
commit;
execute procedure register_sync_table_ex('bill_chairs', 'p', 1);
commit;

