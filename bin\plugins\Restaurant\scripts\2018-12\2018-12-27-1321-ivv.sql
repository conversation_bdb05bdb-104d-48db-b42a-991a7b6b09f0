create table order_type (
    id u_id,
    number integer,
    name varchar(100),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint order_type_pk primary key (id)
);
commit;
grant all on order_type to untilluser;
commit;
execute procedure register_sync_table_ex('order_type', 'b', 1);
commit;
execute procedure register_bo_table('order_type', '', '');
commit;

alter table bill add id_order_type bigint,
  add constraint bill_order_type_fk foreign key (id_order_type) references order_type(id);
commit;


