CREATE OR ALTER VIEW oi_size_price(
    id_order_item,
    art_size_name,
    price,
    original_price,
    VAT
    )
AS
select order_item.id id_order_item,
    articles.name art_size_name,
    order_item.price + coalesce(oi_size.price,0) price,
    order_item.original_price + coalesce(oi_size.original_price,0) original_price,
    order_item.vat + coalesce(oi_size.vat,0) vat
from order_item
left outer join order_item_sizes oi_size on oi_size.id_order_item=order_item.id
left outer join articles on articles.id=oi_size.id_articles
where order_item.id_article_options is null;
commit;

CREATE OR ALTER VIEW pi_size_price(
    id_pbill_item,
    art_size_name,
    price,
    original_price,
    VAT
    )
AS
select pbill_item.id id_pbill_item,
    articles.name art_size_name,
    pbill_item.price + coalesce(oi_size.price,0) price,
    pbill_item.original_price + coalesce(oi_size.original_price,0) original_price,
    pbill_item.vat + coalesce(oi_size.vat,0) vat
from pbill_item
left outer join order_item_sizes oi_size on oi_size.id_pbill_item=pbill_item.id
left outer join articles on articles.id=oi_size.id_articles
where pbill_item.id_article_options is null;
commit;

CREATE OR ALTER VIEW mi_size_price(
    id_menu_item,
    art_size_name,
    price,
    original_price,
    VAT
    )
AS
select menu_item.id id_menu_item,
    articles.name art_size_name,
    menu_item.price + coalesce(oi_size.price,0) price,
    menu_item.original_price + coalesce(oi_size.original_price,0) original_price,
    menu_item.vat + coalesce(oi_size.vat,0) vat
from menu_item
left outer join order_item_sizes oi_size on oi_size.id_menu_item=menu_item.id
left outer join articles on articles.id=oi_size.id_articles
where menu_item.rowbeg=1 or (menu_item.id_article_options is null);

grant all on oi_size_price to untilluser;
commit;
grant all on pi_size_price to untilluser;
commit;
grant all on mi_size_price to untilluser;
commit;
