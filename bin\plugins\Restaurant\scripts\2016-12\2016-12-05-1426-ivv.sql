create table ks_wf_stage_template_item (
    id u_id,
    id_stage_template bigint,
    id_kitchen_screens bigint,
    ks_purpose    integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint kswfsti_pk primary key (id),
    constraint kswfsti_fk1 foreign key (id_stage_template) references ks_wf_stage_template(id),
    constraint kswfsti_fk2 foreign key (id_kitchen_screens) references kitchen_screens(id)
);
commit;
grant all on ks_wf_stage_template_item to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_stage_template_item', 'b', 1);
commit;
execute procedure register_bo_table('ks_wf_stage_template_item', 'id_stage_template', 'ks_wf_stage_template');
commit;
execute procedure register_bo_table('ks_wf_stage_template_item', 'id_kitchen_screens', 'kitchen_screens');
commit;

