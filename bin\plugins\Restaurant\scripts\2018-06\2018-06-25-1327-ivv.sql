create table ta_delivers (
    id u_id,
    id_ta_bills u_id,
    id_untill_users bigint,
    dt timestamp,
    id_deliverman bigint,
    constraint ta_delivers_pk primary key (id),
    constraint ta_delivers_fk1 foreign key (id_ta_bills) references ta_bills (id),
    constraint ta_delivers_fk2 foreign key (id_untill_users) references untill_users (id),
    constraint ta_delivers_fk3 foreign key (id_deliverman) references untill_users (id)
);
commit;
grant all on ta_delivers to untilluser;
commit;
execute procedure register_sync_table_ex('ta_delivers', 'p', 1);
commit;

