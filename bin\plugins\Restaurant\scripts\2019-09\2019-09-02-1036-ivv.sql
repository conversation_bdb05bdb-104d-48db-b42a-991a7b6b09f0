create table article_size_modifier (
    id u_id,
    id_articles 	  bigint,
    ID_SIZE_MODIFIER_ITEM bigint,
    id_unity_sales 	  bigint,
    is_active 		  smallint,
    IS_ACTIVE_MODIFIED 	  timestamp,
    IS_ACTIVE_MODIFIER 	  varchar(30),
    constraint article_size_modifier_pk primary key (id),
    constraint article_size_modifier_fk1 foreign key (ID_SIZE_MODIFIER_ITEM) references SIZE_MODIFIER_ITEM(id),
    constraint article_size_modifier_fk2 foreign key (id_articles) references articles(id),
    constraint article_size_modifier_fk3 foreign key (id_unity_sales) references UNITY_CONVERSION(id)
);
commit;
grant all on size_modifier_item to untilluser;
commit;
execute procedure register_sync_table_ex('article_size_modifier', 'b', 1);
commit;
execute procedure register_bo_table('article_size_modifier', 'id_articles', 'articles');
commit;
execute procedure register_bo_table('article_size_modifier', 'ID_SIZE_MODIFIER_ITEM', 'SIZE_MODIFIER_ITEM');
commit;
execute procedure register_bo_table('article_size_modifier', 'id_unity_sales', 'UNITY_CONVERSION');
commit;



