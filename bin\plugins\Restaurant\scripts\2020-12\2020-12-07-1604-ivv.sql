create table ta_bill_statuses(
    id u_id,
    datetime 			timestamp,	 
    id_untill_users	        bigint,
    status			integer, 
    comments			varchar(255),	
    is_active             	smallint,
    IS_ACTIVE_MODIFIED         timestamp,
    IS_ACTIVE_MODIFIER         varchar(30),
    constraint tabs_pk primary key (id),
    constraint tabs_fk0 foreign key (id_untill_users) references untill_users(id));
commit;
grant all on ta_bill_statuses to untilluser;
commit;
execute procedure register_sync_table_ex('ta_bill_statuses', 'p', 1);
commit;

