create table table_area_ks (
    id u_id,
    ID_table_area bigint,
    ID_KS_From bigint,
    ID_KS_TO   bigint,
    ID_periods bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint taks_pk primary key (id),
	constraint taks_fk_ta foreign key (ID_table_area) references table_area(id),
	constraint taks_fk_ks1 foreign key (ID_KS_From) references kitchen_screens(id),
	constraint taks_fk_ks2 foreign key (ID_KS_To) references kitchen_screens(id)
);
commit;
grant all on table_area_ks to untilluser;
commit;
execute procedure register_sync_table_ex('table_area_ks', 'b', 1);
commit;
execute procedure register_bo_table('table_area_ks', '', '');
commit;

