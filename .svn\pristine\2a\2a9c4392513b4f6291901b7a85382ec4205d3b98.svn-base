inherited ScheduleDialog: TScheduleDialog
  Left = 369
  Top = 255
  Caption = 'ScheduleDialog'
  ClientHeight = 236
  ClientWidth = 378
  ExplicitWidth = 384
  ExplicitHeight = 261
  PixelsPerInch = 96
  TextHeight = 13
  inherited panClient: TTntPanel
    Width = 378
    Height = 191
    ExplicitWidth = 378
    ExplicitHeight = 191
    object UntillPanel1: TUntillPanel
      Left = 8
      Top = 8
      Width = 361
      Height = 185
      BevelOuter = bvNone
      TabOrder = 0
      BorderColor = clBlack
      object lblPeriodicity: TTntLabel
        Left = 16
        Top = 20
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblPeriodicity'
      end
      object lblEvery: TTntLabel
        Left = 16
        Top = 52
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblEvery'
      end
      object lblItem: TTntLabel
        Left = 272
        Top = 52
        Width = 73
        Height = 13
        AutoSize = False
        Caption = 'lblItem'
      end
      object lblTime: TTntLabel
        Left = 16
        Top = 84
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblTime'
      end
      object lblDayOfWeek: TTntLabel
        Left = 16
        Top = 116
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblDayOfWeek'
      end
      object lblAfterComplete: TTntLabel
        Left = 16
        Top = 148
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblAfterComplete'
      end
      object lblDayOfMonth: TTntLabel
        Left = 16
        Top = 116
        Width = 129
        Height = 13
        AutoSize = False
        Caption = 'lblDayOfMonth'
      end
      object dtpDate: TUntillDateTimePicker
        Left = 152
        Top = 48
        Width = 105
        Height = 21
        Date = 38501.472452546300000000
        Time = 38501.472452546300000000
        TabOrder = 6
        DateTime = 38501.472452546300000000
      end
      object cmbPeriodicity: TUntillComboBox
        Left = 152
        Top = 16
        Width = 193
        Height = 21
        Style = csDropDownList
        TabOrder = 0
        OnChange = cmbPeriodicityChange
        ColorActive = -301989881
      end
      object useEvery: TUntillSpinEdit
        Left = 152
        Top = 48
        Width = 105
        Height = 20
        ParentBackground = False
        TabStop = True
        TabOrder = 1
        Alignment = taLeftJustify
        Value = 1
        MaxValue = 999
        MinValue = 1
        MaxLength = 0
      end
      object dtpTime: TUntillDateTimePicker
        Left = 152
        Top = 80
        Width = 105
        Height = 21
        Date = 38501.472452546300000000
        Time = 38501.472452546300000000
        Kind = dtkTime
        TabOrder = 2
        DateTime = 38501.472452546300000000
      end
      object cmbDayOfWeek: TUntillComboBox
        Left = 152
        Top = 112
        Width = 193
        Height = 21
        Style = csDropDownList
        TabOrder = 3
        ColorActive = -301989881
      end
      object cmbAfterComplete: TUntillComboBox
        Left = 152
        Top = 144
        Width = 193
        Height = 21
        Style = csDropDownList
        TabOrder = 5
        ColorActive = -301989881
      end
      object useDayOfMonth: TUntillSpinEdit
        Left = 152
        Top = 113
        Width = 105
        Height = 20
        ParentBackground = False
        TabStop = True
        TabOrder = 4
        Alignment = taLeftJustify
        Value = 1
        MaxValue = 31
        MinValue = 1
        MaxLength = 0
      end
    end
  end
  inherited panButtons2: TTntPanel
    Top = 191
    Width = 378
    ExplicitTop = 191
    ExplicitWidth = 378
    inherited panUDFBottomRight: TTntPanel
      Left = 370
      ExplicitLeft = 370
    end
    inherited panUDFBottomLeft: TTntPanel
      Width = 370
      ExplicitWidth = 370
      inherited panButtons: TTntPanel
        Left = 216
        ExplicitLeft = 216
        inherited btnOk: TUntillButton
          ModalResult = 1
        end
        inherited btnCancel: TUntillButton
          ModalResult = 2
        end
      end
    end
  end
end
