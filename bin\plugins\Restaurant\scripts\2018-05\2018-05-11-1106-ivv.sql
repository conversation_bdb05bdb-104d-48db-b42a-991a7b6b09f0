create table rent_bill_prolong (
	id 						u_id,
	id_rent_bill_original 	bigint,	
	id_rent_bill		 	bigint,	
    constraint rntbp_pk 	primary key (id),
	constraint rntbp_fk1 	foreign key (id_rent_bill_original) references rent_bills(id),
	constraint rntbp_fk2 	foreign key (id_rent_bill) references rent_bills(id)
);
commit;
grant all on rent_bill_prolong to untilluser;
commit;
execute procedure register_sync_table_ex('rent_bill_prolong', 'p', 1);
commit;



