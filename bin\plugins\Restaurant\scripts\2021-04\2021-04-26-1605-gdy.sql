-- reduce pbill_cleancash_info.clean_cash_signature size from 100 to 80 to be able to create an index on it
alter trigger pbill_cleancash_info_lt inactive;
alter table pbill_cleancash_info add cleancashsignature_temp varchar(80) character set unicode_fss collate unicode_fss;
update pbill_cleancash_info set cleancashsignature_temp = clean_cash_signature;
alter table pbill_cleancash_info drop clean_cash_signature;
alter table pbill_cleancash_info alter cleancashsignature_temp to clean_cash_signature;
alter trigger pbill_cleancash_info_lt active;
commit;
CREATE INDEX PBILL_CLEANCASH_INFO_IDX1 ON PBILL_CLEANCASH_INFO (CLEAN_CASH_SIGNATURE);
commit;