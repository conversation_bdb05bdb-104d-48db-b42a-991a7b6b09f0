inherited ButtonPropFrame: TButtonPropFrame
  Width = 451
  Height = 304
  Align = alClient
  OnResize = FrameResize
  ExplicitWidth = 451
  ExplicitHeight = 304
  object PanelMain: TUntillPanel
    Left = 0
    Top = 0
    Width = 451
    Height = 285
    Align = alClient
    BevelInner = bvRaised
    BevelOuter = bvNone
    TabOrder = 0
    BorderColor = clBlack
    object lblScript2: TTntLabel
      Left = 16
      Top = 14
      Width = 114
      Height = 17
      AutoSize = False
      Caption = 'Action script:'
      Transparent = True
    end
    object lblSelectedColor: TTntLabel
      Left = 16
      Top = 87
      Width = 89
      Height = 15
      AutoSize = False
      Caption = 'Selected color:'
      Transparent = True
      WordWrap = True
    end
    object lblSelector: TTntLabel
      Left = 234
      Top = 87
      Width = 65
      Height = 15
      AutoSize = False
      Caption = 'Select type:'
      Transparent = True
      WordWrap = True
    end
    object cbSelectedColor: TTntColorComboBox
      Left = 124
      Top = 84
      Width = 93
      Height = 21
      Selected = clYellow
      Style = [cbStandardColors, cbExtendedColors, cbSystemColors, cbIncludeNone, cbIncludeDefault, cbCustomColor]
      ItemHeight = 15
      TabOrder = 6
      ColorValue = clYellow
    end
    object btnLoad2: TUntillButton
      Left = 359
      Top = 8
      Width = 87
      Height = 25
      Caption = 'Load...'
      TabOrder = 0
      OnClick = btnLoad2Click
      ColorActive = clBlack
      Alignment = taLeftJustify
      Layout = tlCenter
      BitmapPos = bpDefault
    end
    object chkFrame: TUntillCheckBox
      Left = 16
      Top = 38
      Width = 122
      Height = 17
      Caption = 'Show frame'
      TabOrder = 2
      OnClick = chkFrameClick
    end
    object chkCheck: TUntillCheckBox
      Left = 140
      Top = 38
      Width = 109
      Height = 19
      Caption = 'Checkable'
      TabOrder = 3
      OnClick = chkFrameClick
    end
    object cmbDB: TUntillCheckBox
      Left = 16
      Top = 61
      Width = 177
      Height = 17
      Caption = 'Load content from database'
      TabOrder = 5
      OnClick = cmbDBClick
    end
    object btnmandateList: TUntillButton
      Left = 359
      Top = 36
      Width = 87
      Height = 25
      Caption = 'Security...'
      TabOrder = 1
      OnClick = btnmandateListClick
      ColorActive = clBlack
      Alignment = taLeftJustify
      Layout = tlCenter
      BitmapPos = bpDefault
    end
    object chkCaption: TUntillCheckBox
      Left = 236
      Top = 38
      Width = 122
      Height = 17
      Caption = 'Show caption'
      TabOrder = 4
    end
    object chkAutoDisable: TUntillCheckBox
      Left = 16
      Top = 107
      Width = 114
      Height = 17
      Caption = 'Auto disable '
      TabOrder = 7
      OnClick = chkAutoDisableClick
    end
    object PageControlAdv: TUntillPageControl
      Left = 16
      Top = 130
      Width = 435
      Height = 302
      ActivePage = TabSheetContentFromDB
      TabIndex = 1
      TabOrder = 8
      object TabSheetExceptions: TTabSheet
        BorderWidth = 10
        Caption = 'Action exception periods'
        object chkPeriods: TCheckListBox
          Left = 0
          Top = 0
          Width = 407
          Height = 254
          Align = alClient
          ItemHeight = 13
          TabOrder = 0
          OnClick = chkPeriodsClick
        end
      end
      object TabSheetContentFromDB: TTabSheet
        Caption = 'Content from database'
        ImageIndex = 1
        object lbldataset: TTntLabel
          Left = 10
          Top = 61
          Width = 88
          Height = 17
          AutoSize = False
          Caption = 'Action script:'
          Transparent = True
        end
        object lblNumber: TTntLabel
          Left = 10
          Top = 88
          Width = 136
          Height = 17
          AutoSize = False
          Caption = 'Sequence number:'
          Transparent = True
        end
        object chkIgnore: TUntillCheckBox
          Left = 10
          Top = 10
          Width = 217
          Height = 19
          Caption = 'Ignore interface datasource settings'
          TabOrder = 0
          OnClick = chkIgnoreClick
        end
        object chkUnderlight: TUntillCheckBox
          Left = 10
          Top = 34
          Width = 121
          Height = 17
          Caption = 'Under light'
          TabOrder = 2
          OnClick = chkUnderlightClick
        end
        object btnParameters: TUntillButton
          Left = 320
          Top = 86
          Width = 87
          Height = 26
          Caption = 'Parameters...'
          TabOrder = 5
          OnClick = btnParametersClick
          ColorActive = clBlack
          Alignment = taLeftJustify
          Layout = tlCenter
          BitmapPos = bpDefault
        end
        object chkMultiButton: TUntillCheckBox
          Left = 10
          Top = 114
          Width = 177
          Height = 19
          Caption = 'Multiple item button'
          TabOrder = 6
          OnClick = chkMultiButtonClick
        end
        object cmbDataset: TUntillComboBox
          Left = 108
          Top = 59
          Width = 299
          Height = 21
          Style = csDropDownList
          ParentShowHint = False
          ShowHint = True
          TabOrder = 3
          OnChange = cmbDatasetChange
          OnClick = cmbDatasetClick
          ColorActive = -301989881
        end
        object edtNumber: TUntillSpinEdit
          Left = 108
          Top = 86
          Width = 50
          Height = 20
          ParentBackground = False
          TabStop = True
          TabOrder = 4
          Alignment = taCenter
          Value = 0
          MaxValue = 999999
          MinValue = 0
          MaxLength = 10
          OnChange = edtNumberChange
        end
        object pnlMulti: TTntPanel
          Left = -2
          Top = 139
          Width = 175
          Height = 110
          BevelOuter = bvNone
          TabOrder = 7
          object lblBtnSpace: TTntLabel
            Left = 12
            Top = 60
            Width = 119
            Height = 17
            AutoSize = False
            Caption = 'Button item space:'
            Transparent = True
          end
          object lblBtnHeight: TTntLabel
            Left = 12
            Top = 35
            Width = 119
            Height = 17
            AutoSize = False
            Caption = 'Button item height:'
            Transparent = True
          end
          object lblBtnWidth: TTntLabel
            Left = 12
            Top = 10
            Width = 119
            Height = 17
            AutoSize = False
            Caption = 'Button item width:'
            Transparent = True
          end
          object edtBtnSpace: TUntillSpinEdit
            Left = 110
            Top = 58
            Width = 50
            Height = 20
            ParentBackground = False
            TabStop = True
            TabOrder = 2
            Alignment = taCenter
            Value = 0
            MaxValue = 999999
            MinValue = 0
            MaxLength = 10
            OnChange = edtNumberChange
          end
          object edtBtnWidth: TUntillSpinEdit
            Left = 110
            Top = 8
            Width = 50
            Height = 20
            ParentBackground = False
            TabStop = True
            TabOrder = 0
            Alignment = taCenter
            Value = 0
            MaxValue = 999999
            MinValue = 0
            MaxLength = 10
            OnChange = edtNumberChange
          end
          object edtBtnHeight: TUntillSpinEdit
            Left = 110
            Top = 33
            Width = 50
            Height = 20
            ParentBackground = False
            TabStop = True
            TabOrder = 1
            Alignment = taCenter
            Value = 0
            MaxValue = 999999
            MinValue = 0
            MaxLength = 10
            OnChange = edtNumberChange
          end
          object btnHideUnusedButtons: TUntillCheckBox
            Left = 12
            Top = 81
            Width = 218
            Height = 23
            Caption = 'Hide empty buttons'
            TabOrder = 3
            OnClick = chkUnderlightClick
          end
        end
        object cmbneon: TUntillComboBox
          Left = 108
          Top = 32
          Width = 189
          Height = 21
          Style = csDropDownList
          TabOrder = 1
          Items.Strings = (
            'Above button'
            'Under button'
            'Top'
            'Bottom'
            'Small')
          ColorActive = -301989881
        end
      end
      object TabSheetAutoDisable: TTabSheet
        BorderWidth = 4
        Caption = 'Auto disable'
        ImageIndex = 2
        object pnlDisableCl: TUntillPanel
          Left = 150
          Top = 0
          Width = 269
          Height = 266
          Align = alClient
          BevelOuter = bvNone
          TabOrder = 1
          BorderColor = clBlack
          object pnlLayersCaption: TUntillPanel
            Left = 0
            Top = 0
            Width = 269
            Height = 21
            Align = alTop
            BevelOuter = bvNone
            TabOrder = 0
            BorderColor = clBlack
            object TntLabel1: TTntLabel
              Left = 39
              Top = 4
              Width = 19
              Height = 15
              AutoSize = False
              Caption = 'On'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              Transparent = True
            end
            object TntLabel2: TTntLabel
              Left = 58
              Top = 4
              Width = 19
              Height = 15
              AutoSize = False
              Caption = 'Off'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              Transparent = True
            end
            object TntLabel3: TTntLabel
              Left = 83
              Top = 4
              Width = 221
              Height = 17
              AutoSize = False
              Caption = 'Name'
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -11
              Font.Name = 'Tahoma'
              Font.Style = [fsBold]
              ParentFont = False
              Transparent = True
            end
            object UntillPanel3: TUntillPanel
              Left = 45
              Top = 3
              Width = 1
              Height = 18
              BevelOuter = bvNone
              TabOrder = 0
              BorderColor = clBlack
            end
            object UntillPanel4: TUntillPanel
              Left = 68
              Top = 3
              Width = 1
              Height = 18
              BevelOuter = bvNone
              TabOrder = 1
              BorderColor = clBlack
            end
          end
          object tvLayers: TTntTreeView
            Left = 0
            Top = 21
            Width = 269
            Height = 245
            Align = alClient
            Ctl3D = True
            Images = imgStates
            Indent = 35
            ParentCtl3D = False
            ReadOnly = True
            TabOrder = 1
            OnMouseDown = tvLayersMouseDown
          end
        end
        object pnlAutoDisableLeft: TUntillPanel
          Left = 0
          Top = 0
          Width = 150
          Height = 266
          Align = alLeft
          BevelOuter = bvNone
          TabOrder = 0
          BorderColor = clBlack
          object GridFBLayers: TDrawGrid
            Left = 0
            Top = 21
            Width = 146
            Height = 245
            Align = alClient
            ColCount = 1
            DefaultColWidth = 120
            DoubleBuffered = True
            FixedCols = 0
            RowCount = 15
            FixedRows = 0
            Options = [goVertLine, goHorzLine]
            ParentDoubleBuffered = False
            ScrollBars = ssVertical
            TabOrder = 0
            OnDblClick = GridFBLayersDblClick
            OnDrawCell = GridFBLayersDrawCell
            OnMouseUp = GridFBLayersMouseUp
            OnSelectCell = GridFBLayersSelectCell
          end
          object UntillPanel1: TUntillPanel
            Left = 146
            Top = 21
            Width = 4
            Height = 245
            Align = alRight
            BevelOuter = bvNone
            TabOrder = 1
            BorderColor = clBlack
          end
          object pnlCheck: TPanel
            Left = 0
            Top = 0
            Width = 150
            Height = 21
            Align = alTop
            BevelOuter = bvNone
            TabOrder = 2
            object btnUncheck: TButton
              AlignWithMargins = True
              Left = 36
              Top = 0
              Width = 32
              Height = 19
              Margins.Left = 4
              Margins.Top = 0
              Margins.Right = 0
              Margins.Bottom = 2
              Align = alLeft
              Caption = 'Off'
              TabOrder = 0
              TabStop = False
              OnClick = btnUncheckClick
            end
            object btnCheck: TButton
              AlignWithMargins = True
              Left = 0
              Top = 0
              Width = 32
              Height = 19
              Margins.Left = 0
              Margins.Top = 0
              Margins.Right = 0
              Margins.Bottom = 2
              Align = alLeft
              Caption = 'On'
              TabOrder = 1
              TabStop = False
              OnClick = btnCheckClick
            end
            object btnAccess: TButton
              AlignWithMargins = True
              Left = 72
              Top = 0
              Width = 71
              Height = 19
              Margins.Left = 4
              Margins.Top = 0
              Margins.Right = 0
              Margins.Bottom = 2
              Align = alLeft
              Caption = 'Security'
              TabOrder = 2
              TabStop = False
              OnClick = btnAccessClick
            end
          end
        end
      end
    end
    object cmbSelector: TUntillComboBox
      Left = 305
      Top = 84
      Width = 141
      Height = 21
      Style = csDropDownList
      TabOrder = 9
      OnChange = cmbSelectorChange
      ColorActive = clBlack
    end
  end
  object TntStatusBar1: TTntStatusBar
    Left = 0
    Top = 285
    Width = 451
    Height = 19
    Panels = <>
    SimplePanel = True
    Visible = False
  end
  object imgStates: TImageList
    Height = 12
    Width = 32
    Left = 328
    Top = 176
    Bitmap = {
      494C010103000500100020000C00FFFFFFFFFF10FFFFFFFFFFFFFFFF424D3600
      0000000000003600000028000000800000000C00000001002000000000000018
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      0000000000000000000000000000000000000000000000000000000000000000
      000000000000000000000000000000000000424D3E000000000000003E000000
      28000000800000000C0000000100010000000000C00000000000000000000000
      000000000000000000000000FFFFFF00FFFFFFFFFFFFFFFFFFFFFFFF00000000
      FFFFFFFFFFFFFFFFFFFFFFFF00000000E01FE01FE01FE01FE01FE01F00000000
      EFDFEFDFEFDFEFDFEFDFEFDF00000000EFDFEFDFE85FEFDFEFDFE85F00000000
      EFDFEFDFE85FEFDFEFDFE85F00000000EFDFEFDFE85FEFDFEFDFE85F00000000
      EFDFEFDFE85FEFDFEFDFE85F00000000EFDFEFDFEFDFEFDFEFDFEFDF00000000
      E01FE01FE01FE01FE01FE01F00000000FFFFFFFFFFFFFFFFFFFFFFFF00000000
      FFFFFFFFFFFFFFFFFFFFFFFF0000000000000000000000000000000000000000
      000000000000}
  end
end
