SET TERM ^ ;

create or alter procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text
  from menu_item where menu_item.id >= :id_menu_item order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || kind;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || id_articles;
    end
    idx = idx + 1;
  end
  suspend;
end
^

SET TERM ; ^

grant execute on procedure GETMENUITEMOSIG to untilluser;

COMMIT;

SET TERM ^ ;

create or alter procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable MENU_ITEM_OSIG varchar(1024);
declare variable ID_MENU_ITEM bigint;
declare variable ID_MENU bigint;
begin
  execute procedure GETORDERITEMOSIG(:id_order_item) returning_values(:osig);

  select order_item.ID_MENU from order_item where order_item.id = :id_order_item into :id_menu;

  for select menu_item.ID from menu_item where menu_item.ID_MENU = :id_menu
    and menu_item.rowbeg = 1 INTO :id_menu_item do begin
    EXECUTE procedure GETMENUITEMOSIG(:id_menu_item) returning_values(:menu_item_osig);
    osig = osig || '/' || menu_item_osig;
  end
  suspend;
end
^

SET TERM ; ^

grant execute on procedure GETORDERITEMOSIG_AKS to untilluser;

COMMIT;

CREATE OR ALTER VIEW ORDERED_OSIG_AKS(
    ID_ORDER_ITEM,
    OSIG)
AS
select order_item.ID, (select osig from GETORDERITEMOSIG_AKS(order_item.ID))
from order_item;

COMMIT;

grant all on ORDERED_OSIG_AKS to untilluser;

COMMIT;

COMMENT ON COLUMN AKS_ITEM_ENTRIES.IS_COMMITTED IS
'NOT USED';
ALTER TABLE AKS_ITEM_ENTRIES ALTER COLUMN IS_COMMITTED
SET DEFAULT 1;
COMMENT ON COLUMN AKS_ITEM_ENTRIES.ID_BILL IS
'NOT USED';

COMMIT;