create table customer_emails (
    id u_id,
    email  varchar(50),
    description varchar(100),
    id_clients bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint customer_emails_pk primary key (id),
	constraint customer_emails_fk1 foreign key (id_clients) references clients(id)
);
commit;
grant all on customer_emails to untilluser;
commit;
execute procedure register_sync_table_ex('customer_emails', 'b', 1);
commit;
execute procedure register_bo_table('customer_emails', 'id_clients', 'clients');
commit;
