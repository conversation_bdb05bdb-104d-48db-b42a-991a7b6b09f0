create table ug_report_groups (
    id u_id,
    id_ug  bigint,
    id_report_groups bigint,
    ugrg_number integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint ugrpg_pk primary key (id),
    constraint ugrpg_fk1 foreign key (id_ug) references UNTILL_GROUP_USERS(id),
    constraint ugrpg_fk2 foreign key (id_report_groups) references report_groups(id)
);
commit;
grant all on ug_report_groups to untilluser;
commit;
execute procedure register_sync_table_ex('ug_report_groups', 'b', 1);
commit;
execute procedure register_bo_table('ug_report_groups', 'id_ug', 'UNTILL_GROUP_USERS');
commit;
execute procedure register_bo_table('ug_report_groups', 'id_report_groups', 'report_groups');
commit;



