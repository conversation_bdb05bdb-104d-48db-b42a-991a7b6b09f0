create table ks_workflow (
    id u_id,
    id_bill     bigint,
    id_courses  bigint,
    constraint kswf_pk primary key (id),
	constraint kswf_fk_b foreign key (id_bill) references bill(id),
	constraint kswf_fk_c foreign key (id_courses) references courses(id)
	);
commit;
grant all on ks_workflow to untilluser;
commit;
execute procedure register_sync_table_ex('ks_workflow', 'p', 1);
commit;

create table ks_wf_steps (
    id u_id,
    id_ks_workflow bigint,
    course_time timestamp,
    id_untill_users bigint,
    ks_status integer,
    constraint kswfs_pk primary key (id),
	constraint kswfs_fk_uu foreign key (id_untill_users) references untill_users(id),
	constraint kswfs_fk_wfs foreign key (id_ks_workflow) references ks_workflow(id)
	);
commit;
grant all on ks_wf_steps to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_steps', 'p', 1);
commit;

create table ks_workflow_items (
    id u_id,
    id_articles bigint,
    id_ks_workflow  bigint,
    quantity integer,
    countdown_time timestamp,
    text varchar(1024),
    item_complete smallint,
    break_time timestamp,
    constraint kswfi_pk primary key (id),
	constraint kswfi_fk_a foreign key (id_articles) references articles(id),
	constraint kswfi_fk_wfs foreign key (id_ks_workflow) references ks_workflow(id)
);
commit;
grant all on ks_workflow_items to untilluser;
commit;
execute procedure register_sync_table_ex('ks_workflow_items', 'p', 1);
commit;

