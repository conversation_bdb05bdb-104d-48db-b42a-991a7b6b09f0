create table client_smartcard_uids(
    id u_id,
    id_clients bigint,
    smartcard_uid varchar(50),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint client_smartcard_uids_pk primary key (id),
    constraint client_smartcard_uids_fk0 foreign key (id_clients) references clients(id)
);
commit;
grant all on client_smartcard_uids to untilluser;
commit;
execute procedure register_sync_table_ex('client_smartcard_uids', 'b', 1);
commit;
execute procedure register_bo_table('client_smartcard_uids', 'id_clients', 'clients');
commit;

CREATE INDEX client_smartcard_uids_idx1 ON client_smartcard_uids (smartcard_uid);
commit;

insert into client_smartcard_uids (id_clients, smartcard_uid, is_active)
select id, smartcard_uid, 1 from clients
where (not (smartcard_uid is null)) and (char_length(smartcard_uid)>0);
commit;

