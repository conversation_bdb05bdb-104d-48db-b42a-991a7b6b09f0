create table BO_SERVICE_CHARGE (
    id u_id,
    sc_number integer,
    sc_name   varchar(50),
    sc_type   integer,	
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint bosc_pk primary key (id)
    );
commit;
grant all on BO_SERVICE_CHARGE to untilluser;
commit;
execute procedure register_sync_table_ex('BO_SERVICE_CHARGE', 'b', 1);
commit;
execute procedure register_bo_table('BO_SERVICE_CHARGE', '', '');
commit;

create table BO_SC_ITEMS (
    id u_id,
    sci_number    integer,
    amount        decimal(17,4),
    id_bsc        bigint,
    id_category   bigint,
    id_department bigint,
    id_food_group  bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint bosci_pk primary key (id),
    constraint bosci_fk0 foreign key (id_bsc) references BO_SERVICE_CHARGE(id),
    constraint bosci_fk1 foreign key (id_category) references category(id),
    constraint bosci_fk2 foreign key (id_department) references department(id),
    constraint bosci_fk3 foreign key (id_food_group) references food_group(id)
    );
commit;
grant all on BO_SC_ITEMS to untilluser;
commit;
execute procedure register_sync_table_ex('BO_SC_ITEMS', 'b', 1);
commit;
execute procedure register_bo_table('BO_SC_ITEMS', 'id_bsc', 'BO_SERVICE_CHARGE');
commit;






