create table discount_group_articles (
    id u_id,
    id_discount_groups bigint,
    id_articles bigint,
    discount_value decimal(17,4), --default discount in percent, can be different at each client
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint dgra_pk primary key (id),
    constraint dgra_fk1 foreign key (id_discount_groups) references discount_groups(id),
    constraint dgra_fk2 foreign key (id_articles) references articles(id)
);
commit;
grant all on discount_group_articles to untilluser;
commit;
execute procedure register_sync_table_ex('discount_group_articles', 'b', 1);
commit;
execute procedure register_bo_table('discount_group_articles', 'id_discount_groups', 'discount_groups');
commit;

create table client_discount_groups (
    id u_id,
    id_discount_groups bigint,	
    id_clients bigint,
    id_accounts bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint cdgr_pk primary key (id),
    constraint cdgr_fk1 foreign key (id_discount_groups) references discount_groups(id),
    constraint cdgr_fk2 foreign key (id_clients) references clients(id),
    constraint cdgr_fk3 foreign key (id_accounts) references accounts(id)
);
commit;
grant all on client_discount_groups to untilluser;
commit;
execute procedure register_sync_table_ex('client_discount_groups', 'b', 1);
commit;
execute procedure register_bo_table('client_discount_groups', 'id_clients', 'clients');
commit;

create table client_discount_groups_items (
    id u_id,
    id_client_discount_groups bigint,
    id_articles bigint,
    discount_value decimal(17,4), --default discount in percent, can be different at each client
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint cdgri_pk primary key (id),
    constraint cdgri_fk1 foreign key (id_client_discount_groups) references client_discount_groups(id),
    constraint cdgri_fk2 foreign key (id_articles) references articles(id)
);
commit;
grant all on client_discount_groups_items to untilluser;
commit;
execute procedure register_sync_table_ex('client_discount_groups_items', 'b', 1);
commit;
execute procedure register_bo_table('client_discount_groups_items', 'id_client_discount_groups', 'client_discount_groups');
commit;

