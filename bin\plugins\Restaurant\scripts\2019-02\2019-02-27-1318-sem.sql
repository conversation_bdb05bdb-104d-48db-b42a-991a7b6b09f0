create table func_payment_buttons (
    id u_id,
    button_code varchar(40),
    button_text varchar(40),
    action_data varchar(80),
    ml_name BLOB SUB_TYPE 0 SEGMENT SIZE 80,
    num smallint,
    is_active smallint,
    is_active_modified timestamp,
    is_active_modifier varchar(30),
    constraint func_payment_buttons_pk primary key (id)
);

commit;
grant all on func_payment_buttons to untilluser;
commit;
execute procedure register_sync_table_ex('func_payment_buttons', 'b', 1);
commit;
execute procedure register_bo_table('func_payment_buttons', '', '');
commit;
