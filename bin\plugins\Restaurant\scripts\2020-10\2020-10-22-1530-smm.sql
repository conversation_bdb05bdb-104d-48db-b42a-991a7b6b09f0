set term !! ;
create or alter procedure fix_food_group_guids
as 
  declare variable server_no integer;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;
  update food_group set guid=uuid_to_char(gen_uuid());
  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_food_group_guids to untilluser
!!
commit
!!
execute procedure fix_food_group_guids
!!
commit
!!
set term ; !!
