unit SelectPrinterFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ActionParamsFram, StdCtrls, TntCompatibilityU,
  ExtCtrls, UntillPanelU, DialogsU, UntillComboBoxU, IBSQL,
  UntillDBU, UntillCheckBoxU,  UntillScreenU, UntillGraphicElementsU, UntillSelectBoxU,
  PrinterEntityManager, CommonU, BOUntillDBU, UTF8U;

type
  TSelectPrinterFrame = class(TActionParamsFrame)
    UntillPanel1: TUntillPanel;
    usbPrinter: TUntillSelectBox;
    TntLabel1: TTntLabel;
    procedure usbPrinterButtonClick(Sender: TObject);
  private
    { Private declarations }
  public
    emPrinter : TPrinterEntityManager;
    procedure GetParameters(var Parameters: array of Variant); override;
    procedure SetParameters(Parameters: array of Variant); override;
    procedure TranslateStrings; override;

    constructor Create(AOwner: TComponent; AActionObject:TObject; ANameManager:TObject; AUntillDB:TBOCustomUntillDB); override;
    destructor Destroy; override;
    { Public declarations }
  end;

var
  SelectPrinterFrame: TSelectPrinterFrame;

implementation
uses NameManagerU, InterpretatorU, PluginU, DataControlsU;
{$R *.dfm}

{ TTestActionFrame }

constructor TSelectPrinterFrame.Create(AOwner: TComponent; AActionObject:TObject;
  ANameManager: TObject; AUntillDB: TBOCustomUntillDB);
begin
  inherited;
  emPrinter := TPrinterEntityManager.Create(Self,UntillDB);
end;

procedure TSelectPrinterFrame.GetParameters(var Parameters: array of Variant);
begin
  inherited;
  if usbPrinter.Value <> 0 then
    Parameters[0] := usbPrinter.Text
  else
    Parameters[0] := '';
end;

procedure TSelectPrinterFrame.SetParameters(Parameters: array of Variant);
var q : IIBSQL;
begin
  inherited;
  if Length(Parameters) < 1 then exit;

  q := UntillDB.GetIIbSql;
  q.SetText('select ID from printers where is_active=1 and name = :name');
  q.q.ParamByName('name').asString := UTF8_Encode({Trim(}Parameters[0]{)});
  q.ExecQuery;
  if not q.Eof then FillUsb(usbPrinter, StrToInt64Def(q.q.fields[0].asstring,0), emPrinter, 'name');
  q.Close;

end;

procedure TSelectPrinterFrame.TranslateStrings;
begin
  inherited;
  TntLabel1.Caption  := Plugin.TranslateLabel('SelectPrinterFram','Printer');
end;

procedure TSelectPrinterFrame.usbPrinterButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emPrinter.ShowModal(usbPrinter.Value);
  if res>0 then with usbPrinter do begin
    Value:=res;
    Text:=emPrinter.GetWideStringById('name',res);
  end;
  usbPrinter.SetFocus;
end;

destructor TSelectPrinterFrame.Destroy;
begin
  FreeAndNil(emPrinter);
  inherited;
end;

end.
