create table screen_images (
    id u_id,
    name  varchar(50),
    description varchar(100),
    screen_type integer,
    image blob,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint screen_images_pk primary key (id)
);
commit;
grant all on screen_images to untilluser;
commit;
execute procedure register_sync_table_ex('screen_images', 'b', 1);
commit;
execute procedure register_bo_table('screen_images', '', '');
commit;
