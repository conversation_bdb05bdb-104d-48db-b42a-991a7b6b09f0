unit ToolListener4U;

interface

uses Listener<PERSON>er<PERSON><PERSON>, UntillDBU;

type

TToolListener4 = class (TListenerInterface)
private
  FUntillDB : TUntillDB;
  function  OpenUntillDB(DatabaseGUID: String; User, Password: String; var errmsg: String):boolean;
  procedure DisconnectDB;
//  procedure CheckLicense;
protected
  procedure ProcessGetArtList;
  procedure ProcessGetBillList;
  procedure ProcessCheckHosts;
  procedure ProcessAddTask;
  procedure ProcessAddClearTask;
  procedure ProcessGetTaskList;
  procedure ProcessGetRecpList;
  procedure ProcessDeleteTask;
  procedure ProcessRestartRecp;
  procedure ProcessGetTimeSettings;
public
  class function GetName: string; override;
  procedure Execute; override;
end;


implementation
uses SysUtils, math, UntillLogsU, Windows, DBClient, Classes, IBQuery,
  CommonU, DateUtils, DB, TaskUtilsU, Forms, IB, RegClassesU,
  BLOrderLogU, UntillAdminClientU;

{ TToolListener4 }

{procedure TToolListener4.CheckLicense;
begin
  RegClassesU.CheckRegFunction(FUntillDB, FUNC_TOOL);
end;}

procedure TToolListener4.DisconnectDB;
begin
  if FUntillDB.IsConnected then FUntillDB.Disconnect;
end;

procedure TToolListener4.Execute;
var
  s: string;

begin
  FUntillDB := TUntillDb.Create(nil);
  try
    while (true) do begin
      s := ReadLn();
      if (s = 'quit') then begin
        Exit;
      end;
      if (s = 'getartlist') then begin
        ProcessGetArtList;
        Continue;
      end;
      if (s = 'checknetworkhosts') then begin
        ProcessCheckHosts;
        Continue;
      end;
      if (s = 'getbilllist') then begin
        ProcessGetBillList;
        Continue;
      end;
      if (s = 'addtask') then begin
        ProcessAddTask;
        Continue;
      end;
      if (s = 'addcleartask') then begin
        ProcessAddClearTask;
        Continue;
      end;
      if (s = 'gettasklist') then begin
        ProcessGetTaskList;
        Continue;
      end;
      if (s = 'getrecplist') then begin
        ProcessGetRecpList;
        Continue;
      end;
      if (s = 'deletetask') then begin
        ProcessDeleteTask;
        Continue;
      end;
      if (s = 'gettimesettings') then begin
        ProcessGetTimeSettings;
        Continue;
      end;
      if (s = 'restartrecp') then begin
        ProcessRestartRecp;
        Continue;
      end;
      WriteLn('???');
    end;

  finally
    FreeAndNil(FUntillDB);
  end;

end;

class function TToolListener4.GetName: string;
begin
  Result := 'Tool4';
end;


function TToolListener4.OpenUntillDB(DatabaseGUID: String; User, Password: String; var errmsg: String):boolean;
var
  sPath: string;
  sDBName: string;
  bFound: boolean;
  DB: TUntillDB;
  iSql: TIBSQLU;
  list : TStringList;
  rec : String;

  procedure CheckIsAdmin(UntillDB: TUntillDB; AUser, APassword: String);
  var
    iq: IIBSQL;
    mandates: TStringList;
    ms: TMemoryStream;

  begin
    Result := false;
    iq := UntillDB.GetIIbSql;
    iq.SetText('select mandates from untill_users where name='''+AUser+''' and user_code='''+Password+'''');
    iq.ExecQuery;
    if not (iq.Eof) then begin
      mandates := TStringList.Create;
      try
        ms := TMemoryStream.Create;
        try
          iq.FieldByName('mandates').SaveToStream(ms);
          ms.Position := 0;
          mandates.LoadFromStream(ms);
          if (mandates.IndexOf(MD_ADMIN_ID) < 0) then begin
            raise Exception.Create('User should be administrator');
          end;

        finally
          ms.Free;
        end;

      finally
        mandates.Free;
      end;

    end else begin
      raise Exception.Create('Wrong user name / password');
    end;

  end;

begin
  Result := false;
  errmsg := '';

  sPath := ExtractFilePath(Application.ExeName) + 'DB';
  list := TStringList.Create;
  ListDBFiles(list);
  bFound := false;
  sDBName := EmptyStr;
  try
    for rec in list do begin
      sDBName := sPath + '\' + rec;
      DB := TUntillDB.Create(nil);
      iSql := TIBSQLU.Create(nil);
      try
        try
          DB.ConnectAsUntillUser(rec, false, tdbOnline, CommonU.GetUntillSrvPort);
          iSql.SQL.Text := 'select guid, name from sync_db where (guid=''' + DatabaseGUID + ''') and (ser=(select ser_sync_db from untill_db))';
          DB.Open(iSql);
          if (not iSql.Eof) then begin
            bFound := true;
            Break;
          end;

        except
          on E: Exception do begin
            if E is EIBInterbaseError then begin
              case EIBInterbaseError(E).IBErrorCode of
                335544472: errmsg := 'Invalid user name / password';
                else errmsg := E.Message;
              end;//of case

            end else begin
              errmsg := E.Message;
            end;

          end;

        end;

      finally
        db.TestConnected; // gmp - to avoid hanging
        FreeAndNil(iSql);
        FreeUndNil(DB);
      end;

    end;

  finally
    FreeAndNil(list);
  end;

  if (bFound) then begin
    FUntillDB := TUntillDB.Create(nil);
    try
      FUntillDB.ConnectAsUntillUser(sDBName, true, tdbOnline, CommonU.GetUntillSrvPort);
      CheckIsAdmin(FUntillDB, User, Password);
      Result := true;
    except
      on E: Exception do begin
        errmsg := E.Message;
        Exit; // result:=false;
      end;
    end;
  end;
end;

procedure TToolListener4.ProcessAddClearTask;
var
  DBGUID : String;
  ms: TMemoryStream;
  cdsSize: Integer;
  tran: IWTran;
  q: TIBQueryU;
  TaskId: Int64;
  User, Password: String;
  ErrMsg: String;
begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;
  ms := TMemoryStream.Create;
  try
    cdsSize := ReadLongInteger;
    if (cdsSize > 0) then begin
      try
        ms.SetSize(cdsSize);
        ReadBuffer(ms.Memory^, cdsSize);
        ms.Position := 0;
        if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin

//          CheckLicense;

          TaskId := FUntillDB.GetID;
          tran := FUntillDB.getWTran;
          q := tran.GetTempQuery;
          q.SQL.Text := 'insert into db_tasks (id,name,params,datetime,srv_only) values (:id, :name, :params, :datetime, 1)';
          q.ParamByName('id').AsString := IntToStr(TaskId);
          q.ParamByName('name').AsString := 'restaurant.tclearsalestask';
          q.ParamByName('params').LoadFromStream(ms, ftBlob);
          q.ParamByName('datetime').AsDateTime:=GetSysNow;
          q.ExecSQL;
          q.Close;
          tran.commit;
          CreateTaskRecipients(FUntillDB, TaskId, rcpAllBO);
          WriteLn('ok');
          WriteBuffer(TaskId, Sizeof(Int64));
          DisconnectDB;
        end else begin
          ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
          WriteLn('Can''t open database: '+ErrMsg);
        end;

      except
        on E: Exception do begin
          WriteLn('Error: '+E.Message);
          ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetBillList');
        end;
      end;
    end;
  finally
    FreeAndNil(ms);
  end;
end;

procedure TToolListener4.ProcessAddTask;
var
  DBGUID : String;
  ms: TMemoryStream;
  cdsSize: Integer;
  tran: IWTran;
  q: TIBQueryU;
  TaskId: Int64;
  User, Password: String;
  ErrMsg: String;
begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;
  ms := TMemoryStream.Create;
  try
    cdsSize := ReadLongInteger;
    if (cdsSize > 0) then begin
      try
        ms.SetSize(cdsSize);
        ReadBuffer(ms.Memory^, cdsSize);
        ms.Position := 0;
        if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin

//          CheckLicense;

          TaskId := FUntillDB.GetID;
          tran := FUntillDB.getWTran;
          q := tran.GetTempQuery;
          q.SQL.Text := 'insert into db_tasks (id,name,params,datetime,srv_only) values (:id, :name, :params, :datetime, 1)';
          q.ParamByName('id').AsString := IntToStr(TaskId);
          q.ParamByName('name').AsString := 'restaurant.tool4';
          q.ParamByName('params').LoadFromStream(ms, ftBlob);
          q.ParamByName('datetime').AsDateTime:=GetSysNow;
          q.ExecSQL;
          q.Close;
          tran.commit;
          CreateTaskRecipients(FUntillDB, TaskId, rcpAllBO);
          WriteLn('ok');
          WriteBuffer(TaskId, Sizeof(Int64));
          DisconnectDB;
        end else begin
          ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
          WriteLn('Can''t open database: '+ErrMsg);
        end;

      except
        on E: Exception do begin
          WriteLn('Error: '+E.Message);
          ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetBillList');
        end;

      end;

    end;

  finally
    FreeAndNil(ms);
  end;
end;

procedure TToolListener4.ProcessDeleteTask;
var
  DBGUID: String;
  tran: IWTran;
  q: TIBQueryU;
  TaskId: Int64;
  User, Password: String;
  ErrMsg: String;

begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;
  TaskId := StrToInt64Def(ReadLn, 0);

  try
    if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
      tran := FUntillDB.getWTran;

      q := tran.GetTempQuery;
      q.SQL.Text := 'delete from db_tasks_recipients where id_db_tasks = :taskid';
      q.ParamByName('taskid').AsString := IntToStr(TaskId);
      q.ExecSQL;
      q.Close;

      q := tran.GetTempQuery;
      q.SQL.Text := 'delete from db_tasks where id = :taskid';
      q.ParamByName('taskid').AsString := IntToStr(TaskId);
      q.ExecSQL;
      q.Close;

      tran.commit;
      WriteLn('ok');
      DisconnectDB;
    end else begin
      ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
      WriteLn('Can''t open database: '+ErrMsg);

    end;

  except
    on E: Exception do begin
      WriteLn('Error: '+E.Message);
      ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessDeleteTask');
    end;

  end;

end;

procedure TToolListener4.ProcessGetArtList;
var
  DateFrom, DateTo: TDateTime;
  DBGUID: String;
  cds: TClientDataSet;
  ms: TMemoryStream;
  User, Password: String;
  ErrMsg: String;

begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;
  ReadBuffer(DateFrom, SizeOf(TDateTime));
  ReadBuffer(DateTo, SizeOf(TDateTime));

  if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
    try
      cds := TClientDataSet.Create(nil);
      try
        BLOrderLogU._GetArtList(FUntillDB, cds, DateFrom, DateTo);
        ms:=TMemoryStream.Create;
        try
          cds.SaveToStream(ms);
          WriteLn('ok');
          WriteInteger(ms.Size);
          WriteBuffer(ms.Memory^, ms.Size);
        finally
          FreeAndNil(ms);
        end;
      finally
        FreeAndNil(cds);
      end;
      DisconnectDB;
    except
      on E: Exception do begin
        WriteLn('Error: '+E.Message);
        ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetArtList');
      end;
    end;
  end else begin
    ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
    WriteLn('Can''t open database: '+ErrMsg);
  end;
end;

procedure TToolListener4.ProcessGetBillList;
var
    DateFrom, DateTo  : TDateTime;
//    Query             : IIBQuery;
    DBGUID            : String;
    cds               : TClientDataSet;
    ms                : TMemoryStream;
    User, Password    : String;
    ErrMsg            : String;
//    qq                : IIBSQL;
//    ArtID             : String;
//    NewQ              : Integer;
//    RefQ              : Integer;
//    NewSum              : Currency;
//    RefSum              : Currency;
begin
  ErrMsg:='';
  DBGUID:=ReadLn;
  User:=ReadLn;
  Password:=ReadLn;
  ReadBuffer(DateFrom, SizeOf(TDateTime));
  ReadBuffer(DateTo, SizeOf(TDateTime));

  if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
    try
      cds:=TClientDataSet.Create(nil);
      try
        BLOrderLogU._GetBillList(FUntillDB, cds, DateFrom, DateTo);
        ms:=TMemoryStream.Create;
        try
          cds.SaveToStream(ms);
          WriteLn('ok');
          WriteInteger(ms.Size);
          WriteBuffer(ms.Memory^, ms.Size);
          DisconnectDB;
        finally
          FreeAndNil(ms);
        end;
      finally
        FreeAndNil(cds);
      end;
    except
      on E: Exception do begin
        WriteLn('Error: '+E.Message);
        ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetBillList');
      end;
    end;
  end else begin
    ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
    WriteLn('Can''t open database: '+ErrMsg);
  end;
end;

procedure TToolListener4.ProcessGetRecpList;
var
    Query             : IIBQuery;
    DBGUID            : String;
    cds               : TClientDataSet;
    ms                : TMemoryStream;
    User, Password    : String;
    TaskId            : Int64;
    ErrMsg            : String;
begin
  ErrMsg:='';
  DBGUID:=ReadLn;
  User:=ReadLn;
  Password:=ReadLn;
  TaskId:=StrToInt64Def(ReadLn, 0);

  if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
    try
      cds:=TClientDataSet.Create(nil);
      try
        Query:= FUntillDB.GetIIBQuery;
        cds.SetProvider( Query.q );
        Query.SetText( 'select recp_info, status, result, id, recipient from db_tasks_recipients where id_db_tasks=:taskid' );
        Query.q.ParamByName('taskid').AsString:=IntToStr(TaskId);
        Query.Open;
        cds.Open;
        ms:=TMemoryStream.Create;
        try
          cds.SaveToStream(ms);
          WriteLn('ok');
          WriteInteger(ms.Size);
          WriteBuffer(ms.Memory^, ms.Size);
          DisconnectDB;
        finally
          FreeAndNil(ms);
        end;
      finally
        FreeAndNil(cds);
      end;
    except
      on E: Exception do begin
        WriteLn('Error: '+E.Message);
        ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetRecpList');
      end;
    end;
  end else begin
    ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
    WriteLn('Can''t open database: '+ErrMsg);
  end;
end;

procedure TToolListener4.ProcessGetTaskList;
const  LF = #10;
var
    Query             : IIBQuery;
    DBGUID            : String;
    cds               : TClientDataSet;
    ms                : TMemoryStream;
    User, Password    : String;
    ErrMsg            : String;
    control_guid      : string;
begin
  ErrMsg:='';
  DBGUID:=ReadLn;
  User:=ReadLn;
  Password:=ReadLn;
  control_guid := con.IOHandler.ReadLn(LF,1000);

  if control_guid<>'{96D7CB56-9E89-4ec4-AFE9-620BFA1C03A8}' then begin
    con.IOHandler.WriteLn('Unknown Socket error');
    ExceptionLog.WriteString('Unknown Socket error');
  end;
  if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
    try
      cds:=TClientDataSet.Create(nil);
      try
        Query:= FUntillDB.GetIIBQuery;
        cds.SetProvider( Query.q );
        Query.SetText( 'select id, name, datetime from db_tasks where srv_only=1 order by datetime desc' );
        Query.Open;
        cds.Open;
        ms:=TMemoryStream.Create;
        try
          cds.SaveToStream(ms);
          WriteLn('ok');
          WriteInteger(ms.Size);
          WriteBuffer(ms.Memory^, ms.Size);
          DisconnectDB;
        finally
          FreeAndNil(ms);
        end;
      finally
        FreeAndNil(cds);
      end;
    except
      on E: Exception do begin
        WriteLn('Error: '+E.Message);
        ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetTaskList');
      end;
    end;
  end else begin
    ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
    WriteLn('Can''t open database: '+ErrMsg);
  end;
end;

procedure TToolListener4.ProcessGetTimeSettings;
var
  DBGUID: String;
  User, Password: String;
  ErrMsg: String;
  Query: IIBQuery;
  TimeFrom, TimeTo: TDateTime;
  DateTimeFrom, DateTimeTo: TDateTime;

begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;

  try
    if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
      {}
      TimeFrom := 0;
      TimeTo := 0;
      Query:= FUntillDB.GetIIBQuery;
      Query.SetText('select count(id)c, fromtime, totime from settings group by fromtime, totime');
      Query.Open;
      while (not Query.Eof) do begin
        if (Query.FieldByName('c').AsInteger <> 1) then begin
          raise Exception.Create('Settings table broken!');
        end;
        TimeFrom := Query.FieldByName('fromtime').AsDateTime;
        TimeTo := Query.FieldByName('totime').AsDateTime;
        Query.Next;
      end;
      GetFromTillDateTime(TimeFrom, TimeTo, Date, DateTimeFrom, DateTimeTo);
      DateTimeFrom := SysDateTimeToLocal(DateTimeFrom);
      DateTimeTo := SysDateTimeToLocal(DateTimeTo);
      WriteLn('ok');
      WriteBuffer(DateTimeFrom, SizeOf(TDateTime));
      WriteBuffer(DateTimeTo, SizeOf(TDateTime));
      {}
      DisconnectDB;
    end else begin
      ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
      WriteLn('Can''t open database: '+ErrMsg);
    end;
  except
    on E: Exception do begin
      WriteLn('Error: '+E.Message);
      ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessGetTimeSettings');
    end;
  end;
end;

procedure TToolListener4.ProcessRestartRecp;
var
  DBGUID            : String;
  RecpId            : Int64;
  User, Password    : String;
  ErrMsg            : String;
begin
  ErrMsg:='';
  DBGUID:=ReadLn;
  User:=ReadLn;
  Password:=ReadLn;
  RecpId:=StrToInt64Def(ReadLn, 0);

  try
    if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
      RestartTask(FUntillDB, RecpId);
      WriteLn('ok');
      DisconnectDB;
    end else begin
      ExceptionLog.WriteString('unTill params: can''t open database: '+DBGUID+'. '+ErrMsg);
      WriteLn('Can''t open database: '+ErrMsg);
    end;
  except
    on E: Exception do begin
      WriteLn('Error: '+E.Message);
      ExceptionLog.WriteExceptionInfo(e, 'unTill params.ProcessRestartRecp');
    end;
  end;
end;

procedure TToolListener4.ProcessCheckHosts;
var clnt : TUntillAdminClient;
    iq   : IIBSQL;
    DBGUID, User, Password : String;
    ErrMsg : String;
begin
  ErrMsg := '';
  DBGUID := ReadLn;
  User := ReadLn;
  Password := ReadLn;

  if OpenUntillDB(DBGUID, User, Password, ErrMsg) then begin
    clnt := TUntillAdminClient.Create;
    try
      iq := FUntillDB.GetIIbSql;
      iq.SetText('select ip, port from sync_db where is_active=1');
      iq.ExecQuery;
      while not iq.eof do begin
        try
          if clnt.Connected then clnt.disconnect;
          clnt.Connect(iq.q.fields[0].asString, iq.q.fields[1].asInteger);
          if clnt.Connected then clnt.Ping;
        except
          on e:Exception do begin
              WriteLn('IP: "' + iq.q.fields[0].asString + '" on port: "' + iq.q.fields[1].asString + '" is not available.');
              DisconnectDB;
            end;
        end;
        iq.Next;
      end;
      WriteLn('ok');
      DisconnectDB;
    finally
      clnt.Free;
    end;
  end;

end;


end.
