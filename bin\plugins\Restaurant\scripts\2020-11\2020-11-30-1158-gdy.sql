SET TERM ^ ;

create or alter procedure REMOVE_AGGREGATES
as
begin
	-- remove old-style stored procedures and views (UPTA: Compact db issue https://dev.untill.com/projects/#!563076)
	if (exists(select 1 from rdb$procedures where rdb$Procedure_name = 'PBILL_CARD_PAYMENTS_INFO_AGGR')) then 
		execute statement 'drop procedure PBILL_CARD_PAYMENTS_INFO_AGGR;';
	if (exists(select 1 from rdb$procedures where rdb$Procedure_name = 'PBILL_HOTEL_PAYMENTS_INFO_AGGR')) then 
		execute statement 'drop procedure PBILL_HOTEL_PAYMENTS_INFO_AGGR;';
	if (exists(select 1 from rdb$procedures where rdb$Procedure_name = 'PBILL_PAYMENTS_BOOKP_AGGR')) then 
		execute statement 'drop procedure PBILL_PAYMENTS_BOOKP_AGGR;';
	if (exists(select 1 from rdb$relations where rdb$relation_name = 'PBILL_ITEM_AGGR_VIEW')) then
        execute statement 'DROP VIEW PBILL_ITEM_AGGR_VIEW;';
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'VOUCHER_PBILL_AGGR_VIEW')) then
        execute statement 'DROP VIEW VOUCHER_PBILL_AGGR_VIEW;';
		
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'PIVOTS_VERSION')) then
        execute statement 'drop table PIVOTS_VERSION;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'VOUCHER_PBILL_PAYMENTS_AGGR')) then
        execute statement 'DROP TRIGGER VOUCHER_PBILL_PAYMENTS_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'VOUCHERS_AGGR')) then
        execute statement 'DROP TRIGGER VOUCHERS_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'SOLD_ARTICLES_AGGR')) then
        execute statement 'DROP TRIGGER SOLD_ARTICLES_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'PBILL_PAYMENTS_BOOKP_AGGR')) then
        execute statement 'DROP TRIGGER PBILL_PAYMENTS_BOOKP_AGGR;';
    if (exists(select 1  from rdb$triggers where rdb$trigger_name = 'PBILL_PAYMENTS_AGGR')) then
        execute statement 'DROP TRIGGER PBILL_PAYMENTS_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'PBILL_ITEM_AGGR')) then
        execute statement 'DROP TRIGGER PBILL_ITEM_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'PBILL_HOTEL_PAYMENTS_INFO_AGGR')) then
        execute statement 'DROP TRIGGER PBILL_HOTEL_PAYMENTS_INFO_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'PBILL_CARD_PAYMENTS_INFO_AGGR')) then
        execute statement 'DROP TRIGGER PBILL_CARD_PAYMENTS_INFO_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'NEG_PBILL_AGGR')) then
        execute statement 'DROP TRIGGER NEG_PBILL_AGGR;';
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'DEPOSIT_CARD_PAYMENTS_INFO_AGGR')) then
        execute statement 'DROP TRIGGER DEPOSIT_CARD_PAYMENTS_INFO_AGGR;';
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'PIVOT_SA')) then 
        execute statement 'drop table PIVOT_SA;';
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'PIVOT_PAYMENTS')) then 
        execute statement 'drop table PIVOT_PAYMENTS;';
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'SA_AGGR_VIEW')) then
        execute statement 'DROP VIEW SA_AGGR_VIEW;';
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'PBILL_PAYMENTS_AGGR_VIEW')) then
        execute statement 'DROP VIEW PBILL_PAYMENTS_AGGR_VIEW;';
end
^

SET TERM ; ^

commit;

/* Existing privileges on this procedure */

GRANT EXECUTE ON PROCEDURE REMOVE_AGGREGATES TO SYSDBA;
GRANT EXECUTE ON PROCEDURE REMOVE_AGGREGATES TO UNTILLUSER;
commit;

ALTER TABLE SETTINGS ALTER COLUMN IGNORE_PIVOTS SET DEFAULT 1;
commit;

update settings set ignore_pivots = 1 where ignore_pivots is null;
commit;
