unit TPAPIPOSHelpersU;

interface
uses Classes, Contnrs, SysUtils, NamedVarsU, TPAPIPosTypesU;

const

  ORDERITEM_ARTICLE = 0;
  ORDERITEM_MUSTHAVE_OPTION = 1;
  ORDERITEM_FREE_OPTION = 2;
  ORDERITEM_SUPPLEMENT = 3;
  ORDERITEM_CONDIMENT = 4;
  ORDERITEM_MENUITEM = 5;
  ORDERITEM_ARTICLE_MSG = 6;

  KEY_ACTIVETABLE_TOTAL = 'TotalAmount';
  KEY_ACTIVETABLE_REMAINING = 'RemainingAmount';
  KEY_ACTIVETABLE_EXTRA = 'ExtraAmount';
  KEY_BILL_TEXT = 'bill_text';
  KEY_RECEIPT_TEXT = 'receipt_text';
  KEY_RESULT_IDS = 'result_ids';
  KEY_OIF_DATE = 'OifDate';
  KEY_TAKEAWAY_DATE = 'ta_timestamp';
  KEY_TAKEAWAY_ON_HOLD = 'ta_onhold';
  KEY_TAKEAWAY_NOTES = 'ta_notes';
  KEY_TAKEAWAY_EXTERNAL_ID = 'ta_external_id';
  KEY_TABLE_CARD_ID = 'TableSmartcardId';
  KEY_TABLE_CARD_UID = 'TableSmartcardUid';
  KEY_UNTILL_VERSION = 'untill_version';
  KEY_OIF_DEPOSIT_AMOUNT = 'OifDepositAmount';
  KEY_OIF_DEPOSIT_PAYMENT_MODE_ID = 'OifDepositPaymentModeId';
  KEY_GUEST_NOTIFIER_CODE = 'GuestNotifierCode';
  KEY_ID_BILL = 'TPAPI_ID_BILL';
  KEY_TABLE_NO = 'TPAPI_TABLE_NO';
  KEY_TABLE_PART = 'TPAPI_TABLE_PART';

  KEY_POS_STATUS_ACTIVATE_ONHOLD_TA_ORDER = '#activateOnholdTaOrder#';
  KEY_POS_STATUS_ACTIVATE_ONHOLD_TA_BILL_ID = '#activateOnholdTaBillId#';
  KEY_POS_STATUS_REOPEN_BILL_BY_NUM = '#rbbyN#';
  KEY_POS_STATUS_SET_DATE_TIME = '#chposdt#';
  KEY_POS_STATUS_RESET_DATE_TIME = '#chposdtres#';
  KEY_POS_STATUS_CLEAR_SALES = '#ppcs#';
  KEY_POS_STATUS_COPY_AND_RETURN = '#ppcopyandret#';
  KEY_POS_STATUS_PLAY_TRACE = '#playtrace#';

  TPAPI_VALUE_DATETIME_FMT = 'yyyymmdd hhnnss';

type

  ETPAPIPOSException = class(Exception);
  ETPAPIPOSValidationException = class(ETPAPIPOSException);
  ETPAPIPOSUnableToProcessRequest = class(ETPAPIPOSException);
  ETPAPIPOSNoTpapiPermission = class(ETPAPIPOSException);

  TBaseTPAPIRecord = class
  public
    procedure SaveToStream(Stream: TStream); virtual; abstract;
    procedure LoadFromStream(Stream: TStream); virtual; abstract;
  end;

  TOrderItemRec = class(TBaseTPAPIRecord)
  private
    FManualPrice: Currency;
    FArticleId: Int64;
    FItemNumber: Integer;
    FQuantity: Integer;
    FOrderItemType: Integer;
    FText: WideString;
    FLast: Boolean;
    FCourseId: Int64;
    FSizeModifierItemId: Int64;
    FOverridePrice: Boolean;
    FAllergens: String;
    procedure SetArticleId(const Value: Int64);
    procedure SetItemNumber(const Value: Integer);
    procedure SetManualPrice(const Value: Currency);
    procedure SetOrderItemType(const Value: Integer);
    procedure SetQuantity(const Value: Integer);
    procedure SetText(const Value: WideString);
    procedure SetLast(const Value: Boolean);
    procedure SetCourseId(const Value: Int64);
    procedure SetSizeModifierItemId(const Value: Int64);
    procedure SetOverridePrice(const Value: Boolean);
    procedure SetAllergens(const Value: String);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  ItemNumber: Integer read FItemNumber write SetItemNumber;
    property  ArticleId: Int64 read FArticleId write SetArticleId;
    property  OrderItemType: Integer read FOrderItemType write SetOrderItemType;
    property  Text: WideString read FText write SetText;
    property  ManualPrice: Currency read FManualPrice write SetManualPrice;
    property  Quantity: Integer read FQuantity write SetQuantity;
    property  Last: Boolean read FLast write SetLast;
    property  CourseId: Int64 read FCourseId write SetCourseId;
    property  SizeModifierItemId : Int64 read FSizeModifierItemId write SetSizeModifierItemId;
    property  OverridePrice: Boolean read FOverridePrice write SetOverridePrice;
    property  Allergens: String read FAllergens write SetAllergens;
  end;

  TClientDepositRecord = class(TBaseTPAPIRecord)
  private
    FClientId: Int64;
    FAmount: Currency;
    FPaymentId: Int64;
    FPassword: WideString;
    FUserName: WideString;
    FReceiptRequested: Boolean;
    FDontShowInReports: Boolean;
    procedure SetAmount(const Value: Currency);
    procedure SetClientId(const Value: Int64);
    procedure SetPaymentId(const Value: Int64);
    procedure SetPassword(const Value: WideString);
    procedure SetUserName(const Value: WideString);
    procedure SetReceiptRequested(const Value: Boolean);
    procedure SetDontShowInReports(const Value: Boolean);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;

    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
    property ClientId: Int64 read FClientId write SetClientId;
    property PaymentId: Int64 read FPaymentId write SetPaymentId;
    property Amount: Currency read FAmount write SetAmount;
    property ReceiptRequested: Boolean read FReceiptRequested write SetReceiptRequested;
    property DontShowInReports: Boolean read FDontShowInReports write SetDontShowInReports;
  end;

  TCreateOrderRecord = class(TBaseTPAPIRecord)
  private
    FTableNumber: Integer;
    FItems: TObjectList;
    FTablePart: WideString;
    FOrderName: WideString;
    FClientName: WideString;
    FOrderDescr: WideString;
    FUserName: WideString;
    FPassword: WideString;
    FCovers: Integer;
    FExtraFields: INamedVars;
    FClientId: Int64;
    procedure SetClientName(const Value: WideString);
    procedure SetOrderDescr(const Value: WideString);
    procedure SetOrderName(const Value: WideString);
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
    procedure SetPassword(const Value: WideString);
    procedure SetUserName(const Value: WideString);
    function GetItems(const Number: Integer): TOrderItemRec;
    procedure SetItems(const Number: Integer; const Value: TOrderItemRec);
    procedure SetCovers(const Value: Integer);
    procedure SetExtraFields(const Value: INamedVars);
    procedure SetClientId(const Value: Int64);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    constructor Create;
    destructor Destroy; override;
    procedure AddItem(Item: TOrderItemRec);
    function  ItemsCount: Integer;
    function  ItemHasManualPriceMenuItems(index: integer): Boolean;

    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
    property  ClientName: WideString read FClientName write SetClientName;
    property  OrderName: WideString read FOrderName write SetOrderName;
    property  OrderDescr: WideString read FOrderDescr write SetOrderDescr;
    property  Covers: Integer read FCovers write SetCovers;
    property  Items[const Number: Integer]: TOrderItemRec read GetItems write SetItems;
    property  ExtraFields: INamedVars read FExtraFields write SetExtraFields;
    property  ClientId: Int64 read FClientId write SetClientId;

  end;

  TGetPosStatusRecord = class(TBaseTPAPIRecord)
  private
    FExtraFields: INamedVars;
    FPassword: WideString;
    FUserName: WideString;
    procedure SetExtraFields(const Value: INamedVars);
    procedure SetPassword(const Value: WideString);
    procedure SetUserName(const Value: WideString);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    constructor Create;
  	property ExtraFields: INamedVars read FExtraFields write SetExtraFields;
    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
  end;

  TCloseOrderRecord = class(TBaseTPAPIRecord)
  private
    FPaymentId: Int64;
    FTableNumber: Integer;
    FTablePart: WideString;
    FUserName: WideString;
    FPassword: WideString;
    FExternalId: String;
    FAmount: Currency;
    FEFTData: String;
    FGetBill: Boolean;
    FBillLayout: Int64;
    FTip: Currency;
    FProformaSignature: String;
    FPrinterId: Int64;
    FEftParams: String;
    FFolioNumber: String;
    procedure SetPaymentId(const Value: Int64);
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
    procedure SetPassword(const Value: WideString);
    procedure SetUserName(const Value: WideString);
    procedure SetAmount(const Value: Currency);
    procedure SetEFTData(const Value: String);
    procedure SetExternalId(const Value: String);
    procedure SetGetBill(const Value: Boolean);
    procedure SetBillLayout(const Value: Int64);
    procedure SetTip(const Value: Currency);
    procedure SetProformaSignature(const Value: String);
    procedure SetPrinterId(const Value: Int64);
    procedure SetEftParams(const Value: String);
    procedure SetFolioNumber(const Value: String);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
    property  PaymentId: Int64 read FPaymentId write SetPaymentId;
    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
    property  ExternalId: String read FExternalId write SetExternalId;
    property  Amount: Currency read FAmount write SetAmount;
    property  EFTData: String read FEFTData write SetEFTData;
    property  GetBill: Boolean read FGetBill write SetGetBill;
    property  BillLayout: Int64 read FBillLayout write SetBillLayout;
    property  PrinterId: Int64 read FPrinterId write SetPrinterId;
    property  Tip: Currency read FTip write SetTip;
    property  ProformaSignature: String read FProformaSignature write SetProformaSignature;
    property  EftParams: String read FEftParams write SetEftParams;
    property  FolioNumber: String read FFolioNumber write SetFolioNumber;
  end;

  TGetActiveTableInfoRecord = class(TBaseTPAPIRecord)
  private
    FTableNumber: Integer;
    FPassword: WideString;
    FTablePart: WideString;
    FUserName: WideString;
    procedure SetPassword(const Value: WideString);
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
    procedure SetUserName(const Value: WideString);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
  end;

  TNextCourseRecord = class(TBaseTPAPIRecord)
  private
    FTableNumber: Integer;
    FPassword: WideString;
    FTablePart: WideString;
    FUserName: WideString;
    procedure SetPassword(const Value: WideString);
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
    procedure SetUserName(const Value: WideString);
  protected
    class function STREAM_VER: Integer;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
  end;

  TUserPasswordRecord = class(TBaseTPAPIRecord)
  private
    FPassword: WideString;
    FuserName: WideString;
    procedure SetPassword(const Value: WideString);
    procedure SetUserName(const Value: WideString);
  protected
    class function STREAM_VER: Integer; virtual;
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  UserName: WideString read FUserName write SetUserName;
    property  Password: WideString read FPassword write SetPassword;
  end;

  TPOSTableRecord = class(TUserPasswordRecord)
  private
    FTableNumber: Integer;
    FTablePart: WideString;
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
  end;

  TPrintProformaRecord = class(TPOSTableRecord)
  private
    FLayoutId: Int64;
    FGetReceipt: Boolean;
    FHandleByDrivers: Boolean;
    FPrinterId: Int64;
    procedure SetLayoutId(const Value: Int64);
    procedure SetGetReceipt(const Value: Boolean);
    procedure SetHandleByDrivers(const Value: Boolean);
    procedure SetPrinterId(const Value: Int64);
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property LayoutId: Int64 read FLayoutId write SetLayoutId;
    property PrinterId: Int64 read FPrinterId write SetPrinterId;
    property GetReceipt: Boolean read FGetReceipt write SetGetReceipt;
    property HandleByDrivers: Boolean read FHandleByDrivers write SetHandleByDrivers;
  end;

  TPrintReportRecord = class(TUserPasswordRecord)
  private
    FPrinterId: Int64;
    FLayoutId: Int64;
    FArguments: INamedVars;
    FFromDateTime: TDateTime;
    FTillDateTime: TDateTime;
    FCsvReport: String;
    procedure SetArguments(const Value: INamedVars);
    procedure SetLayoutId(const Value: Int64);
    procedure SetPrinterId(const Value: Int64);
    procedure SetFromDateTime(const Value: TDateTime);
    procedure SetTillDateTime(const Value: TDateTime);
    procedure SetCsvReport(const Value: String);
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property LayoutId: Int64 read FLayoutId write SetLayoutId;
    property PrinterId: Int64 read FPrinterId write SetPrinterId;
    property FromDateTime: TDateTime read FFromDateTime write SetFromDateTime;
    property TillDateTime: TDateTime read FTillDateTime write SetTillDateTime;
    property Arguments: INamedVars read FArguments write SetArguments;
    property CsvReport: String read FCsvReport write SetCsvReport;
    constructor Create;
  end;

  TApplyDiscountToItemsRecord = class(TUserPasswordRecord)
{  public
    property  TableNumber: Integer;
    property  TablePart: WideString;}
  end;

  TSetTableCourseRecord = class(TUserPasswordRecord)
  private
    FTableNumber: Integer;
    FCourseId: Int64;
    FTablePart: WideString;
    procedure SetCourseId(const Value: Int64);
    procedure SetTableNumber(const Value: Integer);
    procedure SetTablePart(const Value: WideString);
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  TableNumber: Integer read FTableNumber write SetTableNumber;
    property  TablePart: WideString read FTablePart write SetTablePart;
    property  CourseId: Int64 read FCourseId write SetCourseId;
  end;

  TSetOrderItemCourseRecord = class(TUserPasswordRecord)
  private
    FOrderItemId: Int64;
    FCourseId: Int64;
    procedure SetCourseId(const Value: Int64);
    procedure SetOrderItemId(const Value: Int64);
  public
    procedure SaveToStream(Stream: TStream); override;
    procedure LoadFromStream(Stream: TStream); override;
    property  OrderItemId: Int64 read FOrderItemId write SetOrderItemId;
    property  CourseId: Int64 read FCourseId write SetCourseId;
  end;

  TGetActiveTableExResp = class(TTPApiPosResponse)
  private
    FTransactionId: Int64;
    FExtraAmount: Currency;
    FRemainingAmount: Currency;
    FTotalAmount: Currency;
    FTableNo: Integer;
    FTablePart: String;
    procedure SetExtraAmount(const Value: Currency);
    procedure SetRemainingAmount(const Value: Currency);
    procedure SetTotalAmount(const Value: Currency);
    procedure SetTransactionId(const Value: Int64);
    procedure SetTableNo(const Value: Integer);
    procedure SetTablePart(const Value: String);
  published
    property TotalAmount: Currency read FTotalAmount write SetTotalAmount;
    property RemainingAmount: Currency read FRemainingAmount write SetRemainingAmount;
    property ExtraAmount: Currency read FExtraAmount write SetExtraAmount;
    property TransactionId: Int64 read FTransactionId write SetTransactionId;
    property TableNo: Integer read FTableNo write SetTableNo;
    property TablePart: String read FTablePart write SetTablePart;
  end;

  TPAPIPOSHelpers = class
  public
    class function HasExtraProperty(Extra: TExtraInfoArray; PropName: String; var PropValue: String): Boolean;
    class function GetExtraProperty(Extra: TExtraInfoArray; PropName: String; DefaultValue: String): String;
  end;

implementation
uses ClassesU;
{ TOrderItemRec }

procedure TOrderItemRec.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TOrderItemRec.LoadFromStream');

  Stream.Read(FItemNumber, SizeOf(Integer));
  Stream.Read(FArticleId, SizeOf(Int64));
  Stream.Read(FOrderItemType, SizeOf(Integer));
  Stream.Read(FQuantity, SizeOf(Integer));
  Stream.Read(FManualPrice, SizeOf(Currency));
  FText := ReadWideStrFromStream(Stream);
  if ver > 1 then begin
    Stream.Read(FLast, SizeOf(Boolean));
  end;
  if ver > 2 then begin
    Stream.Read(FCourseId, SizeOf(Int64));
  end;
  if ver > 3 then begin
    Stream.Read(FSizeModifierItemId, SizeOf(Int64));
  end;
  if ver > 4 then begin
    Stream.Read(FOverridePrice, SizeOf(Boolean));
  end;
  if ver > 5 then begin
    FAllergens:= ReadWideStrFromStream(Stream);
  end;
end;

procedure TOrderItemRec.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FItemNumber, SizeOf(Integer));
  Stream.Write(FArticleId, SizeOf(Int64));
  Stream.Write(FOrderItemType, SizeOf(Integer));
  Stream.Write(FQuantity, SizeOf(Integer));
  Stream.Write(FManualPrice, SizeOf(Currency));
  WriteWideStrToStream(Stream, FText);
  Stream.Write(FLast, SizeOf(Boolean));
  Stream.Write(FCourseId, SizeOf(Int64));
  Stream.Write(FSizeModifierItemId, SizeOf(Int64));
  Stream.Write(FOverridePrice, SizeOf(Boolean));
  WriteWideStrToStream(Stream, FAllergens);
end;

procedure TOrderItemRec.SetAllergens(const Value: String);
begin
  FAllergens:= Value;
end;

procedure TOrderItemRec.SetArticleId(const Value: Int64);
begin
  FArticleId := Value;
end;

procedure TOrderItemRec.SetCourseId(const Value: Int64);
begin
  FCourseId := Value;
end;

procedure TOrderItemRec.SetItemNumber(const Value: Integer);
begin
  FItemNumber := Value;
end;

procedure TOrderItemRec.SetLast(const Value: Boolean);
begin
  FLast := Value;
end;

procedure TOrderItemRec.SetManualPrice(const Value: Currency);
begin
  FManualPrice := Value;
end;

procedure TOrderItemRec.SetOrderItemType(const Value: Integer);
begin
  FOrderItemType := Value;
end;

procedure TOrderItemRec.SetOverridePrice(const Value: Boolean);
begin
  FOverridePrice := Value;
end;

procedure TOrderItemRec.SetQuantity(const Value: Integer);
begin
  FQuantity := Value;
end;

procedure TOrderItemRec.SetSizeModifierItemId(const Value: Int64);
begin
  FSizeModifierItemId := Value;
end;

procedure TOrderItemRec.SetText(const Value: WideString);
begin
  FText := Value;
end;

class function TOrderItemRec.STREAM_VER: Integer;
begin
  result := 6;
end;

{ TCreateOrderRecord }

procedure TCreateOrderRecord.AddItem(Item: TOrderItemRec);
begin
  FItems.Add(Item);
end;

constructor TCreateOrderRecord.Create;
begin
  FItems:=TObjectList.Create;
  FItems.OwnsObjects := true;
  FExtraFields := TNamedVars.Create;
end;

destructor TCreateOrderRecord.Destroy;
begin
  FreeAndNil(FItems);
  inherited;
end;

function TCreateOrderRecord.GetItems(const Number: Integer): TOrderItemRec;
begin
  result := TOrderItemRec(FItems[Number])
end;

function TCreateOrderRecord.ItemHasManualPriceMenuItems(index: integer): Boolean;
var rec: TOrderItemRec;
    i: integer;
begin
  result := false;
  rec := Items[index];
  if rec.OrderItemType <> ORDERITEM_ARTICLE then
    exit;
  for i:=index+1 to FItems.Count-1 do begin
    rec := Items[i];
    if rec.OrderItemType <> ORDERITEM_MENUITEM then break;
    if rec.OverridePrice then begin
      result := true;
      break;
    end;
  end;
end;

function TCreateOrderRecord.ItemsCount: Integer;
begin
  result := FItems.Count;
end;

procedure TCreateOrderRecord.LoadFromStream(Stream: TStream);
var ver, count, i: Integer;
  item: TOrderItemRec;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TCreateOrderRecord.LoadFromStream');

  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
  FOrderName := ReadWideStrFromStream(Stream);
  FClientName := ReadWideStrFromStream(Stream);
  FOrderDescr := ReadWideStrFromStream(Stream);
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);

  FItems.Clear;
  Stream.Read(count, SizeOf(Integer));
  for i:=0 to count-1 do begin
    item := TOrderItemRec.Create;
    item.LoadFromStream(Stream);
    AddItem(item);
  end;

  if ver > 1 then begin
    Stream.Read(FCovers, SizeOf(Integer));
  end;

  FExtraFields.Clear;
  if ver > 2 then begin
    StringToNamedVars(ReadStrFromStream(Stream), FExtraFields);
  end;
  Stream.Read(FClientId, SizeOf(FClientId));

end;

procedure TCreateOrderRecord.SaveToStream(Stream: TStream);
var ver, count, i: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
  WriteWideStrToStream(Stream, FOrderName);
  WriteWideStrToStream(Stream, FClientName);
  WriteWideStrToStream(Stream, FOrderDescr);
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
  // items
  count := ItemsCount;
  Stream.Write(count, SizeOf(Integer));
  for i:=0 to count-1 do
    Items[i].SaveToStream(Stream);

  Stream.Write(FCovers, SizeOf(Integer));

  WriteStrToStream(Stream, NamedVarsToString(FExtraFields));
  Stream.Write(FClientId, SizeOf(FClientId));
end;

procedure TCreateOrderRecord.SetClientId(const Value: Int64);
begin
  FClientId := Value;
end;

procedure TCreateOrderRecord.SetClientName(const Value: WideString);
begin
  FClientName := Value;
end;

procedure TCreateOrderRecord.SetCovers(const Value: Integer);
begin
  FCovers := Value;
end;

procedure TCreateOrderRecord.SetExtraFields(const Value: INamedVars);
begin
  FExtraFields := Value;
end;

procedure TCreateOrderRecord.SetItems(const Number: Integer;
  const Value: TOrderItemRec);
begin
  FItems[Number] := Value;
end;

procedure TCreateOrderRecord.SetOrderDescr(const Value: WideString);
begin
  FOrderDescr := Value;
end;

procedure TCreateOrderRecord.SetOrderName(const Value: WideString);
begin
  FOrderName := Value;
end;

procedure TCreateOrderRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TCreateOrderRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TCreateOrderRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;

procedure TCreateOrderRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TCreateOrderRecord.STREAM_VER: Integer;
begin
  result := 3;
end;

{ TCloseOrderRecord }

procedure TCloseOrderRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TCloseOrderRecord.LoadFromStream');

  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
  Stream.Read(FPaymentId, SizeOf(Int64));
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);

  if ver > 1 then begin
    FExternalId := ReadWideStrFromStream(Stream);
    FEFTData:= ReadWideStrFromStream(Stream);
    Stream.Read(FAmount, SizeOf(Currency));
  end;

  if ver > 2 then begin
    Stream.Read(FGetBill, SizeOf(Boolean));
  end;

  if ver > 3 then begin
    Stream.Read(FBillLayout, SizeOf(Int64));
  end;

  if ver > 4 then begin
    Stream.Read(FTip, SizeOf(Currency));
  end;

  if ver > 5 then begin
    FProformaSignature := ReadWideStrFromStream(Stream);
  end;

  if ver > 6 then begin
    Stream.Read(FPrinterId, SizeOf(Int64));
    FEftParams := ReadWideStrFromStream(Stream);
  end;

  if ver > 7 then begin
    FFolioNumber := ReadWideStrFromStream(Stream);
  end;
end;

procedure TCloseOrderRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
  Stream.Write(FPaymentId, SizeOf(Int64));
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
  WriteWideStrToStream(Stream, FExternalId);
  WriteWideStrToStream(Stream, FEFTData);
  Stream.Write(FAmount, SizeOf(Currency));
  Stream.Write(FGetBill, SizeOf(Boolean));
  Stream.Write(FBillLayout, SizeOf(Int64));
  Stream.Write(FTip, SizeOf(Currency));
  WriteWideStrToStream(Stream, FProformaSignature);
  Stream.Write(FPrinterId, SizeOf(Int64));
  WriteWideStrToStream(Stream, FEftParams);
  WriteWideStrToStream(Stream, FFolioNumber);
end;

procedure TCloseOrderRecord.SetAmount(const Value: Currency);
begin
  FAmount := Value;
end;

procedure TCloseOrderRecord.SetBillLayout(const Value: Int64);
begin
  FBillLayout := Value;
end;

procedure TCloseOrderRecord.SetEFTData(const Value: String);
begin
  FEFTData := Value;
end;

procedure TCloseOrderRecord.SetEftParams(const Value: String);
begin
  FEftParams := Value;
end;

procedure TCloseOrderRecord.SetExternalId(const Value: String);
begin
  FExternalId := Value;
end;

procedure TCloseOrderRecord.SetFolioNumber(const Value: String);
begin
  FFolioNumber := Value;
end;

procedure TCloseOrderRecord.SetGetBill(const Value: Boolean);
begin
  FGetBill := Value;
end;

procedure TCloseOrderRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TCloseOrderRecord.SetPaymentId(const Value: Int64);
begin
  FPaymentId := Value;
end;

procedure TCloseOrderRecord.SetPrinterId(const Value: Int64);
begin
  FPrinterId := Value;
end;

procedure TCloseOrderRecord.SetProformaSignature(const Value: String);
begin
  FProformaSignature := Value;
end;

procedure TCloseOrderRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TCloseOrderRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;


procedure TCloseOrderRecord.SetTip(const Value: Currency);
begin
  FTip := Value;
end;

procedure TCloseOrderRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TCloseOrderRecord.STREAM_VER: Integer;
begin
  result := 8;
end;

{ TPAPIPOSHelpers }

class function TPAPIPOSHelpers.GetExtraProperty(Extra: TExtraInfoArray;
  PropName, DefaultValue: String): String;
var i: integer;
begin
  result := DefaultValue;
  if Extra <> nil then begin
    for i:=0 to Length(Extra)-1 do begin
      if assigned(Extra[i]) and SameText(Extra[i].Key, PropName) then begin
        result := Extra[i].Value;
        exit;
      end;
    end;
  end;
end;

class function TPAPIPOSHelpers.HasExtraProperty(Extra: TExtraInfoArray; PropName: String;
  var PropValue: String): Boolean;
var i: integer;
begin
  result := false;
  PropValue := '';
  if Extra <> nil then begin
    for i:=0 to Length(Extra)-1 do begin
      if assigned(Extra[i]) and SameText(Extra[i].Key, PropName) then begin
        PropValue := Extra[i].Value;
        result := true;
        exit;
      end;
    end;
  end;
end;

{ TClientDepositRecord }

procedure TClientDepositRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TClientDepositRecord.LoadFromStream');

  Stream.Read(FClientId, SizeOf(Int64));
  Stream.Read(FPaymentId, SizeOf(Int64));
  Stream.Read(FAmount, SizeOf(Currency));
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);

  if ver > 1 then begin
    Stream.Read(FReceiptRequested, SizeOf(ReceiptRequested));
  end;

  if ver > 2 then begin
    Stream.Read(FDontShowInReports, SizeOf(FDontShowInReports));
  end;
end;

procedure TClientDepositRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FClientId, SizeOf(Int64));
  Stream.Write(FPaymentId, SizeOf(Int64));
  Stream.Write(FAmount, SizeOf(Currency));
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
  Stream.Write(FReceiptRequested, SizeOf(ReceiptRequested));
  Stream.Write(FDontShowInReports, SizeOf(FDontShowInReports));
end;

procedure TClientDepositRecord.SetAmount(const Value: Currency);
begin
  FAmount := Value;
end;

procedure TClientDepositRecord.SetClientId(const Value: Int64);
begin
  FClientId := Value;
end;

procedure TClientDepositRecord.SetDontShowInReports(const Value: Boolean);
begin
  FDontShowInReports := Value;
end;

procedure TClientDepositRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TClientDepositRecord.SetPaymentId(const Value: Int64);
begin
  FPaymentId := Value;
end;

procedure TClientDepositRecord.SetReceiptRequested(const Value: Boolean);
begin
  FReceiptRequested := Value;
end;

procedure TClientDepositRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TClientDepositRecord.STREAM_VER: Integer;
begin
  result := 3;
end;

{ TNextCourseRecord }

procedure TNextCourseRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TNextCourseRecord.LoadFromStream');

  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);
end;

procedure TNextCourseRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
end;

procedure TNextCourseRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TNextCourseRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TNextCourseRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;

procedure TNextCourseRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TNextCourseRecord.STREAM_VER: Integer;
begin
  result := 1;
end;

{ TUserPasswordRecord }

procedure TUserPasswordRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TUserPasswordRecord.LoadFromStream');
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);
end;

procedure TUserPasswordRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
end;

procedure TUserPasswordRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TUserPasswordRecord.SetUserName(const Value: WideString);
begin
  FuserName := Value;
end;

class function TUserPasswordRecord.STREAM_VER: Integer;
begin
  result := 1;
end;

{ TSetOrderItemCourseRecord }

procedure TSetOrderItemCourseRecord.LoadFromStream(Stream: TStream);
begin
  inherited;
  Stream.Read(FOrderItemId, SizeOf(Int64));
  Stream.Read(FCourseId, SizeOf(Int64));
end;

procedure TSetOrderItemCourseRecord.SaveToStream(Stream: TStream);
begin
  inherited;
  Stream.Write(FOrderItemId, SizeOf(Int64));
  Stream.Write(FCourseId, SizeOf(Int64));
end;

procedure TSetOrderItemCourseRecord.SetCourseId(const Value: Int64);
begin
  FCourseId := Value;
end;

procedure TSetOrderItemCourseRecord.SetOrderItemId(const Value: Int64);
begin
  FOrderItemId := Value;
end;

{ TSetTableCourseRecord }

procedure TSetTableCourseRecord.LoadFromStream(Stream: TStream);
begin
  inherited;
  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
  Stream.Read(FCourseId, SizeOf(Int64));
end;

procedure TSetTableCourseRecord.SaveToStream(Stream: TStream);
begin
  inherited;
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
  Stream.Write(FCourseId, SizeOf(Int64));
end;

procedure TSetTableCourseRecord.SetCourseId(const Value: Int64);
begin
  FCourseId := Value;
end;

procedure TSetTableCourseRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TSetTableCourseRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;

{ TGetActiveTableInfoRecord }

procedure TGetActiveTableInfoRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TGetActiveTableInfoRecord.LoadFromStream');
  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);
end;

procedure TGetActiveTableInfoRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
end;

procedure TGetActiveTableInfoRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TGetActiveTableInfoRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TGetActiveTableInfoRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;

procedure TGetActiveTableInfoRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TGetActiveTableInfoRecord.STREAM_VER: Integer;
begin
  result := 1;
end;

{ TPOSTableRecord }

procedure TPOSTableRecord.LoadFromStream(Stream: TStream);
begin
  inherited;
  Stream.Read(FTableNumber, SizeOf(Integer));
  FTablePart := ReadWideStrFromStream(Stream);
end;

procedure TPOSTableRecord.SaveToStream(Stream: TStream);
begin
  inherited;
  Stream.Write(FTableNumber, SizeOf(Integer));
  WriteWideStrToStream(Stream, FTablePart);
end;

procedure TPOSTableRecord.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

procedure TPOSTableRecord.SetTablePart(const Value: WideString);
begin
  FTablePart := Value;
end;

{ TPrintProformaRecord }

procedure TPrintProformaRecord.LoadFromStream(Stream: TStream);
begin
  inherited;
  Stream.Read(FLayoutId, SizeOf(Int64));
  Stream.Read(FGetReceipt, SizeOf(FGetReceipt));
  Stream.Read(FHandleByDrivers, SizeOf(FHandleByDrivers));
  Stream.Read(FPrinterId, SizeOf(Int64));
end;

procedure TPrintProformaRecord.SaveToStream(Stream: TStream);
begin
  inherited;
  Stream.Write(FLayoutId, SizeOf(Int64));
  Stream.Write(FGetReceipt, SizeOf(FGetReceipt));
  Stream.Write(FHandleByDrivers, SizeOf(FHandleByDrivers));
  Stream.Write(FPrinterId, SizeOf(Int64));
end;

procedure TPrintProformaRecord.SetGetReceipt(const Value: Boolean);
begin
  FGetReceipt := Value;
end;

procedure TPrintProformaRecord.SetHandleByDrivers(const Value: Boolean);
begin
  FHandleByDrivers := Value;
end;

procedure TPrintProformaRecord.SetLayoutId(const Value: Int64);
begin
  FLayoutId := Value;
end;

procedure TPrintProformaRecord.SetPrinterId(const Value: Int64);
begin
  FPrinterId := Value;
end;

{ TPrintReportRecord }

constructor TPrintReportRecord.Create;
begin
  FArguments := newnv;
end;

procedure TPrintReportRecord.LoadFromStream(Stream: TStream);
begin
  inherited;
  Stream.Read(FLayoutId, SizeOf(Int64));
  Stream.Read(FPrinterId, SizeOf(Int64));
  Stream.Read(FFromDateTime, SizeOf(TDateTime));
  Stream.Read(FTillDateTime, SizeOf(TDateTime));
  StringToNamedVars(ReadStrFromStream(Stream), FArguments);
  FCsvReport := ReadWideStrFromStream(Stream);
end;

procedure TPrintReportRecord.SaveToStream(Stream: TStream);
begin
  inherited;
  Stream.Write(FLayoutId, SizeOf(Int64));
  Stream.Write(FPrinterId, SizeOf(Int64));
  Stream.Write(FFromDateTime, SizeOf(TDateTime));
  Stream.Write(FTillDateTime, SizeOf(TDateTime));
  WriteStrToStream(Stream, NamedVarsToString(FArguments));
  WriteWideStrToStream(Stream, FCsvReport);
end;

procedure TPrintReportRecord.SetArguments(const Value: INamedVars);
begin
  FArguments := Value;
end;

procedure TPrintReportRecord.SetCsvReport(const Value: String);
begin
  FCsvReport := Value;
end;

procedure TPrintReportRecord.SetFromDateTime(const Value: TDateTime);
begin
  FFromDateTime := Value;
end;

procedure TPrintReportRecord.SetLayoutId(const Value: Int64);
begin
  FLayoutId := Value;
end;

procedure TPrintReportRecord.SetPrinterId(const Value: Int64);
begin
  FPrinterId := Value;
end;

procedure TPrintReportRecord.SetTillDateTime(const Value: TDateTime);
begin
  FTillDateTime := Value;
end;

{ TGetPosStatusRecord }

constructor TGetPosStatusRecord.Create;
begin
	FExtraFields := TNamedVars.Create;
end;

procedure TGetPosStatusRecord.LoadFromStream(Stream: TStream);
var ver: Integer;
begin
  Stream.Read(ver, SizeOf(Integer));
  if ver > STREAM_VER then
    raise Exception.Create('Unsupportable stream version '+IntToStr(ver)+' at TPAPIPOSImplU.TGetPosStatusRecord.LoadFromStream');
  FUserName := ReadWideStrFromStream(Stream);
  FPassword := ReadWideStrFromStream(Stream);
  StringToNamedVars(ReadStrFromStream(Stream), FExtraFields);
end;

procedure TGetPosStatusRecord.SaveToStream(Stream: TStream);
var ver: Integer;
begin
  ver := STREAM_VER;
  Stream.Write(ver, SizeOf(Integer));
  WriteWideStrToStream(Stream, FUserName);
  WriteWideStrToStream(Stream, FPassword);
  WriteStrToStream(Stream, NamedVarsToString(FExtraFields));
end;

procedure TGetPosStatusRecord.SetExtraFields(const Value: INamedVars);
begin
  FExtraFields := Value;
end;

procedure TGetPosStatusRecord.SetPassword(const Value: WideString);
begin
  FPassword := Value;
end;

procedure TGetPosStatusRecord.SetUserName(const Value: WideString);
begin
  FUserName := Value;
end;

class function TGetPosStatusRecord.STREAM_VER: Integer;
begin
	result := 1;
end;

{ TGetActiveTableExResp }

procedure TGetActiveTableExResp.SetExtraAmount(const Value: Currency);
begin
  FExtraAmount := Value;
end;

procedure TGetActiveTableExResp.SetRemainingAmount(const Value: Currency);
begin
  FRemainingAmount := Value;
end;

procedure TGetActiveTableExResp.SetTableNo(const Value: Integer);
begin
  FTableNo := Value;
end;

procedure TGetActiveTableExResp.SetTablePart(const Value: String);
begin
  FTablePart := Value;
end;

procedure TGetActiveTableExResp.SetTotalAmount(const Value: Currency);
begin
  FTotalAmount := Value;
end;

procedure TGetActiveTableExResp.SetTransactionId(const Value: Int64);
begin
  FTransactionId := Value;
end;

end.


