unit InvoicesEmbEntityManager;

interface
uses
  EntityManagerU, Classes, UntillDBU, ClassManagerU, RestaurantPluginU, IBSQL, EmbEntityManagerU,
  UntillFram, TreeNodesU, InvoicesEmbEntityFram, SysUtils, CommonStringsU, TreeFoldersU,
  DB, ShowProgressU, ClientsU, ProgressU, Contnrs, UTF8U, ShellAPI, UntillLogsU, Generics.Collections,
  RegClassesU, InvoicePrintToolU,
  Dialogs;
type

  TActionType = (SavePdf, Print, SendEmail);

  TInvoicesEmbEntityManager = class(TEmbeddedEntityManager)
  private
    FPrintedInvoices: Integer;
    FTotalInvoiceToPrint: Integer;
    FCopies: Integer;
    FMode: TInvoiceMode;
    FItem: WideString;
    FDaysSinceLastInvoice: Integer;

    FMarkPaidPaymentId: Int64;
    FMarkPaidTimestamp: TDateTime;
    FMarkPaid: Boolean;
    FUseWordTemplate : Boolean;
    FPrintTool : IInvoiceHandler;
    FSavePdfDialog : TSaveDialog;

    FSaveErrors: TDictionary<String, String>;
    FBProcessingClientDataset : Boolean;

    function  _getposnow: TDateTime;
    procedure DoAfterRefreshList(Sender: TObject);
    procedure PreviewClick(Sender: TObject);
    procedure PreviewAsPdfClick(Sender: TObject);
    procedure SaveDataSetEx(sp: IShowProgress);
    procedure SetDaysSinceLastInvoice(const Value: Integer);
    procedure BeforeQueryExec(Sender: TObject;  Query: IIBQuery);
    procedure SaveDataset(PrintTool : IInvoiceHandler); reintroduce;
  public
    constructor     Create( AOwner: TComponent;
                      AParentManager: TEntityManager;
                      AParentObjectId:TIdType; Mode: TInvoiceMode; Dlg: TSaveDialog); reintroduce;

    class function  GetEntityFrameClass: TEntityViewFrameClass; override;


    procedure InitializeList(Sender:TEMListView);

    procedure CanInsert(Frame: TUntillFrame; Update: Boolean); override;
    function  SaveContent(Frame: TUntillFrame): Boolean; override;
    procedure SaveNewValues(tran: IWTran; idClient: Int64; From, Till: TDateTime);
    procedure SaveNewFrom(tran: IWTran; idClient: Int64; From: TDateTime);
    procedure SaveNewTill(tran: IWTran; idClient: Int64; Till: TDateTime);

    procedure RefreshContent(Frame: TUntillFrame); override;
    class function GetTableName: String; override;
    function GetStringRepresentation(FieldName: String; Value: Variant; Field: TField = nil): WideString; override;
    class function GetMassModifyFrameClass: TMassModifyFrameClass; override;

    destructor Destroy; override;

    procedure PrintInvoices(MarkAsPaid: boolean; UseWordTemplate: boolean; ActionType: TActionType; ResetChanges: boolean = true);

    property  PrintedInvoices: Integer read FPrintedInvoices;
    property  TotalInvoiceToPrint: Integer read FTotalInvoiceToPrint;
    property  DaysSinceLastInvoice: Integer read FDaysSinceLastInvoice write SetDaysSinceLastInvoice;
    property  SaveErrors: TDictionary<String, String> read FSaveErrors;
  end;

  TSharedInvoicesEmbEntityManager = class(TInvoicesEmbEntityManager)
  public
    class function GetTableName: String; override;
  end;

implementation
uses COmmonU, UntillDateTimeU, DateUtils, UntillAppU, Menus, UntillPLuginU,
  Printers, WinSpool, Windows, PrintUtilsU, Types, UntillPosu,
  EntityFram, InvoicesMassModifyFram, ProgressFrm, Forms, UBLU, Controls, UBLProxyU, DialogsU,
  DbClient, IOUtils, Math, MarkInvoicePaidDlg, Tap2OrderU,
  ExceptionDlg;

{ TInvoicesEmbEntityManager }
procedure TInvoicesEmbEntityManager.BeforeQueryExec(Sender: TObject; Query: IIBQuery);
begin
  Query.q.ParamByName('days').AsInteger := FDaysSinceLastInvoice;
end;

procedure TInvoicesEmbEntityManager.CanInsert(Frame: TUntillFrame; Update: Boolean);
var from, till: IUntillDateTime;
begin
  inherited;
  from:=GetUntillDateTime;
  till:=GetUntillDateTime;
  with TInvoicesEmbEntityFrame(Frame) do begin
    from.Date:=dtpFromDate.Date;
    from.Time:=dtpFromTime.Time;
    till.Date:=dtpTillDate.Date;
    till.Time:=dtpTillTime.Time;
    if (CompareDateTime(from.AsDateTime, till.AsDateTime) = GreaterThanValue) then
      Plugin.RaiseError(Plugin.Translate('InvoicesEmbEntityManager','"Till" date/time is before "From" date/time'));
    if (CompareDateTime(till.AsDateTime, now) = GreaterThanValue) then
      Plugin.RaiseError(Plugin.Translate('InvoicesEmbEntityManager','"Till" date/time is after current date/time'));
  end;
end;

constructor TInvoicesEmbEntityManager.Create( AOwner: TComponent;
                        AParentManager: TEntityManager;
                        AParentObjectId:TIdType; Mode: TInvoiceMode; Dlg: TSaveDialog);
var
  sPaymentsExistCondition: String;
begin
  inherited Create(AOwner, AParentManager, AParentObjectId);
  FSaveErrors:=TDictionary<String, String>.Create;
  FMode := Mode;
  LoadIcon(Plugin.GetImageFileName('invoices.ico'));
  case FMode of
    imClients: begin
  		ListParams.TableAlias := 'a';
      if not IsAnyPaymentsAllowedInInvoice(AParentManager.UntillDB) then
        sPaymentsExistCondition:='exists(select id from accounts_payments p where '
          + ' (p.datetime > a.last_invoice or a.last_invoice is null) and p.id_clients=a.id'
          + ' and (coalesce(A.COMMON_ACCOUNTS, 0) = 0 or p.ACCOUNT_TYPE=1))'
      else
        sPaymentsExistCondition:='exists(select * from pbill where '+
          '(pbill.pdatetime > a.last_invoice or a.last_invoice is null) and'+
          ' pbill.id_clients=a.id )';

      SetListParams(['a.ID',
                'a.name cname',
                'a.inv_new_from ffrom',
                'a.inv_new_till till',
                'a.last_invoice last_invoice'
                ],
              ['clients a'],
              ['( a.oninvoice=1 or (((select oninvoice from accounts where accounts.id=a.ID_ACCOUNTS)=1 ) '
                + ' and (select account_type from ACCOUNTS where ACCOUNTS.ID = A.ID_ACCOUNTS) = 0 ))',
                sPaymentsExistCondition,
                '(current_timestamp - a.last_invoice >= :days or a.last_invoice is null)'
              ],'id');
      FItem:=Plugin.Translate('InvoicesEmbEntityManager','Client');
    end;
    imSharedAccounts: begin
  		ListParams.TableAlias := 'acc';
      if not IsAnyPaymentsAllowedInInvoice(AParentManager.UntillDB) then
        sPaymentsExistCondition:='exists (select p.id from accounts_payments p, clients a where '+
          'a.common_accounts=1 and coalesce(p.account_type,0)=0 and a.id_accounts = acc.id and '+
          '(p.datetime > acc.last_invoice or acc.last_invoice is null) and '+
          'p.id_clients=a.id)'
      else
        sPaymentsExistCondition:='exists (select * from pbill pb, clients a where '+
          '(a.common_accounts=1 and (a.id_accounts = acc.id) and '+
          '(pb.pdatetime > acc.last_invoice or acc.last_invoice is null) and '+
          'pb.id_clients=a.id)';

      SetListParams(['(select first 1 id from clients where clients.id_accounts=acc.id and coalesce(clients.common_accounts, 0)<>0) id',
                'acc.name cname',
                'acc.inv_new_from ffrom',
                'acc.inv_new_till till',

                'acc.last_invoice last_invoice',
                'acc.invoice_email account_email',
                'acc.invoice_name invoice_name'
                ],
              ['accounts acc'],
              ['coalesce(acc.oninvoice, 0)<>0',
                sPaymentsExistCondition,
                '(select count(*) from clients where clients.id_accounts=acc.id and coalesce(clients.common_accounts, 0)<>0 ) > 0',
                '(current_timestamp - acc.last_invoice >= :days or acc.last_invoice is null)'],
              'id');
      FItem:=Plugin.Translate('InvoicesEmbEntityManager','Account');
    end;
    else assert(false);
  end;
  Options := Options + [emoMultipleSelection];
  AfterRefreshList:=DoAfterRefreshList;
  InitializeListProc:=InitializeList;
  BeforeListQueryExec:=BeforeQueryExec;
  DialogMode:=true;
  ColorGrid:=false;
  FSavePdfDialog := Dlg;
  FBProcessingClientDataset := false;
end;

destructor TInvoicesEmbEntityManager.Destroy;
begin
	FreeAndNil(FSaveErrors);
  inherited;
end;

procedure TInvoicesEmbEntityManager.DoAfterRefreshList(Sender: TObject);
var 
    dt: TDateTime;
    rec: integer;
begin
  with ClientDataSet do begin
    dt:=_getposnow;
    rec:=recno;
    DisableControls;
    try
      First;
      while not Eof do begin
        Edit;
        if FieldByName('ffrom').IsNull then
          FieldByName('ffrom').AsDateTime := FieldByName('last_invoice').AsDateTime;
        if FieldByName('till').IsNull then
          FieldByName('till').AsDateTime := dt;
        Post;
        Next;
      end;
      RecNo := rec;
    finally
      EnableControls;
    end;
  end;
end;

class function TInvoicesEmbEntityManager.GetEntityFrameClass: TEntityViewFrameClass;
begin
  Result:= TInvoicesEmbEntityFrame;
end;

class function TInvoicesEmbEntityManager.GetMassModifyFrameClass: TMassModifyFrameClass;
begin
  result:=TInvoicesMassModifyFrame;
end;

function TInvoicesEmbEntityManager.GetStringRepresentation( FieldName: String; Value: Variant; Field: TField): WideString;
var fn: String;
begin
  fn:=lowercase(FieldName);
  if (fn='ffrom')or(fn='till') then begin
    if (Field.IsNull)or(Field.AsFloat=0) then
      result:='-'
    else
      result:=FormatDateTime('dd.mm.yy hh:nn', SysDateTimeToLocal(Field.asDateTime));
  end;
end;

class function TInvoicesEmbEntityManager.GetTableName: String;
begin
  result:='clients';
end;

procedure TInvoicesEmbEntityManager.InitializeList(Sender: TEMListView);
begin
  with Sender do begin
    AddFieldHeader(FItem,dtWideString,30,'cname',true);
    AddFieldHeader(Plugin.Translate('InvoicesEmbEntityManager','From'),
      dtCustom,15,'ffrom',true );
    AddFieldHeader(Plugin.Translate('InvoicesEmbEntityManager','Till'),
      dtCustom,15,'till',true);
    AddFieldHeader('LastInvoice', dtDate, 0, 'last_invoice', false);
    AddFieldHeader('ID',dtWideString,0,'id', false);
    AddToolButton(lbtnEdit);
    AddToolButton(lbtnMassModify);
    AddToolButton(Plugin.Translate('InvoicesEmbEntityManager','Preview invoice'),
                  Plugin.Translate('InvoicesEmbEntityManager','Ctrl + R'),
                  ShortCut(ord('R'), [ssCtrl]),
                  TUntillPlugin(UntillApp.GetPluginByName(APP_NAME)).GetImageFileName('preview.ico'),
                  PreviewClick);
    AddToolButton(Plugin.Translate('InvoicesEmbEntityManager', 'Preview invoice as PDF'),
                  Plugin.Translate('InvoicesEmbEntityManager','Ctrl + P'),
                  ShortCut(ord('P'), [ssCtrl]),
                  TUntillPlugin(UntillApp.GetPluginByName(APP_NAME)).GetImageFileName('PreviewPdf.ico'),
                  PreviewAsPdfClick);
  end;
end;

procedure TInvoicesEmbEntityManager.PreviewClick(Sender: TObject);
var id: Int64;
begin
  with ClientDataSet do
    if (not IsEmpty) then begin
      id:= StrToInt64Def(FieldByName('id').AsString, 0);
      if id>0 then begin
        assert(UntillDB.BOUserId <> 0);
        PrintInvoice(UntillDB,
          id,
          UntillDB.BOUserId,
          _getposnow,
          FieldByName('ffrom').AsDateTime,
          FieldByName('till').AsDateTime,
          0,
          False,
          1, 0, true, 0, FMode);
      end;
    end;
end;

procedure TInvoicesEmbEntityManager.PreviewAsPdfClick(Sender: TObject);
var
  id: Int64;
  uc: TUBLConnection;
  invoiceDataPath, invoiceTemplatePath, invoicePath: string;
  fMergeId : string;
  fOut : TFileStream;
  pdfContent : TByteDynArray;
  dataContent : FileContent;
begin
  with ClientDataSet do
    if (not IsEmpty) then begin
      id:= StrToInt64Def(FieldByName('id').AsString, 0);
      if id>0 then begin
        Screen.Cursor := crHourGlass;
        try
          // XXX hardcode
          invoiceDataPath := IncludeTrailingPathDelimiter(ExtractFileDir(Application.ExeName))
              + 'invoice_data.xml';
          invoiceTemplatePath := 'invoice_template.docx';
          invoicePath := IncludeTrailingPathDelimiter(ExtractFileDir(Application.ExeName))
              + 'invoice.pdf';
          // Get invoice data as xml
          assert(UntillDB.BOUserId <> 0);
          SaveInvoiceAsXml(
            invoiceDataPath,
            UntillDB,
            id,
            UntillDB.BOUserId,
            _getposnow,
            FieldByName('ffrom').AsDateTime,
            FieldByName('till').AsDateTime,
            0,
            False,
            0, true, 0, FMode);
          // Call UBL
          uc := GetUBL(UntillDB);

          dataContent := FileContent.Create;
          PrepareFileContentForUBL(UntillDB.IsRemote, uc, invoiceDataPath, dataContent);

          fMergeId := uc.Soap.docx_merge(uc.SessionID, dataContent, invoiceTemplatePath, UBLProxyU.MergeResultType.PDF);
          try
            pdfContent := uc.Soap.files_getFileContent(uc.SessionID, fMergeId);
            fOut := TFileStream.Create(invoicePath, fmCreate);
            try
              fOut.WriteBuffer(Pointer(pdfContent)^, Length(pdfContent));
            finally
              fOut.Free;
            end;
          finally
            uc.Soap.files_deleteFile(uc.SessionID, fMergeId);
            if(dataContent.type_ = Type_.TemporaryFile) then
              uc.Soap.files_deleteFile(uc.SessionID, dataContent.id);
            dataContent.Destroy;
          end;
          // Show invoice.pdf
          ShellExecute(Application.Handle, nil, PChar(invoicePath), nil, nil, SW_SHOWNORMAL);
        finally
          Screen.Cursor:=crDefault;
        end;
      end;
    end;
end;

procedure TInvoicesEmbEntityManager.RefreshContent(Frame: TUntillFrame);
var from, till, last: IUntillDateTime;
begin
  inherited;
  with TInvoicesEmbEntityFrame(Frame) do begin
    lblClient.Caption:=FItem;
    if View.NewEntity then exit;
    if not ClientDataSet.IsEmpty then begin
      edtName.Text:=UTF8_Decode(ClientDataSet.FieldByName('cname').AsString);
      from:=GetUntillDateTime(ClientDataSet.FieldByName('ffrom').AsDateTime);
      till:=GetUntillDateTime(ClientDataSet.FieldByName('till').AsDateTime);
      last:=GetUntillDateTime(ClientDataSet.FieldByName('last_invoice').AsDateTime);
      from:=from.ToLocal;
      till:=till.ToLocal;
      last:=last.ToLocal;
      dtpFromDate.DateTime:=from.Date;
      dtpFromTime.DateTime:=from.Time;
      dtpTillDate.DateTime:=till.Date;
      dtpTillTime.DateTime:=till.Time;
      dtpLastInvFromDate.DateTime:=last.Date;
      dtpLastInvFromTime.DateTime:=last.Time;
    end;
  end;
end;

function TInvoicesEmbEntityManager.SaveContent(Frame: TUntillFrame): Boolean;
var from, till: IUntillDateTime;
    tran: IWTran;
begin
  from:=GetUntillDateTime;
  till:=GetUntillDateTime;
  with  TInvoicesEmbEntityFrame(Frame) do begin
    result:=false;
    from.Date:=dtpFromDate.DateTime;
    from.Time:=dtpFromTime.DateTime;
    till.Date:=dtpTillDate.DateTime;
    till.Time:=dtpTillTime.DateTime;
    from:=from.ToSys;
    till:=till.ToSys;
    if inherited SaveContent(Frame) = false then exit;
    SaveRecord(['id','cname','ffrom','till'],
               [ObjectId, edtName.Text, from, till]);


    tran:=UntillDB.getWTran();
    SaveNewValues(tran, StrToInt64Def(ClientDataSet.FieldByname('id').AsString, 0), from.AsDateTime, till.AsDateTime);
    tran.commit;

    result:=true;
  end;
end;

procedure TInvoicesEmbEntityManager.SaveDataset(PrintTool : IInvoiceHandler);
var
  ip: IShowProgress;
begin
  if FBProcessingClientDataset then begin
    TraceLog.WriteString('TInvoicesEmbEntityManager is buzy');
    exit;
  end;
  FBProcessingClientDataset := true;
  try
    TraceLog.WriteString('Processing invoices');
    ip:=CreateProgress;
    FPrintedInvoices := 0;
    FTotalInvoiceToPrint := 0;
    FPrintTool := PrintTool;
    ip:=CreateProgressEx(SaveDataSetEx);
  finally
    FBProcessingClientDataset := false;
  end;
  Refresh();
end;

procedure TInvoicesEmbEntityManager.PrintInvoices(MarkAsPaid: boolean; UseWordTemplate: boolean;
    ActionType: TActionType; ResetChanges: boolean = true);
var
  PrintTool : IInvoiceHandler;
  pd: TPrintDialog;
  markAsPaidDlg: TMarkInvoicePaidDialog;
  uc: TUBLConnection;
  chosenPrinter : String;
begin
  FMarkPaid := MarkAsPaid;
  FUseWordTemplate := UseWordTemplate;
  if MarkAsPaid then begin
    markAsPaidDlg:=TMarkInvoicePaidDialog.Create(nil);
    try
      if not markAsPaidDlg.Execute(Self.UntillDB, Now) then exit;
      FMarkPaidPaymentId := markAsPaidDlg.PaymentId;
      FMarkPaidTimestamp := markAsPaidDlg.DateTime;
    finally
      FreeAndNil(markAsPaidDlg);
    end;
  end;

  if ActionType = TActionType.Print then begin
    if (GetA4PrintersCount <= 0) then
      Plugin.RaiseException('This computer has no printers installed','TInvoicesEmbEntityManager.SaveDataset');
    pd:=TPrintDialog.Create(nil);
    try
      try
        pd.Copies:=Plugin.GetSetting('common','LastInvoiceCopiesCount',2);
      except
         // Ignore when operation not supported, #453262
      end;
      if not pd.Execute then exit;
      try
        FCopies:=pd.Copies;
      except
        FCopies := 1;
        // Ignore when operation not supported, #453262
      end;
      Plugin.SetSetting('common','LastInvoiceCopiesCount',FCopies);
    finally
      FreeAndNil(pd);
    end;
    chosenPrinter := Printer.Printers[Printer.PrinterIndex];
    if UseWordTemplate then
      if(IsPrintToPDF(chosenPrinter)) then
        PrintTool := TWordPdfSaver.Create(FSavePdfDialog)
      else
        PrintTool := TWordTemplatePrintTool.Create(chosenPrinter, '', '')
    else
      PrintTool := TPrintTool.Create(chosenPrinter);
  end else if ActionType = TActionType.SendEmail then begin
    uc := GetUBL(UntillDB);
    if uc.Soap.mail_checkMailerConfiguration(uc.SessionID) = false then begin
      UntillMessageDlg(Plugin.Translate('TInvoicesEmbEntityManager',
        'Mail service is not configured'), mtWarning, [mbOk], 0);
      exit;
    end;
    PrintTool := TUBLMailer.Create(FMode);
  end else begin
    if UseWordTemplate then
      PrintTool := TWordPdfSaver.Create(FSavePdfDialog)
    else
      PrintTool := THtmPdfSaver.Create(FSavePdfDialog);
  end;
  SaveDataset(PrintTool);
end;

procedure ClearNewInvInfo(db: TUntillDB; Mode: TInvoiceMode; cid: Int64);
var
  q: TIBSQLU;
  tran: IWTran;
begin
  tran:=db.getWTran();
  q:=tran.GetTempSql;
  case Mode of
    imClients: q.SQL.Text:='update clients set inv_new_from=null, inv_new_till=null, inv_new_discount=null, inv_new_discount_percent=null where id=:id';
    imSharedAccounts: q.SQL.Text:='update accounts set inv_new_from=null, inv_new_till=null, inv_new_discount=null, inv_new_discount_percent=null where id=(select id_accounts from clients where clients.id=:id)';
    else assert(false);
  end;
  q.ParamByName('id').AsInt64:=cid;
  q.ExecQuery;
  tran.commit;
end;

procedure TInvoicesEmbEntityManager.SaveDataSetEx(sp: IShowProgress);
var
  dtFromSys, dtTillSys: TDateTime;
  cid: Int64;
  cl: WideString;
  bCons: boolean;
  SharedAccEmail : String;
  InvoiceId : Int64;
begin
	FSaveErrors.Clear;
  with ClientDataSet do begin
    DisableControls;
    try
      first;
      while not eof do begin
        cl:=UTF8_Decode(FieldByName('cname').AsString);
        sp.ShowProgress(FItem+': '+cl, RecNo, RecordCount);
        if ListView.RecordSelected then begin
          assert(UntillDB.BOUserId <> 0);

          inc(FTotalInvoiceToPrint);
          try
            dtFromSys:=FieldByName('ffrom').AsDateTime;
            dtTillSys:=FieldByName('till').AsDateTime;
            cid:=StrToInt64Def(FieldByName('id').AsString, 0);
            TraceLog.WriteStringFmt('Invoice for client: %s, %d', [cl, cid], false);
            bCons := False;
            if FMode = imClients then
              bCons := ClientHasConsumptions(UntillDB, cid, dtFromSys, dtTillSys)
            else if FMode = imSharedAccounts then begin
              bCons := SharedAccHasConsumptions(UntillDB, cid, dtFromSys, dtTillSys);
              SharedAccEmail := FieldByName('account_email').AsString;
            end;

            if bCons then begin
              FSavePdfDialog.FileName := cl;
              InvoiceId := FPrintTool.HandleInvoice(UntillDB, cid, UntillDB.BOUserId, _getposnow, dtFromSys, dtTillSys,
                0,
                False,
                FCopies, 0, SharedAccEmail, false, 0, FMode);

              if InvoiceId > 0 then
                begin
                  ClearNewInvInfo(UntillDB, FMode, cid);
                  inc(FPrintedInvoices);
                  if FMarkPaid then
                    MarkInvoicePaid(UntillDB,
                      LocalDateTimeToSys(FMarkPaidTimestamp),
                      InvoiceId,
                      FMarkPaidPaymentId,
                      UntillDB.BOUserId,
                      ThisComputerName,
                      UntillDB.ServerOne,
                      FMode, False);
                end
              else
                  UntillMessageDlg(Plugin.Translate('TInvoicesEmbEntityManager',
                    'Sending emails failed. Check network settings and recipient address'), mtWarning, [mbOk], 0);
            end;
          except
          	on e: exception do begin
              ExceptionLog.WriteExceptionInfo(e, 'TInvoicesEmbEntityManager', 'Handle invoice error');
            	FSaveErrors.Add(cl, e.Message);
            end;
          end;
        end;
        next;
      end;
    finally
      EnableControls;
    end;
  end;
end;

procedure TInvoicesEmbEntityManager.SaveNewFrom(tran: IWTran;
  idClient: Int64; From: TDateTime);
var q: TIBSQLU;
begin
  q:=tran.GetTempSql;
  case FMode of
    imClients: q.SQL.Text:='update clients set inv_new_from=:inv_new_from where id=:id';
    imSharedAccounts: q.SQL.Text:='update accounts set inv_new_from=:inv_new_from where id=(select id_accounts from clients where clients.id=:id)';
  end;
  q.ParamByName('id').AsInt64:=idClient;
  q.ParamByName('inv_new_from').AsDateTime:=from;
  q.ExecQuery;
end;

procedure TInvoicesEmbEntityManager.SaveNewTill(tran: IWTran;
  idClient: Int64; Till: TDateTime);
var q: TIBSQLU;
begin
  q:=tran.GetTempSql;
  case FMode of
    imClients: q.SQL.Text:='update clients set inv_new_till=:inv_new_till where id=:id';
    imSharedAccounts: q.SQL.Text:='update accounts set inv_new_till=:inv_new_till where id=(select id_accounts from clients where clients.id=:id)';
  end;
  q.ParamByName('id').AsInt64:=idClient;
  q.ParamByName('inv_new_till').AsDateTime:=till;
  q.ExecQuery;
end;

procedure TInvoicesEmbEntityManager.SaveNewValues(tran: IWTran; idClient: Int64; From,
  Till: TDateTime);
var q: TIBSQLU;
begin
    q:=tran.GetTempSql;
    case FMode of
      imClients: q.SQL.Text:='update clients set inv_new_from=:inv_new_from, inv_new_till=:inv_new_till where id=:id';
      imSharedAccounts: q.SQL.Text:='update accounts set inv_new_from=:inv_new_from, inv_new_till=:inv_new_till where id=(select id_accounts from clients where clients.id=:id)';
    end;
    q.ParamByName('id').AsInt64:=idClient;
    q.ParamByName('inv_new_from').AsDateTime:=from;
    q.ParamByName('inv_new_till').AsDateTime:=till;
    q.ExecQuery;
end;

procedure TInvoicesEmbEntityManager.SetDaysSinceLastInvoice(
  const Value: Integer);
begin
  FDaysSinceLastInvoice := Value;
end;

function TInvoicesEmbEntityManager._getposnow: TDateTime;
begin
  if assigned(upos_) then
    result:=upos.GetPOSNow
  else
    result:=GetSysNow;
end;

class function TSharedInvoicesEmbEntityManager.GetTableName: String;
begin
  result := 'accounts';
end;

initialization
  ClassManager.RegisterClass(TInvoicesEmbEntityManager);
  ClassManager.RegisterClass(TSharedInvoicesEmbEntityManager);

end.


