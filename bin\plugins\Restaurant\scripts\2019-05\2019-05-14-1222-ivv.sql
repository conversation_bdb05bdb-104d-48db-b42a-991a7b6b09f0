create table articles_free_options (
    id u_id,
    id_articles bigint,
    id_options  bigint,
    option_number integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint articles_free_options_pk primary key (id),
    constraint articles_free_options_fk1 foreign key (id_articles) references articles(id),
    constraint articles_free_options_fk2 foreign key (id_options) references options(id)
);
commit;
grant all on articles_free_options to untilluser;
commit;
execute procedure register_sync_table_ex('articles_free_options', 'b', 1);
commit;
execute procedure register_bo_table('articles_free_options', 'id_articles', 'articles');
commit;
