create table articles_free_options_sm(
    id u_id,
    id_ARTICLES 	 	bigint,
    id_OPTION_ARTICLE	 	bigint,
    id_size_modifier_item	bigint,
    id_umc 			bigint,
    constraint articles_free_options_sm_pk primary key (id),
    constraint articles_free_options_sm_fk0 foreign key (id_ARTICLES) references ARTICLES(id),
    constraint articles_free_options_sm_fk1 foreign key (id_OPTION_ARTICLE) references OPTION_ARTICLE(id),
    constraint articles_free_options_sm_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id),
    constraint articles_free_options_sm_fk3 foreign key (id_umc) references UNITY_CONVERSION(id)
);
commit;
grant all on articles_free_options_sm to untilluser;
commit;
execute procedure register_sync_table_ex('articles_free_options_sm', 'b', 1);
commit;
execute procedure register_bo_table('articles_free_options_sm', 'id_ARTICLES', 'ARTICLES');
commit;
