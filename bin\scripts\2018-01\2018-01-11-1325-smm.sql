create table untill_user_fingerprints (
    id u_id,
    id_untill_users u_id, 
    name varchar(100),
    fpdata blob,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint untill_user_fingerprints_pk primary key (id),
    constraint untill_user_fingerprints_fk1 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on untill_user_fingerprints to untilluser;
commit;
execute procedure register_sync_table_ex('untill_user_fingerprints', 'b', 1);
commit;
execute procedure register_bo_table('untill_user_fingerprints', 'id_untill_users', 'untill_users');
commit;
