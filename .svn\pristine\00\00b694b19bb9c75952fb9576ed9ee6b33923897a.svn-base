unit JsonU;

interface
uses <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, TypIn<PERSON>, SysUtils;

type
	TUntillJsonString = class(TJSONString)
  private
    class function EscapeValue(const AValue: string): string;
  public
    constructor Create(const AValue: string); overload;
  end;

  EGsonException = class(Exception);

  {*
    Delphi Object-JSON serialization/deserialization tool
    Supported types:
      - Objects
        - Only published properties serialized
        - Constructor with no arguments required
      - Primitives
        - String
        - WideString
        - Integer
        - Currency
        - Bool
        - TDateTime
      - Arrays of primitives and obj
  *}
  TGson = class
  private
    class function ToJsonValue(Src: TObject): TJsonValue; overload;
    class function ToJsonValue(Kind: TTypeKind; Value: TValue): TJsonValue; overload;
    class function FromJsonValue(T: TRttiType; JsonValue: TJsonValue): TValue;
    class function ParseJsonObject(jv: TJsonValue; Typ: TRttiType): TObject; overload;
  public
    public class function Parse(const Json: String; Cls: TClass): TObject;
    public class function ParseArrayOfString(const Json: String): TArray<String>;
    public class function ToJson(const Src: TObject): String; overload;
    public class function ToJson(const Src: array of String): String; overload;
  end;

function IsJsonArrayOfString(const Json: String): Boolean;

implementation
uses DateUtils;

function IsJsonArrayOfString(const Json: String): Boolean;
begin
  result := false;
  try
    TGson.ParseArrayOfString(Json);
    result := true;
  except
    on e: EGsonException do;
  end;
end;

{ TUntillJsonString }

constructor TUntillJsonString.Create(const AValue: string);
begin
	inherited Create(EscapeValue(AValue));
end;

class function TUntillJsonString.EscapeValue(const AValue: string): string;
procedure AddChars(const AChars: string; var Dest: string; var AIndex: Integer); inline;
  begin
    System.Insert(AChars, Dest, AIndex);
    System.Delete(Dest, AIndex + 2, 1);
    Inc(AIndex, 2);
  end;

  procedure AddUnicodeChars(const AChars: string; var Dest: string; var AIndex: Integer); inline;
  begin
    System.Insert(AChars, Dest, AIndex);
    System.Delete(Dest, AIndex + 6, 1);
    Inc(AIndex, 6);
  end;

var
  i, ix: Integer;
  AChar: Char;
begin
  Result := AValue;
  ix := 1;
  for i := 1 to System.Length(AValue) do
  begin
    AChar :=  AValue[i];
    case AChar of
      '/', '\', '"':
      begin
        System.Insert('\', Result, ix);
        Inc(ix, 2);
      end;
      #8:  //backspace \b
      begin
        AddChars('\b', Result, ix);
      end;
      #9:
      begin
        AddChars('\t', Result, ix);
      end;
      #10:
      begin
        AddChars('\n', Result, ix);
      end;
      #12:
      begin
        AddChars('\f', Result, ix);
      end;
      #13:
      begin
        AddChars('\r', Result, ix);
      end;
      #0 .. #7, #11, #14 .. #31:
      begin
        AddUnicodeChars('\u' + IntToHex(Word(AChar), 4), Result, ix);
      end
      else
      begin
        if Word(AChar) > 127 then
        begin
          AddUnicodeChars('\u' + IntToHex(Word(AChar), 4), Result, ix);
        end
        else
        begin
          Inc(ix);
        end;
      end;
    end;
  end;
end;

{ TGson }

class function TGson.FromJsonValue(T: TRttiType;
  JsonValue: TJsonValue): TValue;
var
  ja: TJSONArray;
  jn: TJSONNumber;
  i: integer;
  arr: array of TValue;
  at: TRttiDynamicArrayType;
begin
  result := nil;
  case T.TypeKind of
    tkInt64:
      result := StrToInt64Def(JsonValue.Value, 0);
    tkInteger:
      result := StrToIntDef(JsonValue.Value, 0);
    tkString, tkUString:
      result := TValue.From(JsonValue.Value);
    tkWString:
      result := TValue.From(WideString(JsonValue.Value));
    tkFloat: begin
      if (System.TypeInfo(TDateTime) = T.Handle) then
        result := TValue.From(UnixToDateTime(StrToInt64Def(JsonValue.Value, 0)))
      else if (System.TypeInfo(TDate) = T.Handle) then
        raise EGsonException.Create('TDate serialization not supported')
      else if (System.TypeInfo(TTime) = T.Handle) then
        raise EGsonException.Create('TTime serialization not supported')
      else begin
        if not (JsonValue is TJSONNumber) then
          raise EGsonException.Create('TJSONNumber expected for property type ' + T.Name);
        jn := TJSONNumber(JsonValue);
        result := TValue.From(jn.AsDouble);
      end;
    end;
    tkEnumeration: begin
      if (System.TypeInfo(Boolean) = T.Handle) then
        result := JsonValue is TJSONTrue
      else
        raise EGsonException.Create('Unsupported enumeration')
    end;
    tkClass: begin
      if JsonValue is TJSONNull then
        result := nil
      else begin
        if not (JsonValue is TJSONObject) then
          raise EGsonException.Create('TJSONObject expected for property type ' + T.Name);
        result := ParseJsonObject(JsonValue, T);
      end;
    end;
    tkDynArray: begin
      if not (JsonValue is TJSONArray) then
        raise EGsonException.Create('TJSONArray expected for property type ' + T.Name);
      ja := TJSONArray(JsonValue);
      at := TRttiDynamicArrayType(t);
      SetLength(arr, ja.Size);
      for i:=0 to ja.Size-1 do begin
        arr[i] := FromJsonValue(at.ElementType, ja.Get(i));
      end;
      result := TValue.FromArray(at.Handle, arr);
    end;
    tkUnknown: begin
      if JsonValue is TJSONNull then
        result := nil
      else
        raise EGsonException.CreateFmt('Type %d can not be converted', [ord(T.TypeKind)]);
    end;
    else
      raise EGsonException.CreateFmt('Type %d can not be converted', [ord(T.TypeKind)]);
  end;
end;

class function TGson.Parse(const Json: String; Cls: TClass): TObject;
var
  jv: TJsonValue;
  ctx: TRttiContext;
begin
  result := nil;
  jv := TJsonObject.ParseJSONValue(Json);
  if jv = nil then
    raise EGsonException.Create('JSON parse error');
  ctx := TRttiContext.Create;
  try
    if jv is TJsonValue then
      result := ParseJsonObject(jv, ctx.GetType(Cls))
    else
      raise EGsonException.Create('Not a JSON value');
  finally
    ctx.Free;
  end;
end;

class function TGson.ParseArrayOfString(const Json: String): TArray<String>;
var
  jv, jvi: TJsonValue;
  ja: TJSONArray;
  i: integer;
  arr: TArray<String>;
begin
  jv := TJsonObject.ParseJSONValue(Json);
  if jv = nil then
    raise EGsonException.Create('JSON parse error');
  if jv is TJSONArray then begin
    ja := TJSONArray(jv);
    SetLength(arr, ja.Size);
    for i:=0 to ja.Size-1 do begin
      jvi := ja.Get(i);
      if jvi is TJsonString then
        arr[i] := TJsonString(jvi).Value
      else
        raise EGsonException.Create('Not a JSON string');
    end;
    result := arr;
  end else
      raise EGsonException.Create('Not a JSON array');
end;

class function TGson.ParseJsonObject(jv: TJsonValue; Typ: TRttiType): TObject;
var
  jo : TJSONObject;
  Prop: TRttiProperty;
  t : TRttiInstanceType;
  i: integer;
  f: TValue;
begin
  if jv is TJSONNull then
    result := nil
  else if jv is TJsonObject then begin
    jo := TJsonObject(jv);

    t:=Typ.AsInstance;
    f:= t.GetMethod('Create').Invoke(t.MetaclassType,[]);
    result := f.AsObject;
//    result := Cls.Create;
    for i:=0 to jo.Size-1 do begin
      Prop := Typ.GetProperty(jo.Get(i).JsonString.Value);
      if (Prop <> nil) and (Prop.Visibility = mvPublished) then begin
        if not Prop.IsWritable then
          raise EGsonException.CreateFmt('Unwritable property %s in class %s', [Prop.Name, Typ.Name]);
        Prop.SetValue(result, FromJsonValue(Prop.PropertyType, jo.Get(i).JsonValue));
      end;
    end;
  end else
    raise EGsonException.Create('Not a JSON object');
end;

class function TGson.ToJson(const Src: TObject): String;
begin
  if Src = nil then begin
    result := 'null';
    exit;
  end;
  result := ToJsonValue(Src).ToString;
end;

class function TGson.ToJsonValue(Src: TObject): TJsonValue;
var
  jo : TJSONObject;
  ctx: TRttiContext;
  objType, propType: TRttiType;
  Prop: TRttiProperty;
begin
  jo := TJSONObject.Create;
  result := jo;
  ctx := TRttiContext.Create;
  try
    objType := ctx.GetType(Src.ClassInfo);
    for Prop in objType.GetProperties do begin
      if Prop.Visibility = mvPublished then begin
        if not Prop.IsReadable then
          raise EGsonException.CreateFmt('Unreadable property %s in class %s', [Prop.Name, objType.Name]);
        propType := Prop.PropertyType;
        jo.AddPair(TJSONPair.Create(Prop.Name, ToJsonValue(PropType.TypeKind, Prop.GetValue(Src))))
      end;
    end;
  finally
    ctx.Free;
  end;
end;

class function TGson.ToJson(const Src: array of String): String;
var
  ja: TJSONArray;
  i: integer;
begin
  ja := TJSONArray.Create();
  for i:=0 to Length(Src)-1 do
    ja.AddElement(TJSONString.Create(TUntillJsonString.EscapeValue(Src[i])));
  result := ja.ToString;
end;

class function TGson.ToJsonValue(Kind: TTypeKind; Value: TValue): TJsonValue;
var
  valE: TValue;
  ja: TJSONArray;
  i: integer;
begin
  result := nil;
  case Kind of
    tkInt64:
      result := TJSONNumber.Create(IntToStr(Value.AsOrdinal));
    tkInteger:
      result := TJSONNumber.Create(Value.AsOrdinal);
    tkString, tkWString, tkUString:
      result := TJSONString.Create(TUntillJsonString.EscapeValue(Value.AsString));
    tkFloat: begin
      if (not Value.IsEmpty) and (System.TypeInfo(TDateTime) = Value.TypeInfo) then
        result := TJSONNumber.Create(DateTimeToUnix(Value.AsExtended))
      else if (not Value.IsEmpty) and (System.TypeInfo(TDate) = Value.TypeInfo) then
        raise EGsonException.Create('TDate serialization not supported')
      else if (not Value.IsEmpty) and (System.TypeInfo(TTime) = Value.TypeInfo) then
        raise EGsonException.Create('TTime serialization not supported')
      else
        result := TJSONNumber.Create(Value.AsExtended);
    end;
    tkEnumeration: begin
      if (not Value.IsEmpty) and (System.TypeInfo(Boolean) = Value.TypeInfo) then begin
        if Value.AsType<Boolean> = true then
          result := TJSONTrue.Create()
        else
          result := TJSONFalse.Create()
      end else
        raise EGsonException.Create('Unsupported enumeration')
    end;
    tkClass: begin
      if Value.IsEmpty then
        result := TJSONNull.Create
      else
        result := ToJsonValue(Value.AsObject);
    end;
    tkArray, tkDynArray: begin
      if Value.IsEmpty then
        result := TJSONNull.Create
      else begin
        ja := TJSONArray.Create();
        for i:=0 to Value.GetArrayLength-1 do begin
          valE := value.GetArrayElement(i);
          ja.AddElement(toJsonValue(valE.Kind, valE))
        end;
        result := ja;
      end;
    end;
    tkUnknown: begin
      if Value.IsEmpty then
        result := TJSONNull.Create;
    end;
  end;
  if result = nil then
    raise EGsonException.CreateFmt('Type %d can not be converted', [ord(Kind)]);
end;

end.
