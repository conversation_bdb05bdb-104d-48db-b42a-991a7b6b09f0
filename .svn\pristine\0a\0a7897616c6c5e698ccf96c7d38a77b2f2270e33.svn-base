unit DbManagementFirebirdRestartU;

interface

uses SysUtils, UntillDBU, DbManagementServiceU, UntillServiceU, UntillSyncU, SrvThreadU;

type

TDbManagementFirebirdRestart = class (TDbManagementTask)
protected
  FDbPort: Integer;
  FDbPath: String;
  FEmulateEListenerProblem: boolean;
  FConnected: boolean;
  FCount: Integer;
  procedure checkEListenerProblem;
  procedure   ProcessDb(db: TUntillDb); override;
end;

EListenerProblem = class(Exception)
end;


implementation

uses UntillSyncUtilsU, ResourceMonitorU, UntillLogsU, ListenerServiceU, CommonU, UntillDBIpClientU, UntillSrvU;

{ TFirebirdRestartService }

procedure TDbManagementFirebirdRestart.checkEListenerProblem();
var
dbc:TUntillDBIpClient;
r: String;
begin
  dbc := TUntillDBIpClient.Create();
  try
    try
      dbc.Connect('localhost', FDbPort);
      dbc.IsDatabaseShutdown(FDbPath, r);
      if FEmulateEListenerProblem then begin
        Inc(FCount);
        if FCount > 1 then begin
          raise EUntillDbClientProblem.Create('Simulation');
        end;
      end;
      FConnected := true;
    except
      on e:Exception do begin
        if FConnected and assigned(UntillSrvU.usrv) and (UntillSrvU.usrv.Status in [usStarted]) then begin
            UntillLogsU.ExceptionLog.WriteExceptionInfo(e, 'TDbManagementFirebirdRestart.checkEListenerProblem');
            raise EListenerProblem.Create('Error checking shutdown for ' + FDbPath);
        end;
      end;
    end;
  finally
    FreeAndNil(dbc);
  end;
end;

procedure TDbManagementFirebirdRestart.ProcessDb(db: TUntillDb);
var
rm: TResourceMonitor;
res: String;
checkFirebird: boolean;
begin
  rm := TResourceMonitor.Create;

  checkFirebird :=   Self.GetParam('common', 'NoFirebirdRestart', '1') = '0';
  FEmulateEListenerProblem :=   Self.GetParam('debug', 'EmulateEListenerProblem', '0') = '1';


  rm.addResourceWatcher(ListenerServiceU.TListenerServiceResourceWatcher.Create(true));
  rm.addResourceWatcher(ListenerServiceU.TListenerServiceResourceWatcher.Create(false));

  try
    FDbPort := CommonU.GetUntillSrvPort;
    FDbPath := db.GetDBPath;
    db.Disconnect;
    Self.Wait(5);
    while true do begin
      if checkFirebird then begin
        UntillSyncUtilsU.CheckRawLogAttempt;
      end;
      res := rm.check;
      if length(res) > 0 then begin
        TraceLog.WriteString(res);
      end;
      checkEListenerProblem();
      Self.Wait(5);
    end;
  finally
    FreeAndNil(rm);
  end;
end;

initialization
  UntillLogsU.RegisterFatalException(EListenerProblem, '');

end.
