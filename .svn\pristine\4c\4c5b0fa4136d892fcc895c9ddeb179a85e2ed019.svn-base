unit TTGCardsU;

interface
uses KeysDiversification, DesfireCardsU;

type

  TTTGCardType = (ttgNotDefined=0, ttgBillPay=1, ttgPrePay=2, ttgStaff=3, ttgAdmin=4, ttgInstall=5, ttgCleaning=6);

  TTTGDesFireFile = class(TDesFireFile)
  protected
    function loadInt32(Position: Integer): Integer;
    function loadSecureInt32(Position: Integer): Integer;
    procedure storeInt32(Position: Integer; Value: Integer);
    procedure storeSecureInt32(Position: Integer; Value: Integer);

    function loadInt16(Position: Integer): Smallint;
    function loadSecureInt16(Position: Integer): Smallint;
    procedure storeInt16(Position: Integer; Value: Smallint);
    procedure storeSecureInt16(Position: Integer; Value: Smallint);

    function loadString(Position: Integer; maxLength: Integer): AnsiString;
    procedure storeQString(Position: Integer; maxLength: Integer; Value: AnsiString);
  public
    class function getAID: Cardinal; override;
  end;



  TTtgInstallFile = class(TTTGDesFireFile)
  private
    function GetPassword: AnsiString;
    function GetUsername: AnsiString;
    procedure SetPassword(const Value: AnsiString);
    procedure SetUsername(const Value: AnsiString);
  protected
		const POSITION_USERNAME=0;
		const POSITION_PASSWORD=32;
  public
    class function getFileIndex: Byte; override;
    class function getKeyNr: Byte; override;
    class function getSize: Cardinal; override;

    property Username: AnsiString read GetUsername write SetUsername;
    property Password: AnsiString read GetPassword write SetPassword;
  end;

	TTtgDataFile = class(TTTGDesFireFile)
	private
		const CONFIGURATION_OFFSET=24;
    function GetCreationTimestamp: Integer;
    procedure SetCreationTimestamp(const Value: Integer);
    function GetVersion: Integer;
    procedure SetVersion(const Value: Integer);
    function GetExpirationTimestamp: Integer;
    procedure SetExpirationTimestamp(const Value: Integer);
    function GetValidityPeriod: Integer;
    procedure SetValidityPeriod(const Value: Integer);
    function GetValueFull: Integer;
    procedure SetValueFull(const Value: Integer);
    function GetCardType: TTTGCardType;
    procedure SetCardType(const Value: TTTGCardType);
    function GetServingsLimit: Integer;
    procedure SetServingsLimit(const Value: Integer);
    function GetServingsReactivation: Integer;
    procedure SetServingsReactivation(const Value: Integer);
    function GetPatrons: Integer;
    procedure SetPatrons(const Value: Integer);
    function GetValue: Integer;
    procedure SetValue(const Value: Integer);
    function GetServings: Integer;
    procedure SetServings(const Value: Integer);
    function GetName: AnsiString;
    procedure SetName(const Value: AnsiString);
    function GetCreationName: AnsiString;
    procedure SetCreationName(const Value: AnsiString);
		const CONFIGURATION_SIZE=160;
		const VALUE_OFFSET=0;
		const VALUE_SIZE=24;

		const CURRENT_VERSION=0;
  public
    class function getSize: Cardinal; override;
    class function getKeyNr: Byte; override;
    class function getFileIndex: Byte; override;

    constructor Create(ABuffer: TArrayOfByte); override;
    constructor Create; override;

    property CreationTimestamp: Integer read GetCreationTimestamp write SetCreationTimestamp;
    property ExpirationTimestamp: Integer read GetExpirationTimestamp write SetExpirationTimestamp;
    property ValidityPeriod: Integer read GetValidityPeriod write SetValidityPeriod;
    property Version: Integer read GetVersion write SetVersion;
    property ValueFull: Integer read GetValueFull write SetValueFull;
    property CardType: TTTGCardType read GetCardType write SetCardType;
    property ServingsLimit: Integer read GetServingsLimit write SetServingsLimit;
    property ServingsReactivation: Integer read GetServingsReactivation write SetServingsReactivation;
    property Patrons: Integer read GetPatrons write SetPatrons;
    property Value: Integer read GetValue write SetValue;
    property Servings: Integer read GetServings write SetServings;
    property Name: AnsiString read GetName write SetName;
		property CreationName: AnsiString read GetCreationName write SetCreationName;
  end;

implementation
uses SysUtils;

const
	POSITION_VALUE=0;
	POSITION_SERVINGS=12;
	POSITION_CREATION_TIMESTAMP=24;
	POSITION_EXPIRATION_TIMESTAMP=36;
	POSITION_VALIDITY_PERIOD=48;
	POSITION_VALUEFULL=60;
	POSITION_SERVINGSLIMIT=72;
	POSITION_SERVINGSREACTIVATION=84;
	POSITION_PATRONS=96;
	POSITION_VERSION=108;
	POSITION_CARDTYPE=114;
	POSITION_CREATION_NAME=120;
	POSITION_NAME=152;

{ TTTGDesFireFile }

class function TTTGDesFireFile.getAID: Cardinal;
begin
	result := $ACECC7;
end;

function TTTGDesFireFile.loadInt16(Position: Integer): Smallint;
var
    n: SmallInt;
begin
  result := 0;
  n := FBuffer[position];
  result := result or n;

  n := FBuffer[position+1];
  result := result or (n shl 8);
end;

function TTTGDesFireFile.loadInt32(Position: Integer): Integer;
var i: integer;
    n: integer;
begin
  result := 0;
  for i:=0 to 3 do begin
    n := FBuffer[position + i];
    result := result or (n shl (i*8));
  end;
end;

function TTTGDesFireFile.loadSecureInt16(Position: Integer): Smallint;
var value1, value2, value3: SmallInt;
begin
  value1 := loadInt16(position);
  value2 := not loadInt16(position+2);
  value3 := loadInt16(position+4);

  //bitwise majority decision
  result := (value1 and value2) or (value1 and value3) or (value2 and value3);
end;

function TTTGDesFireFile.loadSecureInt32(Position: Integer): Integer;
var value1, value2, value3: Integer;
begin
  value1 := loadInt32(position);
  value2 := not loadInt32(position+4);
  value3 := loadInt32(position+8);

  //bitwise majority decision
  // a b c | y
  // 0 0 0 | 0
  // 0 0 1 | 0
  // 0 1 0 | 0
  // 0 1 1 | 1
  // 1 0 0 | 0
  // 1 0 1 | 1
  // 1 1 0 | 1
  // 1 1 1 | 1
  //
  //y=(a&b)|(a&c)|(b&c)
  result := (value1 and value2) or (value1 and value3) or (value2 and value3);
end;

function TTTGDesFireFile.loadString(Position, maxLength: Integer): AnsiString;
var
		sb: TStringBuilder;
    b: byte;
    i: integer;
begin
	sb := TStringBuilder.Create;
  try
		i := 0;
    while (i < maxLength) do begin
    	b := FBuffer[position + i];
      if (b = 0) then
      	break;
      sb.Append(chr(b));
    	inc(i);
    end;
    result := AnsiString(sb.ToString);
  finally
  	sb.Free;
  end;
end;

procedure TTTGDesFireFile.storeInt16(Position: Integer; Value: Smallint);
begin
	FBuffer[position  ] := (Value    ) and $FF;
	FBuffer[position+1] := (Value shr 8)and $FF;
end;

procedure TTTGDesFireFile.storeInt32(Position, Value: Integer);
begin
	FBuffer[position  ] := (Value    ) and $FF;
	FBuffer[position+1] := (Value shr 8) and $FF;
	FBuffer[position+2] := (Value shr 16) and $FF;
	FBuffer[position+3] := (Value shr 24) and $FF;
end;

procedure TTTGDesFireFile.storeQString(Position, maxLength: Integer;
  Value: AnsiString);
var i: integer;
begin
	for i:=0 to Length(Value)-1 do begin
  	if i >= maxLength then
    	break;
    FBuffer[Position + i] := ord(Value[1+i]);
  end;
	//padding with '\0'
  for i:=Length(Value) to maxLength-1 do begin
  	FBuffer[Position + i] := 0;
  end;
end;

procedure TTTGDesFireFile.storeSecureInt16(Position: Integer; Value: Smallint);
begin
  storeInt16(position,   Value);
  storeInt16(position+2, not Value);
  storeInt16(position+4, Value);
end;

procedure TTTGDesFireFile.storeSecureInt32(Position, Value: Integer);
begin
  storeInt32(position,   Value);
  storeInt32(position+4, not Value);
  storeInt32(position+8, Value);
end;

{ TTtgData }

constructor TTtgDataFile.Create(ABuffer: TArrayOfByte);
begin
  inherited;
	SetVersion(CURRENT_VERSION);
end;

constructor TTtgDataFile.Create;
begin
  inherited;
	SetVersion(CURRENT_VERSION);
end;

function TTtgDataFile.GetCardType: TTTGCardType;
begin
	result := TTTGCardType(loadSecureInt16(POSITION_CARDTYPE));
end;

function TTtgDataFile.GetCreationName: AnsiString;
begin
	result := loadString(POSITION_CREATION_NAME,32);
end;

function TTtgDataFile.GetCreationTimestamp: Integer;
begin
  result := loadSecureInt32(POSITION_CREATION_TIMESTAMP);
end;

function TTtgDataFile.GetExpirationTimestamp: Integer;
begin
	result := loadSecureInt32(POSITION_EXPIRATION_TIMESTAMP);
end;

class function TTtgDataFile.getFileIndex: Byte;
begin
	result := 0;
end;

class function TTtgDataFile.getKeyNr: Byte;
begin
	result := 3;
end;

function TTtgDataFile.GetName: AnsiString;
begin
  result := loadString(POSITION_NAME,32);
end;

function TTtgDataFile.GetPatrons: Integer;
begin
	result := loadSecureInt32(POSITION_PATRONS);
end;

function TTtgDataFile.GetServings: Integer;
begin
  result := loadSecureInt32(POSITION_SERVINGS);
end;

function TTtgDataFile.GetServingsLimit: Integer;
begin
	result := loadSecureInt32(POSITION_SERVINGSLIMIT);
end;

function TTtgDataFile.GetServingsReactivation: Integer;
begin
  result := loadSecureInt32(POSITION_SERVINGSREACTIVATION);
end;

class function TTtgDataFile.getSize: Cardinal;
begin
	result := 512;
end;

function TTtgDataFile.GetValidityPeriod: Integer;
begin
  result := loadSecureInt32(POSITION_VALIDITY_PERIOD);
end;

function TTtgDataFile.GetValue: Integer;
begin
  result := loadSecureInt32(POSITION_VALUE);
end;

function TTtgDataFile.GetValueFull: Integer;
begin
	result := loadSecureInt32(POSITION_VALUEFULL);
end;

function TTtgDataFile.GetVersion: Integer;
begin
  result := loadSecureInt16(POSITION_VERSION);
end;

procedure TTtgDataFile.SetCardType(const Value: TTTGCardType);
begin
	storeSecureInt16(POSITION_CARDTYPE, ord(value));
end;

procedure TTtgDataFile.SetCreationName(const Value: AnsiString);
begin
  storeQString(POSITION_CREATION_NAME,32,value);
end;

procedure TTtgDataFile.SetCreationTimestamp(const Value: Integer);
begin
  storeSecureInt32(POSITION_CREATION_TIMESTAMP,value);
end;

procedure TTtgDataFile.SetExpirationTimestamp(const Value: Integer);
begin
	storeSecureInt32(POSITION_EXPIRATION_TIMESTAMP,value);
end;

procedure TTtgDataFile.SetName(const Value: AnsiString);
begin
  storeQString(POSITION_NAME,32,value);
end;

procedure TTtgDataFile.SetPatrons(const Value: Integer);
begin
	storeSecureInt32(POSITION_PATRONS,value);
end;

procedure TTtgDataFile.SetServings(const Value: Integer);
begin
  storeSecureInt32(POSITION_SERVINGS,value);
end;

procedure TTtgDataFile.SetServingsLimit(const Value: Integer);
begin
	storeSecureInt32(POSITION_SERVINGSLIMIT,value);
end;

procedure TTtgDataFile.SetServingsReactivation(const Value: Integer);
begin
  storeSecureInt32(POSITION_SERVINGSREACTIVATION,value);
end;

procedure TTtgDataFile.SetValidityPeriod(const Value: Integer);
begin
  storeSecureInt32(POSITION_VALIDITY_PERIOD,value);
end;

procedure TTtgDataFile.SetValue(const Value: Integer);
begin
  storeSecureInt32(POSITION_VALUE,value);
end;

procedure TTtgDataFile.SetValueFull(const Value: Integer);
begin
	storeSecureInt32(POSITION_VALUEFULL,value);
end;

procedure TTtgDataFile.SetVersion(const Value: Integer);
begin
  storeSecureInt16(POSITION_VERSION,value);
end;

{ TTtgInstallFile }

class function TTtgInstallFile.getFileIndex: Byte;
begin
	result := 1;
end;

class function TTtgInstallFile.getKeyNr: Byte;
begin
	result := 2;
end;

function TTtgInstallFile.GetPassword: AnsiString;
begin
	result := loadString(POSITION_PASSWORD,16);
end;

class function TTtgInstallFile.getSize: Cardinal;
begin
	result := 64;
end;

function TTtgInstallFile.GetUsername: AnsiString;
begin
	result := loadString(POSITION_USERNAME,  16);
end;

procedure TTtgInstallFile.SetPassword(const Value: AnsiString);
begin
	storeQString(POSITION_PASSWORD,16,value);
end;

procedure TTtgInstallFile.SetUsername(const Value: AnsiString);
begin
	storeQString(POSITION_USERNAME,16,value);
end;

end.
