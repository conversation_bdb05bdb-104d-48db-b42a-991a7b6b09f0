create table kitchen_screens (
    id u_id,
    ks_number integer,
    ks_name varchar(50),
    ks_type integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint ks_pk primary key (id)
);
commit;
grant all on kitchen_screens to untilluser;
commit;
execute procedure register_sync_table_ex('kitchen_screens', 'b', 1);
commit;
execute procedure register_bo_table('kitchen_screens', '', '');
commit;

create table kitchen_screen_purposes (
    id u_id,
    skp_number integer,
    skp_name varchar(50),
    skp_description varchar(100),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint ksp_pk primary key (id)
);
commit;
grant all on kitchen_screen_purposes to untilluser;
commit;
execute procedure register_sync_table_ex('kitchen_screen_purposes', 'b', 1);
commit;
execute procedure register_bo_table('kitchen_screen_purposes', '', '');
commit;

alter table articles add id_article_ksp bigint,
	add constraint articles_ksp_fk foreign key (id_article_ksp) references kitchen_screen_purposes(id);
/*
insert into kitchen_screen_purposes(skp_number, skp_name, is_active) values('1','Preparation',1);
insert into kitchen_screen_purposes(skp_number, skp_name, is_active) values('2','Information',1);
insert into kitchen_screen_purposes(skp_number, skp_name, is_active) values('3','Distribution',1);
*/
commit;

