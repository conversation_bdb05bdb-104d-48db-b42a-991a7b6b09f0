create table ks_workflow_link (
    id u_id,
    ID_KS_WORKFLOW bigint,
    ID_KS_FROM bigint,
    ID_KS_TO bigint,
    ID_untill_users bigint,
    datetime timestamp,
    constraint kswl_pk primary key (id),
	constraint kswl_FK1 foreign key (ID_KS_FROM) references kitchen_screens(id),
	constraint kswl_FK11 foreign key (ID_KS_TO) references kitchen_screens(id),
	constraint kswl_FK2 foreign key (ID_KS_WORKFLOW) references KS_WORKFLOW(id),
	constraint kswl_FK3 foreign key (ID_untill_users) references untill_users(id)
	);
commit;
execute procedure register_sync_table_ex('ks_workflow_link', 'p', 1);
commit;
grant all on ks_workflow_link to untilluser;
commit;


