create table coupon_items (
    id u_id,
    cpi_number  integer,
    id_coupons  bigint,
    barcode     varchar(1024),
    constraint coupon_items_pk primary key (id),
	constraint coupon_items_fk1 foreign key (id_coupons) references coupons(id)
);
commit;
grant all on coupon_items to untilluser;
commit;
execute procedure register_sync_table_ex('coupon_items', 'b', 1);
commit;
execute procedure register_bo_table_ex('coupon_items', 'id_coupons', 'coupons', 1);
commit;


