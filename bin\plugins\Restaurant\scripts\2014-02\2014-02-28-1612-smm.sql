create table gift_card_ops (
    id u_id,
	number integer,
	suffix varchar(3),
	pdatetime timestamp,
	pcname varchar(50),
	id_untill_users u_id,
	op_kind smallint,
	amount decimal(17,4),
	card_data blob sub_type 0 segment size 80,
    
    constraint gift_card_ops_pk primary key (id),
    constraint gift_card_ops_fk1 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on gift_card_ops to untilluser;
commit;
execute procedure register_sync_table_ex('gift_card_ops', 'p', 1);
commit;

create table gift_card_void_ops (
    id u_id,
	id_source_op u_id,
	id_void_op u_id,
    
    constraint gift_card_void_ops_pk primary key (id),
    constraint gift_card_void_ops_fk1 foreign key (id_source_op) references gift_card_ops(id),
    constraint gift_card_void_ops_fk2 foreign key (id_void_op) references gift_card_ops(id)
);
commit;
grant all on gift_card_void_ops to untilluser;
commit;
execute procedure register_sync_table_ex('gift_card_void_ops', 'p', 1);
commit;
