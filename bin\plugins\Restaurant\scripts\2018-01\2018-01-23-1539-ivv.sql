CREATE OR ALTER VIEW VIEW_TURNOVER_INCL_ROOM
AS
SELECT * FROM RDB$DATABASE; -- full view definition moved to 2022-11-30-1800-kvn-remove-ib_udf.sql
commit;
grant all on VIEW_TURNOVER_INCL_ROOM to untilluser;
commit;


CREATE OR ALTER VIEW VIEW_TURNOVER_EXCL_ROOM
AS
SELECT * FROM RDB$DATABASE; -- full view definition moved to 2022-11-30-1800-kvn-remove-ib_udf.sql
commit;
grant all on VIEW_TURNOVER_EXCL_ROOM to untilluser;
commit;

