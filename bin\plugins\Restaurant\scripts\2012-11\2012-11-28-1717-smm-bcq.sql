set term !! ;

create or alter procedure BCQ_ORDER_ITEM (
    ID_ORDER_ITEM bigint)
as
declare variable MQ integer;
declare variable MA bigint;
declare variable ID_ORDERS bigint;
declare variable ID_ARTICLES bigint;
declare variable ID_MENU bigint;
declare variable QUANTITY integer;
declare variable ID integer;
declare variable FUTURE_ORDER smallint;
begin

  select OI.ID,  OI.ID_ORDERS, OI.QUANTITY, OI.ID_MENU, ID_ARTICLES
  from ORDER_ITEM OI where oi.id=:id_order_item
  into :ID, :ID_ORDERS, :QUANTITY, :ID_MENU, :ID_ARTICLES;

  if (not (:id is null)) then begin
      -- skip if future order
      select count(*)
      from DELAY_BILLS DB, BILL, ORDERS
      where DB.ID_BILL = BILL.ID and
            BILL.ID = ORDERS.ID_BILL and
            ORDERS.ID = :ID_ORDERS
      into :FUTURE_ORDER;
    
      if (:FUTURE_ORDER = 0) then begin
        execute procedure BEVERAGE_PROCESS_ORDERITEM(:ID_ARTICLES, :ID_ORDERS, :QUANTITY);
        if (:ID_MENU > 0) then
        begin
          for select MI.ID_ARTICLES, MI.QUANTITY * :QUANTITY
              from MENU_ITEM MI
              where MI.ID_MENU = :ID_MENU
              into :MA, :MQ
          do
          begin
            execute procedure BEVERAGE_PROCESS_ORDERITEM(:MA, :ID_ORDERS, :MQ);
          end
        end
      end
  end

end
!!
commit
!!
grant execute on procedure BCQ_ORDER_ITEM to untilluser
!!
commit
!!
set term ; !!

