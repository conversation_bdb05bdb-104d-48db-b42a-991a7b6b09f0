﻿unit ArticleEntityManager;

interface

uses
  EntityManagerU, Classes, UntillDBU, ClassManagerU, RestaurantPluginU, IBSQL,
  UntillFram, TreeNodesU, SysUtils, CommonStringsU, Graphics,FixedIdU,
  RestaurantTreeFoldersU,  Contnrs, IB, BOUntillDBU, ArticlesFilterFram, UntillComboBoxU,
  ArticlePriceEmbEntityManager, BLRArticleU, UntillToolBarU, TntCompatibilityU, Menus, DB,
  UTF8U, ColorComboU, ButtonPropSelectFram, Messages, UntillGraphicElementsU, PNGImage,
  StdCtrls, ClassesU, DBRestaurantTableU, ArticleKSStageEmbEntityManager, BOCommonEntityActionsU,
  ArticleSizeUMEmbEntityManager, StockManagerU, Generics.Collections, ExtraFieldsEntityManagerU,
  QuickEditFrm;

type
  TArtSequence = record
    number       : Integer;
    pc_sequence  : Integer;
    hht_sequence : Integer;
  end;

  TArtDefFlagType = (dftColor, dftFont, dftAltFont);

  TArticleBaseEntityManager = class(TEntityManager)
  protected
    ffram: TArticleFilterFrame;
    procedure OnFilterChanged(Sender: TObject);
    procedure InserDefaultArticleData(atran: IWTran; aid_article, aid_scrg: Int64; aprefix: string);
    procedure ChangeAppearance(
      tran: IWTran; btnType: TBtnType;
      ArtId: Int64; cmbGroup: TUntillCombobox;
      ChangeColor: Boolean; cmbColor: TTntColorComboBox;
      ChangeFont: Boolean; AFont: TFont;
      ChangeFontAlt: Boolean; AFontAlt: TFont;
      ADefaultColor, ADefaultFont, ADefaultFontAlt: boolean);
    procedure UpdateArtDefFlag(
      ATran: IWTran; ABtnType: TBtnType;
      AFlagType: TArtDefFlagType; AId: Int64; AValue: boolean);
  end;

  TArticleEntityManager = class(TArticleBaseEntityManager)
  private
    FSMIPriceList : TDictionary<Int64, Currency>;
    SC        : TStockManager;
    fCurObjId : Int64;
    OldArticleSequence : TArtSequence;
    OldIDKSWF : Int64;
    FMainFrame  : TUntillFrame;
    flistview : TEMListView;
    procedure   FillFilterCombo;
    procedure   BeforeRefresh(Sender: TObject);
    procedure   AfterPriceSaveUpdate(Sender: TObject);
    function    FindArticleByName(ArticleName: String; ExceptId: Int64): Boolean;
    procedure   FixArticleSequence( Frame : TUntillFrame );

    procedure   _RefreshContent(Frame: TUntillFrame);
    procedure   _RefreshLightContent(Frame: TUntillFrame);
    function    _SaveContent(Frame: TUntillFrame): boolean;
    function    _SaveLightContent(Frame: TUntillFrame): boolean;
    procedure   _CanInsert(Frame: TUntillFrame; Update: Boolean);
    procedure   _CanInsertLight(Frame: TUntillFrame; Update: Boolean);
    function    GetDefaultCourseId: Int64;
    function    GetDefaultSalesAreaId: Int64;
    procedure CheckDeletePossible(id_articles: Int64);
    procedure GetArticleNumberSequence(id_dep : Int64; art_num: Integer; var aseq, armseq: Integer);
    function IsCourseChangeable(adb: TUntillDb; aid_course: Int64): Boolean;
    function ArticleManualWeight(Untilldb: TUntillDB; id: Int64): boolean;
    procedure CheckAllPrices(adb: TUntillDB; Frame: TUntillFrame);
    function NeedMaxArticleNum(adb: TUntilldb): boolean;
    procedure CopyArticlePOS(AFrame: TUntillFrame; ASrcArtId, ADestArtId: Int64);
  public
    constructor Create(AOwner :TComponent; AUntillDB:TBOCustomUntillDB); override;
    destructor Destroy; override;
    class procedure GetBaseClassList(AClassList: TPlaceInTreeList); override;
    class function  GetEntityFrameClass: TEntityViewFrameClass; override;
    class function  GetMassModifyFrameClass: TMassModifyFrameClass; override;
    class function GetTableName: String; override;
    class function GetNodeName: WideString; override;
    property MainFrame : TUntillFrame read FMainFrame;
    property CurObjId  : Int64 read FCurObjId write FCurObjId;
    procedure OnFilterConfirmed(Sender: TObject);
    procedure OnGPEdit( aid_smi, aid_prices : Int64; value : Currency );
    procedure OnPriceEdit( aid_smi, aid_prices : Int64; value : Currency );
    procedure OnPurchasePriceEdit( aid_smi : Int64; value : Currency );

    procedure InitializeList(Sender:TEMListView);
    procedure TranslateStrings; override;
    procedure CanInsert(Frame: TUntillFrame; Update: Boolean); override;
    function  SaveContent(Frame: TUntillFrame): Boolean; override;
    procedure GetDefaultCurrency(var IdCurrency: Int64; var CurrencyName: WideString);
    procedure SetArticlePurchasePrice(aFrame : TUntillFrame);
    procedure RefreshContent(Frame: TUntillFrame); override;

    procedure OnAfterDelete(Sender : TObject; Tran: IWTran; recordid : Int64);
    procedure CanDelete(Frame: TUntillFrame; Update: Boolean; ObjectId: Int64 = 0); override;
    procedure CanUndelete(ObjectId: Int64); override;
    procedure MassModifyRecords(Frame: TUntillFrame; RecordsIds: TObjectList); override;
    procedure CanMassModify(Frame:TUntillFrame); override;
    procedure OnSaveRecord(Sender : TObject; Tran: IWTran);
    procedure OnInsertRecord(Sender : TObject; Tran: IWTran);
    procedure SetNativePurchasePrice( Frame : TUntillFrame);

    function  GetStringRepresentation(FieldName:String; Value:Variant; Field:TField = nil):WideString; override;
    function  IsQuickEditable(FieldName: String): Boolean; override;
    procedure SaveQuickEdit(FieldName: String; Data: Variant); override;
    procedure AddNewKSStages(aObjectID : Int64; aWFTemplateID : Int64; aemStage :TArticleKSStageEmbEntityManager);
    procedure RefreshPPSizes(id_smi : Int64; aprice : Currency);
    function GetPPSizePrice(id_smi: Int64): Currency;
  end;

  TArticleExtraFieldDefKind = class (TExtraFieldDefKind)
  public
    class function GetKind(): Integer; override;
    class function GetId(): Int64; override;
    class function GetCaption(): WideString; override;
    class function IsLicensed(UntillDb:TUntillDb): Boolean; override;
  end;


function GetLikeNameString(aStr : string) : string;

procedure AddNewPrice(
  adb : TUntillDB; idp: int64; pricename: WideString;
  emArticlePrices: TArticlePriceEmbEntityManager; ObjectId: Int64
);

implementation

uses
  ArticleMassModifyFram, CommonU, EmbEntityManagerU, ArticleOptionEmbEntityManager,
  ArticleAvailEmbEntityManager, ArticleNotifyEmbEntityManager, ArticleFreeOptionEmbEntityManager,
  Controls, ProgressFrm, DataControlsU, MassModifyFram, Math, DateUtils, ArticleDiscountEmbEntityManager,
  DepartmentEntityManager, EntityFram, Variants, StrUtils, DialogsU, DBEntityListFram,
  DailyStockU, ArticleLightEntityFram, ArticleEntityFram, ThreadObserverU, MassModifyU,
  FoodGroupEntityManager, Windows, Dialogs, CurrencyU, ArticleKSNotifyEmbEntityManager, JournalLogsU,
  UntillEquU, UntillAppU, LangU, HelpConsts, RestaurantBOU, RestaurantJLogU,
  RestaurantCommonStringsU, ArticleGrossPriceEmbEntityManager, PermsProviderU,
  KernelSettingsU;

{ TArticleEntityManager }

var
  STR_Article :WideString;
  STR_Number :WideString;
  STR_Name :WideString;
  StrWithTheSameNumberAlreadyExists :WideString;

procedure TArticleEntityManager.BeforeRefresh(Sender: TObject);
var ind, depind, catind: integer;
    strName : string;
begin
  if not assigned(ffram) then exit;

  FSMIPriceList.Clear;
  strName := SafeSQLParam(ffram.cmbName.Text);
  ind:=ListParams.IndexOfCondition('department.id =');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  ind:=ListParams.IndexOfCondition('(lower(articles.name) like ');
  if ind > -1 then
    ListParams.DeleteCondition(ind);

  depind:=ffram.cmbDepartment.ItemIndex;
  ind:=ListParams.IndexOfCondition('department.id  =');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if depind > 0 then
    ListParams.AddCondition('department.id  = '+IntToStr(TInt64(ffram.cmbDepartment.Items.Objects[depind]).Value));

  ind:=ListParams.IndexOfCondition('not exists(select id from article_available');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  ind:=ListParams.IndexOfCondition('exists(select id from article_available');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if ffram.rbShowNotLinked.checked then
    ListParams.AddCondition(
      'not exists(select id from article_available where article_available.id_articles = articles.id and article_available.is_active = 1)'
    );
  if ffram.rbShowLinked.checked then
    ListParams.AddCondition(
      'exists(select id from article_available where article_available.id_articles = articles.id and article_available.is_active = 1)'
    );

  ind:=ListParams.IndexOfCondition('food_group.id_category  =');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  catind:=ffram.cmbCategory.ItemIndex;
  if catind > 0 then
    ListParams.AddCondition('food_group.id_category  = '+IntToStr(TInt64(ffram.cmbCategory.Items.Objects[catind]).Value));
  if ffram.cmbName.Text <> '' then
    ListParams.AddCondition(GetLikeNameString(strName));
  Plugin.SetInterfaceSetting('ArticleFilter','NameFilter',UTF8_Encode(ffram.cmbName.Text));
  if depind > 0 then
    Plugin.SetInterfaceSetting('ArticleFilter','DepartmentFilter',IntToStr(TInt64(ffram.cmbDepartment.Items.Objects[depind]).Value))
  else
    Plugin.SetInterfaceSetting('ArticleFilter','DepartmentFilter','');
  if catind > 0 then
    Plugin.SetInterfaceSetting('ArticleFilter','CategoryFilter',IntToStr(TInt64(ffram.cmbCategory.Items.Objects[catind]).Value))
  else
    Plugin.SetInterfaceSetting('ArticleFilter','CategoryFilter','');
end;

procedure TArticleEntityManager.CanDelete;
begin
  inherited;
  if (ObjectId=ID_ARTICLES_WORK_IN) or (ObjectId=ID_ARTICLES_WORK_OUT) then
    plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this article') );

  CheckDeletePossible(ObjectID);
end;

procedure TArticleEntityManager.CheckDeletePossible(id_articles : Int64);
var iq : IIBSQL;
begin

  iq := UntillDB.GetPreparedIIbSql('select party_article from articles where id=:id_articles');
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
      plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this article. It was imported from a 3rd party system') );

  iq := UntillDB.GetPreparedIIbSql('select count(*) from order_item '
     + ' join orders on orders.id=order_item.id_orders '
     + ' join bill on bill.id=orders.id_bill '
     + ' where bill.close_datetime is null and order_item.id_articles=:id_articles');
  iq.q.Params[0].AsInt64 := id_articles;
  iq.ExecQuery;
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
     plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this article. It is ordered and the table is not closed yet') );

  iq := UntillDB.GetPreparedIIbSql('select count(*) from bill ' +
      'where bill.close_datetime is null and id_time_article=:id_articles');
  iq.q.Params[0].AsInt64 := id_articles;
  iq.ExecQuery;
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
     plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this time article. It is ordered and the table is not closed yet') );

  iq := UntillDB.GetPreparedIIbSql('select count(*) from INVENTORY_ITEM '
     + ' join articles on articles.ID_INVENTORY_ITEM = INVENTORY_ITEM.id and INVENTORY_ITEM.is_active=1'
     + ' where articles.ID =:ID');
  iq.q.Params[0].AsInt64 := id_articles;
  iq.ExecQuery;
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
     plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this article. '
      + 'It is linked linked to stock ingredient') );
end;

procedure TArticleEntityManager.CanInsert(Frame: TUntillFrame; Update: Boolean);
begin
  if not IsUntillLightMode then
    _CanInsert(Frame, Update)
  else
    _CanInsertLight(Frame, Update)
end;

procedure TArticleEntityManager.CanMassModify(Frame:TUntillFrame);
begin
  inherited;
  with TArticleMassModifyFrame(Frame) do begin
    if chbChangeDepartment.Checked then
      CheckUsbOnEmpty(usbNewDepartment, tsGeneral, strDep);
    if chbChangeCourse.Checked then
      CheckUsbOnEmpty(usbNewCourse, tsGeneral, strCourse);
    if chbChangePromotion.Checked then
      CheckUsbOnEmpty(usbNewPromotion, tsOptions, Plugin.Translate('ArticleEntityManager','Promotion'));
    if chbChangeComission.Checked then
      CheckUsbOnEmpty(usbNewComission, tsOptions, Plugin.Translate('ArticleEntityManager','Commission'));
    if chkPuaGroup.Checked then
      CheckUsbOnEmpty(usbPuaGroup, tsOptions, Plugin.Translate('ArticleEntityManager','Upsellers'));
  end;
end;

constructor TArticleEntityManager.Create(AOwner: TComponent; AUntillDB: TBOCustomUntillDB);
begin
  inherited;
  IDFieldName :='id';
  OldIDKSWF   := 0;
  LoadIcon(Plugin.GetImageFileName('Article.ico'));
  InitializeListProc:=InitializeList;
  DialogMode:=true;
//  Options := Options  + [emoMultipleSelection] + [emoSkipRefreshAfterEdit];
  Options := Options  + [emoMultipleSelection];
  BeforeRefreshList:=BeforeRefresh;

  AfterUpdateRecord := OnSaveRecord;
  AfterDeleteRecord := OnAfterDelete;
  AfterInsertRecord := OnInsertRecord;
  HelpContext := HelpConsts.ARTICLES_ID;

  FMainFrame  := nil;
  SC := TStockManager.Create(nil, Untilldb, ThreadObserverU.ThreadObserver.RefreshMainThread);
  FSMIPriceList := TDictionary<Int64, Currency>.Create;
end;

destructor TArticleEntityManager.Destroy;
begin
  FreeAndNil( SC );
  FreeAndNil( FSMIPriceList );
  inherited;
end;

procedure TArticleEntityManager.FillFilterCombo;
var iq: IIBSQL;
begin
  assert(assigned(ffram));
  ffram.cmbDepartment.ClearWithObjects;
  iq:=UntillDB.GetIIbSql;
  iq.SetText('select id, name from department where is_active=1 order by name');
  iq.ExecQuery;
  ffram.cmbDepartment.AddItem(Plugin.Translate('ArticleEntityManager','All departments'), nil);
  while not iq.Eof do begin
    ffram.cmbDepartment.AddItem(UTF8_Decode(iq.FieldByName('name').AsString), TInt64.Create(iq.FieldByName('id').AsInt64));
    iq.Next;
  end;
  iq:=UntillDB.GetIIbSql;
  iq.SetText('select id, name from category where is_active=1 order by name');
  iq.ExecQuery;
  ffram.cmbCategory.AddItem(Plugin.Translate('ArticleEntityManager','All categories'), nil);
  while not iq.Eof do begin
    ffram.cmbCategory.AddItem(UTF8_Decode(iq.FieldByName('name').AsString), TInt64.Create(iq.FieldByName('id').AsInt64));
    iq.Next;
  end;
end;

class procedure TArticleEntityManager.GetBaseClassList(AClassList: TPlaceInTreeList);
begin
  with AClassList.AddItem do begin
    BaseClass := TProductTreeFolder;
    if not IsUntillLightMode then begin
      InsertClass := TDepartmentEntityManager;
      PlaceInTreeType := ptAfter;
    end else begin
      InsertClass := TFoodGroupEntityManager;
      PlaceInTreeType := ptAfter;
    end;
  end;
end;

procedure TArticleEntityManager.GetDefaultCurrency(var IdCurrency: Int64; var CurrencyName: WideString);
var
  q : IIBSQL;
begin
  IdCurrency:=0;
  q:= UntillDB.GetPreparedIIbSql('select currency.ID, currency.name from currency inner join system_vars on currency.code = system_vars.varvalue order by currency.ID');
  q.ExecQuery;
  if not q.Eof then begin
    CurrencyName := UTF8_Decode(q.fieldByname('name').AsString);
    IdCurrency   := StrToInt64Def(q.fieldByname('id').AsString, 0);
  end else
    Plugin.RaiseException('Default currency is not defined');
end;

class function TArticleEntityManager.GetEntityFrameClass: TEntityViewFrameClass;
begin
  if not IsUntillLightMode then
    Result:= TArticleEntityFrame
  else
    Result:= TArticleLightEntityFrame;
end;

function GetLikeNameString(aStr: string): string;
begin
  result := '(lower(articles.name) like lower(''%' + lowercase(aStr) + '%'') '
      + ' or exists(select * from ARTICLE_BARCODES where ARTICLE_BARCODES.id_articles=articles.id '
      + ' and lower(barcode) like lower(''%' + lowercase(aStr) + '%'')))'
end;

class function TArticleEntityManager.GetMassModifyFrameClass: TMassModifyFrameClass;
begin
  result:=TArticleMassModifyFrame;
end;

class function TArticleEntityManager.GetNodeName: WideString;
begin
  result:=Plugin.Translate('ArticleEntityManager', 'Articles');
end;

function TArticleEntityManager.GetStringRepresentation(FieldName: String;
  Value: Variant; Field: TField): WideString;
var fn : String;
begin
  fn:=lowercase(FieldName);
  if (LeftStr(fn, 5) = 'price') and (trim(ClientDataset.fieldByName('sm_name').asString)<>'') then
    result := CurrToStr(0)
  else
    result := inherited GetStringRepresentation(FieldName, Value, Field);
end;

class function TArticleEntityManager.GetTableName: String;
begin
  result:='articles'
end;

procedure TArticleEntityManager.InitializeList(Sender: TEMListView);
var fr:TDBEntityListFrame;
  q:IIBSQL;
  tn,fn:String;
  id:Int64;
  i: integer;
begin
  flistview := Sender;
  with Sender do begin
    AddFieldHeader(CommonStringsU.StrNumberCaption, dtWideString, 15, 'article_number', true,false,taCenter);
    AddFieldHeader(STR_Name, dtWideString, 30, 'name', true);
    AddFieldHeader(strDep, dtWideString, 20, 'dname', true ); // warning before delete or rename: garbage disables this field by name
    AddFieldHeader(Plugin.Translate('ArticleEntityManager','Group'),
      dtWideString, 20, 'gname', true ); // warning before delete or rename: garbage disables this field by name
    AddFieldHeader(Plugin.Translate('ArticleEntityManager','Category'),
      dtWideString, 20, 'catname', true); // warning before delete or rename: garbage disables this field by name
    AddFieldHeader('ID',dtWideString, 0, 'id', false);
    AddFieldHeader('sequence',dtinteger, 0, 'seq', false);
    AddFieldHeader('rmsequence',dtinteger, 0, 'rmseq', false);
    AddFieldHeader('id_extra_field_values',dtWideString,0,'id_extra_field_values',false);

    if not IsUntillLightMode then
      AddToolButton(lbtnNew);
    AddToolButton(lbtnDuplicate);
    AddToolButton(lbtnEdit);
    AddToolButton(lbtnDelete);
    AddToolButton(lbtnRefresh);
    AddToolButton(lbtnDefault);
    AddToolButton(lbtnFilter);
    AddToolButton(lbtnMassModify);

    SetListParams(['articles.ID ID', 'article_number','articles.name name',
                'department.name dname','food_group.name gname',
                'articles.daily_stock daily_stock', 'articles.time_active time_active', 'articles.article_hash article_hash',
                'case when articles.id_inventory_item is null and articles.id_recipe is null then 0 else 1 end on_stock ',
                'coalesce(articles.show_in_kitchen_screen,0) show_in_kitchen_screen',
                'category.name catname','articles.sequence seq',
                'articles.rm_sequence rmseq, coalesce(DAILYSTOCK_BALANCE.quantity,0) daily_stock_balance',
                'articles.ask_course ask_course','article_manual', 'WEIGHT_REQUIRED',
                'SIZE_MODIFIER.sm_name', 'id_extra_field_values'],'ID');

    q:=UntillDB.GetIIbSql;
    q.SetText('select id, name from prices where is_active=1');
    q.ExecQuery;
    while not q.Eof do begin
      tn:='aprices'+IntToStr(q.FieldByName('id').AsInt64);
      fn:='price'+IntToStr(q.FieldByName('id').AsInt64);
      ListParams.AddField(tn+'.price '+fn);
      ListParams.AddJoin('left outer join article_prices '+tn+' on articles.id = '+tn+'.id_articles and '+tn+'.id_prices = '+IntToStr(q.FieldByName('id').AsInt64)+' and ' + tn + '.is_active=1');
      AddFieldHeader(q.FieldByname('name').AsString, dtCurrency, 10, fn, true);
      q.next;
    end;
    AddFieldHeader(Plugin.Translate('ClientEntityManager','DS balance'),dtInteger,10, 'daily_stock_balance',true);
    if not IsUntillLightMode then begin
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Stock'),dtCheckbox,10,'on_stock',true);
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Kitchen screen'),dtCheckbox,10,'show_in_kitchen_screen',true);
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Manual price'),dtCheckbox,10,'article_manual',true);
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Weight'),dtCheckbox,10,'WEIGHT_REQUIRED',true);
    end else begin
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Daily Stock'),dtCheckbox,10, 'daily_stock',true);
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Time Article'),dtCheckbox,10,'time_active',true);
      AddFieldHeader(Plugin.Translate('ClientEntityManager','Hash'),dtCheckbox,10,'article_hash',true);
    end;
    AddFieldHeader(strSizeModifier,dtWideString,15, 'sm_name',true);

    ListParams.AddTable('department');
    ListParams.AddTable('food_group');
    ListParams.AddTable('category');
    ListParams.AddTable('articles');
    ListParams.AddCondition('articles.id_departament = department.ID');
    ListParams.AddCondition('department.id_food_group = food_group.id');
    ListParams.AddCondition('food_group.id_category = category.id');
    ListParams.AddCondition('coalesce(promo,0)=0');
    ListParams.AddJoin('left outer join DAILYSTOCK_BALANCE on DAILYSTOCK_BALANCE.id_articles=articles.id');
    ListParams.AddJoin('left outer join SIZE_MODIFIER on articles.id_SIZE_MODIFIER=SIZE_MODIFIER.id and SIZE_MODIFIER.is_active=1');

    fr:=TDBEntityListFrame(ListView.Frame);

    if fr.FindComponent('ArticleFilterFrame') = nil then
      ffram:=TArticleFilterFrame.Create(fr);

    ffram.name := 'ArticleFilterFrame';
    fr.panTop.Height:=ffram.Height;
    ffram.Parent:=fr.panTop;
    ffram.Align:=alClient;
    ffram.OnFilterConfirmed := OnFilterConfirmed;
    ffram.OnFilterChanged   := OnFilterChanged;
    FillFilterCombo;
    ffram.cmbName.Text:=UTF8_Decode(Plugin.GetInterfaceSetting('ArticleFilter','NameFilter',''));
    id:=StrToInt64Def(Plugin.GetInterfaceSetting('ArticleFilter','DepartmentFilter',''), 0);
    if (id > 0) then begin
      for i:=1 to ffram.cmbDepartment.Items.Count-1 do
        if TInt64(ffram.cmbDepartment.Items.Objects[i]).Value = id then begin
          ffram.cmbDepartment.ItemIndex:=i;
          break;
        end;
    end;
  end;
end;

function TArticleEntityManager.IsQuickEditable(FieldName: String): Boolean;
var fn: string;
begin
  fn:=lowercase(FieldName);
  result:=false;
  if (LeftStr(fn, 5) = 'price') and (trim(ClientDataset.fieldByName('sm_name').asString)='') then result:=true;
  if fn = 'name' then result:=true;
end;

function TArticleEntityManager.ArticleManualWeight(Untilldb : TUntillDB; id : Int64) : boolean;
var q : IIBSQL;
begin
  result := true;
  if id<=0 then exit;
  q := untilldb.GetPreparedIIbSql('select WEIGHT_REQUIRED, ARTICLE_MANUAL from articles where id=:id');
  q.q.ParamByName('id').AsInt64 := id;
  q.ExecQuery;
  if q.eof then exit;

  result := (q.Fields[0].asInteger = 1) or (q.Fields[1].asInteger = 1);
end;

procedure TArticleEntityManager.MassModifyRecords(Frame: TUntillFrame; RecordsIds: TObjectList);
var
  i,j : integer;
  id, id_prices: int64;
  fields: array of string;
  params: array of TVarRec;
  idDep, idCour: int64;
  idPUAGroup, idsm, idSupplier, idPromo, idCommiss, idVDGroup, idFreeOption : int64;
  pf: TProgressForm;
  ws:WideString;
  q: TIBSQLU;
  tran    : IWTran;

  function AddField(name:string):integer;
  begin
    SetLength(fields, Length(fields)+1);
    fields[Length(fields)-1]:=name;
    SetLength(params, Length(params)+1);
    result:=Length(params)-1;
  end;

  procedure  SaveEmbeddedMgr(id: Int64; em:TEmbeddedEntityManager; {ControlIdField:String; }
    ControlEmClass:TEmbeddedManagerClass; addItems : TEditModeItem; id_entity_field : string);
  var em_old:TEmbeddedEntityManager;
      deleteList : TStringList;
      i : Integer;
      bCond : boolean;
      idx_num    : Integer;
  begin
    em.ParentId:=id;

    idx_num := 0;
    if em is TArticleOptionEmbEntityManager then  begin
      idx_num := GetFirstAvailNumberDb(em.UntillDB, em.GetTableName, 'option_number', 'is_active=1 and id_articles='+IntToStr(id));
      if idx_num=0 then idx_num := 1;
    end;

    with em.ClientDataSet do begin
      try
        DisableControls;
        {-- initialize parent id --}
        em.ClientDataSet.IndexName:='';
        em.ClientDataSet.first;
        while not eof do begin
          em.ClientDataSet.edit;
          em.ClientDataSet.FieldByName(em.idFieldName).AsString:=IntToStr(GenerateId);
          em.ClientDataSet.FieldByName('id_articles').AsString:=IntToStr(id);
          if em is TArticleOptionEmbEntityManager then begin
            em.ClientDataSet.FieldByName('option_number').AsInteger := idx_num;
            Inc(idx_num);
          end;
          em.ClientDataSet.post;
          em.ClientDataSet.next;
        end;

        {-- clear old records --}
        em_old:=ControlEmClass.Create(Self, Self, id);
        try
          em_old.Prepare;
          em_old.ListView.RefreshList;
          if (id_entity_field <> '') and (addItems <> emiChange) then begin
            deleteList := TStringList.Create;
            try
              First;
              while not eof do begin
                em_old.ClientDataSet.first;
                while not em_old.ClientDataSet.eof do begin
                  bCond := (em_old.ClientDataSet.FieldByName(id_entity_field).asString=FieldByName(id_entity_field).asString);
                  if bCond then
                    deleteList.Add(em_old.ClientDataSet.FieldByName(IDFieldName).asString);
                  em_old.ClientDataSet.next;
                end;
                Next;
              end;
              if deleteList.Count>0 then
                for i := 0 to Pred(deleteList.count) do
                  em_old.DeleteEntity(StrToInt64Def(deleteList[i],0));
            finally
              FreeAndNil( deleteList );
            end;
          end else
            em_old.DeleteAll;
          em_old.SaveDataset;
        finally
          FreeAndNil(em_old);
        end;

        if (addItems in [emiAdd, emiChange]) or (id_entity_field='') then begin
          em.SaveDataset(false);
        end;

      finally
        EnableControls;
      end;
    end;
  end;

  var edtMode : TEditModeItem;
begin
  inherited;
  with TArticleMassModifyFrame(Frame) do begin
    pf:=TProgressForm.Create(Self);
    pf.show;
    try
      if NothingToUpdate then exit;

      {--Department--}
      if (chbChangeDepartment.Checked) then begin
        idDep:=usbNewDepartment.Value;
        with params[AddField('id_departament')] do begin
          VInt64:=@idDep;
          VType:=vtInt64;
        end;
      end;
      {--Course--}
      if (chbChangeCourse.Checked) then begin
        idCour:=usbNewCourse.Value;
        with params[AddField('id_courses')] do begin
          VInt64:=@idCour;
          VType:=vtInt64;
        end;
      end;
      {--Size modifier--}
      if (chkSizeModifier.Checked) then begin
        for i:=0 to RecordsIds.Count-1 do begin
          id:=TInt64(RecordsIds.Items[i]).Value;
          if ArticleManualWeight(Untilldb, id) then
            plugin.RaiseError('You can''t assign size groups to weight required and manual priced articles');
        end;
        idsm:=usbSizeModifier.Value;
        with params[AddField('id_size_modifier')] do begin
          VInt64:=@idsm;
          VType:=vtInt64;
        end;
      end;
      {--Promotion--}
      if (chbChangePromotion.Checked) then begin
        idPromo:=usbNewPromotion.Value;
        with params[AddField('id_promotions')] do begin
          VInt64:=@idPromo;
          VType:=vtInt64;
        end;
      end;
      {--Free options--}
      if (chbFreeOption.Checked) then begin
        idFreeOption:=usbFreeOption.Value;
        with params[AddField('id_free_option')] do begin
          VInt64:=@idFreeOption;
          VType:=vtInt64;
        end;
      end;
      {--PUA group--}
      if (chkPUAGroup.Checked) then begin
        idPUAGroup:=usbPUAGroup.Value;
        with params[AddField('id_pua_groups')] do begin
          VInt64:=@idPUAGroup;
          VType:=vtInt64;
        end;
      end;
      {--Commission--}
      if (chbChangeComission.Checked) then begin
        idCommiss:=usbNewComission.Value;
        with params[AddField('id_commission')] do begin
          VInt64:=@idCommiss;
          VType:=vtInt64;
        end;
      end;
      {--Save points--}
      if (chbChangeSavePoints.Checked) then
        with params[AddField('savepoints')] do begin
          VInteger:=useNewSavePoints.Value;
          VType:=vtInteger;
      end;
      if (chbSupplier.Checked) then begin
        idSupplier:=usbSupplier.Value;
        with params[AddField('id_suppliers')] do begin
          VInt64:=@idSupplier;
          VType:=vtInt64;
        end;
      end;
      {--Prices Hide in reports--}
      if (chbChangePrices.Checked) and(rbnSetToZero.Checked) then
        with params[AddField('SENSITIVE')] do begin
          if chbHideInReports.Checked then
            VInteger:=1
          else
            VInteger:=0;
          VType:=vtInteger;
        end;

      {--VanDuijnen--}
      if (chbVanDuijnen.Checked) then begin
        if rbnVDUseGroup.Checked then
          idVDGroup:=usbVDGroup.Value
        else
          idVDGroup:=0;
        with params[AddField('id_vd_group')] do begin
          VInt64:=@idVDGroup;
          VType:=vtInt64;
        end;
        with params[AddField('plu_number_vanduijnen')] do begin
          VInteger:=0;
          VType:=vtInteger;
        end;
      end;

      {--Ask course--}
      if chkMMAskCourse.checked then
        with params[AddField('ask_course')] do begin
          VInteger:=Integer(chkAskCourseValue.checked);
          VType:=vtInteger;
        end;

      {--Auto size modify--}
      if chkAutoSM.checked then
        with params[AddField('auto_sm')] do begin
          VInteger:=Integer(chkAskAutoSM.checked);
          VType:=vtInteger;
        end;

      {--HHT default settings--}
      with params[AddField('hht_default_color')] do begin
        VInteger:=Integer(chbHHTDefaultColor.checked);
        VType:=vtInteger;
      end;
      with params[AddField('hht_default_font')] do begin
        VInteger:=Integer(chbHHTDefaultFont.checked);
        VType:=vtInteger;
      end;
      with params[AddField('hht_default_alt_font')] do begin
        VInteger:=Integer(chbHHTDefaultAltFont.Checked);
        VType:=vtInteger;
      end;
      {--OMan default setting--}
      with params[AddField('oman_default_color')] do begin
        VInteger:=Integer(chbOManDefaultColor.checked);
        VType:=vtInteger;
      end;
      with params[AddField('oman_default_font')] do begin
        VInteger:=Integer(chbOManDefaultFont.checked);
        VType:=vtInteger;
      end;
      with params[AddField('oman_default_alt_font')] do begin
        VInteger:=Integer(chbOManDefaultAltFont.checked);
        VType:=vtInteger;
      end;
      {--Stock control--}
      if (chbStockControl.Checked) then begin
        with params[AddField('daily_stock')] do begin
          VInteger:=Integer(chbStockControlEnabled.Checked);
          VType:=vtInteger;
        end;
        with params[AddField('warning_level')] do begin
          VInteger:=useWarningLevel.Value;
          VType:=vtInteger;
        end;
      end;
      if (chbKitchenFollowScreen.Checked) then begin
        with params[AddField('show_in_kitchen_screen')] do begin
          VInteger:=Integer(chbEnableKS.Checked);
          VType:=vtInteger;
        end;
        with params[AddField('prep_min')] do begin
          VInteger:=edtPrepMin.value;
          VType:=vtInteger;
        end;
        with params[AddField('warn_min')] do begin
          VInteger:=edtKSWarning.value;
          VType:=vtInteger;
        end;
      end;
      if (chbAddFGOptions.Checked) or (chbRemoveFGOptions.Checked) then begin
        with params[AddField('SINGLE_FREE_OPTION')] do begin
          if chkSingleFreeOption.Checked then
            VInteger:=1
          else
            VInteger:=0;
          VType:=vtInteger;
        end;
      end;

      ws:=Plugin.Translate('ArticleEntityManager', 'Processing record: %d of %d','Mass modifying progress');
      for i:=0 to RecordsIds.Count-1 do begin
        pf.RefreshProgress(round((i/RecordsIds.Count)*100),
          WideFormat(ws,[i, RecordsIds.Count]));

        id:=TInt64(RecordsIds.Items[i]).Value;
        {--Main fields--}
        if length(params)>0 then SQLUpdate(id, fields, params);
        {--PC Settings--}
        if (chbChangePCAppearance.Checked) then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnPC,
            id, cmbPCAppGroup,
            chbChangePCColor.Checked, cmbNewPCColor,
            chbChangePCFont.Checked, NewPCFont,
            chbChangePCAltFont.Checked, NewPCAltFont,
            false, false, false
          );
          tran.commit;
        end;
        {--HHT Settings--}
        if chbChangeHHTAppearance.Checked then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnHHT,
            id, cmbHHTAppGroup,
            chbChangeHHTColor.Checked, cmbHHTColor,
            chbChangeHHTFont.Checked, NewHHTFont,
            chbChangeHHTAltFont.Checked, NewHHTAltFont,
            chbHHTDefaultColor.Checked, chbHHTDefaultFont.Checked, chbHHTDefaultAltFont.Checked
          );
          tran.commit;
        end;
        {--Orderman Settings--}
        if chbChangeOManAppearance.Checked then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnOman,
            id, cmbOmanAppGroup,
            chbChangeOmanColor.Checked, cmbOmanColor,
            chbChangeOmanFont.Checked, NewOmanFont,
            chbChangeOmanAltFont.Checked, NewOmanAltFont,
            chbOmanDefaultColor.Checked, chbOmanDefaultFont.Checked, chbOmanDefaultAltFont.Checked
          );
          tran.commit;
        end;
        {--Available--}
        if chbAddAvailable.Checked or chbRemoveAvailable.Checked or chbChangeAvailable.Checked then begin
          if chbAddAvailable.Checked then
            edtMode := emiAdd
          else if chbChangeAvailable.Checked then
            edtMode := emiChange
          else
            edtMode := emiRemove;
          SaveEmbeddedMgr(id, emArticleAvail, TArticleAvailEmbEntityManager, edtMode, 'id_sales_area');
        end;
        {--Notify--}
        if chbAddNotify.Checked or chbRemoveNotify.Checked or chbChangeNotify.Checked then begin
          if chbAddNotify.Checked then
            edtMode := emiAdd
          else if chbChangeNotify.Checked then
            edtMode := emiChange
          else
            edtMode := emiRemove;
          SaveEmbeddedMgr(id, emArticleNotify, TArticleNotifyEmbEntityManager, edtMode, 'id_preparation_area');
        end;
        {--Prices--}
        emArticleModifyPrices.ParentId:=id;
        if (chbChangePrices.Checked) then begin
          if (rbnModify.Checked) then
            emArticleModifyPrices.SaveDataset
          else if (rbnSetToZero.Checked) then
            emArticleModifyPrices.ClearPrices;
        end;
        {--Options--}
        if (chbAddOptions.Checked) or chbRemoveOptions.Checked or chbChangeOptions.Checked then begin
          if chbAddOptions.Checked then
            edtMode := emiAdd
          else if chbChangeOptions.Checked then
            edtMode := emiChange
          else
            edtMode := emiRemove;
          SaveEmbeddedMgr(id, emArticleOption, TArticleOptionEmbEntityManager, edtMode, 'id_options');
        end;
        {--Free Option groups--}
        if (chbAddFGOptions.Checked) or (chbRemoveFGOptions.Checked) or chbChangeFGOptions.Checked then begin
          if chbAddFGOptions.Checked then
            edtMode := emiAdd
          else if chbChangeFGOptions.Checked then
            edtMode := emiChange
          else
            edtMode := emiRemove;
          SaveEmbeddedMgr(id, emArticleFGOption, TArticleFreeOptionEmbEntityManager, edtMode, 'ID_options');
        end;

        {--Kicthen screen params--}
        if (chbKitchenFollowScreen.Checked) then SaveEmbeddedMgr(id, emKSN, TArticleKSNotifyEmbEntityManager, emiAdd, '');

        {-- delete prices --}
        if rbnDeletePrices.Checked then begin
          for j:=0 to lbDeletePrices.Count-1 do
            if lbDeletePrices.Checked[j] then begin
              id_prices:=TInt64(lbDeletePrices.Items.Objects[j]).Value;
              tran:=UntillDB.getWTran();

              q:=tran.GetTempSql;
              q.SQL.Text:='delete from ARTICLE_PRICE_PERIODS '
              + ' where ID_ARTICLE_PRICES in '
              + ' (select id from article_prices where id_articles=:ida and id_prices=:idp)';
              q.ParamByName('ida').AsInt64 := id;
              q.ParamByName('idp').AsInt64 := id_prices;
              q.ExecQuery;

              q:=tran.GetTempSql;
              q.SQL.Text:='delete from article_prices where id_articles=:ida and id_prices=:idp';
              q.ParamByName('ida').AsInt64 := id;
              q.ParamByName('idp').AsInt64 := id_prices;
              q.ExecQuery;

              q:=tran.GetTempSql;
              q.SQL.Text:='delete from article_discount_prices where id_prices=:idp and id_article_discount in (select id from article_discount where id_articles=:ida)';
              q.ParamByName('ida').AsInt64 := id;
              q.ParamByName('idp').AsInt64 := id_prices;
              q.ExecQuery;

              q:=tran.GetTempSql;
              q.SQL.Text:='delete from option_article_prices where id_prices=:idp and id_option_article in (select id from option_article where id_articles=:ida)';
              q.ParamByName('ida').AsInt64 := id;
              q.ParamByName('idp').AsInt64 := id_prices;
              q.ExecQuery;

              tran.commit;
            end;
        end;
        // Make dummy Article table update
        tran:=UntillDB.getWTran();
        DBRestaurantTableU.DummyUpdateDBArticle(tran, id);
        tran.commit;
      end;
    finally
      FreeAndNil(pf);
    end;
  end;
end;

procedure TArticleEntityManager.OnSaveRecord(Sender: TObject;
  Tran: IWTran);
begin
  DBRestaurantTableU.DummyUpdateDBArticle(tran,fCurObjId);
end;

procedure TArticleEntityManager.RefreshContent(Frame: TUntillFrame);
begin
  fCurObjId := 0;
  if IsUntillLightMode then
    _RefreshLightContent(Frame)
  else begin
    _RefreshContent(Frame);
    FMainFrame := Frame;
  end;
end;

procedure TArticleEntityManager.RefreshPPSizes(id_smi: Int64; aprice: Currency);
begin
  if id_smi = 0 then exit;
  FSMIPriceList.AddOrSetValue(id_smi, aprice);
end;

function TArticleEntityManager.GetPPSizePrice(id_smi: Int64) : Currency;
var pp : Currency;
begin
  result := 0;
  if FSMIPriceList.TryGetValue(id_smi, pp) then
    result := pp;
end;

function TArticleEntityManager.SaveContent(Frame: TUntillFrame): Boolean;
var iq : IIBSQL;
begin
  iq := UntillDB.GetPreparedIIbSql('select party_article from articles where id=:id');
  if not iq.Eof then begin
    if iq.q.Fields[0].asInteger>0 then
      plugin.RaiseException('Article cannot be modified, it was imported by a 3rd party system');
  end;

  if IsUntillLightMode then
    result := _SaveLightContent(Frame)
  else
    result := _SaveContent(Frame);
end;

procedure TArticleEntityManager.SaveQuickEdit(FieldName: String; Data: Variant);
var idPrice, idArticle: int64;
    q:TIBSQLU;
    tran: IWTran;
    emArtPrices: TArticlePriceEmbEntityManager;
    newId: Int64;
    idCur: Int64;
    CurName: WideString;
    OldArticleName: String;
    NameList      : TLabelCaptionList;
begin
  inherited;
  OldArticleName := ClientDataSet.fieldByName('name').asString;

  if LeftStr(lowercase(FieldName), 5)='price' then begin
    idPrice:=StrToInt64Def(RightStr(FieldName, Length(FieldName)-5), 0);
    idArticle:=StrToInt64Def(ClientDataSet.fieldByName(IDFieldName).asString, 0);

    if SQLCheckExists(['id_articles','id_prices'],[idArticle, idPrice],'','article_prices') then begin
      tran := UntillDB.getWTran;
      q := tran.GetTempSql;
      q.SQL.Text:='update article_prices set price=:price where id_articles=:id_articles and id_prices=:id_prices';
      q.ParamByName('price').AsCurrency:=Data;
      q.ParamByName('id_articles').AsInt64:=idArticle;
      q.ParamByName('id_prices').AsInt64:=idPrice;
      q.ExecQuery;
      // Update Article in order to refresh articles on screens
      DBRestaurantTableU.DummyUpdateDBArticle(tran, idArticle);
      tran.commit;

      JournalLog.Log(UntillDB, NewJDetails(jeBackoffice, TRJDSubtype.BACKOFFICE_ARTICLE_PRICE_CHANGED, UntillDB.BOUserId)
      		.AddData(TRJDetails.KEY_ARTICLE_NAME, OldArticleName)
          .AddData(TRJDetails.KEY_PRICE_NAME, GetpriceName(UntillDB, IntToStr(idPrice)))
          .AddOldNew(TRJDetails.KEY_PRICE, CurrToStr(ClientDataSet.fieldByName(FieldName).AsCurrency, FmtSettings), CurrToStr(Data, FmtSettings))
          );

    end else begin
      emArtPrices:=TArticlePriceEmbEntityManager.Create(Self, Self, idArticle, arptPrice);
      try
        emArtPrices.AfterPriceSave := AfterPriceSaveUpdate;
        GetDefaultCurrency(idCur, CurName);
        newId:=GenerateId;
        emArtPrices.Prepare;
        emArtPrices.Refresh;
        emArtPrices.AddRecord(['id','id_prices','id_currency','id_articles','price','vat','vat_sign','cname'],
          [newId, idPrice, idCur, idArticle, Currency(Data), nil, nil, CurName]);
        emArtPrices.EditEntity(newId);
        if emArtPrices.LastDialogResult = mrOk then begin
          emArtPrices.SaveDataset;

          JournalLog.Log(UntillDB, NewJDetails(jeBackoffice, TRJDSubtype.BACKOFFICE_ARTICLE_PRICE_ADDED, UntillDB.BOUserId)
              .AddData(TRJDetails.KEY_ARTICLE_NAME, OldArticleName)
              .AddData(TRJDetails.KEY_PRICE_NAME, GetpriceName(UntillDB, IntToStr(idPrice)))
              .AddData(TRJDetails.KEY_PRICE, CurrToStr(Data, FmtSettings))
              );

        end;
      finally
        FreeAndNil(emArtPrices);
      end;
    end;
    exit;
  end;
  if lowercase(FieldName) = 'name' then begin

    if Trim(String(Data)) = '' then
      Plugin.RaiseError(Plugin.Translate('ArticleEntityManager', 'Article name cannot be empty'));

    idArticle:=StrToInt64Def(ClientDataSet.fieldByName(IDFieldName).asString, 0);
    if FindArticleByName(String(Data), idArticle) then
      Plugin.RaiseError(Plugin.Translate('ArticleEntityManager', 'Article with the same name already exists'));

    NameList   := nil;
    try
      NameList   := TLabelCaptionList.Create;

      if NameList.Value[DEFAULT_LANG_ID]='' then
        NameList.Value[DEFAULT_LANG_ID] := String(Data);
      NameList.Value[UntillApp.GetLang] := String(Data);

      tran := UntillDB.getWTran;
      q := tran.GetTempSql;
      q.SQL.Text:='update articles set name=:name, internal_name=:name, '
        + ' pc_text=:name, rm_text=:name, oman_text=:name where id=:id';
      q.ParamByName('id').AsInt64:=idArticle;
      q.ParamByName('name').AsString:=String(Data);
      q.ExecQuery;
    finally
      FreeAndNil(NameList);
    end;
    DBRestaurantTableU.DummyUpdateDBArticle(tran, idArticle);
    tran.commit;
    JournalLog.Log(UntillDB, NewJDetails(jeBackoffice, TRJDSubtype.BACKOFFICE_ARTICLE_RENAMED, UntillDB.BOUserId)
    		.AddOldNew(TRJDetails.KEY_ARTICLE_NAME, OldArticleName, Data));
  end;
end;

procedure TArticleEntityManager.SetNativePurchasePrice(Frame : TUntillFrame);
begin
  with TArticleEntityFrame(Frame) do begin
    edtPurchasePrice.Enabled := true;
    edtPurchasePrice.value   := edtPurcahsePriceHidden.value;
  end;
end;

procedure TArticleEntityManager.TranslateStrings;
begin
  STR_Article := ArticleNameName;
  STR_Name := CommonStringsU.StrNameCaption;
  STR_Number := CommonStringsU.StrNumberCaption;
  StrWithTheSameNumberAlreadyExists := CommonStringsU.StrWithTheSameNumberAlreadyExists;
  inherited;
end;

procedure TArticleEntityManager.AfterPriceSaveUpdate(Sender: TObject);
begin
  DBRestaurantTableU.DummyUpdateDBArticle(Untilldb, FCurObjId);
end;

function GetFieldMLName(MS : TMemoryStream; lang_id : String) : String;
var NameList : TLabelCaptionList;
    mlname  : String;
begin
  MS.Seek(0,0);
  NameList := nil;
  try
    if ms.Size>0 then begin
      NameList := TLabelCaptionList.Create;
      try
        ms.Seek(0,0);
        NameList.LoadFromStream(ms);
        mlname := NameList.Value[lang_id];
        if trim(mlname) <> '' then
          result := mlname;
      except
      end;
    end;
  finally
    FreeAndNil(NameList);
  end;
end;

function TArticleEntityManager.FindArticleByName(
  ArticleName: String; ExceptId: Int64): Boolean;
var iq     : iibsql;
    name   : String;
begin
  result := false;
  iq := UntillDB.GetIIbSql;
  iq.SetText('select name from articles where is_active=1 and id<>:id');
  iq.q.Params[0].AsInt64 := ExceptId;
  iq.ExecQuery;
  while not iq.eof do begin
    name := iq.q.fields[0].AsString;
    if sameText(name, ArticleName) then begin
       result := true;
       exit;
    end;
    iq.Next;
  end;
end;

procedure TArticleEntityManager.FixArticleSequence(Frame : TUntillFrame);
var newseq, newrmseq : Integer;
    newNum : Integer;
    id_dep : Int64;
begin
  // user changed number - we need to change sequence also: Default sorting articles in POS
  // offsxp.sigma-soft.ru/sigma/#!450516
  newNum := TArticleEntityFrame(Frame).edtNumber.Value;
  id_dep := TArticleEntityFrame(Frame).usbDep.value;
  if OldArticleSequence.number <> newNum then begin
    GetArticleNumberSequence(id_dep,newNum, newseq, newrmseq);
    if (newseq > 0) and (newrmseq > 0) then begin
      TArticleEntityFrame(Frame).Sequence   := newseq;
      TArticleEntityFrame(Frame).RMSequence := newrmseq;
    end;
  end;
end;

procedure TArticleEntityManager.GetArticleNumberSequence(id_dep : Int64;
  art_num : Integer; var aseq : Integer; var armseq : Integer);
var iq : IIBSQL;
begin
  aseq    := 0;
  armseq  := 0;
  iq := UntillDB.GetPreparedIIbSql('select first 1 ARTICLE_NUMBER, SEQUENCE, RM_SEQUENCE from ARTICLES '
    + ' where ARTICLE_NUMBER <= :art_num and is_active=1 and ID_DEPARTAMENT=:ID_DEPARTAMENT'
    + ' order by ARTICLE_NUMBER desc');
  iq.q.ParamByName('art_num').asInteger      := art_num;
  iq.q.ParamByName('ID_DEPARTAMENT').asInt64 := id_dep;
  iq.ExecQuery;
  if iq.eof then exit;

  if iq.q.FieldByName('ARTICLE_NUMBER').asInteger = art_num then begin
  // article with new number is found - replace
    aseq    := iq.q.fields[1].asInteger;
    armseq  := iq.q.fields[2].asInteger;
  end else if iq.q.FieldByName('ARTICLE_NUMBER').asInteger < art_num then begin
    aseq    := iq.q.fields[1].asInteger + 1;
    armseq  := iq.q.fields[2].asInteger + 1;
  end;
end;

procedure TArticleEntityManager.AddNewKSStages(aObjectID : Int64;
  aWFTemplateID : Int64; aemStage :TArticleKSStageEmbEntityManager);
var qSel    : IIBSQL;
    NeedAdd : boolean;
    stagePrepMin, stagePrepSec, stageWarnMin : Integer;
    stageItemID, stageID   : Int64;
    stageName : String;
    stageNumber : Integer;
begin
  qSel:= Untilldb.GetPreparedIIbSql('select KS_WF_TEMPLATE_ITEM.id ID_KS_WF, KS_WF_TEMPLATE_ITEM.item_number, '
    + ' KS_WF_TEMPLATE_ITEM.ID_STAGE_TEMPLATE,  KS_WF_STAGE_TEMPLATE.* from KS_WF_STAGE_TEMPLATE '
    + ' join KS_WF_TEMPLATE_ITEM on KS_WF_TEMPLATE_ITEM.ID_STAGE_TEMPLATE = KS_WF_STAGE_TEMPLATE.ID and KS_WF_TEMPLATE_ITEM.is_active=1'
    + ' where KS_WF_STAGE_TEMPLATE.is_active=1 and KS_WF_TEMPLATE_ITEM.ID_KS_WF_TEMPLATE=:id');
  qSel.q.ParamByName('id').asInt64 := aWFTemplateID;
  qSel.ExecQuery;
  while not qSel.Eof do begin
    NeedAdd := True;
    stageItemID  := StrToInt64Def(qSel.q.FieldByName('ID_KS_WF').asString,0);
    stageID      := StrToInt64Def(qSel.q.FieldByName('ID_STAGE_TEMPLATE').asString,0);
    stageName    := qSel.q.FieldByName('Name').asString;
    stageNumber  := qSel.q.FieldByName('item_number').asInteger;
    stagePrepMin := qSel.q.FieldByName('PREP_TIME_MIN').asInteger;
    stagePrepSec := qSel.q.FieldByName('PREP_TIME_SEC').asInteger;
    stageWarnMin := qSel.q.FieldByName('WARNING_MIN').asInteger;
    aemStage.ClientDataSet.First;
    while not aemStage.ClientDataSet.eof do begin
       if StrToInt64Def(aemStage.ClientDataSet.FieldByName('ID_KS_WF_TEMPLATE_ITEM').asString,0) = stageItemID then begin
         NeedAdd := False;
         Break;
       end;
       aemStage.ClientDataSet.Next;
    end;

    if NeedAdd then
      aemStage.AddRecord(['id', 'ID_ARTICLES', 'ID_KS_WF_TEMPLATE_ITEM', 'item_number',
            'PREP_TIME_MIN', 'PREP_TIME_SEC','WARNING_MIN', 's_name', 's_id', 'is_active'],
             [GenerateID, aObjectID, stageItemID, stageNumber, stagePrepMin, stagePrepSec,
              stageWarnMin, stageName, stageId, 1]);
    qSel.q.Next;
  end;
end;

procedure AddNewPrice(adb : TUntillDB; idp: int64; pricename: WideString;
  emArticlePrices: TArticlePriceEmbEntityManager; ObjectId: Int64);
var q : IIBSQL;
    ResAdd :Boolean;
begin
  if idp  = 0 then begin
     q:= adb.GetPreparedIIbSql('select currency.ID, currency.name from currency, system_vars where currency.code = system_vars.varvalue and system_vars.varname=''Currency'' order by currency.ID');
     try
       q.ExecQuery;
     except
      on E : EIBInterbaseError do Plugin.RaiseException('Please specify default currency');
     end;
     if not q.Eof then begin
        pricename := UTF8_Decode(q.q.fieldByname('name').AsString);
        idp       := StrToInt64Def(q.q.fieldByname('id').AsString, 0);
     end else begin
       q.q.Close;
       q:= adb.GetPreparedIIbSql('select ID, name from currency where is_active=1 order by ID');
       q.ExecQuery;
       if not q.Eof then begin
         pricename := UTF8_Decode(q.q.fieldByname('name').AsString);
         idp       := StrToInt64Def(q.q.fieldByname('id').AsString, 0);
       end;
       q.q.Close;
     end;
  end;

  if idp = 0 then exit;
  if not emArticlePrices.ClientDataset.Active then exit; // Read disallowed -> emArticlePrices was not inited

    q:= adb.GetPreparedIIbSql('select ID, name from prices where is_active=1 order by id');
    q.ExecQuery;
    while not q.Eof do begin
        ResAdd := True;
        emArticlePrices.ClientDataSet.First;
        while not emArticlePrices.ClientDataSet.eof do begin
           if SameText(emArticlePrices.ClientDataSet.FieldByName('pname').asString, q.q.FieldByName('name').asString) then begin
             ResAdd := False;
             Break;
           end;
           emArticlePrices.ClientDataSet.Next;
        end;

        if ResAdd then
        emArticlePrices.AddRecord(['id', 'id_prices', 'pname',
                'price', 'id_currency','cname','vat',
                'id_articles', 'use_group_vat'],
                 [adb.GetID, StrToInt64Def(q.q.fieldByname('id').AsString, 0),
                 UTF8_Decode(q.q.fieldByname('name').AsString),
                 0, idp, pricename, 0,
                 ObjectID, 1]);
       q.q.Next;
    end;
end;

procedure TArticleEntityManager.CheckAllPrices(adb : TUntillDB; Frame: TUntillFrame);
var q : IIBSQL;
    idp :Int64;
    pricename :WideString;
begin
  q:= aDB.GetPreparedIIBSQL('select currency.ID, currency.name from currency inner join system_vars ' +
    ' on currency.code = system_vars.varvalue order by currency.ID');
  q.ExecQuery;
  if not q.Eof then begin
    pricename := q.fieldByname('name').AsString;
    idp       := StrToInt64Def(q.fieldByname('id').AsString, 0);
    AddNewPrice(adb, idp, pricename, TArticleEntityFrame(Frame).emArticlePrices, TArticleEntityFrame(Frame).ObjectId);
    AddNewPrice(adb, idp, pricename, TArticleEntityFrame(Frame).emArticleGP, TArticleEntityFrame(Frame).ObjectId);
  end;
end;

procedure TArticleEntityManager.SetArticlePurchasePrice(aFrame : TUntillFrame);
begin
  with TArticleEntityFrame(aFrame) do begin
    if usbUM.Value > 0 then begin
      edtPurchasePrice.value := edtPurcahsePriceHidden.value;
      if rbInventory.checked then
        edtPurchasePrice.value := SC.GetArticlePurchasePriceOnFly( ObjectId, 1, usbInv.value, UsbUM.value)
      else if rbRecipe.checked then
        edtPurchasePrice.value := SC.GetArticlePurchasePriceOnFly( ObjectId, 2, usbRecipe.value, UsbUM.value);
    end;
  end;
end;

procedure TArticleEntityManager._RefreshContent(Frame: TUntillFrame);
var q : IIBSQL;
  Num, seq, rmseq : Integer;
  i: integer;
  MS          : TMemoryStream;
  bOwnPPrice  : boolean;
begin
  inherited;
  with TArticleEntityFrame(Frame) do begin
    pcView.TabIndex :=0;
    rbPriceDay.checked := true;
    edtOneLimit.value := 0;
    rbNone.checked := true;
    DailyStockChanged := False;
    if NewEntity then begin
      chkEmpty.Checked := false;
      PCButtonPropFram.ImBitmap.visible := false;
      PCButtonPropFram.cbPCColor.Selected  := clBtnFace;
      HHTButtonPropFram.cbPCColor.Selected := clWhite;
      OManButtonPropFram.cbPCColor.Selected := clBlack;
      edtName.Text:=Plugin.Translate('ArticleEntityManager', 'New article');
    end;
    if NewEntity or Duplicating then begin
      HHTButtonPropFram.IsDefaultColor := true;
      HHTButtonPropFram.IsDefaultFont := true;
      HHTButtonPropFram.IsDefaultAltFont := true;
      OManButtonPropFram.IsDefaultColor := true;
      OManButtonPropFram.IsDefaultFont := true;
      OManButtonPropFram.IsDefaultAltFont := true;
    end;
    if Duplicating then begin
      PCButtonPropFram.cmbScreenGroups.Enabled := false;
      HHTButtonPropFram.cmbScreenGroups.Enabled := false;
      OManButtonPropFram.cmbScreenGroups.Enabled := false;
    end;
    CheckAllPrices(UntillDB, Frame);

    if not NewEntity then begin
      q := BOCommonEntityActionsU.GetArticleButtonPropQuery( UntillDB, ObjectId );
      fCurObjId := ObjectId;
      if not q.eof then begin
        chkIgnoreSPTotal.checked :=q.FieldByName('exclude_sp_total').AsInteger = 1;
        chkBlockDiscount.Checked := q.FieldByName('block_discount').AsInteger <> 0;
        chkOmitTPAPI.Checked := q.FieldByName('OmitTPAPI').AsInteger <> 0;
        chkBlockTransfer.Checked := q.FieldByName('block_transfer').AsInteger <> 0;
        chkAskCourse.Checked := q.FieldByName('ask_course').AsInteger <> 0;
        chkAutoOnHold.Checked := q.FieldByName('auto_onhold').AsInteger <> 0;
        chbCreditDebit.Checked := q.FieldByName('bc_debitcredit').AsInteger <> 0;
        chbWeightRequired.Checked := q.FieldByName('weight_required').AsInteger <> 0;
        chbConditionCheckInOrder.Checked := q.FieldByName('condition_check_in_order').AsInteger <> 0 ;
        chkBlockResetQuestion.Checked := q.FieldByName('auto_resetcourse').AsInteger = 1 ;
        chkSkipProduction.Checked := q.FieldByName('ks_skip_production').AsInteger = 1 ;
        chkSingleFreeOption.Checked := q.FieldByName('single_free_option').AsInteger = 1 ;
        edtDelaySeparateTimeFrame.Value := q.FieldByName('delay_separate_mins').AsInteger;
        chkAllergen.Checked  := q.FieldByName('allergen').AsInteger = 1;
        rbPriceDay.checked   := (q.FieldByName('rent_price_type').AsInteger=0);
        chkfastlane.Checked  := q.FieldByName('fastlane').AsInteger = 1;
        rbPriceHour.checked  := not rbPriceDay.checked;
        edtName.Text         := q.FieldByName('name').AsString;
        chkAutoSM.Checked    := (q.FieldByName('auto_sm').AsInteger=1);
        chkExcludePU.Checked := (q.FieldByName('exclude_pu_overall').AsInteger=1);
        chkAllergenClick(chkAllergen);

        edtName.Enabled  := not IsSalesData(UntillDB, ObjectId);
        usbDep.Enabled   := edtName.Enabled;

        if edtName.Enabled then
          edtName.Enabled  := q.FieldByName('pos_article_type').AsInteger = Ord(patRegular);

        edtPopUpInfo.Text:=UTF8_Decode(q.FieldByName('popup_info').AsString);
        edtInternal.Text:=UTF8_Decode(q.FieldByName('INTERNAL_NAME').AsString);
        dtpCreationDate.DateTime := q.FieldByName('creation_date').AsDatetime;
        PCButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('PC_TEXT').AsString);
        HHTButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('RM_TEXT').AsString);
        OManButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('oman_text').AsString);
        chbDisabled.Checked := q.FieldByName('is_active').AsInteger = 0;
        edtOneLimit.value := q.FieldByName('one_hand_limit').AsInteger;
        usbSP.value := q.FieldByName('surface_point').AsInteger;
        edtAYCETime.value := q.FieldByName('ayce_mins').AsInteger;
        edtAYCETimeAdd.value := q.FieldByName('ayce_mins_add').AsInteger;
        chbKitchenFollowScreen.Checked := q.FieldByName('show_in_kitchen_screen').AsInteger <> 0;
        chkMustCombine.Checked := q.FieldByName('must_combined').AsInteger <> 0;
        chkMustCombineclick(chkMustCombine);
        chkDefaultOptions.Checked := q.FieldByName('has_default_options').AsInteger <> 0;
        chkDefaultOptionsclick(chkDefaultOptions);
        chbKitchenFollowScreenClick(chbKitchenFollowScreen);
        rbSingleWF.Checked := q.FieldByName('ks_wf_type').AsInteger = 0;
        if rbSingleWF.Checked then
          rbSingleWFClick(rbSingleWF)
        else begin
          rbMultiWF.Checked := true;
          rbMultiWFClick(rbMultiWF);
        end;
        edtPrepMin.value :=q.FieldByName('prep_min').AsInteger;
        edtPrepSec.value :=q.FieldByName('prep_sec').AsInteger;
        edtKSWarning.value :=q.FieldByName('warn_min').AsInteger;
        usbFree.Value:=q.FieldByName('free_after_pay').AsInteger;
        edtNumber.Value :=q.FieldByName('article_number').AsInteger;
        Sequence:=q.FieldByName('sequence').AsInteger;
        RmSequence:=q.FieldByName('rm_sequence').AsInteger;

        OldArticleSequence.number       := edtNumber.Value;
        OldArticleSequence.pc_sequence  := Sequence;
        OldArticleSequence.hht_sequence := RmSequence;

        HHTButtonPropFram.IsDefaultColor := q.FieldByName('hht_default_color').AsInteger=1;
        HHTButtonPropFram.IsDefaultFont := q.FieldByName('hht_default_font').AsInteger=1;
        HHTButtonPropFram.IsDefaultAltFont := q.FieldByName('hht_default_alt_font').AsInteger=1;

        OManButtonPropFram.IsDefaultColor := q.FieldByName('oman_default_color').AsInteger=1;
        OManButtonPropFram.IsDefaultFont := q.FieldByName('oman_default_font').AsInteger=1;
        OManButtonPropFram.IsDefaultAltFont := q.FieldByName('oman_default_alt_font').AsInteger=1;

        chkManual.Checked :=( q.FieldByName('article_manual').AsInteger <> 0 );
        if not chkManual.checked then begin
          rbMainPriceSale.Checked :=( q.FieldByName('main_price').AsInteger = 0 );
          rbMainPricePurchase.Checked :=( q.FieldByName('main_price').AsInteger = 1 );
        end;
        emArticlePrices.mainPrice     := 0;
        if rbMainPricePurchase.checked then
          emArticlePrices.mainPrice     := 1;
        chkSurcharge.Visible := chkManual.Checked;
        chkSurcharge.Checked :=( q.FieldByName('surcharge').AsInteger <> 0 );
        chkHash.Checked :=( q.FieldByName('article_hash').AsInteger <> 0 );
        chkHashClick(Self);
        chkSavePoints.Checked :=( q.FieldByName('can_savepoints').AsInteger <> 0 );
        edtConsQty.Value :=q.FieldByName('consolidate_quantity').AsInteger;
        edtConsAlias.Text :=UTF8_Decode(q.FieldByName('consolidate_alias_name').AsString);
        edtHqId.Text :=UTF8_Decode(q.FieldByName('hq_id').AsString);
        edtExternalId.Text := q.FieldByName('external_id').AsString;
        FillUsb(usbFreeOption, q.FieldByName('id_free_option').AsInt64, emOptions, 'name');
        FillUsb(usbSizeModifier, q.FieldByName('id_size_modifier').AsInt64, emSizeModifier, 'sm_name');
        usbSizeModifierChange(usbSizeModifier);
        chkEmpty.Checked := q.FieldByName('empty_article').AsInteger <> 0;
        FillUsb(usbKSWF, q.FieldByName('id_ks_wf').AsInt64, emKSWF, 'name');
        chkPUDevice.Checked := not q.FieldByName('id_ksc').IsNull;
        chkSingleInCourse.checked := q.FieldByName('ks_single_item').AsInteger <> 0;
        FillUsb(usbKSC, q.FieldByName('id_ksc').AsInt64, embKSC, 'KSC_NAME');
        OldIDKSWF := usbKSWF.value;

        edtNumeric1.value := SimpleRoundTo(q.FieldByName('daily_numeric_1').AsDouble,-4);
        edtNumeric2.value := SimpleRoundTo(q.FieldByName('daily_numeric_2').AsDouble,-4);
        with usbCourse do begin
          Value:=StrToInt64Def(q.fieldByname('id_courses').AsString, 0);
          if Value<>0 then
            Text:=emCourse.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbDep do begin
          Value:=StrToInt64Def(q.fieldByname('id_departament').AsString, 0);
          if Value<>0 then
            Text:=emDepartment.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbRentalGroup do begin
          Value:=StrToInt64Def(q.fieldByname('id_rental_group').AsString, 0);
          if Value<>0 then
            Text:=emRentalGroup.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbGroup do begin
          Value:=StrToInt64Def(q.fieldByname('id_food_group').AsString, 0);
          if Value<>0 then
            Text:=emGroup.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbAgeGroup do begin
          Value:=StrToInt64Def(q.fieldByname('id_age_groups').AsString, 0);
          if Value<>0 then
            Text:=emAgeGroup.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbPUAGroups do begin
          Value:=StrToInt64Def(q.fieldByname('id_pua_groups').AsString, 0);
          if Value<>0 then
            Text:=emPUAGroups.GetWideStringById('name',Value)
          else
            Text:=''
        end;

        BOCommonEntityActionsU.RefreshItemPCButtonpropFram(PCButtonPropFram, ObjectId, q);
        with HHTButtonPropFram do begin
          RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
          pbUsedFont.Text      := pnlButton.Font.Name;
        end;
        with OManButtonPropFram do begin
          RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
          pbUsedFont.Text      := pnlButton.Font.Name;
        end;
        PCButtonPropFram.ResizeButton;
        HHTButtonPropFram.ResizeButton;
        OManButtonPropFram.ResizeButton;
        RefreshFonts;

        edtPurchasePrice.value := q.FieldByName('purchase_price').AsCurrency;
        edtPurcahsePriceHidden.value := q.FieldByName('purchase_price').AsCurrency;
        chbDailyStock.Checked:=( q.FieldByName('daily_stock').AsInteger <> 0 );
        edtwarning.value := q.FieldByName('warning_level').AsInteger;
        with usbCommission do begin
          Value:=StrToInt64Def(q.fieldByname('id_commission').AsString, 0);
          if Value<>0 then
            Text:=emCommission.GetWideStringById('name',Value)
          else
            Text:=''
        end;
        with usbPromotion do begin
          Value:=StrToInt64Def(q.fieldByname('id_promotions').AsString, 0);
          if Value<>0 then
            Text:=emPromotion.GetWideStringById('name',Value)
          else
           Text:=''
        end;
        edtsavepoints.Value := q.fieldByname('savepoints').AsInteger;
        edtDSP.Value := q.fieldByname('decrease_savepoints').AsInteger;
        edtquantity.Value := q.FieldByName('qty').asInteger;
        edtTimeAfter.Value := q.FieldByName('AfterMin').asInteger;
        sePeriodMin.Value := q.FieldByName('PeriodMin').asInteger;
        seTimeRound.Value :=  q.FieldByName('RoundMin').asInteger;
        if Duplicating then begin
          emArticleBarcodes.DeleteAll;
          emRentUnits.DeleteAll;
          useVDNumber.Value := 0;
        end else begin
          useVDNumber.Value := q.FieldByName('plu_number_vanduijnen').AsInteger;
          FillUsb(usbVDGroup, q.FieldByName('id_vd_group').AsInt64, emVDGroups, 'name');
          rbnUseNotUsed.Checked := true;
        end;
        rbnUseGroup.Checked := usbVDGroup.Value > 0;
        rbnUseNumber.Checked := useVDNumber.Value > 0;
        if sePeriodMin.Value > 0 then begin
          rbArtTime.Checked := true;
          rbArtTimeClick( rbArtTime );
        end else if ( edtAYCETime.Value + edtAYCETimeAdd.Value ) > 0  then begin
          rbArtAYCE.Checked := true;
          rbArtAYCEClick( rbArtAYCE );
          chkAYCETimeAdd.checked := (edtAYCETimeAdd.Value) > 0;
          chkAYCETimeAddClick( chkAYCETimeAdd );
        end;

        useStandardDosage.Value := q.fieldByname('bc_standard_dosage').asInteger;
        useAlternativeDosage.Value := q.fieldByname('bc_alternative_dosage').asInteger;
        FillUsb(usbBeCoGroup, q.fieldByname('id_beco_group').asInt64, emBecoGroups, 'name');
        FillUsb(edtRentPeriod, q.fieldByname('id_rent_periods').asInt64, emRentPeriod,'name');
        emRentPrice.id_rent_period := edtRentPeriod.Value;
        emRentPrice.ListView.RefreshList;

        chbBcDisableBalance.Checked := q.fieldByname('bc_disablebalance').AsShort<>0;
        chbUseLocations.Checked := q.fieldByname('bc_use_locations').AsShort<>0;
        chkActive.Checked := (q.FieldByName('time_active').asInteger <> 0) or ((edtAYCETime.Value + edtAYCETimeAdd.Value) > 0);

        UpdateKSWFEntity;
        if assigned(emKSWFStage) then
          AddNewKSStages(ObjectID, usbKSWF.Value, emKSWFStage);

        rbNone.Checked :=( q.FieldByName('article_type').AsInteger = 0 );
        rbInventory.Checked :=( q.FieldByName('article_type').AsInteger = 1 );
        rbRecipe.Checked    :=( q.FieldByName('article_type').AsInteger = 2 );
        pnlInv.visible := (not rbNone.Checked);
        usbInv.Visible := rbInventory.checked;
        usbRecipe.Visible := rbRecipe.checked;
        chkMenu.Checked :=( q.FieldByName('menu').AsInteger <> 0 );
        chkMenuClick(Self);
        chbAsArticle.Checked :=( q.FieldByName('SENSITIVE').AsInteger <> 0 );
        chbAsOption.Checked  :=( q.FieldByName('Sensitive_option').AsInteger <> 0 );
        lblSizeUM.Visible := (usbSizeModifier.Value > 0) and not rbNone.checked;
        tbSizeUM.Visible := (usbSizeModifier.Value > 0) and not rbNone.checked;

        try
          MS := TMemoryStream.Create;
          try
            InfoList.Add(DEFAULT_LANG_ID, q.FieldByName('info').asString);
            EdtInfo.Text := q.FieldByName('info').asString;
            edtName.Text := q.FieldByName('NAME').asString;
            Caption:=STR_Article +': ' + edtName.Text;
            stopModify := false;
          finally
            FreeAndNil(MS);
          end;
        except
        end;

        if rbNone.Checked then begin
          usbInv.value := 0;
          usbRecipe.value := 0;
        end else if rbInventory.Checked then begin
          with usbInv do begin
            lblUsedUM.Visible := false;
            lblUsageUM.Visible := false;
            Text:='';
            Value:=StrToInt64Def(q.fieldByname('id_inv').AsString, 0);
            if Value<>0 then begin
              Text:=emInv.GetWideStringById('name',Value);
              lblUsageUM.Caption := GetUsageUM(Value);
              lblUsedUM.Visible := True;
              lblUsageUM.Visible := True;
            end;
          end;
        end else if rbRecipe.Checked then begin
          with usbRecipe do begin
            Value:=StrToInt64Def(q.fieldByname('id_recipe').AsString, 0);
            if Value<>0 then
              Text:=emRecipe.GetWideStringById('name',Value)
            else
              Text:=''
          end;
        end;
        with usbSupplier do begin
          Enabled := True;
          Value:=StrToInt64Def(q.fieldByname('id_suppliers').AsString, 0);
          if Value<>0 then begin
            Text:=emSupplier.GetWideStringById('name',Value)
          end else
            Text:=''
        end;
        usbSupplier.Visible := not rbRecipe.checked;
        lblSupplier.Visible   := usbSupplier.Visible;

        with usbUM do begin
          Enabled := True;
          Value:=StrToInt64Def(q.fieldByname('id_unity_sales').AsString, 0);
          Text:='';
          try
            if Value<>0 then
              Text:=StockManagerU.GetUMCByConvId(Untilldb,Value);
          except
          end;
        end;

        SetArticlePurchasePrice(Frame);

        RecreateSizeUM;

        bOwnPPrice := not (rbInventory.checked or rbRecipe.Checked);
        FreeAndNil(emGrossPrice);
        emGrossPrice   := TArticleGrossPriceEmbEntityManager.Create(
          Self, Self, ObjectId, usbSizeModifier.value, bOwnPPrice);
        emGrossPrice.CreateListFrame(pnlSizePPrice);

      end;

    end;

    if NewEntity or Duplicating then begin
      Num := ClientDataset.FieldByName('article_number').AsInteger;
      Seq := ClientDataset.FieldByName('seq').AsInteger;
      Rmseq := ClientDataset.FieldByName('rmseq').AsInteger;
      if NeedMaxArticleNum(Untilldb) then
        edtNumber.Value := GetMaxAvailNumberDb(UntillDB, 'articles', 'article_number', ' coalesce(promo,0)=0 ')
      else
        edtNumber.Value := GetFirstAvailNumberDb(self, Num,  'article_number', ' coalesce(promo,0)=0 ');
      if not Duplicating then
        useVDNumber.Value := edtNumber.Value;
      Sequence := GetFirstAvailNumberDb(Self, seq, 'sequence');
      RmSequence := GetFirstAvailNumberDb(Self,rmseq, 'rm_sequence');
      edtExternalId.Text := '';
      dtpCreationDate.DateTime := LocalDatetimeToSys(now);
    end;

    q := UntillDB.GetPreparedIIbSql('select first 1 price from article_prices where id_articles=:id_articles and is_active=1 order by price desc');
    q.q.Params[0].asInt64 := ObjectId;
    q.ExecQuery;

    edtTimePrice.Value := 0;
    if not q.Eof then
      edtTimePrice.Value := q.Fields[0].asCurrency;

    RefreshActiveTime;
    VanDuijnenRbnClicked(Self);

    if (ObjectId=ID_ARTICLES_WORK_IN) or (ObjectId=ID_ARTICLES_WORK_OUT) then begin
      // Work in / Work Out, ref. https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=359112

      for i := 0 to pcMain.PageCount-1 do
        RecursiveChangeEnabled(pcMain.Pages[i], False);
      chbDisabled.Enabled := false;
      edtName.Enabled := false;
      lblName.Enabled := false;

      lblDep.Enabled := true;
      lblCourse.Enabled := true;
      RecursiveChangeEnabled(usbDep, True);
      RecursiveChangeEnabled(usbCourse, True);

      pcMain.Pages[0].Enabled := True;

      RecursiveChangeEnabled(pcView, True);
      PCButtonPropFram.edtPCText.Enabled := False;
      HHTButtonPropFram.edtPCText.Enabled := False;
      OManButtonPropFram.edtPCText.Enabled := False;

      RecursiveChangeEnabled(pcMain.Pages[1], True); // Area

    end;
  end;
end;

procedure TArticleEntityManager._RefreshLightContent(Frame: TUntillFrame);
var
  q : IIBSQL;
  Num, seq, rmseq : Integer;
  iq: IIBSQL;
  i: integer;
begin
  inherited;

  with TArticleLightEntityFrame(Frame) do begin
    DailyStockChanged := False;
    if NewEntity or Duplicating then begin
      HHTButtonPropFram.IsDefaultColor := True;
      HHTButtonPropFram.IsDefaultFont := True;
      HHTButtonPropFram.IsDefaultAltFont := True;
      OManButtonPropFram.IsDefaultColor := True;
      OManButtonPropFram.IsDefaultFont := True;
      OManButtonPropFram.IsDefaultAltFont := True;
    end;
    if NewEntity then begin
        edtName.Text:=Plugin.Translate('ArticleEntityManager', 'New article');

        chkEmpty.Checked := false;
        CheckAllPrices(UntillDB, Frame);
    end;

    q:= UntillDB.GetPreparedIIBSQL('select articles.*, dailystock_balance.quantity qty,  ' +
      ' pc_font.color pc_color1, pc_font.font_name pc_font_name1, pc_font.font_size pc_font_size1, pc_font.font_attr pc_font_attr1, pc_font.font_color pc_font_color1,' +
      ' hht_font.color hht_color1, hht_font.font_name hht_font_name1, hht_font.font_size hht_font_size1, hht_font.font_attr hht_font_attr1, hht_font.font_color hht_font_color1' +
      ' from articles ' +
      ' left outer join article_button_setting pc_font on pc_font.id_articles=articles.id and pc_font.id_screen_groups=:pc_screen_groups' +
      ' left outer join article_button_setting hht_font on hht_font.id_articles=articles.id and hht_font.id_screen_groups=:hht_screen_groups' +
      ' left outer join dailystock_balance on articles.id=dailystock_balance.id_articles '+
      ' where articles.id=:id');
    q.q.ParamByName('id').asString := IntToStr(ObjectId);
    q.q.ParamByName('pc_screen_groups').asInt64  := ID_SCREEN_GROUPS_PC;
    q.q.ParamByName('hht_screen_groups').asInt64 := ID_SCREEN_GROUPS_FALCON;
    q.ExecQuery;
    fCurObjId := ObjectId;

    if not q.eof then begin
      chbDisabled.Checked := q.FieldByName('is_active').AsInteger = 0;
      Caption:=STR_Article +': '+UTF8_Decode(q.FieldByName('name').AsString);
      edtName.Text:=UTF8_Decode(q.FieldByName('name').AsString);

      edtName.Enabled := not IsSalesData(UntillDB, ObjectId);
      usbDep.Enabled := edtName.Enabled;
      if edtName.Enabled then
        edtName.Enabled   := q.FieldByName('pos_article_type').AsInteger = Ord(patRegular);

      edtNumber.Value     :=q.FieldByName('article_number').AsInteger;
      Sequence:=q.FieldByName('sequence').AsInteger;
      RmSequence:=q.FieldByName('rm_sequence').AsInteger;
      chkManual.Checked :=( q.FieldByName('article_manual').AsInteger <> 0 );
      chkHash.Checked :=( q.FieldByName('article_hash').AsInteger <> 0 );
      chbWeightRequired.Checked := q.FieldByName('weight_required').AsInteger <> 0;
      FillUsb(usbFreeOption, q.FieldByName('id_free_option').AsInt64, emOptions, 'name');
      chkEmpty.Checked := q.FieldByName('empty_article').AsInteger <> 0;
      PCButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('PC_TEXT').AsString);
      HHTButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('RM_TEXT').AsString);
      OManButtonPropFram.edtPCText.text := UTF8_Decode(q.FieldByName('oman_text').AsString);

      HHTButtonPropFram.IsDefaultColor := q.FieldByName('hht_default_color').AsInteger=1;
      HHTButtonPropFram.IsDefaultFont := q.FieldByName('hht_default_font').AsInteger=1;
      HHTButtonPropFram.IsDefaultAltFont := q.FieldByName('hht_default_alt_font').AsInteger=1;

      OManButtonPropFram.IsDefaultColor := q.FieldByName('oman_default_color').AsInteger=1;
      OManButtonPropFram.IsDefaultFont := q.FieldByName('oman_default_font').AsInteger=1;
      OManButtonPropFram.IsDefaultAltFont := q.FieldByName('oman_default_alt_font').AsInteger=1;

      PCColor := TColor(q.FieldByName('pc_color1').asInteger);
      PCFontName := q.FieldByName('pc_font_name1').asString;
      PCFontSize := q.FieldByName('pc_font_size1').asInteger;
      PCFontAttr := q.FieldByName('pc_font_attr1').AsShort;
      PCFontColor := TColor(q.FieldByName('pc_font_color1').asInteger);

      if q.FieldByName('hht_color1').AsVariant=null then
        HhtColor := TColor(q.FieldByName('pc_color1').asInteger)
      else
        HhtColor := TColor(q.FieldByName('hht_color1').asInteger);

      if q.FieldByName('hht_font_name1').AsVariant=null then
        HhtFontName :=  q.FieldByName('pc_font_name1').asString
      else
        HhtFontName :=  q.FieldByName('hht_font_name1').asString;

      if q.FieldByName('hht_font_size1').AsVariant=null then
        HhtFontSize := q.FieldByName('pc_font_size1').asInteger
      else
        HhtFontSize := q.FieldByName('hht_font_size1').asInteger;

      if q.FieldByName('hht_font_attr1').AsVariant=null then
        HhtFontAttr := q.FieldByName('pc_font_attr1').AsShort
      else
        HhtFontAttr := q.FieldByName('hht_font_attr1').AsShort;

      if q.FieldByName('pc_font_color1').asVariant =null then
        HhtFontColor := TColor(q.FieldByName('pc_font_color1').asInteger)
      else
        HhtFontColor := TColor(q.FieldByName('hht_font_color1').asInteger);

      Course := q.fieldByname('id_courses').AsInt64;

      with usbDep do begin
        Value:=StrToInt64Def(q.fieldByname('id_departament').AsString, 0);
        if Value<>0 then
          Text:=emDepartment.GetWideStringById('name',Value)
        else
          Text:=''
      end;
      edtwarning.value := q.FieldByName('warning_level').AsInteger;

      edtquantity.Value := q.FieldByName('qty').asInteger;
      edtTimeAfter.Value := q.FieldByName('AfterMin').asInteger;
      sePeriodMin.Value := q.FieldByName('PeriodMin').asInteger;

      seTimeRound.Value :=  q.FieldByName('RoundMin').asInteger;

      BOCommonEntityActionsU.RefreshItemPCButtonpropFram(PCButtonPropFram, ObjectId, q);
      with HHTButtonPropFram do begin
        RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
        pbUsedFont.Text      := pnlButton.Font.Name;
      end;
      with OManButtonPropFram do begin
        RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
        pbUsedFont.Text      := pnlButton.Font.Name;
      end;

      if q.FieldByName('rm_text').AsVariant=null then
        edtHHTText.Text := UTF8_Decode(q.FieldByName('pc_text').asString)
      else
        edtHHTText.Text := UTF8_Decode(q.FieldByName('rm_text').asString);

      chkActive.Checked := (q.FieldByName('time_active').asInteger <> 0);
      RefreshActiveTime;
    end;
    if NewEntity or Duplicating then begin
      Num := ClientDataset.FieldByName('article_number').AsInteger;
      Seq := ClientDataset.FieldByName('seq').AsInteger;
      Rmseq := ClientDataset.FieldByName('rmseq').AsInteger;

      if NeedMaxArticleNum(Untilldb) then
        edtNumber.Value := GetMaxAvailNumberDb(UntillDB, 'articles',  'article_number', ' coalesce(promo,0)=0 ')
      else
        edtNumber.Value:=GetFirstAvailNumberDb(Self, Num,  'article_number', ' coalesce(promo,0)=0 ');

      Sequence:=GetFirstAvailNumberDb(Self, seq, 'sequence');
      RmSequence:=GetFirstAvailNumberDb(Self,rmseq, 'rm_sequence');
    end;
    chbAsArticle.Checked :=( q.FieldByName('SENSITIVE').AsInteger <> 0 );
    chbAsOption.Checked :=( q.FieldByName('Sensitive_option').AsInteger <> 0 );
    chbDailyStock.Checked:=( q.FieldByName('daily_stock').AsInteger <> 0 );

    iq := untilldb.GetIIbSql;
    iq.SetText('select ID_PREPARATION_AREA, name from article_notify, PREPARATION_AREA where id_articles=:id_articles and ' +
      'ID_PREPARATION_AREA=PREPARATION_AREA.id and article_notify.is_active=1 and PREPARATION_AREA.is_active=1');
    iq.q.ParamByName('id_articles').AsInt64 := ObjectId;
    iq.ExecQuery;
    if not iq.Eof then begin
      usbPrepArea.Value := iq.FieldByName('ID_PREPARATION_AREA').AsInt64;
      usbPrepArea.Text := iq.FieldByName('name').AsString;
    end;

    iq := untilldb.GetIIbSql;
    iq.SetText('select barcode from article_barcodes where id_articles=:id_articles and is_active=1');
    iq.q.ParamByName('id_articles').AsInt64 := ObjectId;
    iq.ExecQuery;
    if not iq.Eof then begin
      edtBarcode.Text := iq.FieldByName('barcode').AsString;
    end;

    RefreshActiveTime;

    if Duplicating then begin
      chbDisabled.Checked := false;
    end;

    if (ObjectId=ID_ARTICLES_WORK_IN) or (ObjectId=ID_ARTICLES_WORK_OUT) then begin
      // Work in / Work Out, ref. https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=359112

      for i := 0 to pcMain.PageCount-1 do
        RecursiveChangeEnabled(pcMain.Pages[i], False);
      chbDisabled.Enabled := false;
      edtName.Enabled := false;
      lblName.Enabled := false;

      lblDep.Enabled := true;
      RecursiveChangeEnabled(usbDep, True);

      pcMain.Pages[0].Enabled := True;

    end;

  end;
end;

function TArticleEntityManager.NeedMaxArticleNum(adb : TUntilldb) : boolean;
var qSel : IIBSQL;
begin
  result := false;
  assert( assigned ( adb ));
  qSel := adb.GetPreparedIIbSql('select bo_article_num from settings');
  qSel.ExecQuery;
  if qSel.eof then exit;

  result := qSel.Fields[0].asInteger = 1;
end;

function TArticleEntityManager.IsCourseChangeable( adb : TUntillDb; aid_course: Int64 ) : Boolean;
var qSel : IIBSQL;
begin
  result := false;
  assert( assigned ( adb ));
  qSel := adb.GetPreparedIIbSql('select changeable from courses where id=:id');
  qSel.q.Params[0].AsInt64 := aid_course;
  qSel.ExecQuery;
  if qSel.eof then exit;

  result := qSel.Fields[0].asInteger = 1;
end;

function TArticleEntityManager._SaveContent(Frame: TUntillFrame): boolean;
var
   wt: IWTran;
   MSPC : TMemoryStream;
   Art_Type : Integer;
   MS  : TMemoryStream;
   def_name, def_int_name : String;
   ks_wf_type : Integer;
   bAskCourse : Boolean;
   q  : IIBSQL;
   timeActive  : boolean;
   dc : TDateTimeStorage;
   qSel : IIBSQL;
begin
  with TArticleEntityFrame(Frame)  do begin
      if not rbnUseNumber.Checked then useVDNumber.Value:=0;
      if not rbnUseGroup.Checked then usbVDGroup.Value:=0;

      dc   :=TDateTimeStorage.Create(dtpCreationDate.DateTime);
      result:=false;
      if inherited SaveContent(Frame) = false then exit;
      MSPC   := nil;
      MS     := nil;
      try
        MSPC   := TMemoryStream.Create;
        MS     := TMemoryStream.Create;

        GetImagePropStream(PCButtonPropFram, MSPC);

        Art_Type := 0;
        if rbInventory.Checked then
          Art_Type := 1
        else if rbRecipe.Checked then
          Art_Type := 2;
        if not chkManual.Checked then
          chkSurCharge.Checked := false;

        InfoList.Value[UntillApp.GetLang] := edtInfo.Text;
        InfoList.SaveToStream(MS);
        MS.Seek(0,0);
        timeActive := false;
        if chkActive.checked then begin
          if rbArtTime.Checked then begin
            timeActive := true;
            if (sePeriodMin.Value > 0) then begin
              edtAYCETime.Value     := 0;
              edtAYCETimeAdd.Value  := 0;
            end;
          end else if rbArtAYCE.Checked and ((edtAYCETime.Value + edtAYCETimeAdd.Value) > 0) then begin
            sePeriodMin.Value   := 0;
            seTimeRound.Value   := 0;
            edtTimeAfter.Value  := 0;
          end;
          if not chkAYCETimeAdd.checked then
            edtAYCETimeAdd.Value  := 0;
        end else begin
          edtAYCETime.Value     := 0;
          edtAYCETimeAdd.Value  := 0;
          sePeriodMin.Value     := 0;
          seTimeRound.Value     := 0;
          edtTimeAfter.Value    := 0;
        end;

        ks_wf_type := 0;
        if rbMultiWF.checked then
          ks_wf_type := 1;

        def_name := edtname.text;
        def_int_name := edtInternal.text;

        bAskCourse := chkAskCourse.Checked;
        if not IsCourseChangeable( Untilldb, usbCourse.value ) then
          bAskCourse := false;
        if not (NewEntity or Duplicating) then
          FixArticleSequence(Frame);
        emExtraFields.SaveDataset;
        SaveRecord(['ID', 'article_number','name','internal_name','article_manual',
                'article_hash','id_courses','id_departament', 'pc_bitmap','pc_text',
                'id_commission', 'id_promotions','savepoints',
                'hideonhold','time_active','AfterMin', 'exclude_sp_total',
                'PeriodMin', 'RoundMin','control_active','control_time','plu_number_vanduijnen',
                'sequence', 'rm_sequence', 'purchase_price', 'id_vd_group', 'id_currency','menu',
                'SENSITIVE','Sensitive_option', 'daily_stock', 'info', 'warning_level',
                'free_after_pay','id_food_group','article_type','id_inventory_item','id_recipe',
                'id_unity_sales', 'can_savepoints', 'show_in_kitchen_screen','decrease_savepoints',
                'rm_text', 'tip', 'bc_standard_dosage', 'bc_alternative_dosage',
                'id_beco_group', 'bc_disablebalance', 'bc_use_locations', 'id_free_option', 'id_pua_groups',
                'promo','one_hand_limit', 'is_active', 'consolidate_quantity', 'consolidate_alias_name','hq_id',
                'rent_price_type', 'id_rental_group', 'condition_check_in_order', 'weight_required',
                'daily_numeric_1','daily_numeric_2','prep_min', 'warn_min','empty_article', 'bc_debitcredit',
                'prep_sec', 'id_suppliers', 'main_price', 'oman_text', 'id_age_groups','surcharge', 'id_ks_wf',
                'info_data', 'auto_onhold', 'ks_wf_type', 'ask_course', 'popup_info',
                'must_combined', 'block_discount', 'has_default_options',
                'hht_default_setting', 'hht_default_color',
                'hht_default_font', 'hht_default_alt_font',
                'oman_default_setting', 'oman_default_color',
                'oman_default_font', 'oman_default_alt_font',
                'id_rent_periods', 'delay_separate_mins', 'id_ksc', 'ks_single_item',
                'allergen', 'auto_resetcourse','single_free_option', 'block_transfer', 'id_size_modifier','auto_sm',
                'id_extra_field_values', 'surface_point', 'fastlane', 'external_id', 'OmitTPAPI', 'ayce_mins',
                'ayce_mins_add', 'creation_date', 'ks_skip_production','exclude_pu_overall'],
                [ObjectId, edtNumber.Value, def_name, def_int_name, SmallInt(chkManual.checked),
                SmallInt(chkHash.checked),usbCourse.Value, usbDep.Value, MSPC, PCButtonPropFram.edtPCText.text,
                usbCommission.Value,usbPromotion.Value, edtSavePoints.Value,
                0, SmallInt(timeActive), edtTimeAfter.Value, SmallInt(chkIgnoreSPTotal.checked),
                sePeriodMin.Value, seTimeRound.Value, 0, 0,
                useVDNumber.Value, Sequence, RmSequence, edtPurcahsePriceHidden.value, usbVDGroup.Value,
                nil{usbRoundCurrency.Value, deprecated},  SmallInt(chkMenu.checked),
                SmallInt(chbAsArticle.checked), SmallInt(chbAsOption.checked),
                Integer(chbDailyStock.checked), edtInfo.text, edtwarning.value,
                usbFree.Value, usbGroup.Value,Art_Type,
                usbInv.Value,usbRecipe.Value,usbUM.Value, SmallInt(chkSavePoints.Checked),
                SmallInt(chbKitchenFollowScreen.Checked), edtDSP.value,HHTButtonPropFram.edtPCText.text,
                0, useStandardDosage.Value, useAlternativeDosage.Value,
                {usbBeCoLocation.Value, (obsolete) }usbBeCoGroup.Value, SmallInt(chbBcDisableBalance.Checked),
                SmallInt(chbUseLocations.Checked), usbFreeOption.Value, usbPUAGroups.Value, 0, edtOneLimit.value,
                SmallInt(not chbDisabled.Checked), edtConsQty.Value, edtConsAlias.Text, edtHqId.Text,
                SmallInt(rbPriceHour.checked), usbRentalGroup.Value, SmallInt(chbConditionCheckInOrder.Checked),
                SmallInt(chbWeightRequired.Checked), SimpleRoundTo(edtnumeric1.value,-4),
                SimpleRoundTo(edtnumeric2.value,-4), edtPrepMin.value, edtKSWarning.value, SmallInt(chkEmpty.checked),
                SmallInt(chbCreditDebit.Checked), edtPrepSec.value, usbSupplier.value, SmallInt(rbMainPricePurchase.Checked),
                OManButtonPropFram.edtPCText.text, usbAgeGroup.value, SmallInt(chkSurCharge.Checked),
                usbKSWF.Value, MS, SmallInt(chkAutoOnHold.checked), ks_wf_type, SmallInt(bAskCourse),
                edtPopUpInfo.text,SmallInt(chkMustCombine.checked), SmallInt(chkBlockDiscount.Checked), SmallInt(chkDefaultOptions.checked),
                0, SmallInt(HHTButtonPropFram.IsDefaultColor),
                SmallInt(HHTButtonPropFram.IsDefaultFont), SmallInt(HHTButtonPropFram.IsDefaultAltFont),
                0, SmallInt(OManButtonPropFram.IsDefaultColor),
                SmallInt(OManButtonPropFram.IsDefaultFont), SmallInt(OManButtonPropFram.IsDefaultAltFont),
                edtRentPeriod.Value, edtDelaySeparateTimeFrame.Value, usbKSC.value, SmallInt(chkSingleInCourse.checked),
                SmallInt(chkAllergen.checked), SmallInt(chkBlockResetQuestion.Checked),
                SmallInt(chkSingleFreeOption.Checked), SmallInt(chkBlockTransfer.Checked),
                usbSizeModifier.value, SmallInt(chkAutoSM.Checked), emExtraFields.ParentId,
                usbSP.value, SmallInt(chkfastlane.Checked), edtExternalId.Text,
                SmallInt(chkOmitTPAPI.checked), edtAYCETime.value, edtAYCETimeAdd.value,
                dc, SmallInt(chkSkipProduction.Checked), Smallint(chkExcludePU.Checked)]);

      finally
        MSPC.Free;
        MS.Free;
      end;
      if DailyStockChanged then begin
        wt := UntillDB.getWTran;
        DailyStockU.ChangeDailyStockAbsolute(UntillDB, wt, ObjectId, edtQuantity.Value, UntillDB.BOUserId, GetSysNow, EmptyStr);
        wt.commit;
        wt := nil;
      end else
        DailyStockU.RenewDailyStockForArticle(UntillDB, ObjectId, UntillDB.BOUserId, GetSysNow, EmptyStr); // to update listener in POS

      PCButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      HHTButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      OManButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      emArticlePrices.UpdateProfit(edtPurchasePrice.value);
      emArticlePrices.SaveDataset;
      if assigned(emSizeUM) then begin
        if emSizeUM.ClientDataSet.recordcount>0 then
          emSizeUM.SaveDataset
        else begin
          if usbSizeModifier.value > 0 then begin
            wt := UntillDB.getWTran;
            try
              qSel := wt.GetPreparedIIbSql('select count(*) '
                + ' from article_size_modifier '
                + ' join SIZE_MODIFIER_ITEM on SIZE_MODIFIER_ITEM.id=ARTICLE_SIZE_MODIFIER.ID_SIZE_MODIFIER_ITEM'
                + ' where SIZE_MODIFIER_ITEM.ID_SIZE_MODIFIER=:ID_SIZE_MODIFIER and article_size_modifier.id_articles=:id_articles');
              qSel.q.ParamByName('id_articles').AsInt64 := Objectid;
              qSel.q.ParamByName('ID_SIZE_MODIFIER').AsInt64 := usbSizeModifier.value;
              qSel.ExecQuery;
              if qSel.Fields[0].AsInteger = 0 then begin
                q := wt.GetPreparedIIbSql('insert into article_size_modifier'
                  + ' (id_articles, ID_SIZE_MODIFIER_ITEM, is_active) '
                  + ' select :id_articles, SIZE_MODIFIER_ITEM.id, 1 '
                  + ' from SIZE_MODIFIER_ITEM where ID_SIZE_MODIFIER=:ID_SIZE_MODIFIER');
                q.q.ParamByName('id_articles').AsInt64 := Objectid;
                q.q.ParamByName('ID_SIZE_MODIFIER').AsInt64 := usbSizeModifier.value;
                q.ExecQuery;
              end;
            finally
              wt.commit;
            end;
          end;
        end;
      end;
      if assigned(emArtOptUM) then emArtOptUM.SaveDataset;
      emArticleAvail.SaveDataset;
      emArticleNotify.SaveDataset;
      emArticleAllergen.SaveDataset;
      ArticleBonusEmbEntityManager.SaveDataset;
      emGrossPrice.SaveDataset;
      if not pnlOptions.Enabled then
        emArticleOption.DeleteAll;

      emArticleOption.SaveDataset;
      emFreeArticleOption.SaveDataset;

      if (not pnlDiscount.Visible) or (not pnlDiscount.Enabled) then
        emArticleDiscount.DeleteAll;

      emArticleDiscount.SaveDataset;
      emRentUnits.SaveDataset;
      emRentPrice.SaveDataset;
      emArticleBarcodes.SaveDataset;

      if not tbVanDuijnen.Enabled then begin
        emVdReceipt.DeleteAll;
        emBecoLocations.DeleteAll;
      end;
      emVdReceipt.SaveDataset;
      emBecoLocations.SaveDataset;
      emKSN.SaveDataset;
      if assigned(emKSWFStage) then begin
        if OldIDKSWF <> usbKSWF.value then
          emKSWFStage.Clear;
        emKSWFStage.SaveDataset;
      end;

      InsertArticlePOS(UntillDB, ObjectId, usbDep.Value, Sequence);

      if Duplicating then
        CopyArticlePOS(Frame, fCurObjId, ObjectId);

      fCurObjId := ObjectID;

      Caption:=STR_Article + ': ' + edtName.Text;
      result:=true;
  end;
end;

procedure TArticleEntityManager.CopyArticlePOS(AFrame: TUntillFrame; ASrcArtId, ADestArtId: Int64);
var
  wt: IWTran;
  qr: IIBSQL;
  sg: string;
begin
  if not TArticleEntityFrame(AFrame).Duplicating then exit;
  sg :=
    IntToStr(ID_SCREEN_GROUPS_PC) + ', ' +
    IntToStr(ID_SCREEN_GROUPS_FALCON) + ', ' +
    IntToStr(ID_SCREEN_GROUPS_ORDERMAN) + ', ' +
    IntToStr(ID_SCREEN_GROUPS_MENU);
  wt := UntillDB.getWTran;
  qr := wt.GetPreparedIIbSql(
    'delete' +
    '  from article_button_setting' +
    ' where id_articles = :id_articles' +
    '   and id_screen_groups not in (' + sg + ')'
  );
  qr.q.ParamByName('id_articles').AsInt64 := ADestArtId;
  qr.ExecQuery;
  //
  qr := wt.GetPreparedIIbSql(
    'insert' +
    '  into article_button_setting (id_articles, id_screen_groups, color, font_name, font_size, font_attr, font_color, font_name_alt, font_size_alt, font_attr_alt, font_color_alt) ' +
    'select :id_articles_dest, id_screen_groups, color, font_name, font_size, font_attr, font_color, font_name_alt, font_size_alt, font_attr_alt, font_color_alt' +
    '  from article_button_setting' +
    ' where id_articles = :id_articles_src' +
    '   and id_screen_groups not in (' + sg + ')'
  );
  qr.q.ParamByName('id_articles_dest').AsInt64 := ADestArtId;
  qr.q.ParamByName('id_articles_src').AsInt64 := ASrcArtId;
  qr.ExecQuery;
  wt.commit;
end;

function TArticleEntityManager._SaveLightContent(Frame: TUntillFrame): boolean;
var wt: IWTran;
    q: TIBSQLU;
    MSPC : TMemoryStream;
    W, H, ver :Integer;
begin
  with TArticleLightEntityFrame(Frame)  do begin
    if (Course = 0) then
      Course := GetDefaultCourseId;
    result:=false;
    if inherited SaveContent(Frame) = false then exit;
      MSPC  := TMemoryStream.Create;
      try
        with PCButtonPropFram do begin
          W:=imBitmap.Picture.Bitmap.Width;
          H:=imBitmap.Picture.Bitmap.Height;
          Ver := CURRENT_VERSION;
          MSPC.Write(ver,sizeof(ver));
          MSPC.Write(w,sizeof(w));
          MSPC.Write(h,sizeof(h));
          ImBitmap.Picture.Bitmap.SaveToStream(MSPC);
          MSPC.Position:=0;
        end;
        FixArticleSequence(Frame);
        SaveRecord(
          [
            'ID', 'article_number','name','article_manual',
            'article_hash','id_departament',
            'time_active','AfterMin',
            'PeriodMin','RoundMin',
            'sequence', 'rm_sequence', 'id_currency','menu',
            'sensitive','Sensitive_option', 'daily_stock', 'warning_level',
            'tip', 'id_free_option', 'rm_text', 'hideonhold', 'control_active',
            'internal_name', 'pc_text',
            'id_courses', 'promo', 'is_active', 'weight_required',
            'empty_article', 'main_price', 'oman_text',
            'hht_default_setting',
            'hht_default_color',
            'hht_default_font',
            'hht_default_alt_font',
            'oman_default_setting',
            'oman_default_color',
            'oman_default_font',
            'oman_default_alt_font'
          ],
          [
            ObjectId, edtNumber.Value, edtName.text, SmallInt(chkManual.checked),
            SmallInt(chkHash.checked),usbDep.Value,
            SmallInt(chkActive.checked), edtTimeAfter.Value, sePeriodMin.Value,
            seTimeRound.Value, Sequence, RmSequence, nil, SmallInt(false),
            SmallInt(chbAsArticle.checked), SmallInt(chbAsOption.checked),
            Integer(chbDailyStock.checked), edtwarning.value,
            0, usbFreeOption.Value, edtHHTText.Text,
            0, 0, edtName.text, edtName.text,
            Course, 0, Smallint(not chbDisabled.Checked),
            SmallInt(chbWeightRequired.Checked), SmallInt(chkEmpty.checked),0,
            OManButtonPropFram.edtPCText.text,
            0,
            SmallInt(HHTButtonPropFram.IsDefaultColor),
            SmallInt(HHTButtonPropFram.IsDefaultFont),
            SmallInt(HHTButtonPropFram.IsDefaultAltFont),
            0,
            SmallInt(OManButtonPropFram.IsDefaultColor),
            SmallInt(OManButtonPropFram.IsDefaultFont),
            SmallInt(OManButtonPropFram.IsDefaultAltFont)
          ]);
      finally
        MSPC.Free;
      end;
      if DailyStockChanged then begin
        wt := UntillDB.getWTran;
        DailyStockU.ChangeDailyStockAbsolute(UntillDB, wt, ObjectId, edtQuantity.Value, UntillDB.BOUserId, GetSysNow, EmptyStr);
        wt.commit;
        wt := nil;
      end else
        DailyStockU.RenewDailyStockForArticle(UntillDB, ObjectId, UntillDB.BOUserId, GetSysNow, EmptyStr); // to update listener in POS

      if not pnlOptions.Enabled then
        emArticleOption.DeleteAll;

      emArticleOption.SaveDataset;

      emArticleAvail.SaveDataset;
      emArticleNotify.SaveDataset;
      emArticlePrices.SaveDataset;

      InsertArticlePOS(UntillDB, ObjectId, usbDep.Value, Sequence);

      PCButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      HHTButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      OManButtonPropFram.SaveArtProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);

      if (not pnlDiscount.Visible) or (not pnlDiscount.Enabled) then
        emArticleDiscount.DeleteAll;

      emArticleDiscount.SaveDataset;

      // Save preparation area
      wt := UntillDB.getWTran;
      q := wt.GetTempSql;
      q.SQL.Text := 'delete from article_notify where id_articles=:id_articles';
      q.ParamByName('id_articles').AsInt64 := ObjectId;
      q.ExecQuery;

      q := wt.GetTempSql;
      q.SQL.SetText('insert into article_notify (id, id_preparation_area, purpose, id_articles, is_active) values '+
                                              '(:id, :id_preparation_area, :purpose, :id_articles, 1)');
      q.ParamByName('id').AsInt64 := UntillDb.GetID;
      q.ParamByName('id_preparation_area').AsInt64 := usbPrepArea.Value;
      q.ParamByName('purpose').AsInteger := 0;
      q.ParamByName('id_articles').AsInt64 := ObjectId;
      q.ExecQuery;
      wt.commit;

      // Save available, if new article
      If NewEntity and not Duplicating then begin
        wt := UntillDB.getWTran;
        q := wt.GetTempSql;
        q.SQL.Text := 'delete from article_available where id_articles=:id_articles';
        q.ParamByName('id_articles').AsInt64 := ObjectId;
        q.ExecQuery;

        q := wt.GetTempSql;
        q.SQL.Text := 'insert into article_available (id, id_sales_area, sequence, rmsequence, limited, id_periods, id_articles, is_active) values '+
                                          '(:id, :id_sales_area, 0, 0, 0, null, :id_articles, 1)';
        q.ParamByName('id').AsInt64 := UntillDB.GetId;
        q.ParamByName('id_sales_area').AsInt64 := GetDefaultSalesAreaId;
        q.ParamByName('id_articles').AsInt64 := ObjectId;
        q.ExecQuery;
        wt.commit;
      end;

      // Save barcode
      wt := UntillDB.getWTran;
      q := wt.GetTempSql;
      q.SQL.Text := 'delete from article_barcodes where id_articles=:id_articles';
      q.ParamByName('id_articles').AsInt64 := ObjectId;
      q.ExecQuery;

      q := wt.GetTempSql;
      q.SQL.SetText('insert into article_barcodes(id_articles, barcode, is_active) values '+
                                              '(:id_articles, :barcode, 1)');
      q.ParamByName('id_articles').AsInt64 := ObjectId;
      q.ParamByName('barcode').AsString := edtBarcode.text;
      q.ExecQuery;

      wt.commit;

      Caption:=STR_Article + ': ' + edtName.Text;
      result:=true;
  end;
end;

procedure TArticleEntityManager._CanInsert(Frame: TUntillFrame;
  Update: Boolean);
var q : IIBSQL;
begin
  inherited;
  if not assigned(Frame) then exit;
  with TArticleEntityFrame(Frame) do begin
    if chkHash.checked and chkMenu.checked then
       plugin.RaiseError('Menu article cannot be Hash article');
    if chbDisabled.Checked then CheckDeletePossible(ObjectId);

    CheckEdtOnEmpty(edtName, nil, STR_Article);
    CheckUsbOnEmpty(usbDep, nil, strDep);
    CheckUsbOnEmpty(usbCourse, tbGeneral, strCourse);
    if not chbDisabled.Checked then begin
      CheckSameName(edtName, STR_Article, nil,'name',' and coalesce(promo,0)=0 and is_active=1');
      if NeedMaxArticleNum(Untilldb) then
        CheckSameNumber(edtNumber, STR_Article, nil, 'article_number', true, ' and coalesce(promo,0)=0 and coalesce(is_active,0) in (0,1)')
      else
        CheckSameNumber(edtNumber, STR_Article, nil, 'article_number', true, ' and coalesce(promo,0)=0 and is_active=1');
      CheckSameNumber(useVDNumber, STR_Article, tbVanDuijnen, 'plu_number_vanduijnen', false, ' and is_active=1');
      if edtExternalId.Text <> '' then
        CheckSameTextField(edtExternalId, STR_ARTICLE, Plugin.Translate('ArticleEntityManager', '3rd party article Id'), tbAdditional, 'external_id', False);
    end;

    if pnlCommonKS.Visible then begin
      if rbSingleWF.checked then begin
        q := untilldb.GetPreparedIIbSql('select course_prep_min from courses where id=:ID_COURSES');
        q.q.Params[0].AsInt64 := usbCourse.Value;
        q.ExecQuery;
        if q.q.fields[0].asInteger>0 then
          if edtPrepMin.value>q.q.fields[0].asInteger then
            plugin.RaiseError('Article preparation time can''t exceed time frame for it Course');
      end else begin
        if usbKSWF.Value = 0 then
         plugin.RaiseError('Please define Kitchen screen workflow');
      end;
    end;

    q := untilldb.GetPreparedIIbSql('select stock_on from restaurant_vars');
    q.ExecQuery;
    if q.q.fields[0].asInteger = 1  then begin
      if rbInventory.checked then
         CheckUsbOnEmpty(usbInv, nil, Plugin.Translate('ArticleEntityManager','Inventory item'))
      else if rbRecipe.checked then
         CheckUsbOnEmpty(usbRecipe, nil, Plugin.Translate('ArticleEntityManager','Recipe'));
      if not rbNone.checked then
        CheckUsbOnEmpty(usbUM, nil, Plugin.Translate('ArticleEntityManager','Unity of measure'));
    end;
  end;
end;

procedure TArticleEntityManager._CanInsertLight(Frame: TUntillFrame;
  Update: Boolean);
begin
  inherited;
  if not assigned(Frame) then exit;
  with TArticleLightEntityFrame(Frame) do begin
    CheckEdtOnEmpty(edtName, nil, STR_Article);
    CheckUsbOnEmpty(usbDep, nil, strDep);
    if not chbDisabled.Checked then
      CheckSameName(edtName, STR_Article, nil,'name',' and coalesce(promo,0)=0 and is_active=1');
    if not chbDisabled.Checked then
      CheckSameNumber(edtNumber, STR_Article, nil, 'article_number', true,' and coalesce(promo,0)=0 and is_active=1');
    CheckUsbOnEmpty(usbPrepArea, nil, Plugin.Translate('ArticleEntityManager','Preparation Area'));
  end;
end;

function TArticleEntityManager.GetDefaultCourseId: Int64;
var iq: IIBSQL;
begin
  iq := UntillDB.GetPreparedIIbSql('select first(1) id from courses');
  iq.ExecQuery;
  assert(not iq.eof, 'No Course exist in database');
  result := iq.Fields[0].AsInt64;
end;

function TArticleEntityManager.GetDefaultSalesAreaId: Int64;
var iq: IIBSQL;
begin
  iq := UntillDB.GetPreparedIIbSql('select first(1) id from sales_area');
  iq.ExecQuery;
  assert(not iq.eof, 'Sales area is not exist in database');
  result := iq.Fields[0].AsInt64;
end;

procedure TArticleEntityManager.OnFilterConfirmed(Sender: TObject);
begin
  flistview.unselectAll;
  flistview.RefreshList;
end;

procedure TArticleEntityManager.OnGPEdit(aid_smi, aid_prices: Int64; value : Currency);
begin
  if not assigned(FMainFrame) then exit;
  TArticleEntityFrame(FMainFrame).emArticlePrices.SavePrice(aid_smi, aid_prices, value);
end;

procedure TArticleEntityManager.OnPriceEdit(aid_smi, aid_prices: Int64; value : Currency);
begin
  if not assigned(FMainFrame) then exit;
  TArticleEntityFrame(FMainFrame).emArticleGP.SaveGP(aid_smi, aid_prices, value);
end;

procedure TArticleEntityManager.OnPurchasePriceEdit(aid_smi : Int64; value: Currency);
begin
  if not assigned(FMainFrame) then exit;
  TArticleEntityFrame(FMainFrame).emArticleGP.UpdateGP( aid_smi, value );
end;

procedure TArticleEntityManager.OnInsertRecord(Sender: TObject; Tran: IWTran);
begin
  DBRestaurantTableU.DummyUpdateDBArticle(tran,fCurObjId);
end;

procedure TArticleEntityManager.OnAfterDelete(Sender: TObject; Tran: IWTran; recordid : Int64);
var iq : IIBSQL;
begin
  if recordid <=0 then exit;
  if not assigned(Tran) then exit;

  iq := tran.GetPreparedIIBSQL('select is_active from articles where id=:id');
  iq.q.Params[0].AsInt64 := recordid;
  iq.ExecQuery;

  if iq.eof then exit;

  iq := tran.GetPreparedIIBSQL('update article_options set is_active=0 where id in (select article_options.id ' +
      ' from article_options ' +
      ' join options on options.id=article_options.id_options and options.option_type=1 ' +
      ' join option_article on option_article.id_options=options.id and option_article.id_articles =:id_articles and article_options.is_active=1)');
  iq.q.Params[0].asInt64 := recordid;
  iq.ExecQuery;

  iq := tran.GetPreparedIIBSQL('insert into DAILYSTOCK_TURNOVER(id_articles, quantity, id_untill_users, posted)'
    + ' values(:id_articles, 0, :id_untill_users, :posted )');
  iq.q.Params[0].asInt64 := recordid;
  iq.q.Params[1].asInt64 := UntillDB.BOUserId;
  iq.q.Params[2].asDatetime := now;
  iq.ExecQuery;

end;

procedure TArticleEntityManager.CanUndelete(ObjectId: Int64);
var iq: IIBSQL;
    name: String;
    number: Integer;
begin
  iq := UntillDB.GetIIbSql;
  iq.SetText('select name, article_number from articles where id=:id');
  iq.q.ParamByName('id').AsInt64 := ObjectId;
  iq.ExecQuery;
  assert(not iq.eof);
  name := iq.q.fields[0].AsString;
  number := iq.q.fields[1].AsInteger;

  iq := UntillDB.GetIIbSql;
  iq.SetText('select id from articles where id<>:id and is_active=1 and coalesce(promo,0)=0 and name=:name');
  iq.q.ParamByName('id').AsInt64 := ObjectId;
  iq.q.ParamByName('name').AsString := name;
  iq.ExecQuery;

  if not iq.Eof then
    If UntillMessageDlg(Plugin.Translate('ArticleEntityManager', 'Article with the same name already exists. Continue?'), mtConfirmation, [mbYes, mbNo, mbCancel], 0) <> mrYes then
      abort;

  iq := UntillDB.GetIIbSql;
  iq.SetText('select id from articles where id<>:id and is_active=1 and coalesce(promo,0)=0 and article_number=:number');
  iq.q.ParamByName('id').AsInt64 := ObjectId;
  iq.q.ParamByName('number').AsInteger := number;
  iq.ExecQuery;

  if not iq.Eof then
    If UntillMessageDlg(Plugin.Translate('ArticleEntityManager', 'Article with the same number already exists. Continue?'), mtConfirmation, [mbYes, mbNo, mbCancel], 0) <> mrYes then
      abort;

  inherited;
end;

{ TClientExtraFieldDefKind }

class function TArticleExtraFieldDefKind.GetCaption: WideString;
begin
  result := Plugin.Translate('ArticlesEntityManager', 'Articles');
end;

class function TArticleExtraFieldDefKind.GetId: Int64;
begin
  result := ARTICLES_EXTRA_FIELDS;
end;

class function TArticleExtraFieldDefKind.GetKind: Integer;
begin
  result := 2;
end;

class function TArticleExtraFieldDefKind.IsLicensed(UntillDb: TUntillDb): Boolean;
begin
  result:=PermsProvider.IsEntityLicensed(UntillDb, TArticleEntityManager.ClassName);
end;

{ TArticleBaseEntityManager }

procedure TArticleBaseEntityManager.UpdateArtDefFlag(
  ATran: IWTran; ABtnType: TBtnType;
  AFlagType: TArtDefFlagType; AId: Int64; AValue: boolean);
var
  qr: IIBSQL;
  sql: string;
begin
  sql := 'update articles set';
  case ABtnType of
    btnPC: exit;
    btnHHT:
      case AFlagType of
        dftColor:    sql := sql + ' hht_default_color = :value';
        dftFont:     sql := sql + ' hht_default_font = :value';
        dftAltFont:  sql := sql + ' hht_default_alt_font = :value';
      end;
    btnOman:
      case AFlagType of
        dftColor:   sql := sql + ' oman_default_color = :value';
        dftFont:    sql := sql + ' oman_default_font = :value';
        dftAltFont: sql := sql + ' oman_default_alt_font = :value';
      end;
  end;

  sql := sql + ' where id = :id';
  qr := ATran.GetPreparedIIbSql(sql);
  qr.q.ParamByName('id').AsInt64 := AId;
  if AValue then
    qr.q.ParamByName('value').AsInteger := 1
  else
    qr.q.ParamByName('value').AsInteger := 0;
  qr.q.ExecQuery;
end;

procedure TArticleBaseEntityManager.ChangeAppearance(
  tran: IWTran; btnType: TBtnType;
  ArtId: Int64; cmbGroup: TUntillCombobox;
  ChangeColor: Boolean; cmbColor: TTntColorComboBox;
  ChangeFont: Boolean; AFont: TFont;
  ChangeFontAlt: Boolean; AFontAlt: TFont;
  ADefaultColor, ADefaultFont, ADefaultFontAlt: boolean);
var
  FontAttr    : SmallInt;
  prefix      : string;
  id_main_scg : Int64;
  id_default_scg : Int64;
  id_scrg     : Int64;
  qUpd, qSel,qIns : IIBSQL;
  bFound      : boolean;
begin
  if (not ChangeColor) and (not ChangeFont) and (not ChangeFontAlt) then
    exit;

  ///////////////////////////////////////////////////////////////////////////////
  ///  Not exists, insert by copying info from article
  ///
  id_main_scg    := ID_SCREEN_GROUPS_PC;
  if btnType = btnPC then begin
    prefix := 'pc';
    id_default_scg := 0;
  end else if btnType = btnHHT then begin
    prefix := 'hht';
    id_default_scg := ID_SCREEN_GROUPS_FALCON;
  end else begin
    prefix := 'hht';
    id_default_scg := ID_SCREEN_GROUPS_ORDERMAN;
  end;

  id_scrg := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
  if id_scrg = 0 then exit;

  qSel := tran.GetPreparedIIbSQL(
    'select count(*)' +
    '  from article_button_setting' +
    ' where id_articles = :id_articles' +
    '   and id_screen_groups = :id_screen_groups'
  );
  qSel.q.ParamByName('id_articles').AsInt64     := ArtId;
  qSel.q.ParamByName('id_screen_groups').AsInt64 := id_scrg;
  qSel.ExecQuery;

  if qSel.q.fields[0].AsInteger=0 then begin
    bFound := false;
    while true do begin
      qSel := tran.GetPreparedIIbSQL(
        'select color, font_name, font_size, font_attr, font_color,' +
        '       font_name_alt, font_size_alt,font_attr_alt,font_color_alt' +
        '  from article_button_setting' +
        ' where id_articles = :id_articles' +
        '   and id_screen_groups = :id_screen_groups'
      );
      qSel.q.ParamByName('id_articles').AsInt64 := ArtId;
      if id_default_scg > 0 then
        qSel.q.ParamByName('id_screen_groups').AsInt64 := id_default_scg
      else
        qSel.q.ParamByName('id_screen_groups').AsInt64 := id_main_scg;
      qSel.ExecQuery;
      if not qSel.eof then begin
        qIns := tran.GetPreparedIIbSql(
          'insert' +
          '  into article_button_setting (' +
          '       id_articles, id_screen_groups, color, font_name, font_size, font_attr, font_color,' +
          '       font_name_alt, font_size_alt, font_attr_alt, font_color_alt) ' +
          'values (' +
          '       :id_articles, :id_screen_groups, :color, :font_name, :font_size, :font_attr, :font_color,' +
          '       :font_name_alt, :font_size_alt, :font_attr_alt, :font_color_alt)'
        );
        qIns.q.ParamByName('id_articles').AsInt64      := ArtId;
        qIns.q.ParamByName('id_screen_groups').AsInt64 := id_scrg;
        qIns.q.ParamByName('color').AsInteger          := qSel.FieldByName('color').AsInteger;
        qIns.q.ParamByName('font_name').AsString       := qSel.FieldByName('font_name').AsString;
        qIns.q.ParamByName('font_size').AsInteger      := qSel.FieldByName('font_size').AsInteger;
        qIns.q.ParamByName('font_attr').AsInteger      := qSel.FieldByName('font_attr').AsInteger;
        qIns.q.ParamByName('font_color').AsInteger     := qSel.FieldByName('font_color').AsInteger;
        qIns.q.ParamByName('font_name_alt').AsString   := qSel.FieldByName('font_name_alt').AsString;
        qIns.q.ParamByName('font_size_alt').AsInteger  := qSel.FieldByName('font_size_alt').AsInteger;
        qIns.q.ParamByName('font_attr_alt').AsInteger  := qSel.FieldByName('font_attr_alt').AsInteger;
        qIns.q.ParamByName('font_color_alt').AsInteger := qSel.FieldByName('font_color_alt').AsInteger;
        qIns.q.ExecQuery;
        bFound := true;
        break;
      end else begin
        if id_default_scg = 0 then
          break
        else
          id_default_scg := 0;
      end;
    end;

    if not bFound then // artilce settings don't exisst for no one screen group yet
      InserDefaultArticleData(tran, ArtId, id_scrg, prefix);
  end;

  if ChangeColor then begin
    UpdateArtDefFlag(tran, btnType, dftColor, ArtId, ADefaultColor);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultColor) then begin
      qUpd := tran.GetPreparedIIbSql(
        'update article_button_setting' +
        '   set color = :color' +
        ' where id_articles = :id_articles' +
        '   and id_screen_groups = :id_screen_groups'
      );
      qUpd.q.ParamByName('id_articles').AsInt64 := ArtId;
      qUpd.q.ParamByName('id_screen_groups').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      qUpd.q.ParamByName('color').AsInteger := ColorToRGB(cmbColor.Selected);
      qUpd.q.ExecQuery;
    end;
  end;

  if ChangeFont then begin
    UpdateArtDefFlag(tran, btnType, dftColor, ArtId, ADefaultFont);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultFont) then begin
      FontAttr := 0;
      if fsBold in AFont.Style then FontAttr := FontAttr + FONT_BOLD;
      if fsItalic in AFont.Style then FontAttr := FontAttr or FONT_ITALIC;
      if fsUnderline in AFont.Style then FontAttr := FontAttr or FONT_UNDERLINE;
      if fsStrikeout in AFont.Style then FontAttr := FontAttr or FONT_STRIKEOUT;
      qUpd := tran.GetPreparedIIbSql(
        'update article_button_setting' +
        '   set font_name = :font_name, font_size = :font_size, font_attr = :font_attr, font_color = :font_color' +
        ' where id_articles = :id_articles' +
        '   and id_screen_groups = :id_screen_groups'
      );
      qUpd.q.ParamByName('id_articles').AsInt64 := ArtId;
      qUpd.q.ParamByName('id_screen_groups').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      qUpd.q.ParamByName('font_name').AsString := AFont.Name;
      qUpd.q.ParamByName('font_size').AsInteger := AFont.Size;
      qUpd.q.ParamByName('font_attr').AsInteger := FontAttr;
      qUpd.q.ParamByName('font_color').AsInteger := ColorToRGB(AFont.Color);
      qUpd.q.ExecQuery;
    end;
  end;

  if ChangeFontAlt then begin
    UpdateArtDefFlag(tran, btnType, dftColor, ArtId, ADefaultFontAlt);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultFontAlt) then begin
      FontAttr := 0;
      if fsBold in AFontAlt.Style then FontAttr := FontAttr + FONT_BOLD;
      if fsItalic in AFontAlt.Style then FontAttr := FontAttr or FONT_ITALIC;
      if fsUnderline in AFontAlt.Style then FontAttr := FontAttr or FONT_UNDERLINE;
      if fsStrikeout in AFontAlt.Style then FontAttr := FontAttr or FONT_STRIKEOUT;
      qUpd := tran.GetPreparedIIbSql(
        'update article_button_setting' +
        '   set font_name_alt = :font_name, font_size_alt = :font_size, font_attr_alt = :font_attr, font_color_alt = :font_color' +
        ' where id_articles = :id_articles' +
        '   and id_screen_groups = :id_screen_groups'
      );
      qUpd.q.ParamByName('id_articles').AsInt64 := ArtId;
      qUpd.q.ParamByName('id_screen_groups').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      qUpd.q.ParamByName('font_name').AsString := AFontAlt.Name;
      qUpd.q.ParamByName('font_size').AsInteger := AFontAlt.Size;
      qUpd.q.ParamByName('font_attr').AsInteger := FontAttr;
      qUpd.q.ParamByName('font_color').AsInteger := ColorToRGB(AFontAlt.Color);
      qUpd.q.ExecQuery;
    end;
  end;
end;

procedure TArticleBaseEntityManager.InserDefaultArticleData(atran: IWTran;
  aid_article, aid_scrg: Int64; aprefix: string);
var qSel, qIns  : IIBSQL;
    strSql      : String;
begin
  assert(assigned(atran));
  assert(aid_scrg>0);
  assert(aid_article>0);

  strSql := Format('select %0:s_color, %0:s_font_name, %0:s_font_size, %0:s_font_attr, %0:s_font_color from articles where id=:aid', [aprefix]);
  qSel := atran.GetPreparedIIbSql(strSQL);
  qSel.q.Params[0].AsInt64 := aid_article;
  qSel.ExecQuery;
  assert(not qSel.Eof, 'article not found in mass TArticleEntityManager.ChangeAppearance');

  qIns := atran.GetPreparedIIbSql('insert into article_button_setting '
    + ' (id, id_articles, id_screen_groups, color, font_name, font_size, font_attr, font_color) values '
    + ' (:id, :id_articles, :id_screen_groups, :color, :font_name, :font_size, :font_attr, :font_color)');
  qIns.q.Params[0].AsInt64 := atran.GetID;
  qIns.q.Params[1].AsInt64 := aid_article;
  qIns.q.Params[2].AsInt64 := aid_scrg;
  qIns.q.Params[3].AsInteger := qSel.FieldByName(Format('%s_color', [aprefix])).AsInteger;
  qIns.q.Params[4].AsString  := qSel.FieldByName(Format('%s_font_name', [aprefix])).AsString;
  qIns.q.Params[5].AsInteger := qSel.FieldByName(Format('%s_font_size', [aprefix])).AsInteger;
  qIns.q.Params[6].AsInteger := qSel.FieldByName(Format('%s_font_attr', [aprefix])).AsInteger;
  qIns.q.Params[7].AsInteger := qSel.FieldByName(Format('%s_font_color', [aprefix])).AsInteger;
  qIns.q.ExecQuery;
end;

procedure TArticleBaseEntityManager.OnFilterChanged(Sender: TObject);
var letNum : Integer;
    iq : IIBSQL;
    idx : Integer;
    strsql  : string;
    oldText : string;
    strS    : String;
    oldCount : Integer;
    i : Integer;
begin

  with ffram do begin
    if cmbName.text='' then exit;
    oldText := cmbName.text;

    strS := lowercase(ffram.cmbName.text);
    letNum := Length(cmbName.text);

    strsql := 'select name, id from articles where is_active=1 and coalesce(PROMO,0)=0 and lower(name) like :s';
    strsql := strsql + ' union ';
    strsql := strsql + ' select name, id from articles where coalesce(PROMO,0)=0 and is_active=1 and lower(name) '
      + ' like :s and not lower(name) like :s';
    iq := self.UntillDB.GetPreparedIIbSql(strsql);
    iq.q.ParamByName('s').asString := '%' + lowercase(strS) + '%';
    iq.ExecQuery;
    idx := 0;

    oldCount := cmbName.Items.Count;
    while not iq.Eof do begin
      cmbName.Items.Add(iq.q.fields[0].asString);
      Inc(idx);
      if idx>=10 then begin
        cmbName.Items.Add('...');
        break;
      end;
      iq.next;
    end;
    for I := pred(OldCount) downto 0 do
      cmbName.Items.Delete(i);

    if not cmbName.DroppedDown then begin
      cmbName.Perform(WM_KEYDOWN, VK_F4,0);
      cmbName.ItemIndex := -1;
      cmbName.Text := OldText;
    end;

    cmbName.SelStart  := letNum;
    cmbName.SelLength := 0;
  end;
end;

initialization
  ClassManager.RegisterClass(TArticleEntityManager);
  RegisterExtraFieldKind(TArticleExtraFieldDefKind);
end.
