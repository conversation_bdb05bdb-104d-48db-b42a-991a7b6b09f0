unit UserExtenderCommissionFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, UntillFram, ExtCtrls, TntCompatibilityU, UntillPanelU, StdCtrls,
  UntillComboBoxU;

type
  TUserExtenderCommissionFrame = class(TUntillFrame)
    pnlCalculation: TUntillPanel;
    lblCommission: TBlockHeading;
  private
    { Private declarations }
  public
    { Public declarations }
    procedure TranslateStrings; override;

  end;

var
  UserExtenderCommissionFrame: TUserExtenderCommissionFrame;

implementation
uses RestaurantPluginU;

{$R *.dfm}

{ TUserExtenderCommissionFrame }

procedure TUserExtenderCommissionFrame.TranslateStrings;
begin
  inherited;
  lblCommission.Caption:=Plugin.Translate('WaiterEntityManager', 'Calculation');
end;

end.
 