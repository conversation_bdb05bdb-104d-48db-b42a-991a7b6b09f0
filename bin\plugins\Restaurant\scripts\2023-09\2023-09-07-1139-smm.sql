set term ^ ;
CREATE OR ALTER procedure GET_FIRST_AVAILABLE_CALLER (
    ID_GROUP bigint,
    AFTER_CODE varchar(50))
returns (
    ID_CALLER bigint,
    CODE varchar(50),
    BARCODE varchar(50))
as
declare variable ORDERING_MODE integer;
declare variable NUMBER_DISTR integer;
declare variable C varchar(100);
declare variable O varchar(30);
declare variable X varchar(100);
declare variable STMT varchar(700);
begin    
    select ordering_mode, number_distr from caller_groups where id=:id_group into :ordering_mode, :number_distr;
    if (:ordering_mode = 1) then begin
        o = ' order by cast(code as numeric)';
    end else begin
        o = ' order by code';
    end
    if (coalesce(:after_code, '') != '') then begin
        c = ' and code > :after_code';
    end else begin
        c = '';
    end
    if (:number_distr = 1) then begin
        x = ' and ID not in (select ID_CALLER from CALLER_LOCKS lock where lock.ID_CALLER = CALLERS.ID and (current_timestamp - lock.LOCK_TS < 120))';
    end else begin
        x = '';
    end
    stmt = 'select first 1 id, code, barcode from callers where id_caller_groups='|| :id_group || ' and is_active=1 and id not in (' ||
        ' select r.id_callers from CAL_ORDERS r, callers c, bill b, orders o' ||
        ' where r.ID_ORDERS = o.id and r.inactive is null and o.ID_BILL = b.id and c.id = r.ID_CALLERS' ||
        ' and ((r.CLOSE_DATETIME is null) or (b.CLOSE_DATETIME is null)) ' ||
        ' )' || x || c || o;
    execute statement stmt into :id_caller, :code, :barcode;

    if (:number_distr = 1 and coalesce(:id_caller,0)!=0) then begin
        insert into caller_locks(id_caller, lock_ts) values (:id_caller, current_timestamp);
    end

    suspend;
end^
set term ; ^
commit;
