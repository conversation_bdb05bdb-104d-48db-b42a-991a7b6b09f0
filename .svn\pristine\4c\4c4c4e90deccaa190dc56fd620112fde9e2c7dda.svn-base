unit SavePointEntityFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, StdCtrls, TntCompatibilityU, ExtCtrls, UntillPanelU,
  UntillButtonU, EntityFram, RestaurantPluginU, ComCtrls, Mask,
  UntillSpinEditU, UntillSelectBoxU, ArticlePriceEmbEntityManager,
  UntillRadioButtonU, UntillGroupBoxU, UntillDateTimePickerU, ExtDlgs,
  DelphiZXIngQRCode, Barcode, IBSQL, UntillDBU, ClassesU, DialogsU,
  UntillCheckBoxU, pngimage, DepartmentEntityManager, UntillPageControl,
  UntillFram, ButtonPropSelectFram, ArticleNotifyEmbEntityManager,
  ArticleAvailEmbEntityManager, RestaurantCommonStringsU, UntillMemoU;

type

  TSavePointEntityFrame = class(TEntityFrame)
    pgProps: TUntillPageControl;
    tbGeneral: TTabSheet;
    usbDep: TUntillSelectBox;
    edtNotes: TUntillMemo;
    lblDep: TTntLabel;
    lblNotes: TTntLabel;
    tbArea: TTabSheet;
    pnlNotify: TTntPanel;
    lblNotify: TBlockHeading;
    seNumber: TUntillSpinEdit;
    lblNumber: TTntLabel;
    edtName: TTntEdit;
    lblName: TTntLabel;
    lblAvail: TBlockHeading;
    pnlAvail: TTntPanel;
    pcView: TUntillPageControl;
    tbPCView: TTntTabSheet;
    PCButtonPropFram: TButtonPropSelectFrame;
    tbHHTView: TTntTabSheet;
    HHTButtonPropFram: TButtonPropSelectFrame;
    tbOrdermanView: TTabSheet;
    OrdermanButtonPropFram: TButtonPropSelectFrame;
    edtSavePoints: TUntillSpinEdit;
    lblSavePoints: TTntLabel;
    edtBlock: TUntillSpinEdit;
    pnlPrice: TTntPanel;
    lblPrices: TBlockHeading;
    procedure rbSingleClick(Sender: TObject);
    procedure rbUniqueClick(Sender: TObject);
    procedure usbDepButtonClick(Sender: TObject);
    procedure PCButtonPropFramcmbScreenGroupsChange(Sender: TObject);
    procedure HHTButtonPropFramcmbScreenGroupsChange(Sender: TObject);
    procedure OrdermanButtonPropFramcmbScreenGroupsChange(Sender: TObject);
    procedure PCButtonPropFrambtnBitmapClick(Sender: TObject);
    procedure PCButtonPropFramedtPCTextChange(Sender: TObject);
    procedure HHTButtonPropFramedtPCTextChange(Sender: TObject);
    procedure OrdermanButtonPropFramedtPCTextChange(Sender: TObject);
    procedure OrdermanButtonPropFrambtnErasePictureClick(Sender: TObject);
    procedure edtNameChange(Sender: TObject);
    procedure OrdermanButtonPropFrambtnBitmapClick(Sender: TObject);
    procedure HHTButtonPropFrambtnErasePictureClick(Sender: TObject);
    procedure PCButtonPropFrambtnErasePictureClick(Sender: TObject);
    procedure pcViewChange(Sender: TObject);
    procedure HHTButtonPropFramchkDefaultViewClick(Sender: TObject);
    procedure OrdermanButtonPropFramchkDefaultViewClick(Sender: TObject);
  private
    procedure UpdateNonPCTabs( aPropFram : TButtonPropSelectFrame );
    { Private declarations }
  public
    emArticlePrices  : TArticlePriceEmbEntityManager;
    emDepartment    : TDepartmentEntityManager;
    emArticleNotify : TArticleNotifyEmbEntityManager;
    emArticleAvail  : TArticleAvailEmbEntityManager;
    { Public declarations }
    procedure CheckAllPrices(Frame: TUntillFrame);
    procedure TranslateStrings; override;
    procedure RefreshFonts;
    procedure DisablePOSControls;
    procedure DisablePOSControlsAll;
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation
uses CommonStringsU, DataControlsU, BLRVouchersU, BLRSavePointsU, ArticleEntityManager,
  BarcodesU, UntillPOSU, TicketProcU, ShowProgressU, RestaurantDatasetParamsU, FixedIdU;

{$R *.dfm}

{ TPrepaidArticlesEntityFrame }

procedure TSavePointEntityFrame.PCButtonPropFrambtnBitmapClick(Sender: TObject);
begin
  inherited;
  PCButtonPropFram.btnBitmapClick(Sender);
end;

procedure TSavePointEntityFrame.PCButtonPropFrambtnErasePictureClick(
  Sender: TObject);
begin
  inherited;
  PCButtonPropFram.btnErasePictureClick(Sender);
end;

procedure TSavePointEntityFrame.PCButtonPropFramcmbScreenGroupsChange(
  Sender: TObject);
begin
  inherited;
  PCButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
end;

procedure TSavePointEntityFrame.PCButtonPropFramedtPCTextChange(Sender: TObject);
begin
  inherited;
  PCButtonPropFram.edtPCTextChange(Sender);
end;

procedure TSavePointEntityFrame.pcViewChange(Sender: TObject);
begin
  inherited;
  if pcView.ActivePageIndex = 1 then begin
    UpdateNonPCTabs( HHTButtonPropFram );
    HHTButtonPropFram.ApplyBtnPropList;
  end else if pcView.ActivePageIndex = 2 then begin
    UpdateNonPCTabs( OrdermanButtonPropFram );
    OrdermanButtonPropFram.ApplyBtnPropList;
  end;
end;

procedure TSavePointEntityFrame.DisablePOSControls;
begin
  seNumber.Enabled := false;
  edtName.Enabled  := false;
  usbDep.enabled   := false;
end;

procedure TSavePointEntityFrame.DisablePOSControlsAll;
begin
  DisablePOSControls;
  edtNotes.Enabled := false;
end;

constructor TSavePointEntityFrame.Create(AOwner: TComponent);
begin
  inherited;

  emArticlePrices := TArticlePriceEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId, arptPrice);
  emArticlePrices.CreateListFrame(pnlPrice);

  emDepartment := TDepartmentEntityManager.Create(Self,UntillDB);
  emArticleAvail := TArticleAvailEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId);
  emArticleAvail.CreateListFrame(pnlAvail);
  emArticleNotify := TArticleNotifyEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId);
  emArticleNotify.CreateListFrame(pnlNotify);
  PCButtonPropFram.UntillDB := UntillDB;
  HHTButtonPropFram.UntillDB := UntillDB;
  PCButtonPropFram.ButtonType := btnPC;
  HHTButtonPropFram.ButtonType := btnHHT;
  OrdermanButtonPropFram.UntillDB := UntillDB;
  OrdermanButtonPropFram.ButtonType := btnOrderman;
  PCButtonPropFram.pnlButton.Color  := clBlack;
  HHTButtonPropFram.pnlButton.Color := clBlack;
  OrdermanButtonPropFram.pnlButton.Color := clBlack;
end;

procedure TSavePointEntityFrame.OrdermanButtonPropFrambtnBitmapClick(
  Sender: TObject);
begin
  inherited;
  OrdermanButtonPropFram.btnBitmapClick(Sender);
end;

procedure TSavePointEntityFrame.OrdermanButtonPropFrambtnErasePictureClick(
  Sender: TObject);
begin
  inherited;
  OrdermanButtonPropFram.btnErasePictureClick(Sender);
end;

procedure TSavePointEntityFrame.RefreshFonts;
begin
  PCButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
  HHTButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
  OrdermanButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
end;

procedure TSavePointEntityFrame.OrdermanButtonPropFramchkDefaultViewClick(
  Sender: TObject);
begin
  inherited;
  if OrdermanButtonPropFram.chkDefaultView.checked then
    UpdateNonPCTabs( OrdermanButtonPropFram )
  else
    OrdermanButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
  OrdermanButtonPropFram.chkDefaultViewClick(Sender);
end;

procedure TSavePointEntityFrame.OrdermanButtonPropFramcmbScreenGroupsChange(
  Sender: TObject);
begin
  inherited;
  OrdermanButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
end;

procedure TSavePointEntityFrame.OrdermanButtonPropFramedtPCTextChange(
  Sender: TObject);
begin
  inherited;
  OrdermanButtonPropFram.edtPCTextChange(Sender);
end;

destructor TSavePointEntityFrame.Destroy;
begin
  FreeAndNil(emArticlePrices);
  FreeAndNil(emDepartment);
  FreeAndNil(emArticleNotify);
  FreeAndNil(emArticleAvail);
  inherited;
end;

procedure TSavePointEntityFrame.edtNameChange(Sender: TObject);
begin
  inherited;
  PCButtonPropFram.edtPCText.Text  := edtName.Text;
  HHTButtonPropFram.edtPCText.Text := edtName.Text;
  OrdermanButtonPropFram.edtPCText.Text := edtName.Text;
end;

procedure TSavePointEntityFrame.HHTButtonPropFrambtnErasePictureClick(
  Sender: TObject);
begin
  inherited;
  HHTButtonPropFram.btnErasePictureClick(Sender);

end;

procedure TSavePointEntityFrame.HHTButtonPropFramchkDefaultViewClick(
  Sender: TObject);
begin
  inherited;
  if HHTButtonPropFram.chkDefaultView.checked then
    UpdateNonPCTabs( HHTButtonPropFram )
  else
    HHTButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
  HHTButtonPropFram.chkDefaultViewClick(Sender);
end;

procedure TSavePointEntityFrame.HHTButtonPropFramcmbScreenGroupsChange(
  Sender: TObject);
begin
  inherited;
  HHTButtonPropFram.RefreshButtonProps(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
end;

procedure TSavePointEntityFrame.HHTButtonPropFramedtPCTextChange(Sender: TObject);
begin
  inherited;
  HHTButtonPropFram.edtPCTextChange(Sender);

end;

procedure TSavePointEntityFrame.rbSingleClick(Sender: TObject);
begin
  inherited;
  usbdep.enabled := False;
end;

procedure TSavePointEntityFrame.rbUniqueClick(Sender: TObject);
begin
  inherited;
  usbdep.enabled := true;
end;

procedure TSavePointEntityFrame.TranslateStrings;
begin
  inherited;
  lblSavePoints.Caption  := Plugin.Translate('SavePointEntityManager','Amount');
  lblName.Caption    := CommonStringsU.StrNameLabelCaption;
  lblNumber.Caption  := CommonStringsU.StrNumberLabelCaption;
  lblPrices.Caption  := Plugin.Translate('SavePointEntityManager','Prices');
  tbGeneral.Caption  := Plugin.Translate('SavePointEntityManager','General');
  tbArea.Caption     := Plugin.Translate('SavePointEntityManager','Area/View');
  lblNotify.Caption  := ItemNotifyName;
  lblAvail.Caption   := ItemAvailableName;
end;

procedure TSavePointEntityFrame.UpdateNonPCTabs(aPropFram: TButtonPropSelectFrame);
var idx : Integer;
begin
  // Copy modified font params to HHT and Oman props if needed
  if not aPropFram.chkDefaultView.checked then exit;

  PCButtonPropFram.UpdatePropList(ObjectID, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
  if PCButtonPropFram.ArtBtnPropList.count > 0  then begin
    idx := PCButtonPropFram.ArtBtnPropList.FindItem(ID_SCREEN_GROUPS_PC);
    if idx >=0 then
      aPropFram.CopyArtBtnProp( PCButtonPropFram.ArtBtnPropList.items[idx] );
  end;
end;

procedure TSavePointEntityFrame.usbDepButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  with usbDep, emDepartment do begin
    res:=ShowModal(Value);
    if res>0 then begin
      Value:=res;
      Text := GetWideStringById('name',res);
    end;
    SetFocus;
  end;
end;

procedure TSavePointEntityFrame.CheckAllPrices(Frame: TUntillFrame);
var q : IIBSQL;
    idp :Int64;
    pricename :WideString;
begin
  q:= View.Manager.UntillDB.GetPreparedIIBSQL('select currency.ID, currency.name from currency inner join system_vars ' +
    ' on currency.code = system_vars.varvalue order by currency.ID');
  q.ExecQuery;
  if not q.Eof then begin
    pricename := q.fieldByname('name').AsString;
    idp       := StrToInt64Def(q.fieldByname('id').AsString, 0);
    ArticleEntityManager.AddNewPrice(View.Manager.UntillDB, idp, pricename, emArticlePrices, ObjectId);
  end;
end;

end.
