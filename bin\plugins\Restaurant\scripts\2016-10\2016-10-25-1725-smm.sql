set term !!;
CREATE OR ALTER procedure BEVERAGE_PREPROCESS_ORDERITEM (
    ID_ORDERS bigint,
    QUANTITY integer,
    ID_ARTICLES bigint)
as
declare variable FUTURE_ORDER integer;
declare variable ORDER_KIND integer;
begin
  if (not(:ID_ORDERS is null)) then
  begin
    -- skip if future order
    select count(*)
    from DELAY_BILLS DB, BILL, ORDERS
    where DB.ID_BILL = BILL.ID and
          BILL.ID = ORDERS.ID_BILL and
          ORDERS.ID = :ID_ORDERS
    into :FUTURE_ORDER;

    select kind from orders where id=:ID_ORDERS into :ORDER_KIND;

    if ((:FUTURE_ORDER = 0) and
        (:ORDER_KIND <> 2) and (:ORDER_KIND <> 4)) then
    begin
      execute procedure BEVERAGE_PROCESS_ORDERITEM(:ID_ARTICLES, :ID_ORDERS, :QUANTITY);
    end
  end
end
!!
commit
!!
set term ; !!

