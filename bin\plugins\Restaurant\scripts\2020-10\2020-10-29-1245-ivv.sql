set term !! ;
create or alter procedure fill_vat_exlcuded
as 
  declare variable VAT_TYPE smallint;
begin
        
    select first 1 VAT_TYPE from RESTAURANT_VARS INTO VAT_TYPE;

    if (:VAT_TYPE = 1) then begin
      update bill set vat_excluded=1;
    end     

end;
!!
commit
!!
grant execute on procedure fill_vat_exlcuded to untilluser
!!
commit
!!
execute procedure fill_vat_exlcuded;
!!
commit
!!
set term ; !!

