[Start]
**************************************************
TimeStamp=16.12.2015 16:24
Interval=0;30.12.1899
[Class]
Name=TBLMessageKeyboardString
TimeStamp=16.12.2015 16:24
Interval=1.58680559252389E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=16.12.2015 16:24
Interval=2.5706016458571E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=16.12.2015 16:24
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestBlockFireCourse
TimeStamp=16.12.2015 16:24
Interval=3.59259283868596E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=2.23726819967851E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=2.31484591495246E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=16.12.2015 16:25
Interval=2.00578724616207E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000120
[Class]
Name=TBLRMsgArticle
TimeStamp=16.12.2015 16:25
Interval=7.07175786374137E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000146
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgArticle
TimeStamp=16.12.2015 16:25
Interval=5.43981150258332E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000141
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=16.12.2015 16:25
Interval=8.69212817633525E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table:   2 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>            <GROUP>,70     1,70   0,36    1,70 21,0%         1,70    
--------------------Separate2--------------------                               
 1      <USER>            <GROUP>,70     1,70   0,36    1,70 21,0%         1,70    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgNextOrderCourse
TimeStamp=16.12.2015 16:25
Interval=1.64583398145624E-5;30.12.1899 0:00:01
Exception=
StateGUID={52C8D702-EB4F-4DB8-A50F-50D22847948A}
StateName=TBLRGetTableToChangeCourse
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=9.51388938119635E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Next
02.02.04                                        
Next course for table  2                        
by   Peter                                      
Bar                                             
------------------------------------------------
Aticle              Count                Price  
    Separate1                                   
Separate1           1                      1,70 
                             Total:         1,70

<<<Pos ticket
<<<<<EOSD
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgNextOrderCourse
TimeStamp=16.12.2015 16:25
Interval=1.77314796019346E-5;30.12.1899 0:00:01
Exception=
StateGUID={52C8D702-EB4F-4DB8-A50F-50D22847948A}
StateName=TBLRGetTableToChangeCourse
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=1.1932868801523E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Next
02.02.04                                        
Next course for table  2                        
by   Peter                                      
Bar                                             
------------------------------------------------
Aticle              Count                Price  
    Separate2                                   
Separate2           1                      1,70 
                             Total:         1,70

<<<Pos ticket
<<<<<EOSD
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=2.515046071494E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgTableNo
TimeStamp=16.12.2015 16:25
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=2
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=16.12.2015 16:25
Interval=9.05092747416347E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=16.12.2015 16:25
Interval=1.50463165482506E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   2 a   Waiter:   Peter 02.02.2004 11:45                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>            <GROUP>,70     1,70   0,36    1,70 21,0%         1,70    
--------------------Separate2--------------------                               
 1      <USER>            <GROUP>,70     1,70   0,36    1,70 21,0%         1,70    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        4,11        4,11        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=14
AsTips=False
BillQty=1
EFTData=
ExternalID=
idParts=0
id_payments=5000001501
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=16.12.2015 16:25
Interval=1.28587926155888E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=16.12.2015 16:25
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestBlockFireCourse
TimeStamp=16.12.2015 16:25
Interval=3.64004663424566E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=False
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=16.12.2015 16:25
Interval=8.57638951856643E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=16.12.2015 16:25
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=16.12.2015 16:25
Interval=2.06365730264224E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
