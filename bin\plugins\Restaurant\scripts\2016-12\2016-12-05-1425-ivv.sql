create table ks_wf_stage_template (
    id u_id,
    name   varchar(100),
    description   varchar(250),
    prep_time_min integer,
    prep_time_sec integer,
    warning_min   integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint kswfst_pk primary key (id)
);
commit;
grant all on ks_wf_stage_template to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_stage_template', 'b', 1);
commit;
execute procedure register_bo_table('ks_wf_stage_template', '', '');
commit;

