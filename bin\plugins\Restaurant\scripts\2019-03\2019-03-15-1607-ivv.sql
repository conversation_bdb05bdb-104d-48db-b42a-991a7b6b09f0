insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999931, 1,'Egg', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999932, 2,'Gluten', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999933, 3,'Lupin', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999934, 4,'Milk', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999935, 5,'Mustard', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999936, 6,'Nuts', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999937, 7,'Peanuts', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999938, 8,'Crustaceans', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999939, 9,'Celery', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999940, 10,'Sesame', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999941, 11,'Soya', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999942, 12,'Fish', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999943, 13,'Mollusks', 1 );
insert into allergens ( ID, NUMBER, NAME, IS_ACTIVE ) values(9999999944, 14,'Sulphite', 1 );
commit;
