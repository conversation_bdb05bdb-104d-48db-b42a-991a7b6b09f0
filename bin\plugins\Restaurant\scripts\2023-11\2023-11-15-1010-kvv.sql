set term ^ ;
create or alter procedure get_first_available_caller (
  id_group bigint,
  after_code varchar(50)
) returns (
  id_caller bigint,
  code varchar(50),
  barcode varchar(50)
) as
  declare variable ordering_mode integer;
  declare variable number_distr integer;
  declare variable c varchar(100);
  declare variable o varchar(30);
  declare variable x varchar(100);
  declare variable stmt varchar(700);
begin    
  select ordering_mode, number_distr
    from caller_groups
   where id = :id_group
    into :ordering_mode, :number_distr;
  if (:number_distr = 1) then begin
    x = ' and id not in (select id_caller from caller_locks lock where lock.id_caller = callers.id and (current_timestamp - lock.lock_ts < (0.00001157407*3)))';
  end else begin
    x = '';
  end
  if (coalesce(:after_code, '') != '') then begin
    if (:ordering_mode = 1) then
      c = ' and cast(code as numeric) > cast(''' || :after_code || ''' as numeric)';
    else
      c = ' and code > ''' || :after_code || '''';
  end else begin
    c = '';
  end
  if (:ordering_mode = 1) then begin
    o = ' order by cast(code as numeric)';
  end else begin
    o = ' order by code';
  end
  stmt =
    'select first 1 id, code, barcode' ||
    '  from callers' ||
    ' where id_caller_groups = ' || :id_group ||
    '   and is_active = 1' ||
    '   and id not in (' ||
    '     select r.id_callers' ||
    '       from cal_orders r, callers c, bill b, orders o' ||
    '      where r.id_orders = o.id' ||
    '        and r.inactive is null' ||
    '        and o.id_bill = b.id' ||
    '        and c.id = r.id_callers' ||
    '        and ((r.close_datetime is null) or (b.close_datetime is null)) ' ||
    '   )' ||
    x || c || o;
  execute statement stmt into :id_caller, :code, :barcode;
  if (:number_distr = 1 and coalesce(:id_caller, 0) != 0) then begin
    insert
      into caller_locks(id_caller, lock_ts)
    values (:id_caller, current_timestamp);
  end
  suspend;
end
^
commit
^
set term ; ^
