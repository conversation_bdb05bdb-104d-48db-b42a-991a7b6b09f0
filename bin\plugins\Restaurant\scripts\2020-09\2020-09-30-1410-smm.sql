alter table food_group add guid varchar(36);
commit;
CREATE INDEX idx_food_group_guid ON food_group(guid);
commit;
update food_group set guid=uuid_to_char(gen_uuid());
commit;
alter table food_group add constraint food_group_guid unique(guid);
commit;
set term !! ;
EXECUTE BLOCK AS BEGIN
  IF (SUBSTRING(RDB$GET_CONTEXT('SYSTEM', 'ENGINE_VERSION') FROM 1 FOR 2) = '2.') THEN
    EXECUTE STATEMENT 'UPDATE RDB$RELATION_FIELDS SET RDB$NULL_FLAG = 1 WHERE RDB$FIELD_NAME = ''GUID'' AND RDB$RELATION_NAME = ''FOOD_GROUP''';
  ELSE
    EXECUTE STATEMENT 'ALTER TABLE FOOD_GROUP ALTER COLUMN GUID SET NOT NULL';
END
!!
COMMIT
!!
create or alter trigger food_group_guid_trigger for food_group
active before insert
as
begin
    if (new.guid is null) then begin
      new.guid = uuid_to_char(gen_uuid());
    end                                            
end
!!
commit
!!
set term ; !!

