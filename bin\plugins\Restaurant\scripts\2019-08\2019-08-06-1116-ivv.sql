create table BG_SM_PRICES(
    id u_id,
    id_bg_articles_prices bigint,
    id_size_modifier_item bigint,
    price		decimal(17,4),	
    is_active 		smallint,
    IS_ACTIVE_MODIFIED 	timestamp,
    IS_ACTIVE_MODIFIER 	varchar(30),
    constraint BG_SM_PRICES_pk primary key (id),
    constraint BG_SM_PRICES_fk1 foreign key (id_bg_articles_prices) references bonus_groups_articles_prices(id),
    constraint BG_SM_PRICES_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on BG_SM_PRICES to untilluser;
commit;
execute procedure register_sync_table_ex('BG_SM_PRICES', 'b', 1);
commit;
execute procedure register_bo_table('BG_SM_PRICES', 'id_bg_articles_prices', 'bonus_groups_articles_prices');
commit;
execute procedure register_bo_table('BG_SM_PRICES', 'id_size_modifier_item', 'size_modifier_item');
commit;
