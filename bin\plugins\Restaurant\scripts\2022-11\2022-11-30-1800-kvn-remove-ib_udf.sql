-- Removing dependencies on ib_udf (abs, mod, div) added in old db's in UntillKernel\sql\{48,89,103}\3.sql
-- procedure is done in Restaurant as all dependencies are in Restaurant

SET TERM ^ ;

-- remove SA_AGGR_VIEW if exists (uses abs())
EXECUTE BLOCK AS BEGIN
  IF (EXISTS(SELECT * FROM RDB$RELATIONS WHERE RDB$RELATION_NAME = 'SA_AGGR_VIEW')) THEN BEGIN
    EXECUTE STATEMENT 'ALTER VIEW SA_AGGR_VIEW AS SELECT * FROM RDB$DATABASE';

    -- see https://dev.untill.com/projects/#!643689
    if (exists(select 1 from rdb$triggers where rdb$trigger_name = 'VOUCHER_PBILL_AGGR')) then
      execute statement 'DROP TRIGGER VOUCHER_PBILL_AGGR;';

    EXECUTE PROCEDURE REMOVE_AGGREGATES;
  END
END ^

-- deactivate procedures that use abs() (directly or indirectly)
ALTER PROCEDURE FIX_SERVICE_CHARGE AS BEGIN END ^
ALTER PROCEDURE GET_END_OF_DAY_TURNOVER AS BEGIN END ^
ALTER PROCEDURE GET_EXACTGLOBE_TURNOVER AS BEGIN END ^
ALTER PROCEDURE GET_END_OF_DAY_TURNOVER2 AS BEGIN END ^

SET TERM ; ^

COMMIT;

-- deactivate views that use abs()
ALTER VIEW TOTAL_MENU_PRICE AS SELECT * FROM RDB$DATABASE;
ALTER VIEW TOTAL_MENU_ORIG_PRICE AS SELECT * FROM RDB$DATABASE;
ALTER VIEW GET_END_OF_DAY_TURNOVER_V AS SELECT * FROM RDB$DATABASE;
ALTER VIEW VIEW_TURNOVER_INCL_ROOM AS SELECT * FROM RDB$DATABASE;
ALTER VIEW VIEW_TURNOVER_EXCL_ROOM AS SELECT * FROM RDB$DATABASE;

COMMIT;

SET TERM ^ ;

-- remove dependencies
EXECUTE BLOCK AS BEGIN
  IF (EXISTS(SELECT * FROM RDB$FUNCTIONS WHERE RDB$MODULE_NAME IS NOT NULL AND RDB$FUNCTION_NAME = 'DIV')) THEN
    EXECUTE STATEMENT 'DROP EXTERNAL FUNCTION DIV';

  IF (EXISTS(SELECT * FROM RDB$FUNCTIONS WHERE RDB$MODULE_NAME IS NOT NULL AND RDB$FUNCTION_NAME = 'MOD')) THEN
    EXECUTE STATEMENT 'DROP EXTERNAL FUNCTION MOD';

  IF (EXISTS(SELECT * FROM RDB$FUNCTIONS WHERE RDB$MODULE_NAME IS NOT NULL AND RDB$FUNCTION_NAME = 'ABS')) THEN
    EXECUTE STATEMENT 'DROP EXTERNAL FUNCTION ABS';
END ^

SET TERM ; ^

COMMIT;

-- restore of views
alter view total_menu_price ( id_order_item, price )
as
select oi.id, Sum(oi.quantity * (oi.price + coalesce((select sum(abs(quantity) * price)
    from menu_item mi where  mi.id_menu = oi.id_menu),0))) total_price
from order_item oi group by oi.id;

alter view total_menu_orig_price ( id_order_item, original_price )
as
select oi.id, Sum(oi.quantity * (oi.original_price + coalesce((select sum(abs(quantity) * original_price)
    from menu_item mi where  mi.id_menu = oi.id_menu),0))) total_price
from order_item oi group by oi.id;

CREATE OR ALTER VIEW GET_END_OF_DAY_TURNOVER_V(
    ARTICLE_ID,
    ARTICLE_NAME,
    ARTICLE_NUMBER,
    DEPARTMENT_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_NUMBER,
    GROUP_ID,
    GROUP_NAME,
    GROUP_GR_NUMBER,
    SALES_AREA_ID,
    SALES_AREA_NAME,
    SALES_AREA_NUMBER,
    SINGLE_PRICE,
    SINGLE_VAT,
    QUANTITY,
    VAT_SIGN,
    VAT_PERCENT,
    CLOSE_DATETIME,
    ROOM_EXISTS)
AS
select a.id, a.name, a.article_number,
           d.id, d.name, d.dep_number,
           f.id, f.name, f.gr_number,
           s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * (sa.sa_coef * (cast(sa.quantity as float))/ (cast(oi.quantity as float))) * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * (sa.sa_coef * (cast(sa.quantity as float))/ (cast(oi.quantity as float))) * (1 - coalesce(BILL.DISCOUNT, 0)) ) vat,
      oi.quantity,
      oi.vat_sign, oi.vat_percent,
      bill.close_datetime,
      (select coalesce(count(*),0) from pbill_payments
        join pbill on pbill.id=pbill_payments.id_pbill
        join payments on payments.id=pbill_payments.id_payments
        where pbill.id_bill = bill.id and payments.kind=3 
        and not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id) )
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      group by a.id, a.name, a.article_number,
                 d.id, d.name, d.dep_number,
                 f.id, f.name, f.gr_number,
                 s.id, s.name, s.number,
                 oi.vat_sign, oi.vat_percent,
                 oi.quantity, bill.close_datetime,
                 bill.id
    union all
        select a.id, a.name, a.article_number,
           d.id, d.name, d.dep_number,
           f.id, f.name, f.gr_number,
           s.id, s.name, s.number,
        sum(pbi.price) amount,
        sum(pbi.vat) vat,
        sum(pbi.quantity),
        pbi.vat_sign, pbi.vat_percent,
        bill.close_datetime,
      (select coalesce(count(*),0) from pbill_payments
        join pbill on pbill.id=pbill_payments.id_pbill
        join payments on payments.id=pbill_payments.id_payments
        where pbill.id_bill = bill.id and payments.kind=3 )
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null  and (bill.close_datetime is not null)
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles a on a.id = pbi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        left outer join sales_area s on s.id = pbill.id_sales_area
        where coalesce(a.article_hash,0)=0
             and not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id)
        group by a.id, a.name, a.article_number,
           d.id, d.name, d.dep_number,
           f.id, f.name, f.gr_number,
           s.id, s.name, s.number,
           pbi.vat_sign, pbi.vat_percent,
           bill.close_datetime,
           bill.id
;

CREATE OR ALTER VIEW VIEW_TURNOVER_INCL_ROOM(
    ARTICLE_ID,
    ARTICLE_NAME,
    ARTICLE_NUMBER,
    DEPARTMENT_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_NUMBER,
    GROUP_ID,
    GROUP_NAME,
    GROUP_GR_NUMBER,
    SALES_AREA_ID,
    SALES_AREA_NAME,
    SALES_AREA_NUMBER,
    PRICE,
    VAT,
    QUANTITY,
    VAT_SIGN,
    VAT_PERCENT,
    CLOSE_DATETIME)
AS
select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sum(sa.quantity) quantity,
      oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef = 1.00
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, coalesce(parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sa.quantity, oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef < 1.00
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, sa.quantity,
          coalesce(sa.parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
        sum(pbi.price) amount,sum(pbi.vat) vat,sum(pbi.quantity),pbi.vat_sign, pbi.vat_percent,pbill.pdatetime close_datetime
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null  and (bill.close_datetime is not null)
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles a on a.id = pbi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        left outer join sales_area s on s.id = pbill.id_sales_area
        group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
           pbi.vat_sign, pbi.vat_percent,pbill.pdatetime ,bill.id;

CREATE OR ALTER VIEW VIEW_TURNOVER_EXCL_ROOM(
    ARTICLE_ID,
    ARTICLE_NAME,
    ARTICLE_NUMBER,
    DEPARTMENT_ID,
    DEPARTMENT_NAME,
    DEPARTMENT_NUMBER,
    GROUP_ID,
    GROUP_NAME,
    GROUP_GR_NUMBER,
    SALES_AREA_ID,
    SALES_AREA_NAME,
    SALES_AREA_NUMBER,
    PRICE,
    VAT,
    QUANTITY,
    VAT_SIGN,
    VAT_PERCENT,
    CLOSE_DATETIME)
AS
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sum(sa.quantity) quantity,
      oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef = 1.00 and not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime,bill.id, coalesce(parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
      sum((price + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* price) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
      sum((vat + coalesce((select sum(cast(abs(menu_item.quantity) as integer)* vat) from menu_item where menu_item.id_menu = oi.id_menu),0))
        * sa.sa_coef * sa.quantity * (1 - coalesce(BILL.DISCOUNT, 0)))  vat,
      sa.quantity, oi.vat_sign, oi.vat_percent,pbill.pdatetime close_datetime
      from order_item oi
        inner join orders on (orders.id = oi.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null and (bill.close_datetime is not null)
        join sales_area s on s.id = orders.id_sales_area
        join articles a on a.id = oi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        inner join sold_articles sa on (sa.id_order_item = oi.id )
        inner join pbill on (pbill.id = sa.id_pbill)
      where sa.sa_coef < 1.00 and not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
      group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number, s.id,
          s.name, s.number,oi.vat_sign, oi.vat_percent,oi.quantity, pbill.pdatetime ,bill.id, sa.quantity,
          coalesce(sa.parts_id,0)
union all
    select a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
        sum(pbi.price) amount,sum(pbi.vat) vat,sum(pbi.quantity),pbi.vat_sign, pbi.vat_percent,pbill.pdatetime close_datetime
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null  and (bill.close_datetime is not null)
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles a on a.id = pbi.id_articles
        join department d on d.id = a.id_departament
        join food_group f on f.id = d.id_food_group
        left outer join sales_area s on s.id = pbill.id_sales_area
        where not exists (select * from pbill_payments
            join payments on payments.id=pbill_payments.id_payments and payments.kind=3
            where pbill_payments.id_pbill=pbill.id)
        group by a.id, a.name, a.article_number,d.id, d.name, d.dep_number,f.id, f.name, f.gr_number,s.id, s.name, s.number,
           pbi.vat_sign, pbi.vat_percent,pbill.pdatetime ,bill.id;

COMMIT;

SET TERM ^ ;

-- restore of procedures
create or alter procedure fix_service_charge
as 
declare variable id_payment_sc bigint;
declare variable id_pbill bigint;
declare variable cur_id_pbill bigint;
declare variable price_sc decimal(17,4);
declare variable customer_amount_sc decimal(17,4);
declare variable c_price_sc decimal(17,4);
declare variable c_customer_amount_sc decimal(17,4);
declare variable price decimal(17,4);
declare variable customer_amount decimal(17,4);
declare variable vat_sc decimal(17,4);
declare variable id_pbill_payments bigint;
declare variable id_sc bigint;
begin
    select first 1 id from payments where payments.kind=6 and payments.is_active=1 into :id_payment_sc;
    if (:id_payment_sc=0) then exit;

    cur_id_pbill = 0;
    for
      select pbill_payments.id_pbill, sum(pbill_payments.price),sum(pbill_payments.customer_amount),
        sum(pbill_payments.c_price),sum(pbill_payments.c_customer_amount), sum(pbill_payments.vat) from pbill_payments
        where id_payments=:id_payment_sc and pbill_payments.price<0 and
        not exists(select neg_pbill.* from neg_pbill where neg_pbill.id_pbill=pbill_payments.id_pbill)
        group by pbill_payments.id_pbill
        into :id_pbill, :price_sc, :customer_amount_sc, :c_price_sc, :c_customer_amount_sc, :vat_sc do begin

        select first 1 pbill_payments.id from pbill_payments
          where id_payments <> :id_payment_sc and pbill_payments.id_pbill=:id_pbill and pbill_payments.price>0
          and pbill_payments.price>abs(:price_sc) into :id_pbill_payments;

        update pbill_payments set
            price = price + :price_sc,
            customer_amount = customer_amount + :customer_amount_sc,
            c_price = c_price + :c_price_sc,
            c_customer_amount = c_customer_amount + :c_customer_amount_sc,
            vat =  :vat_sc
        where id=:id_pbill_payments;

    end
    update pbill_payments set price = -price, customer_amount = -customer_amount,c_price = -c_price, c_customer_amount = -c_customer_amount, vat=-vat
    where id_payments=:id_payment_sc and price<0;
end
^

CREATE OR ALTER procedure GET_END_OF_DAY_TURNOVER (
    DATE_FROM timestamp,
    DATE_TO timestamp)
returns (
    AMOUNT decimal(17,4),
    AMOUNT_VAT decimal(17,4),
    QUANTITY integer,
    DEPNAME varchar(80),
    DEPNUMBER integer,
    SANUMBER integer)
as
begin
  for
    select
      sum((price + coalesce((select sum(cast(abs(quantity) as integer)* price)  from menu_item where menu_item.id_menu = order_item.id_menu),0))*Coalesce(sold_articles.quantity * sold_articles.SA_COEF, order_item.quantity) * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount,
        sum(vat*Coalesce(sold_articles.quantity * sold_articles.SA_COEF, order_item.quantity) * (1 - coalesce(BILL.DISCOUNT, 0)) ) amount_vat,
        sum(Coalesce(sold_articles.quantity, order_item.quantity)) quantity, department.name depname,
        department.dep_number depnumber, sales_area.number sanumber
        from  order_item
    inner join orders orders on (orders.id = order_item.id_orders)
        inner join bill on bill.id=orders.id_bill and bill.pbill_number is null
        join articles on articles.id = order_item.id_articles
        join department on department.id = articles.id_departament
    inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id )
    inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
        left outer join sales_area on sales_area.id = pbill.id_sales_area
        where coalesce(articles.article_hash,0)=0 and coalesce(articles.promo,0)=0
        group by department.name, department.dep_number, sales_area.number
    union all
    select
       sum(order_item.quantity * menu_item.quantity * menu_item.price * sa.quantity * sa.SA_COEF) * (1 - coalesce(BILL.DISCOUNT, 0)) amount,
        (sum(order_item.quantity * sa.quantity * sa.SA_COEF)  * (1 - coalesce(BILL.DISCOUNT, 0))) amount_vat,
        sum(order_item.quantity * sa.quantity) quantity, department.name depname, department.dep_number depnumber, sales_area.number sanumber
        from menu_item
        join order_item on order_item.id_menu=menu_item.id_menu
        join orders orders on (orders.id = order_item.id_orders)
        join bill on bill.id=orders.id_bill and bill.pbill_number is null
        join articles on articles.id = menu_item.id_articles
        join articles mainart on mainart.id = order_item.id_articles
        join department on department.id = articles.id_departament
        join sold_articles sa on (sa.id_order_item = order_item.id )
        join pbill on (pbill.id = sa.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
        left outer join sales_area on sales_area.id = pbill.id_sales_area
        where coalesce(articles.article_hash,0)=0 and mainart.promo=1
        group by department.name, department.dep_number, sales_area.number, BILL.DISCOUNT
    union all
    select sum(pbi.price*pbi.quantity) amount,
        sum(pbi.vat*pbi.quantity) amount_vat, sum(pbi.quantity), department.name depname,
        department.dep_number depnumber, sales_area.number sanumber
        from pbill_item pbi
        inner join pbill on pbi.id_pbill = pbill.id
        inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null
        join pbill_return on pbill_return.id_pbill=pbill.id
        join articles on articles.id = pbi.id_articles
        join department on department.id = articles.id_departament
        left outer join sales_area on sales_area.id = pbill.id_sales_area
        where pbi.pdatetime>=:date_from  and pbi.pdatetime<:date_to and coalesce(articles.article_hash,0)=0
             and not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id)
        group by department.name, department.dep_number, sales_area.number
    into :amount, :amount_vat, :quantity, :depname, :depnumber, :sanumber
  do begin
     suspend;
  end
end
^

create or alter procedure GET_EXACTGLOBE_TURNOVER (
    DATE_FROM timestamp,
    DATE_TO timestamp)
returns (
    AMOUNT decimal(17,4),
    AMOUNT_VAT decimal(17,4),
    BNAME varchar(50),
    ACC varchar(100),
    VAT_SIGN varchar(10))
as
begin
  for
select  order_item.price*sold_articles.quantity*sold_articles.sa_coef * (1-coalesce(bill.discount,0)) amount,
        order_item.vat*sold_articles.quantity*sold_articles.sa_coef amount_vat,
        bookkeeping.name bname, bookkeeping.account acc, order_item.vat_sign vat_sign
    from  order_item
    inner join orders on orders.id=order_item.id_orders
    inner join bill on bill.id=orders.id_bill and bill.pbill_number is null
    inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id )
    inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    join articles on articles.id = order_item.id_articles
    left outer join order_item_bookp on order_item_bookp.id_order_item = order_item.id
    left outer join bookkeeping on bookkeeping.id = order_item_bookp.id_bookkeeping_turnover
union all
    select pbill_item.price*pbill_item.quantity * (1-coalesce(bill.discount,0)) amount,
        pbill_item.vat*pbill_item.quantity amount_vat, bookkeeping.name bname,
        bookkeeping.account acc, pbill_item.vat_sign vat_sign
    from  pbill_item
    inner join pbill on pbill.id=pbill_item.id_pbill and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null
    join pbill_return on pbill_return.id_pbill=pbill.id
    join articles on articles.id = pbill_item.id_articles
    left outer join pbill_item_bookp on pbill_item_bookp.id_pbill_item = pbill_item.id
    left outer join bookkeeping on bookkeeping.id = pbill_item_bookp.id_bookkeeping_turnover
union all
    select menu_item.price * cast(abs(menu_item.quantity) as integer) * sold_articles.quantity*sold_articles.sa_coef * (1-coalesce(bill.discount,0)) amount,
        menu_item.vat * menu_item.quantity * sold_articles.quantity*sold_articles.sa_coef amount_vat, bookkeeping.name bname,
        bookkeeping.account acc, menu_item.vat_sign vat_sign
    from  menu_item
    inner join order_item on order_item.id_menu = menu_item.id_menu
    inner join orders on orders.id=order_item.id_orders
    inner join bill on bill.id=orders.id_bill and bill.pbill_number is null
    inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id )
    inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:date_from and pbill.pdatetime<:date_to)
    join articles on articles.id = order_item.id_articles
    left outer join menu_item_bookp on menu_item_bookp.id_menu_item = menu_item.id
    left outer join bookkeeping on bookkeeping.id = menu_item_bookp.id_bookkeeping_turnover
    into :amount, :amount_vat, :bname, :acc, :vat_sign
  do begin
     suspend;
  end
end^

create or alter procedure GET_END_OF_DAY_TURNOVER2 (
    DATE_FROM timestamp,
    DATE_TO timestamp,
    INCLUDE_ROOM_PAYMENTS integer)
returns (
    ARTICLE_ID bigint,
    ARTICLE_NAME varchar(100),
    ARTICLE_NUMBER integer,
    DEPARTMENT_ID bigint,
    DEPARTMENT_NAME varchar(100),
    DEPARTMENT_NUMBER integer,
    GROUP_ID bigint,
    GROUP_NAME varchar(100),
    GROUP_GR_NUMBER integer,
    SALES_AREA_ID bigint,
    SALES_AREA_NAME varchar(100),
    SALES_AREA_NUMBER integer,
    PRICE decimal(17,4),
    VAT decimal(17,4),
    QUANTITY integer,
    VAT_SIGN varchar(10),
    VAT_PERCENT decimal(17,4))
as
begin
  if (INCLUDE_ROOM_PAYMENTS=0) then begin
      for
        select article_id, article_name, article_number,
             department_id, department_name, department_number,
             group_id, group_name, group_gr_number,
             sales_area_id, sales_area_name, sales_area_number,
             sum(price), sum(vat), sum(quantity), vat_sign, vat_percent
        from view_turnover_excl_room v
        where close_datetime >= :DATE_FROM and close_datetime < :DATE_TO
        group by article_id, article_name, article_number,
             department_id, department_name, department_number,
             group_id, group_name, group_gr_number,
             sales_area_id, sales_area_name, sales_area_number,
             vat_sign, vat_percent
        into :article_id, :article_name, :article_number,
             :department_id, :department_name, :department_number,
             :group_id, :group_name, :group_gr_number,
             :sales_area_id, :sales_area_name, :sales_area_number,
             :price, :vat, :quantity, :vat_sign, :vat_percent
     do begin
         suspend;
     end
  end else begin
      for
        select article_id, article_name, article_number,
             department_id, department_name, department_number,
             group_id, group_name, group_gr_number,
             sales_area_id, sales_area_name, sales_area_number,
             sum(price), sum(vat), sum(quantity), vat_sign, vat_percent
        from view_turnover_incl_room v
        where close_datetime >= :DATE_FROM and close_datetime < :DATE_TO
        group by article_id, article_name, article_number,
             department_id, department_name, department_number,
             group_id, group_name, group_gr_number,
             sales_area_id, sales_area_name, sales_area_number,
             vat_sign, vat_percent
        into :article_id, :article_name, :article_number,
             :department_id, :department_name, :department_number,
             :group_id, :group_name, :group_gr_number,
             :sales_area_id, :sales_area_name, :sales_area_number,
             :price, :vat, :quantity, :vat_sign, :vat_percent
     do begin
         suspend;
     end
  end
end^

SET TERM ; ^

execute procedure set_logging_cs;
execute procedure  fix_service_charge;
execute procedure set_logging_off;

COMMIT;
