$LANGUAGE	German (Switzerland)
$FORMNAME	SalesAreaExceptEmbEntityManager
Sales area	Verkaufsbereiche
Period	Periode
Price	Preis
Sales area: Exceptions	Verkaufsbereich: Ausnahmen
Preparation area	Produktionsbereich
Order printer	Bon Drucker
Order layout	Bon Layout
Bill printer	Rechnungsdrucker
Bill layout	Rechnungs Layout
Kitchen screen source	
Kitchen screen destination	
$FORMNAME	ArticleAvailEmbEntityManager
Article: Available	Artikel: Vorhanden
Sales area	Verkaufsbereiche
New article available	Neuer Artikel vorhanden
Article available	Verfügbare Artikel
Sequence	Sequenz
RM sequence	Handy Sequenz
Limited	Begrenzt
Period	Periode
New promo available	Neue Promo Vorhanden
Promo available	Promo vorhanden
$FORMNAME	ArticleDiscountEmbEntityManager
After	Nach
Article: Discount	Artikel: Rabatt
New article discount	Neuer Artikel Rabatt
Article discount	Artikel Rabatt
Prices	Preise
Supplement	Extras
V.A.T.	MwSt
Save points	Sparpunkte
$FORMNAME	ArticleDiscountPriceEmbEntityManager
Price name	Preis Name
Price	Preis
Currency	Währung
Article: Discount price	Artikel: Rabattpreis
$FORMNAME	ArticleEntityManager
New article	Neuer Artikel
Article	Artikel
Internal	Text Intern
Department	Artikelgruppe
General	Allgemein
Area	Bereich
Options	Optionen
Time	Zeit
Control	Steuerung
Daily stock	Tages Bestand
Additional	Zusätzlich
Manual	Freie Preiseingabe
Hash	Auslage
Prices	Preise
Available	Artikel verfügbar
Notify	Bondruck
Course	Gang
Use group	Verwende Gruppe
Promotion	Promotion
Commission	Provision
Articles	Artikel
Group	Sparte
Category	Hauptgruppen
Processing record: %d of %d	Verarbeitung Record: %d von %d
Free per person	Gratis pro Person
Article name cannot be empty	Artikelname darf nicht leer sein
Article type	Artkel Typ
Recipe	Rezept
Item name	Artikel Name
Sales U/M	Umsatz U/M
Inventory item	Invertur Artikel
Unity of measure	Einheit der Maßnahme
Alias name	Alias Name
Consolidate quantity	Konsolidieren Menge
Articles in future consolidate rule	Artikel in Zukunft festigen Regel
Disabled	deaktivert
Handling limit	Ein Grenzbereich
Unit price	Stückpreis
Show in kitchen screen	Ziege auf Küchenmonitor
Can be bought by save points	Mit Punkte erhalten?
Decrease save points	Verringern Sie Sparpunkte
Free option	Freie Option
HQ ID	
Rent units	Mieteinheit
Beverage Control	Durchflusskontrolle
Rent	Miete
Tip	Trinkgeld
BC Recipe	BC Rezeptur
Use PLU number	verwende PLU Nummer
No PLU number	Keine PLU Nummer
You cannot delete this article. It was imported from a 3rd party system	Sie können diesem Artikel nicht löschen. Es war von 3d Parteien-System importiert.
Article with the same name already exists	Artikel mit dem gleichen Namen besteht bereits
Preparation Area	Vorbereitungsort
HHT Name	
All promos	Alle Promos
New promo	Neu Promo
Sample	Probe
* Feature is not licensed	
Exclude article from transfer	
Add only one option per free option group	
Ignore reset course question	
Allergen	
Allergen list	
Can be sent to Pickup as single item	
Rental prices	
Rental price table	
Cannot be discounted	
Must be combined	
Allergen info	
Ask course when ordered (only for changeable course)	
Workflow type	
Kitchen screen workflow	
Put on hold when ordered	
Surcharge	
Age group	
Orderman view	
PC view	
HHT view	
Main price	
Sale	
Purchase	
Usage U/M	
Credit / Debit	
Empty article button	
Warning level	
Preparation time	
Weight required	
Price per hour	
Price per day	
Upsellers	
Misc	
Kitchen screen workflow stages	
Workflow stages	
Single stage	
Multi stage	
Stock/Allergens	
Kitchen screen	
Supplier	
Open fields	
Free Num Field 1	
Free Num Field 2	
You cannot delete this article	
You cannot delete this article. It is ordered and the table is not closed yet	
You cannot delete this time article. It is ordered and the table is not closed yet	
You cannot delete this article. It is linked to a stock ingredient	
All departments	
All categories	
Article with the same name already exists. Continue?	
Article with the same number already exists. Continue?	
You cannot delete this department. Articles from this department are ordered on a table that is not closed yet	
You cannot delete this department. Articles from this department are linked to stock ingredients	
Regular	
Take away	
KS Type	
$FORMNAME	DepartmentEntityManager
Promotion	Promotion
Commission	Provision
Options	Optionen
Barcode	Barcode
Quantity	Anzahl
Active	Aktiv
After	Nach
Period	Periode
Round	Abgerundet
Discount	Rabatt
Menu	Menu
Hide not paid in reports	Ohne Umsatz nicht in Bericht drucken
Hide if article	Nicht anzeigen, wenn Artikel
Hide if option	Nicht Anzeigen, wenn Option
Info	Info
Warning level	Meldebestand
PC color	PC Farbe
PC text	PC Text
PC font	PC Schriftart
New department	Neue Artikelgruppe
Department	Artikelgruppe
Name	Name
Number	Nummer
&1 General	&1 Allgemein
&2 Normal articles	&2 Artikelsortierung
&3 Special articles	&3 Spezielle Artikel
Group	Gruppe
Available	Artikelgruppe verfügbar
Supplement	Extras
Condiment	Kommentare
Special articles	Spezielle Artikel
Normal articles	Artikelsortierung
Departments	Artikelgruppen
Category	Hauptgruppen
Sales area	Verkaufsbereiche
Ingredient	Einkaufsartikel / Zutat
Recipe	Rezept
Not used in stock	Kein Lagerartikel
Standard dosage	Normaldosierung
Alternative dosage	Alternative Dosierung
Increase save points	Erhöhung Bonuspunkte
Calculation starts after	Berechnung startet nachher
per period from	pro Zeitraum von
Rounding up to	Aufrunden auf
Default standard dosage	Standarddosierung
Default alternative dosage	Standard anderweitige Dosierung
BeCo Group	BeCo Gruppe
BeCo Locations	BeCo Bereich
Beconet	
Common	allgemein
Do not show in balance	Nicht im Abgleich zeigen
Get location from PC when ordered	Hole Lage vom PC wenn bestellt
Normally, the location for ordered articles is taken from "Beverage Control/BeCo locations".	Normale Standorte für bestellte Artikel sind aufgenommen mit Sortierung "Durchfeluuskontrolle / Beco Bereiche"
With this option enabled, the location specified in 'Computers' is taken first.	Ist die Auswahl an, wird als erste Lokal vom PC bestiimmt.
Note: for poured articles, the location is always determined using the article's BeCo Locations	Notiz: für den Artikelbereich ist der BeCo Bereich als verwendet bestimmt
HHT color	HHT Farbe
HHT text	HHT Text
HHT font	HHT Schrift
Font	Schrift
Calculation method	Berechnungsmethode
Start date	Startdatum
Purchase order	Umstellung
Type	Typ
Stock location	Lagerort
Must have options	
Free option groups	
Barcodes	
Purchase price Ex.VAT	
Reservation bonus group	
Bonus groups	
Use single Pickup device	
Condition check in order	
PC alt. font	
HHT alt. font	
Orderman color	
Orderman text	
Orderman font	
Orderman alt. font	
Fast Lane department	
&4 Control	
HQ ID	
Age group	
PC view	
HHT view	
Orderman view	
Voucher	
Replenish from	
Replenish to	
Virtual sale price	
$FORMNAME	exceptions
Default currency is not defined	Voreingestellte Währung ist nicht definiert
Please specify default currency	Voreingestellte Währung eingeben
There are no more courses	Es gibt nicht mehr Gänge
Current bill reopened. You cannot transfer it by article	Diese Rechnung wurde wieder geöffnet. Sie können die Artikel nicht übertragen
This computer has no printers installed	Auf diesem Computer sind keine Drucker installiert
Client is in black-list	Kunde ist auf Blackliste
Card unknown	Karte nicht erkannt
Garbage collection settings are not defined for article %s	Abfallansammlung Einstellungen sind nicht für Artikel %s definiert
Article not available	Artikel nicht vorhanden
VAT for the article is less than zero	Mwst. für diesen Artikel ist weniger als null.
VAT for the article is more than 100%	Mwst. für diesen Artikel ist mehr dann 100%
Client not defined	Kunde nicht definiert
Table busy. You cannot save the work	Tisch bereits bearbeitet, Sie können die Bestellung nicht speichern
Order was changed from another computer. You cannot save the work	Bestellung ist von einem anderen Computer bearbeiten worden, Sie können die Bestellung nicht speichern
An error occured on fiscal printer: "%s"	Eine Störung trat auf dem Fiskal Drucker auf: "%s"
You cannot pay with this payment type. Use split bill	Sie können nicht mit dieser Abrechnungsart zahlen. Verwenden Sie Split - Rechnungs Funktion.
Part %s of table %d busy. You cannot reopen bill	Teil %s der Tisches%d bereits beschäftigt. Sie können diese Rechnung nicht wieder öffnen
One or more combi deals is not completed, bill cannot be closed	Menü wird nicht noch beendet, Rechnung kann noch nicht abgeschlossen werden
Not enough money on account	Nicht genügend Geld auf dem Konto
Client is not defined	Kunde ist nicht definiert
Discount amount must be more than zero	Rabattbetrag muß grösser als null sein
Reason for discount not defined	Rabatt Grund ist nicht definiert
Sales area is not specified for computer "%s"	Verkaufsbereich wurde nicht für Computer "%s" spezifiziert
Default currency not defined	Voreingestellte Währung nicht definiert
Sales area for table number %d not defined	Verkaufsbereich für Tischnummer %d nicht definiert
You cannot work with table number "%s"	Sie können nicht mit Tischnummer "%s" arbeiten
You cannot close table number "%s"	Sie können Tisch "%s" nicht schließen
A4 ticket cannot be printed from HHT	A4 Rechnung kann nicht auf mobile Computer gedruckt werden
Unable to load picture: %s	Nicht möglich, Bild zu laden: %s
You have to delete account history related to this client	Sie müssen die Konten History dieses Kunden löschen
Please enter client name	Bitte Kundennamen eingeben
This computer was not found in database	Dieser Computer wurde nicht in der Datenbank gefunden
Deposit printer is not defined	Anzahlungs Drucker ist nicht definiert
Deposit layout is not defined	Anzahlungs Layout ist nicht definiert
Negative balance	Negativer Saldo
Please enter report layout	Tragen Sie bitte das Berichts Layout ein
Course is not defined	Gang wurde nicht definiert
Restaurant settings are empty	Restauranteinstellungen sind leer
Restaurant hardware settings are not filled in properly	Restaurant Hardwareeinstellungen sind nicht richtig ausgefüllt
Restaurant hardware settings are empty	Restaurant Hardwareeinstellungen sind leer
Wrong operation code %d	Falscher Operationscode %d
Wrong PSP model %d	Falsches PSP model %d
Test description %d	Testbeschreibung %d
Only reserved tables can be opened in this sales area	Nur reservierte Tische können in diesem Verkaufsbereich geöffnet werden
Administrator must first allow to do returns	Administrator muß Erlaubnis geben
Transfer to this table (part) is impossible, it has pro forma status	Übertragungen auf diesen Tisch ist unmöglich: Zwischenrechnung ist bereits gedruckt.
Quantity %d cannot be ordered	Menge %d kann nicht bestellt werden
Some tables from reservation list are busy	Einige Tische von der Reservierungsliste sind belegt
You must void the ordered article first	Sie müssen den bestellten Artikel erst stornieren
You can re-open this bill only from %s computer	Sie können diese Rechnung nur von %s Computer wieder öffnen
Discount value must be more than zero	Rabattwert muß grösser als null sein
Unknown code	Unbekannter Code
Table part for transfer reopened bill must be empty.	Zum Tischretten muss dieser Untertisch leer sein.
Transfer from this table (part) is impossible, it has pro forma status	Übertragungen auf diesen Tisch ist unmöglich: Zwischenrechnung ist bereits gedruckt.
Service charge value must be numeric	Service Provision muss nummerisch sein
You cannot reopen bill. One bill from this transaction is already open	Sie können diese Rechnung nicht wieder öffnen, da ein Teil dieser Transaktion bereits geöffnet wurde
You can only reopen bill during hours of service	Sie könne Rechnungen nur während der Ladenöffnungszeit öffnen
Invoice layout is not defined for computer %s	Rechnungs Layout ist nicht definiert für Computer %s
Article cannot be modified, it was imported by a 3rd party system	Artikel nicht änderbar. Es war von 3D Part-System importiert
Total payment (%f) must be more than tips amount (%f)	Zahlungen insgesamt (%f) muss mehr als Tipps Betrag (%f) ergeben
It is impossible to unlock proforma Order in future	Es ist unmöglich den  Proforma Auftrag in Zukunft zu entsperren
It is impossible to change client for Order in future	Es ist unmöglich, Kunde für Auftrag in Zukunft ändern
Split bill is not possible for Order in future	Splitrechnung ist nicht für Auftrag in Zukunft möglich
You cannot print proforma for Order in future	Man kann nicht drucken Proforma für Auftrag in Zukunft
Please enter numeric value	Bitte numerischen Wert eingeben
There are no more free tables in the book on client table range	Kein Tisch mehr in dem Bereich frei zum reservieren
You cannot change client for order in future	Sie können nicht den Kunden für Bestellungen in Zukunft ändern
Order in future can be started only on empty Order screen	Bestellungen in Zukunft kann nur auf leeren Auftrag gestartet werden
Locker cannot be assigned to order in future	Schrank kann für zukünftige Bestellungen verwendet werden
Order in future cannot be moved to locker	Bestellungen in Zukunft können nicht auf Schrank transfieriert werden
Please enter reason of void	Bitte geben Sie den Stornogrund ein
Order must be empty to switch to normal mode	Es dürfen keine Aufträge aktuell sein um in den Normal Modus zu wechseln
Client info is disabled in order in future	Kunde ist gesprerrt für Bestellung in Zukunft
You can only give discount per article for order in future	Sei können nur Rabatt auf einen Artikel für eine Bestellung in zukunft zuweisen
This action cannot be called from order in future	Diese Handlung kann nicht von der Bestellung in der Zukunft genannt werden
Discount payment type in Restaurant settings not specified	Rabatt- Zahlungstyp in nicht angegebenen Restaurant-Einstellungen vorhanden
Locker cannot have value "0"	Schrank kann nicht Wert "0" haben
Option limit is %d, you already entered %d	Auswahl-Grenze ist %d, Sie gingen bereits in %d ein
Supplement limit is %d, you already entered %d	Beilagengrenze ist %d, Sie haben bereits %d
Condiment limit is %d, you already entered %d	Kommentargrenze ist %d, Sie haben bereits %d
The last part is full.	Der letzte Teil ist voll.
Table %s can't be closed on PC "%s", reason: %s	Tisch %s kann nicht auf dem PC "%s" geschlossen werden: Grund %s
There are no free tables in the book on client table range	Es gibt keine freien Tische um Buchungen auf Kundentisch zu tätigen
Please define return reason	Definieren Sie bitte den Rückgabegrund
You already entered some articles, but have not saved the order yet. Client cannot be changed.	Sie tätigten weitere Artikeleingaben, aber haben die Bestellung nicht mit Bonuspunkte versehen. Kunde kann nicht geändert werden.
No payment found for main currency with type CASH	Keine Zahlung mit BAR gefunden
User %s must finish his work	Bediener %s muss seine Arbeit beenden
Amount is incorrect	Betrag ist falsch
You cannot save empty order In future	Sie können keine leere Bestellung für die Zukunft speichern
Option cannot be a menu (article "%s" is a menu)	Auswahl kann nicht ein Menü sein (Artikel "%s" ist ein Menü)
Only one-by-one menu ordering is allowed	Nur eins nach dem anderen wird als Bestellung im Menü erlaubt
%d - is limit for this article	%d - ist die Grenze für den Artikel
Discounted article cannot be voided	Rabattartikel kann nicht gelöscht werden
Bill printer is not available	Rechnungsdrucker ist nicht verfügbar
Close the time article prior to closing the bill	Schließen Sie vor der Rechnung bitte zuerst den Zeitartikel
Order contains discounted and not saved articles and cannot be voided	Bestellung enthält Rabatte und Bonuspunkte und kann nicht gelöscht werden
Entered payment values not enough to pay the part "%s"	Eingegebene Zahlung zu gring, um den Teil "%s" zu bezahlen
Client account balance is not empty. Bill cannot be deleted.	Kundenkonto Vergleich ist nicht leer. Rechnung kann nicht gelöscht werden.
Table %d(%s) is occupied.	Tisch %d (%s) wird besetzt.
Payment with type "Account" does not exist.	Die Zahlung mit dem Typ-"Account" besteht nicht.
Not enough save points	Nicht genug Bonuspunkte
There has to be at least one article for discount	Die Artikel-Menge für den Preisnachlass muss mehr als Null sein
Please specify discount reason	Geben Sie bitte den Rabatt- Grund an
Client with this name already exists	Kunde mit Name existiert bereits
Layout "%s" not found or it is not a report	Layout "%s" wurde nicht gefunden oder ist nicht ein Bericht
Layout is not defined	Layout ist nicht festgelegt
Default sales area not defined	Nicht eingestellter Verkaufsbereich
You already entered one or more location items. So you cannot change Usage U/M	Sie gingen bereits in einen oder mehr Bereichesartikel. So können Sie nicht die Benutzung U/M ändern
You already entered one or more supplier items. So you cannot change Usage U/M	Sie gingen bereits in einen oder mehr Lieferantartikel ein. So können Sie nicht die Benutzung U/M ändern
You already entered one or more substitution items. So you cannot change Usage U/M	Sie gingen bereits in einen oder mehr Ersatz-Artikel ein. So können Sie die Benutzung U/M ändern
You should enter ingredient usage units of measure on first page	Sie sollten in Zutat-Gebrauch-Einheiten des Maßes auf der ersten Seite eingeben
You should enter ingredient usage unit of measure on first page	Sie sollten in Zutat-Gebrauch-Einheit des Maßes auf der ersten Seite eingeben
This document cannot be deleted - it is already conducted	Dieses Dokument kann nicht gelöscht werden - es wird bereits geführt
This document cannot be changed - it is already conducted	Dieses Dokument kann nicht geändert werden - es wird bereits geführt
There are no items for adjustment	Es sind keine Artikel zum einstellen
Stock not closed	Lager nicht geschlossen
Please select Stock location	Bitte Lagerort eingeben
Please enter part Payment	
Payment kind must be "Account"	
There are no free units for this article	
Rental end date/time must be beyond the start date/time	
Rental start date/time must be beyond the current date/time	
Rental end date/time must be beyond the current date/time	
There is no free article	
Wrong repair reason barcode	
Please choose another barcode	
Barcode is currently not in use	
Please choose/scan barcode to return	
Only one item can be selected to be prolonged	
Barcode %s is currently not in use	
Barcode %s is currently not rented/repaired	
Barcode is currently reserved	
Barcode is currently rented	
Barcode is currently waiting for reservation confirmation	
This barcode is not available	
Only unit with status "Rented" can be linked to Barcode	
Barcode not found	
This unit is not linked to barcode yet	
This barcode is currently in use	
This barcode is currently in repair	
Incorrect Rental ID	
Please enter rent barcode	
All rental barcodes must be defined	
Please define linked ingredient/recipe	
Please define Supplier	
Please define usage unity of measure	
Invalid card table range: [%d,%d]	
Card "%s" is already reserved for table %s	
Card "%s" is in blacklist	
There are no free tables in the table range	
This bill is linked to another Smart Card	
Sales area for Card table range is not defined	
Card already has linked orders. Limit cannot be changed	
Cannot reserve a free table for transfers. Change card table range	
Bill on table %s paid partially. Consolidation impossible.	
This is the same card. Please choose another one.	
Please scan new Card code	
Split bill is not possible, because bill was reopened	
Proforma not found	
This voucher already used for payment	
Total parts covers exceeds Bill number of covers	
The parts should be paid one by one because one of the payments requires a gift card payment	
Group does not have members	
One of the members in the list has wrong identifier	
Client %s is not allowed on Invoice	
Payment with type "Account" does not exist	
It's not allowed to change client on Payment screen	
Please select payment part	
You cannot pay a split bill with two or more cards	
Please choose payment with currency: %s	
Please confirm after each payment	
Bill with fiscal number %s not found	
Gift card payment cannot be done manually	
Please specify payment type	
Please choose bill	
Table is busy, order cannot be copied	
The bill was paid with multiple payment types and cannot be returned to OIF state	
The bill was paid manually and cannot be returned to OIF original state	
Please enter correct quantity and total amount	
Entered amount exceeds Bill total	
You cannot split articles that have already been split	
Please choose articles to pay	
Split articles can only be paid in one payment	
This operation is not allowed in Production Screen Professional mode	
User is not permitted to transfer table	
The card is linked to table %s and cannot be used	
Smart card %s is not reserved. Ordering is impossible.	
Option can't be modified, because the bill is already paid partially.	
Service charge amount can't be changed, because the bill is already paid partially.	
Order must be empty	
You can't modify menu: the bill is paid partially	
Current order must be empty before OIF can be activated on this table	
User has no permissions to make void	
Date range can only be changed for not confirmed articles	
Price level cannot be changed, because the bill is paid partially.	
Vat level cannot be changed, because the bill is partially paid	
Void limit is spent	
Please define Take away time slot	
Please choose client or enter order name	
Newyse authorisation failed for card [%s]	
To change client start new booking	
Prepaid system: ordering not possible	
Expected article with manual price	
Unable to specify price	
Coupon is spent	
Please finish Order in Future	
Transfer cannot be completed, the original table has split articles	
Bill paid partially. Transfer impossible.	
Table has been blocked for article transfers, you cannot complete the transfer	
There are no free tables to transfer order to	
You have to login before you can pay open tables	
There is no payment of type "Cash" in this Database	
Table %d cannot be closed automatically. Please do it manually.	
One or more tables cannot be closed automatically. Please do it manually.	
There are no free tables in table range for overdue rentals	
Table number "%d" cannot be opened	
Can't confirm order on PC "%s", reason: %s	
Please choose Shared account	
Unable to open table %d to start ordering	
Please complete the previous order on table %d	
You cannot tranfer this article.	
Article [%s] not found	
No departments found in database	
No courses found in database	
Incorrect Tap Number. Must be from %d to %d	
Incorrect Dosage. Must be from %d to %d	
Incorrect Size. Must be from %d to %d	
Incorrect Interval. Must be from %d to %d	
Beverage Control device not selected	
Beverage Control Device for PLU=%s not found	
Please define time slot for take away order	
Remaining value article (voucher) not found for group "%s"	
Remaining value article (voucher) for group "%s" has "manual" setting enabled	
Remaining value article (negative for payment) not found for group "%s"	
Remaining value article (negative for payment) for group "%s" has "manual" setting enabled	
You cannot modify the order, discount on total is applied and bill is already partially paid	
Pre-auth deposits limit is exceeded	
No cards linked to the order	
Smart card limit is reached	
Quantity must be lower than 100000000	
You can't mix positive and negative amounts in one Bill, when Clean Cash is installed	
Article %s does not have enough daily stock value.	
All combo-promo items must have the same VAT%	
Prepaid system: menu ordering not possible	
Split article cannot be voided	
On hold article cannot be voided	
Bill paid partially. Void impossible.	
Prepaid system: void not possible	
Choose only articles of the same course	
It's impossible to change the course, there is an on hold item in the current course	
You should open Card table overview and scan all cards	
Direct Zapper payments not allowed	
Prepaid system: The bill can't be re-opened	
You cannot reopen bill, it does not exist or is not active	
You cannot reopen bill. Z-report was printed after closing the bill	
Reopening bill not allowed	
To reopen the bill, table must be empty	
It's impossible to reopen bill paid by payment Cashdro	
It's impossible to reopen bill paid by payment Newyse	
Not allowed to reopen bill paid by Zapper	
Some articles are split. Discount is impossible	
Client discount can't be applied again	
Selected articles cannot be discounted, because the bill is paid partially.	
Discount cannot exceed 100%	
Discount value can't be more than Bill total	
cannot return prepaid articles	
EFT %s does not support Completion procedure	
Order contains sold coupons. Void impossible	
Order contains sold coupons. Partial payment impossible.	
Order contains split articles. Void impossible	
Bill paid partially. Discount impossible.	
Client discount can't be cancelled	
No EFT linked to this payment mode	
Request is not supported by EFT %s	
Order cannot be closed until all combined articles are completed	
Specified card has no client assigned	
Cannot create discount article	
Insufficient stock for article %s	
The bill is paid partially and discount value can't be changed	
Card Operation %s not found	
No EFT for selected payment mode on this PC	
Operation not supported by EFT %s	
Returned empty PrepaidStripe by EFT %s	
Gift cards operations printer is not defined	
Gift cards operations layout is not defined	
Card number length cannot exceed  %d symbols	
Deposit value cannot exceed %f	
Please define client	
Bill %s partially paid. Client cannot be changed	
Client is linked to Shared Account, emailing invoice is impossible.	
Client email not defined.	
Mail service is not configured	
Number cannot be empty or zero	
Articles quantity must be more than 1	
Name cannot be empty	
Start date must be before the end date	
Article quantity cannot be zero or negative	
Please choose Coupon template	
You cannot decrease the number of coupons	
You cannot print more than %d coupons	
Coupon cannot be changed, because it was already used	
Please define Coupon layout	
Please define Coupon/Voucher printer	
Please choose department of type Voucher/Coupons	
Coupon is blocked	
Coupon is already used	
Coupon is expired	
Please choose coupons print type	
Maximum number of coupons to be printed is %d	
Please choose coupon.	
Table is occupied	
OIF cannot be created on in the Past.	
Number of periods can't be fewer than 1	
Discount can only be applied on total	
Bill already paid partially, discount amount can't be changed	
Discount amount can't exceed bill total	
Please choose payment type	
Please choose invoice to pay	
Invoice is not specified. Use function "Mark Invoice as Paid"	
Please select orders	
Articles need to have the state "waiting" in order to be started	
Please choose From & To Kitchen screen devices	
Please choose courses to redirect	
The routing cannot proceed. KS device "from" must be different than KS device "to"	
The routing cannot proceed. There are other routing rules from this kitchen screen with courses "%s"	
Bill is closed. Courses cannot be changed.	
Table is active	
You cannot clock out. Not all PSP tips are assigned.	
You cannot execute action as you still have tables open. Transfer them to another waiter or close the bills.	
Computer (id=%s) not found	
Please log in	
Orderman BT printer for Orderman %d not found	
Attempt to print to Orderman BT printer from normal POS	
A4 printing not supported in this process	
Please specify save points discount reason in Restaurant settings	
Please define Take away table range	
Please define Take away time slot minutes number	
Please define take away period to be used	
Maximum bill number(%d) per time slot %s is exceeded	
Please define Take away time block	
You cannot re-assign delivery	
This user cannot be assigned. Check take away user role.	
Please choose order	
Please define Take away time slot transfer to	
Please select bills for transfer	
Please define Time slot tranfer to	
Selected time slot is not valid	
This table cannot be transferred - it's already started in KS	
Please define deliverer	
Bill can only be reprinted once since CleanCash installed	
Operation not supported by EFT	
Please enter void reason	
Voucher range %d to %d is used up completely. Set a new range to create vouchers	
Voucher range %d to %d is exceeded, last number is %d. Set a new range or reduce the number of multi-vouchers	
Voucher cannot be paid by Hash payment	
Voucher cannot be paid by Service charge payment	
Voucher cannot be paid by Discount payment	
Voucher cannot be paid by Voucher payment	
Please enter correct Voucher amount	
Barcode is empty	
Barcode already exists	
Voucher with Barcode already exists	
Scanned barcode already exists	
Voucher is already reserved to pay	
Please enter quantity of vouchers	
Please enter voucher amount	
Voucher number range is exceeded. Set a new range or reduce the number of multi-vouchers	
Voucher bunch cannot be saved: all numbers are used	
Sender and Recipient must be different users	
Transfer waiter functionality is not licensed	
Table %d is occupied.	
Unknown Cashdro model: %s	
No Cashdro device configured for this computer	
Client functionality is not licensed	
Please enter card number	
Client with this card number already exists	
It's impossible to clear Shared account	
No client is linked to shared account	
Client doesn't exist	
Client card is expired (Expiration date is %s)	
Please define Coupon layout and printer	
Hotel Interface not found	
Connect to HotelConcepts failed: %s, %s	
Error: end-of-day paymaster not checked-in	
Not allowed to combine room payment with other payments in the same bill	
Folio number is empty	
Unable to load CashSro DLL: %s	
Unable to connect to CashDro: %s	
CashDro payment failed: %s	
CashDro payment cancelled	
Guest folio doesn't seems to start with leading string [%s]	
HTTP Connection Failure, Status Code: %d	
There are pending articles, cannot finish course	
It's impossible to change course. There's an unfinished item in kitchen screen	
You cannot delete these items: some of them are already prepared	
Course must have state "Started" to start countdown	
Only prepared course can be restarted	
Preparation for article "%s" has not yet started	
Stage with the same number already exists	
There is no Newyse configured for this computer	
Your license doesn’t allow you to view/edit settings in back office related to price levels	
Please define Sales area	
Incorrect card swiped	
Bill printer not defined for computer %s	
Waiting for empty Beverage Control Queue timeout	
There are clocked in users. Z-report cannot be exported.	
Complete meal layout not defined	
Complete meal ticket not defined	
Printer not defined	
Layout not defined	
FDM is not configured for this PC	
The "%s" value can't be more than 100 days.	
"EFT Payment Receipt" dataset cannot be used with this payment mode	
Your license doesn't allow you to view/edit settings in back office related to sales areas	
Incorrect Auth Key Syntax	
The client must have been saved before	
Free table not found in range %d..%d	
Free table not found in "Book On Client" table range %d..%d	
There are open tables. Z-report cannot be exported.	
Please choose supplier	
Please define initial count sheet layout in restaurant settings	
Please choose stock location	
Please choose counting U/M conversion	
Please choose purchase U/M conversion	
Please enter adjustment amount	
Please choose Ingredient	
Default stock location not defined	
Not enough items on stock location "%s"	
Not enough items on Central stock	
Please enter new Normal amount	
Please define default Stock location in BO	
Stock location not defined	
Please enter item quantity	
You should enter ingredient price	
Please choose Stock location to	
The ingredient cannot be transferred because it is (almost) depleted on the original location	
Please choose transfer location to	
Please add ingredients to transfer list	
Please add ingredients to the Order proposal	
Please add ingredients to the Stock invoice	
Please choose Purchase order	
Please define Purchase order layout	
Please define Stock printer	
Please specify Central stock location in BO	
Please add ingredients to Purchase order	
The ingredient cannot be deleted because it is not depleted on this stock location	
Usage U/M of selected items must be equal	
This document cannot be deleted - there are invoices, linked to it	
This document cannot be changed - it is already processed	
Purchase order was already created today	
$FORMNAME	ArticleGarbageEntityExtender
Reduce FV	Reduziere FV
Always paid	Immer bezahlt
Garbage collection	Abfallansammlung
Reduce free visits	Reduziere freie Besuche
Always has to be paid	Muß immer bezahlen
$FORMNAME	ArticleIngEmbEntityManager
$FORMNAME	ArticleMassModifyingDlg
Mass modifying	Felderbearbeitung
New packing	Neue Verpackung
New promotion	Neue Promotion
New commission	Neue Provision
New save points	Neue Sparpunkte
New options	Neue Kommentarfenster
New PC color	Neue Pc Farbe
New PC font	Neu Pc Schriftart
New RM font	Neu Handy Schriftart
New department	Neue Artikelgruppe
New course	Neuer Gang
New prices	Neue Preise
New available	Neue Verkaufsbereiche
New notify	Neuer  Bondrucker
Warning! Specified prices will be deleted from "Articles", "Discount" and "Options"	Warnung! Spezifizierte Preise werden aus "Artikeln", "Rabatt" und "Optionen" gelöscht
Change department	Ändere Artikelgruppe
Change course	Ändere Gänge
Change available	Ändere Verkaufsbereiche
Change notify	Ändere Bondruck
Change prices	Ändere Preise
Change packing	Ändere Verpackung
Change promotions	Ändere Promotion
Change commission	Ändere Provision
Change save points	Ändere Sparpunkte
Change options	Ändere Kommentarfenster
Change PC color	Ändere PC Farbe
Change PC font	Ändere PC Schriftart
Change RM font	Ändere Handy Schriftart
Hide in reports	Nicht in Berichte anzeigen
Modify	Ändere
Set prices to zero	Setze Preise auf Null
Delete prices	Lösche Preise
Use group	Verwende Gruppe
&1 General	&1 Allgemein
&2 Prices	&2 Preise
&3 Options	&3 Optionen
&4 Appearance	&4 Farben
&5 Extra	&5 Extra
By	Durch
V.A.T.	MwSt
New supplement	Neues Extra(Option)
New V.A.T.	Neue MwSt
Change price	Ändere Preis
Change free	Ändere Gratis
Change supplement	Ändere Extra
Change V.A.T.	Ändere Mwst
New free	Neu Gratis
Round to	Abrunden auf
digits after decimal	Stellen nach Dezimalstrich
Change price by	Ändere Preis durch
Warning level	Meldebestand
New Free Option	Neue freie Option
Change Free Option	Wechsel freie Option
Beverage Control	Durchflusskontrolle
Beverage Control disabled	Durchflusskontrolle aus
(not licensed)	
New must have options	
New Free option groups	
New supplier	
New upseller	
Change default view	
Change color	
Change font	
Change alt. font	
On/Off	
Ask course when ordered	
Change Free option groups	
Change PC appearance	
Change HHT appearance	
Change Orderman appearance	
Change supplier	
Change upseller	
Daily stock control	
Daily stock control enabled	
Kitchen screen Pro	
Show in kitchen screen	
Minutes to preparation	
Kitchen screen notifications	
Change profit rate by	
New class	
New frequency	
New location	
New min. quantity	
New par. quantity	
New default yield	
New calculation method	
New substitute	
Change class	
Change frequency	
Change stock location	
Change min. quantity	
Change par quantity	
Change default yield	
Change calculation method	
Change substitute	
&2 Options	
$FORMNAME	ArticleModifyPriceEmbEntityManager
Price name	Preis Name
Currency	Währung
Modify price	Ändere Preise
New price	Neuer Preis
New V.A.T.	Neue MwSt
&Set new price value	&Eingabe neuer Preis
&Change existing price value	&Ändere vorhandenen Preis
Mass-modify price	Preis Felderbearbeitung
Copy From	Kopiere von
Copy price value from &other price	K&opiere Preis vom anderen Preis
Copy from price	Kopiere vom Preis
Group Vat Usage	Steuerart
Do not use Group Vat	Steuerart nicht wechseln
Use Group Vat	benutze Steuerart
New Secondary V.A.T.	
&Change profit rate value	
$FORMNAME	ArticlePriceEmbEntityManager
Price	Preis
Currency	Währung
V.A.T.	MwSt
Price name	Preis Name
Article: Prices	Artikel: Preise
New article price	Neuer Artikelpreis
Article price	Artikelpreis
V.A.T. sign	Mwst Zeichen
(3 symbols)	(3 Symbole)
Use Group Vat	Benutze Steuerart
Profit rate	
Price exceptions	
Secondary V.A.T.	
Secondary V.A.T. sign	
$FORMNAME	ArticleNotifyEmbEntityManager
Purpose	Zweck
Preparation area	Bondruck
Article: Notify	Artikel: Bondruck
Article notify	Artikel Bondruck
$FORMNAME	ArticleOptionEmbEntityManager
New article notify	Neuer Artikel Bondruck
Article: Options	Artikel Optionen
Options sequence	Optionen Reihenfolge
New article option	Neue Artikeloption
Article option	Artikeloption
Compose	Vergleich
Promo item	Promotion Artikel
New promo item group	Neue Promo Artikel Gruppe
Promo item group	Promo Artiklegruppe
Max quantity	Max Menge
New option article	Neue Artikeloption
Option article	Artikeloption
New promo article	Neuer Promo Artikel
Promo article	Promo Artikel
Article	Artikel
Prices	Preis
Name	
Number	Nummer
New KS notification	
New KS workflow stage	
Consider as size modifier	
New promo option	
Promo option	
New KS workflow stage item	
$FORMNAME	OptionArticleEmbEntityManager
Course	Gang
Number	Nummer
Article	Artikel
Articles sequence	Artikelreihenfolge
New option article	Neuer Optionsartikel
Option article	Option Artikel
Prices	Preise
Save points	Sparpunkte
Name	Name
Print	Druck
Default items	
New Pickup device	
Pickup device	
New kitchen screen	
Kitchen screen	
Show messages for	
Sort by field	
Number of lists on screen	
Active tables always show first	
Linked to	
Type	
Language	
Articles to show	
Manual price	
Show in Kitchen screen	
Single	
Multi	
$FORMNAME	ArticleStoredParamsFram
Transfer origin	Übertrage Original
Transfer result	Übertrage Resultat
Bill origin	Rechnung Original
Bill result	Rechnung Resultat
Paid bill	Rechnung bezahlt
Article dataset parameter	Artikeldatensatzparameter
a	a
b	b
c	c
d	d
e	e
f	f
Save point articles	Sparpunkte Artikel
Saved orders	Gespeicherte Aufträge
Orders in future	Aufträge in Zukunft
Select dataset type	Wähle Datensatztyp
Entire order	
By current chair	
Hide reopened bills	
Show reopened bills	
Set Reopen bills color	
Set Void bills color	
Time block	
Time slot	
Order	
$FORMNAME	ArticlesTicketDataSetParamsFram
Only articles with positive prices	Nur Artikel mit positiven Preisen
Only articles with negative prices	Nur Artikel mit negativen Preisen
All articles	Alle Artikel
Purpose	Zweck
Articles of all price types	Artikel aller Preisklassen
Only articles with manual price	Nur Artikel mit manuelen Preis
Only articles with predefined price	Nur Artikel mit vordefinierten Preis
Choose course	Wählen Sie den Gang
All courses	Alle Gänge
Course numbers(comma separated list)	Ablauf-Nummern(durch Komma getrennte Liste)
Print zero price options on bill	Drucken Nullpreisen auf Rechnung
Only opened purchase orders	Nur geöffnete Bestellungen
Only closed purchase orders	Nur abgeschlossene Bestellungen
All purchase orders	Alle Einkauf Bestellungen
Expand Combo-Promo items	
Print main zero prices on Bill	
Consolidate articles with different zero price options	
Not on hold articles	
On hold articles	
General	
Order proposal	
Receive goods	
Modified PO	
Free PO	
Original list	
Result list	
$FORMNAME	BarBalanceStoredParamsFram
Negative balance only	Nur negative Saldo
Group by user	Gruppierung nach Benutzer
Do not group	keine Gruppe
Group by article	Gruppierung pro Artikel
Group by location	Gruppierung pro Lokal
Show single location	Zeige einzelnes Lokal
Do not show zero-balance	Keine 0 Werte anzeigen
$FORMNAME	BarEntityManager
New Bar	Bar neu
Bar	Bar
Bars	Bars
$FORMNAME	BlackListEntityManager
Client	Kunde
Comment	Anmerkung
New record	Neuer Datensatz
Record for client	Datensatz für Kunden
Blacklist	Blacklist
$FORMNAME	BLRAlgChangeOrderCourseU
Select table to change course	Wähle Tisch um Gang zu ändern
$FORMNAME	BLRAlgCombineTablesU
Table connect	Tisch zusammenlegen
$FORMNAME	BLRAlgPaymentU
Payment	Zahlung
Split note	Splitt Rechnung
Enter payer name	Name des Zahlers
Enter number of parts	Anzahl der Gäste
Scan or type the code	
Number of covers	
Enter note/coin value	
Articles to be paid by save points	
$FORMNAME	BLRAlgU
Enter waiter identification	Kellner eingeben
Select table	Wähle Tisch
Enter articles	Artikel eingeben
Enter price	Preis eingeben
Enter option	Kommentare eingeben
Enter supplements	Extras eingeben
Enter condiments	Änderungen eingeben
Select: transfer all items or by article	Wähle: Alle Artikel oder einzelne Artikel übertragen
Select articles to transfer	Wähle zu übertragene Artikel
From table ...	Von Tisch ...
Split table	Tisch splitten
Select table part	Wähle Teil des Tisches
... To table	... Auf Tisch
Change order name	Ändere Bestellung Namen
Enter message	Info eingeben
Activate re-opening bill	Aktiviere Rechnung öffnen
Refund	Retoure
Select client	Wähle Kunde
Bar balance	Bar Saldo
Enter client room or folio number	Gebe Kunden Zimmer oder Folio Nummer ein
Enter deposit amount	Anzahlungsbetrag eingeben
Choose sales area	Wähle Verkaufsbereich
Choose terminal	Wähle Terminal
Enter tip amount	Trinkgeld Betrag eingeben
Tip added and approved	Trinkgeld addiert und genehmigt
Modify client	Ändere Kunden
Show article info	Zeige Artikelinfo
Define discount	Definiere Rabatt
Paid articles in active order	Bezahlte Artikel in aktiver Bestellung
Enter amount to process	Menge eingeben zum Verändern
Enter invoice discount amount	Gebe Rabattbetrag für Rechnung ein
Enter discount amount	Rabattbetrag eingeben
Transfer table(s) from one waiter to the other	Übertrage Tische von einem Kellner zu anderen
Select waiter from	Wähle Kellner von
Select waiter to	Wähle Kellner auf
Number of covers	Gästeanzahl
Enter service charge	Servicebetrag eingeben
Select shared account	Wähle geteiltes Konto
Cash draw amount	Bargeldbetrag
Table history	Tischverlauf
"Direct Sales table number" for user cannot be zero	Benutzer darf nicht Null haben "Direktverkauf Tischnummer" Eigenschaft einstellen
"Direct Sales table area" property not set for current computer	"Direktverkauf Tischbereich" Eigenschaft nicht für aktuelle Computer festgelegt
Bill can only be reprinted once, since CleanCash is installed	Rechnungsausdruck nur einmal da Kassenabschluss
Execute table moving...	Tische bewegen
Select sales area	Wähle Verkausbereich
Select new price level	Wähle neuen Preisbereich
Current price level:	Aktueller Preis ab:
Select sales area first	Verwende Verkausbereich zuerst
In/Out cash operation	Ein-/Ausgben Anwendung
In/Out cash	Ein- /Ausgaben
Show table history	Zeige Tischverlauf
Show combo-promo article set	Zeige Kombinations- Promo Artikelsatz
Change order description	Ändern Sie die Beschreibung
Beconet control screen	Schankkontrolle
Define chair number	Welche Stuhlnmmer
Define chair caption	Definiere Stuhl Beschriftung
Locker number	Schranknummer
Enter locker number	SChranknummer eingeben
Number of articles to be paid with save points	Artikelanzahl der Zahlung mit Bonuspunkte
Define discount undo quantity	Definieren Rabatt rückgängig Menge
Please choose edit mode...	
Enable/Disable articles	
Rearrange articles (absolute mode)	
Rearrange articles (relative mode)	
Rearrange articles (exchange mode)	
Change article price	
Choose article to change sales U/M	
Change article sales unity of measure	
Choose article	
Choose department	
Card table limit	
Client order history	
Filter by table number	
Enter table number	
Enter quantity	
Print complete meal	
Complete meal single quantity	
Table is busy on other PC/HHT	
Card is recognized and table	
is reserved	
Only supported for Tap2Order = By Penko Number (Restaurant Settings)	
Enter articles(by PLU number)	
Enter articles(by PLU number multi)	
Smart card is already assigned to current order	
Smart card %s is recognized and assigned to current order	
Card is not linked to a client or price	
Decimal multiplier	
All menus must be completed before exchanging articles	
Operator ID has not been set, Protel cannot be used.	
Daily stock control (Normal mode)	
Daily stock control (Deactivate Daily Stock mode)	
Daily stock control (Activate Daily Stock mode)	
Show Smart Card Info	
Select articles to put them out of on hold	
Swipe Smart Card	
Enter pre-auth amount	
Read article weight from balance	
Select available guest from the list	
Authorise Newyse customer	
Enter Cash drawer amount	
Choose preparation area	
Choose stock location	
Select voucher group to use its remaining value	
Get weight value	
Choose table to slide caption...	
What to do for this Tap2Order client?	
Tap2Order Group Management. Swipe card to add or remove member	
Remove client from this group?	
Add client to this group?	
This client is already a member of group "%s"	
Swipe card and specify group name	
Active group "%s", made by this client, already exists	
Client already a member of group "%s"	
Enter number of covers	
Choose new course	
Confirm Cash-out	
Enter deposit amount and click payment mode	
Client not selected	
Search article by name	
Modify options	
Enter customer's birth date	
Choose language in which bill is printed	
Enter article PLU number	
Choose Hand held device	
Choose table to switch auto scroll...	
Select coupon from the list or scan the barcode	
Create New TTG Group	
EFT Configuration Selection	
Rental period has not started yet for this item	
Rental period is already over for this item	
Select guest to see the details and/or start ordering	
Input quantity to order	
Change order type	
QR code of the last proforma on this terminal	
Define predefined allergens	
Define custom allergens	
Modify client card	
Bill has discounted items. Continue change client and cancel discounts?	
Create coupon	
Modify coupon	
Choose coupon to modify	
Choose coupon	
How many coupons to print	
What type of coupons to print	
Block/Unblock coupon	
Mark invoice as paid	
Choose payment type for invoice	
Confirm print report for chosen waiter	
On hold viewer	
Choose service charge plan	
Define discount on current article	
Edit PSP tips	
Enter PSP tip amount	
Enter voucher barcode	
Enter voucher value	
Vouchers payment	
Enter quantity of multi vouchers	
Enter amount that will apply to each voucher	
Adjust voucher values	
Show voucher value	
Confirm waiter transfer	
Transfer my tables to another waiter	
Tap2Order queue is not yet empty	
MidiXP task is not responding. Try restarting JServer	
MidiXP is disabled or incorrectly configured	
Error while reading and processing record from MidiXP with IP %s	
Still busy reading drinks from MidiXP with IP %s. Please wait few seconds.	
Unknown MidiXP status. Unable to close group. Try restarting JServer	
Checking callings	
Modify general ingredient properties	
Modify ingredient location data	
Modify ingredient supplier data	
New ingredient location	
New ingredient supplier	
Enter item price	
Purchase order overview	
Enter notes	
Enter ingredient price	
Modify stock supplier data	
$FORMNAME	BLRBillU
Guest is not in house	Gast nicht im Haus
Guest has no credit	Gast hat keinen Kredit
Guest has overlimit	Gast ist über dem Limit
Communication error	Kummunikationsfehler
Print order journal	Drucke Bestellberecht
An error occured during processing CleanCash transaction:	Ein Fehler trat während des Kassenabschluss auf:
Print void order	
Client account balance is exceeded.	
Payment cannot be finished: payment type can only be used to pay the complete bill	
FDM does not allow reopening the bill	
Insurance field is empty for current user	
iTesso: %s	
$FORMNAME	BLRPrintU
Print receipt data for declined transactions	Drucken Rechnungsdaten für abgelehnte Transaktionen
unTill bill notification	
Print rear screen saver	
EFT Receipt	
EFT Result Receipt layout not specified	
Attention! Recovery transaction details printed to except.log. Click payment once more to complete transaction	
Attention! Payment ticket for some previous transaction sent to printer. Click payment once more to complete transaction	
$FORMNAME	BLRReservationU
Select table to reserve	Wähle Tisch zum reservieren
Select table part to reserve	Wähle Tisch Teil zum reservieren
Modify reservations	Ändere Reservierung
Choose filter parameters	
Input reservation note	
Input reservation client info	
Input reservation client contact	
$FORMNAME	ButtonPropSelectFram
Load bitmap from file...	Lade Bitmap von Datei...
Clear bitmap	Lösche Bitmap
Screen group	
Load picture	
$FORMNAME	CategoryEntityManager
New Category	Neue Hauptgruppe
Category	Hauptgruppe
Categories	Hauptgruppen
HQ ID	
Service charge plan	
$FORMNAME	ChangeCourseFram
Courses	Gänge
$FORMNAME	ClientEntityManager
New Client	Neue Kunde
Client	Kunde
&1 General	&1 Allgemein
&2 Contacts	&2 Kontakte
&3 Personal	&3 Persönlich
&4 Price	&4 Preis
&5 Promotions	&5 Promotion
&6 Deposits	&6 Anzahlungen
Garbage collection	Abfallansammlung
Client Name	Kunden Name
Number	Nummer
Address Information	Adress-Information
Country	Land
Address	Adresse
Phone	Telefon
Fax	Fax
E-Mail	E-Mail
Website	Webseite
Contacts	Kontakte
Additional	Optional
Code	Code
Date Of Birth	Geburtstag
Limit	Limit
Account	Konto
Save Points	Sparpunkte
Save Amount	Spar Betrag
Card	Karte
Promotion	Promotion
Exceptions	Ausnahmen
Info	Info
Last invoice	Letzte Rechnung
Change	Wechselgeld
On invoice	Auf Konto
Load &Picture	Lade Foto
Price	Preis
Erase picture	Lösche Foto
Date	Datum
Postcode	Postleitzahl
House number	Hausnummer
House letter	Hausschrift
House number addition	Haus Zusatz
Place	Wohnort
Free visits	Freie Besuche
Invoice management	Rechnungs Verwaltung
Clients	Kunde
Shared account	Gemeinsamer Kunde
Price prefix	Voreigestellter Preis
Combine prices	Kombinierte Preise
Extra	Extra
Active	Activ
DS balance	DS Abgleich
Stock	Lager
Daily Stock	Tagesbestand
Time Article	Zeitartikel
Hash	Zigaretten
Account Number	Konto-Nummer
Bookkeeping	Buchhaltung
Import from XLS	Imort von XLS
Re-order quantity	Wiederbestellmenge
U/M	
Kitchen screen	
Invoice description	
Exempted	
Business/Company	
Can receive save points	
Discounts	
Phone 2	
Invoice e-mail(s)	
Card number	
Creation date	
Expiration date	
SP turnover...	
Siret Number	
NAF Number	
Personal	
Price Active	
No clients selected	
Price level	
Convert Bitmaps	
Account deposit	
Export to XLS	
Client with same Smartcard UID already exist	
This smartcard is already registered in Smartcard Groups	
$FORMNAME	ClientsDatasetParamsFram
All clients	Alle Kunden
Clients with positive account only	Kunden nur mit positivem Konto
Clients with negative account only	Kunden nur mit negativem Konto
$FORMNAME	ClientStoredParamsFram
Modify client info	Ändere Kunden Info
a	a
b	b
c	c
d	d
e	e
f	f
Select dataset type	Wähle Dataset Typ
$FORMNAME	ClientsU
Deposit info task	Anzahlung in Task
Number	Nummer
Name	
Address	Adresse
Info	
Phone	Telefon
E-mail	
Fax	
Website	
Card	Karte
Voucher print task	
Reference	
Zip	
City	
Vat Number	
Language	
Country	
$FORMNAME	CloseWaiterTableEmbEntityManager
Table area	Tischbereich
User: Close table areas	Benutzer: Geschlossene Tischbereiche
$FORMNAME	ClPriceExceptionEmbManager
Period	Periode
Price	Preis
Client: Price exceptions	Kunde: Preisausnahmen
New Exception	Neue Ausnahmen
Exception	Ausnahmen
$FORMNAME	CommissionEntityManager
New Commission	Neue Provision
Commission	Provision
Commissions	Provisionen
$FORMNAME	ComputerEntityExtender
Sales area	Verkaufsbereiche
Bill printer	Rechnungsdrucker
Bill layout	Rechnungslayout
Proforma printer	Zw-Rechnungsdrucker
Proforma layout	Zw-Rechnung Layout
You should specify rear display layout	Kundenanzeigen Layout spezifizieren
Restaurant	Restaurant
Restaurant printers	Restaurantdrucker
Rear display	Kundenanzeige
By table and direct	Auf Tische und direkt
Only by table	Nur auf Tische
Only direct	Nur direkt
Sales kind	Verkaufsart
Period	Periode
Keep &waiter	Kellner halten
&Limited	&Begrenzt
&Double	&Doppelt
Start table number	Start Tischnummer
Auto logoff interval, sec	Auto Abmeldungs Intervall, sec
Journal printer	Journal Drucker
Deposit layout	Anzahlungs Layout
Deposit printer	Anzahlungsdrucker
Invoice layout	Rechnung Layout
Part screen	Tisch-Teil Bildschirm
Order screen	Bestell Bildschirm
Condiment screen	Kommentar Bildschirm
Supplement screen	Extra Bildschirm
Payment screen	Bezahlungsarten Bildschirm
Additional	Zusätzlich
&A4/Letter	&A4/Schreiben
Table number for Order in Future	Tischnummer für zukünftige Bestellung
EFT bill layout	EFT Beleg Layout
Drawer amount layout	Schubladenanzahl Layout
Return articles layout	Stornoartikel Layout
In/out cash layout	Ein-/Ausgaben Layout
In/out cash printer	Ein-/Ausgaben Drucker
Computer "%s" already responsible for on hold printing	
Please enter "Print after(minutes)"	
Beverage Control	
Don't auto log off on HHT	
Bill	
Proforma	
In/out cash	
Deposit	
Take away order	
Manager	
Journal	
Rent	
Gift cards	
Voucher/Coupon	
Multi voucher layout	
Voucher bill layout	
Coupon layout	
Printers	
Stock printer	
Various Layouts	
Email invoice layout	
Fiscal bill footer	
Pre-auth layout	
Re-print order layout	
Journal order layout	
Journal control layout	
Printer	
Article layout	
Second article layout	
Delay (seconds)	
Second article layout shown when this delay expires	
RD screen saver	
Screensaver delay (min)	
Direct Sales area	
Direct table number	
Preparation area for Orders in Future in Direct Sales mode	
Don't print order at logoff	
Don't print order on payment in  Direct Sales mode	
Block creating new client, when card is swiped	
Responsible for on hold printing	
Initial Kitchen screen	
Default stock location	
BeCo Location	
Tap2Order	
Prepaid orders table	
Void layout	
Void printer	
Take away order layout	
Manager report layout	
Manager report printer	
Rent layout	
Rent printer	
Gift cards layout	
Gift cards printer	
Voucher layout	
Voucher bunch layout	
Voucher/Coupon printer	
$FORMNAME	OrdermanEntityExtender
Direct table number	Direktverkauf Tischnummer
Bill layout	Rechnungs Layout
Proforma printer	Zw-Rechnungsdrucker
Proforma layout	Zw-Rechnung Layout
Printer	Drucker
Article layout	Artikel Layout
Restaurant	Restaurant
Bill printer	Rechnungsdrucker
Journal bill layout	Rechnungsjournal Layout
Journal order layout	Bestelljournal Layout
Journal control layout	Kontrolljournal Layout
Rear Display printer	Hinterer Druckeranzeige
Use special Bill printer	Verwende spzial Rechnungs Drucker
Direct sales table number	Direktverkauf Tischnummer
Second article layout	
RD screen saver	
Show after	
Run Kitchen Screen POS	
Kitchen Screen name	
$FORMNAME	CourseEntityManager
New Course	Neuer Gang
Course	Gang
Changeable	Veränderbar
Separate	Unterschiedlich
Number	Nummer
Used Color	Belegt Farbe
Used border	Belegt Rand
Slow Color	Farbe kein Service
Slow Border	Rand Kein Service
Print on hold	Zwischenrechnung
Courses	Gänge
Ask to change course	
Auto fire	
Print when deliver	
Print when sent to Pickup	
Time frame	
Pickup ticket	
Pickup printer	
Second slow border	
Second slow color	
Start countdown	
After	
Order/Next Course	
Manual	
Predefined	
Screen type	
Regular	
Extended	
Sort items in list by	
$FORMNAME	DepArticleEmbEntityManager
Article	Artikel
Department: Articles	Artikelgruppen: Artikel
Sequence	Sequenz
Rm-sequence	Handy Sequenz
Articles sequence	Artikel Sequenz
Articles Rm-sequence	Artikel Handy Sequenz
New department article	Neue Artikelgruppen Artikel
Department article	Artikelgruppen Artikel
RM sequence	Handy Sequenz
Combo-Promo article	Kombinations- Pormoartilkel
$FORMNAME	DepAvailEmbEntityManager
Sales area	Verkaufsbereiche
Department: Available	Artikelgruppe: Verfügbar
New department available	Neue Artikelgruppe verfügbar
Department available	Artikelgruppe verfügbar
Sequence	Sequenz
$FORMNAME	DepNormArticleEmbEntityManager
Department: Normal articles	Arbeitsgebiet: Normale Artikel
Article	Artikel
Sequence	Sequenz
Rm-sequence	Handy Sequenz
Article sequence	Artikel Sequenz
RM article sequence	Artikel Handy Sequenz
$FORMNAME	DepositEmbEntityManagerU
Payment	Bezahlungsart
Deposits	Anzahlung
Date	Datum
Amount	Betrag
New deposit	Neue Anzahlung
Edit deposit	Neue Anzahlung
Don't show in reports	Nicht in Berichten anzeigen
Mode	
Number	
Suffix	
$FORMNAME	DiscountArticlesParamsFram
Report	Bericht
Printing bill	Drucke Rechnung
All PCs	Alle PCs
Only current PC	Nur aktueller PC
Include discount on total	
$FORMNAME	DiscountReasonEntityManager
Reason	Grund
Discount reason	Rabatte
Discount reasons	Rabatte
New reason	Neuer Grund
Don't return items to stock	
Void reason	
$FORMNAME	DragAndDropItemsDlg
Items order	Artikel bestellt
Articles in line	
Sort by Name	
Sort by Number	
Set PC order	
Set HHT order	
$FORMNAME	FalconEntityExtender
Restaurant	Restaurant
$FORMNAME	FoodGroupEntityManager
New Group	Neue Gruppe
Group	Gruppe
Category	Hauptgruppen
Bookkeeping	Buchführung
Turnover	Umsatz
VAT	Mwst
Groups	Gruppen
Free	Frei
Rented	gemietet
Repairing	Reparatur
Group VAT	Gruppe MwSt
Group VAT Sign	Gruppe MwSt Bezeichnung
(3 symbols)	
Secondary Group VAT	zweite Steuergruppe
Secondary VAT Sign	zweite Steuerbezeichnung
Not set	Nicht gesetzt
Secondary VAT	zweite MwSt
Number	
HQ ID	
Normal group	
Voucher/Coupon group	
Voucher	
Type	
$FORMNAME	HCInfoDatasetParamsFram
Hide "Folio sequence"	"folio sequenz" ausblenden
Hide "Folio number"	"folio numbe" ausblenden
$FORMNAME	IngQuantityEmbEntityManager
$FORMNAME	IngQuantityEmbManager
$FORMNAME	IngredientEntityManager
$FORMNAME	IngSupplierEmbManager
$FORMNAME	InputBillFram
Input text	Eingabe Text
$FORMNAME	InvoicesDlg
Invoices management	Rechnungen Management
Print invoices	Drucke Rechnungen
Make invoices	Erstelle Rechnungen
Printed invoices	Gedruckte Rechnungen
Print invoices for selected clients?	Drucke Rechnungen für ausgewählte Kunden?
%d invoice(s) successfully printed	%d Rechnung(en) wurden erfolgreich gedruckt
No invoices were printed	Keine Rechnungen wurden gedruckt
Name	Name
From	Von
Till	Bis
Load	Lade
Apply	Bestätige
Days since last invoice, at least	Tage seit letzter Rechnung, etwa
%d of %d invoices printed	
$FORMNAME	InvoicesEmbEntityManager
Client	Kunden
From	Von
Till	Bis
Invoice	Rechnung
"Till" date/time is before "From" date/time	Von  Datum/Zeit ist eher als Bis Datum/Zeit
Preview invoice	Rechnung Vorschau
Ctrl + R	Ctrl + R
Account	Konto
Last invoice	Letze Rechnung
"Till" date/time is after current date/time	"Bis" Datum/Zeit größer ist als jetzt
Preview invoice as PDF	
Ctrl + P	
Send invoice by email	
Ctrl + M	
$FORMNAME	LCUDispensersEntityManager
Address	Adresse
30 - Master LCU dispenser	30 - Master LCU Spender
New LCU dispenser	Neuer LCU Spender
LCU dispenser	LCU Spender
LCU Dispensers	LCU Spender (mehrzahl)
Location	
$FORMNAME	MarkInvoicePaidDlg
Payment	Zahlung
Mark invoice paid	Markiere Rechnung als bezahlt
Parameters	Parameters
Date	Datum
Time	Zeit
$FORMNAME	NeedPaymentFram
Bill printer	Rechnungsdrucker
Bill layout	Rechnungslayout
Use default settings	Benutze Voreinstellungen
Discount group	
Apply only to new items	
Apply to all items	
Report printer	
Report layout	
Need choose location	
Preview	
$FORMNAME	NeedPaymentIDFram
Payment type	Abrechnungsart
Payment mode	
Layout	
Command	
Do after payment	
Print invoice	
Email invoice	
Invoice layout	
Mark invoice as paid	
Consider return amount as	
Tips	
Voucher	
Print bill	
times	
EFT Params	
$FORMNAME	NeedPrintInvoiceParamsFram
Custom layout	Kunden Layout
PSP model	
Operation	Operation
Main menu	Haupt Menü
Deposit	Anzahlung
Register loyalty	Register-Loyalität
$FORMNAME	NeedProformaFrame
Proforma printer	Zw-Rechnungsdrucker
Proforma layout	Zw-Rechnung layout
Use default settings	Benutze Voreinstellungen
EFT Proforma (CleanCash only)	EFT Zwischenrechnung (nur Kassenabschluss)
$FORMNAME	NeedReopenBillParamsFram
Show current user's bills only	Zeige nur Rechnungen vom jetzgen Benutzer
Filter by payment	Filter Bezahlung
Payment	Bezahlung
Payment kind	Bezahlungsart
Filter by payment kind	Filter Bezahlungart
No user filter	
Filter by user	
No payment filter	
$FORMNAME	NeedSelfReportTicketFram
Report layout	Berichts Layout
Report will be printed on Bill printer	Bericht wird gedruckt auf Rechnungsdrucker
Preview	Vorschau
Recipients	
Date&Time settings	
From	
To	
Selected client/All clients	
Current client	
Current user	
All users	
Print to	
Report params	
Use data of current terminal	
Legal-X	
Specified Date/time range	
Waiter work date/time range	
$FORMNAME	OptionArticlePriceEmbEntityManager
Currency	Währung
Option: Article prices	Option: Artikelpreise
Price	Preis
$FORMNAME	OptionAvailEmbEntityManager
Sales area	Verkaufsbereiche
Option: Available	Option: Verfügbar
New option available	Neue Option/Extra verfügbar
Option available	Option/Extra verfügbar
New PUA-Group available	Neue verfügbare PUA-Gruppe
PUA-Group available	verfügbare PUA-Gruppe
$FORMNAME	OptionEntityManager
New option	Neues Kommentarfenster
Option	Kommentarfenster
Name	Name
PC text	PC Texte
Available	Vorhanden
Articles	Artikel
Options	Kommentarfenster
HHT Name	
Disabled	Abgewählt
Single preparation	
Coupon template	
$FORMNAME	OrderWaiterTableEmbEntityManager
Table area	Tischbereich
New waiter order table area	Neuer Kellner Best. Tischbereich
Waiter order table area	Kellner Bestell:Tischbereich
Waiter: Order table area	Kellner: Bestell.Tischbereich
$FORMNAME	PackingEntityManager
Name	Name
New packing	Neue Verpackung
Packing	Verpackungen
Kinds	Art
$FORMNAME	PackingKindEmbEntityManager
Content	Inhalt
New packing kind	Neue Verpackungsart
Packing kind	Verpackungsart
Packing: Kinds	Verpackung: Art
$FORMNAME	PaidBillDateFram
Change current date	Ändere jetziges Datum
Next day	Nächster Tag
Previous day	Vorheriger Tag
Current day	Heute
$FORMNAME	PropGraphicDlg
$FORMNAME	PreparationEntityManager
New preparation area	Neuer Produktionsbereich
Preparation area	Produktionbereich
Order printer	Bon Drucker
Order layout	Bon Layout
Beep	Piep
Print	Druck
Language	Sprache
times	Zeit
Stock location	Lagerort
De-Consolidate articles	Teile/Vereinigen der Artikel
General	
Kitchen screen	
Notifications	
Pickup screen	
$FORMNAME	PriceEntityManager
New Price	Neuer Preis
Price	Preis
Prices	Preise
HQ ID	
Barcode	
Value	
Voucher	
$FORMNAME	PrintBillParamsFram
Custom layout	Kunden Layout
$FORMNAME	PrintedInvoicesEmbEntityManager
Delete unpaid invoices too?	Unbezahlte Rechnungen auch löschen?
Number	Nummer
Printed	Gedruckt
Client	Kunde
Made by	Ausgeführt von
From	Von
Till	Bis
Paid	Bezahlt
Mark as paid	Markiert als Bezahlt
Re-print	Kopie
Show unpaid invoices only	Zeige nur unbezahlte Rechnungen
Delete all invoices up to and including the date of current invoice	Lösche Rechnungen vor dem Datum der gegenwärtigen Rechnung
Account	Konto
Discount	Rabatt
All printed invoices up to and including %s will be erased. Continue?	Bei diese Operation werden alle Rechnungen gedruckt vorher %s (einschließlich) löschen. Fortsetzten?
Are you sure you want to undelete invoice?	
Re-send	
$FORMNAME	PromotionEntityManager
New Promotion	Neue Promotion
Promotion	Promotion
Promotions	Promotionen
$FORMNAME	PropFontDlg
$FORMNAME	PSPSpecialOpParamsFram
Please specify action	Bitte Aktion spezifizieren
$FORMNAME	RangeEmbEntityManager
From	Von
To	Bis
Table area: Table range	Tischbereich: Nr. Bereich
New table area range	Neuer Tischbereich (Nr.Bereich)
Table area range	Tischbereich (Nr. Bereich)
$FORMNAME	RefreshTestDataU
$FORMNAME	ReopenBillFram
First	Erste
Last	Letzte
Next	Nächste
Previous	Voherige
Entered number	Nummer eingeben
Direction type	
Fiscal document id	
$FORMNAME	ReservationsStoredParamsFram
a	a
b	b
c	c
d	d
e	e
f	f
Select table part	Wähle Tisch - Teil
$FORMNAME	RestaurantDataSetsU
Departments	Artikelgruppen
List of departments	Liste Artikelgruppen
Dep.	Artgr.
Articles	Artikel
List of articles	Liste der Artikel
Art.	Art.
List of articles prepared for printing	Liste der Artikel zum Ausdrucken
Payment	Bezahlung
Payment information	Bezahlinformation
Order	Bestellung
Preparation area	Produktionsbon
Open date & time	Geöffnet Datum / Zeit
Table	Tisch
Waiter	Bediener
Table part	Tisch Teil
Course	Gang
Total	Gesamt
New total	Neues Total
Not paid sum	Betrag nicht bezahlt
Name	Name
New name	Neuer Name
Undo quantity	Storno Anzahl
Article message	Artikel Nachricht
Order number	Bestell Nummer
Failed order number	Falsche Bestelllung Nummer
Transaction number	Tranaktions Nummer
Failed transaction number	Falsche Transaktions Nummer
Refund date	Retoure Datum
Room number	Zimmer Nummer
Current order information	Jetzige Bestell Info
Articles in active order	Artikel aktiv in Bestellung
Current article	Jetzige Artikel
Article	Artikel
Price	Preis
Quantity	Anzahl
Info	Info
Current article information	Jetzige Artikel Info
Options	Optionen
Opt.	Opt.
Database Articles	Datenbank Artikel
List of all articles in database	Liste aller Artikel in der Datenbank
List of options	Liste der Optionen
Supplements	Extras
List of supplements	Liste der Extras
Sup.	Ext.
Condiments	Kommentare
List of condiments	Liste Kommentare
Con.	Kom.
Restaurant	Restaurant
Transfer from table	Übertrage von Tisch
Transfer from table part	Übertrage von Tisch Teil
Transfer to table	Übertrage auf Tisch
Transfer to table part	Übertrage auf Tisch Teil
Quantity to transfer from table	Menge zum Übertragen von Tisch
Quantity to transfer from bill	Menge zum Übertragen von Rechnung
Z-Report number	Z-Bericht Nummer
Last Z-Report date/time	Letzter Z-Bericht Datum/Zeit
Invoice number	Rechnung Nummer
Reopen bill number	Geöffnete Rechnungs Nr.
Bill range from date	Rech.Nr. Bereich von Datum
Bill range till date	Rech.Nr.Bereich bis Datum
Discount value	Rabatt Wert
Discount reason	Rabatt Grund
Table number	Tisch Nummer
Current client filter	Jetziger Kunden Filter
Amount to deposit	Betrag der Anzahlungen
Common restaurant information	Allgemeine Restaurant Informationen
Articles from original bill	Artikel von Orginalrechnung
Origin list of articles	Orginalliste der Artikel
Sold articles	Verkaufte Artikel
Sales area	Verkaufsbereich
PC name	PC Name
List of sold articles	Liste der verkauften Artikel
Article Prices	Artikelpreise
List of all article prices in database	Liste aller Artikelpreise in der Datenbank
Transferred articles	Übertragene Artikel
From table	Von Tisch
From table part	Von Teil
To table	Auf Tisch
To table part	Bis Teil
List transferred articles	Liste der übertragenen Artikel
Voided articles	Stornierte Artikel
List of voided articles	Liste der stornierten Artikel
Ordered articles	Bestellte Artikel
List of ordered articles	Liste der bestellten Artikel
Article number	Artikel Nummer
Poured articles	Ausgeschenkte Artikel
List of Poured articles	Liste der ausgeschenkten Artikel
Sales areas	Verkaufsbereiche
List of sales areas	Liste der Verkaufsbereiche
Sal.	Sal.
Reopened bill	Geöffnete Rechnungen
Pay date & time	Bezahlt Date/ Zeit
Bill number	Rechnungsnummer
Failed bill number	Falsche Rechnungsnummer
Current reopened bill information	Jetzige geöffnete Rechnungs Info
Bill	Rechnung
Sum to pay	Summe zu Bezahlen
Return amount	Retoure Betrag
Bill information	Rechnungs Information
Clients	Kunden
No client	Kein Kunde
List of clients	Liste der Kunden
Cln.	Kun.
Discount reasons	Rabatt Grund
List of discount reasons	Liste der Rabatt Gründe
Rsn.	Gru.
Articles with discount	Artikel mit Rabatt
Discount type	Rabatt Typ
List of articles, ordered with discount	Liste der Artikel mit Rabatt
Payments	Abrechnungsarten
Payments by waiter	Abrechnungsarten pro Kellner
Open tables	Offene Tische
Table name	Tisch Name
Amount to pay	Betrag zu Bezahlen
Open tables list	Liste der offenen Tische
Articles & options	Artikel / Optionen
List for printing articles & options	Liste aller gedruckten Artikel / Optionen
Tik-Tap	
Date/Time	Datum / Zeit
Ordered	Bestellt
Poured	Augeschenkt
Balance	Saldo
LCU dispencer number	LCU Spender Nummer
LCU dispenser name	LCU Spender Name
"Tik-Tap" article statistics	Artikel "Tik-Tap" Statistik
Cash drawer openings	Geldlade geöffnet
Printer	Drucker
User cash drawer openings statistics	Statistik Geldladen geöffnet
Bar balance	Bar Saldo
Current bar balance	Aktueller Bar Saldo
Client information	Kunden Informationen
On invoice	Auf Rechnung
Number	Nummer
Address	Adresse
Address 1	Adresse 1
Address 2	Adresse 2
Country	Land
Phone	Telefon
Fax	Fax
E-mail	E-Mail
Website	Webseite
Code	
In	
Out	
Inside	Innen
Init	
Date of birth	Geburtstag
Limit	
Account	Konto
Save points	Sparpunkte
Added from	Spartotal
Card	Karte
Picture	Bild
Is price active	Ist der Preis aktive
Price name	Preis Name
Promotion name	Promotion Name
(GC) Date	(GC) Datum
(GC) Postcode	(GC) Postleitzahl
(GC) House number	(GC) Hausnummer
(GC) Suffix	(GC) Hausanschrift
(GC) Addition	(GC) Hauszusatz
(GC) Town	(GC) Wohnort
(GC) Free visits	(GC) Freie Besuche
Current client information	Aktuelle Kunden Info
Bills	Rechnungen
List of bills	List der Rechnungen
Hotel guest identification	Hotelgast Information
Value	Wert
HotelConcepts client information	HotelConcepts Kunden Information
Folio sequence	Folio Sequenz
Date / Time	Datum / Zeit
User	Benutzer
Client	Kunde
Group	Gruppe
Category	Hauptgruppen
Count	Anzahl
Amount	Betrag
V.A.T.	MwSt
V.A.T. sign	MwSt Zeichen
Net VAT	Netto MwSt
VAT in percentages	MwSt in Prozent
Currency price	Währungpreis
Single price	Einzelpreis
Purchase price	Einkaufspreis
Customer amount	Kunden Betrag
Currency customer amount	Bezahlbetrag Kunden
Payment amount	Abrechnungsarten Betrag
Currency payment amount	Bezahlbetrag Währungen
Payment mode	Bezahl Mode
Payment kind	Abrechnungsart
Payment date & time	Abrechnung Datum / Zeit
Currency	Währung
Course number	Course Nummer
Department	Artikelgruppe
Profit	Gewinn
(Card) Masked PAN	
(Card) Control No	
(Card) PAN	
(Card) Expire date	
(Card) Authorization No	
(Card) Acquire code	
(Card) Extra Field 1	
(Card) Extra Field 2	
(Card) Extra Field 3	
(Card) Extra Field 4	
(Card) Extra Field 5	
(Card) Loyalty amount	
(Card) Card name	
(Hotel) Room	(Hotel) Zimmer
(Hotel) Guest name	(Hotel) Gast Name
(Hotel) Folio sequence	(Hotel) Folio Sequenz
(Hotel) Folio number	(Hotel) Folio Nummer
Menu	Menu
Menu articles	Menu Artikel
Articles in active menu	Artikel im aktiven Menu
Menu items	Menu Artikel
List of menu items	Liste der Menu Artikel
Men.	Men.
Menu modifiers	Menu Kommentare
List for printing menu modifiers	Liste der Menu Kommentare für den Ausdruck
Hourly turnover	Stundenumsatz
Date	Datum
From time	von Zeit
To time	bis Zeit
Printed proformas	Gedruckte Zwischenrechnungen
Printed proformas information	Gedruckte Zwischenrechnung Infos
Deposit information	Anzahlung Information
Old account status	Alter Kunden Status
The deposit	Die Anzahlung
New account status	Neuer Konten Status
Account history	Konten History
Account-paid items history	Kunden-Konto bezahlte Artikel History
Table reservations	Tisch Reservierung
Note	Anmerkung
Reservation start	Reservierung Start
Duration, min	Dauer, min
Reservation end	Reservierung Ende
Ready	Fertig
List of table reservations	Liste der Reservierungen
Accepted	akzeptiert
Closed	Geschlossen
Not accepted	Nicht akzeptiert
Reservations	Reservierung
Reserv. start	Reserv. start
Reserv. date	Reserv. Datum
Persons	Personen
Data of reservations	Reservierung Daten
Reservation statistics	Reservierungs Statistik
Start date/time	Start Datum/Zeit
Status	Status
Terminal	Termial
Terminals	Terminals
List of teminals	Liste der Terminals
Trm.	Trm.
Prices	Preis
List of prices	Preisliste
Prs.	Pers.
Client deposits	Kunden Anzahlung
Deposit statistics	Kunden Anzahlung StatistiK
Returned articles	Retournierte Artikel
List of returned articles	Liste der retournierten Artikel
Daily stock	Tages Lager
New quantity	Neue Menge
Daily stock state information	Tages Lager Information
Stock articles	Lager Artikel
List of articles available for stock control	Liste der Artikel vorhanden für Lagerkontrolle
St.Art.	La.Art.
Stock departments	Lager Artikelgruppen
List of departments available for stock control	Liste der Artikelgruppen vorhanden für Lagerkontrolle
St.Dep.	La.Gru.
Database clients	Datenbank Kunden
Last invoice	Letzte Rechnung
List of all clients in database	Liste aller Kunden in der Datenbank
Persons quantity	Anzahl der Gäste
Return allowed	Retoure erlaubt
Last Total	Letztes Total
Transfer waiter from	Übertragen Kellner von
Transfer waiter to	Übertragen Kellner auf
Amount for PSP special operation	Betrag für die PSP spezielle Operation
Client name	Kunden Name
Shared account	Gemeinsames Konto
Price without discount	Preis ohne Rabatt
(Bookkeeping) Turonver account	(Buchhaltung) Umsatz Konto
(Bookkeeping) Turonver account name	(Buchhaltung) Umsatz Konto Name
(Bookkeeping) VAT account	(Buchhaltung) MwSt Konto
(Bookkeeping) VAT account name	(Buchhaltung) MWST Konto Name
(Bookkeeping) Payment account	(Buchhaltung) Abrechnungarten Konto
(Bookkeeping) Payment account name	(Buchhaltung) Abrechnungsarten Konto Name
Shared account name	Gemeinsamer Kundenname
Discount	Rabatt
Amount - Discount	Rabatt Betrag
Children	Kinder
Original waiter	Alternative Kellner
Waiter's table	Tische des Kellners
List of waiter's tables	Liste der Tische des Kellners
Tbl.	Ti.
Paid articles	Bezahlte Artikel
Paid articles in active order	Bezahlte Artikel in aktiver Bestellung
All Departments	Alle Artikelgruppen
List of all available departments	Liste aller vorhandenen Artikelgruppen
All Dep.	Alle Artgr.
All articles	Alle Artikel
List of all available articles	Liste aller vorhandener Artikel
All Art.	Alle Art.
Card deposits	Karten Anzahlung
Card deposit statistics	Karten Anzahlung Statistik
Current invoice	Aktuelle Rechnung
Total - Discount	Total Rabatt
New discount value	Neuer Rabatt Wert
Current invoice information	Aktuelle Rechnungs Informationen
Current discount value	Aktueller Rabattwert
Number of covers	Gästeanzahl
Payer name	Name des Bezahlers
New service charge	Neuer Service Betrag
Service charge	Service Betrag
Tips amount	Trinkgeld Betrag
Current shared account filter	Jetziger geteilte Konten Filter
New tip amount	NeuerTrinkgeld Betrag
Sales area number	Verkaufsbereich Nummer
Client card	Kunden Karte
Date: month	Datum: Monat
Date: year	Datum: Jahr
Operation code	Operations Code
Operation name	Operations Name
Menu code	Menu Code
Operation waiter name	Operation Kellner Name
Operation date & time	Operations Datum / Zeit
Shared account number	Gemeinsames Konto
Shared account info	Gemeinsame Konto Info
Shared account address 1	Gemeinsame Konto Addresse 1
Shared account address 2	Gemeinsame Konto Addresse 2
Waiter (who closed)	Kellner (der abgerechnet hat)
Waiter (who opened)	Kellner (der geöffnet hat)
Discount in percentages	Rabatt in Prozent
From	Von
Till	Bis
Paid	Bezahlt
Transaction date	Transaktions Datum
Transaction time	Transaktions Zeit
Discount value/percentages	Rabatt Wert/Prozent
Discount amount	Rabatt Betrag
Invoice	Rechnung
Deposit on account	Anzahlung auf Konto
Reopen bill	Rechnung öffnen
Table areas	Tischbereiche
enabled	Aktiv
disabled	Inaktiv
List of table areas enabled manually	Liste der Tischbereiche manuell aktiviert
TblA.	TiBe.
Transactions	Transaktion
Opened by waiter	Eingabe Kellnername
Open date/time	Geöffnet Daten-Uhrzeit
Close date/time	Geschlossen Daten-Uhrzeit
List of transactions	Liste der Transaktionen
Hash transfers	Auslagen Transfer
Settled waiter name	Abgerechnet Kellnername
Ordered waiter name	Bestellt Kellnername
Amount of money	Geldbetag
Currency amount of money	Währung Geldbetrag
Date & time of payment	Datum / Zeit der Bezahlung
Hash payments ordered by other waiter	Auslagen Bezahlung des aktuellen Kellners
Tips	Trinkgeld
List of tips	Liste des Trinkgeldes
Shared accounts	Gemeinsame Konten
No Shared account	Keine gemeinsamen Konten
List of shared accounts	Liste der gemeinsamen Konten
ShA.	GemK.
Payer	Zahler
Printed invoices	Gedruckte Rechnungen
All printed invoices for the period	Alle gedruckten Rechnungen für die Periode
Daily stock balance	Tägliches Lager Saldo
Current daily stock balance	Aktueller Saldo tägliches Lager
Beconet control	Schnakkontrolle
Tap number	Ausflusshahn Nummer
Dosage	Dosierung
Size (volume)	Größe (Volumen)
Interval	Abstand
Overall status	Allgemeinstatus
Common balance	Allgemeines Gleichgewicht
Beconet control parameters	Schankanlagen Einstellungen
Beconet devices list	Schankanlagen Geräte Liste
Device	Gerät
List of Beconet devices in database	Liste von Schankanlagen in der Datenbank
BeCo groups	Schankanlagen Gruppen
List of BeCo Groups	Liste der Schankanlagen Gruppen
Beconet Articles	Schankanlagen Artikel
PLU	
Volume	Menge
Location	Lokal
Standard Volume	Standardmenge
Alternative Volume	Alternative Menge
List of Beconet articles	Liste der Schankanlagen Artikel
BeCo locations	Schankanlagen Standorte
List of BeCo Locations	Liste der Schankanlagen Standorte
Awaited promo article	Erwarteter Promoartikel
Current chair number	Gegenwärtige Stuhlanzahl
Primary/Secondary Group VAT	Vorängige/Untergeordnete MWSt Gruppe
Start time	Start Zeitpunkt
End time	Beendigungs Zeitpunkt
Total used time	Verwendete Gesamtzeit
Future order date & time	Zukünftiges Bestelldatum und Zeit
Ordering date & time	Bestelldatum und Zeit
Time article name	Zeitartikel Name
Current chair caption	Gegenwärtige Stuhlüberschrift
Locker	Schrank
Option quantity	Mengenauswahl
Proforma waiter	Proforma Kellner
Type	Typ
Total with tips	Gesamt mit Tip
New description	Neue Beschreibung
Price level	Preisniveau
Primary group vat	Vorängige MwSt Gruppe
Secondary group vat	Untergeordnete MwSt Gruppe
Chair number	Stuhlnummer
Chair caption	Stuhlüberschrift
Quantity to split	Menge zum teilen
Original_price	Orginalpreis
Shows "1" when article is a menu, otherwise "0"	Zeige "1", wenn der Artikel ein Menü ist, sonst "0"
Cash drawer amount	Kassenschublade-Betrag
Void/return reason	Storno/Rückgabe Grund
New article PLU number	Neue Artikel PLU Nummer
New chair number	Neue Stuhlnummer
New chair caption	Neue Stuhlüberschrift
Single Discount value	Einzelrabatt Wert
New price for sales area	Neuer Preis für den Verkaufsbereich
Print order?	Bestellung Drucken?
Print bill?	Beleg Drucken?
Operation date	Operationsdatum
Is bill printed?	Wurde die Rechnung gedruckt?
Save points balance	Bonuspunkte abgleich
Payment part	Zahlungsteil
Payment part count	Zahlungsteil Anzahl
Sum to return	Summe zum zurückgeben
Cancelled	Annulliert
BO table name	BO Tischname(Nr)
Original bill number	Original Rechnungsnummer
Beverage Control group name	Schankanlagen Gruppenname
Beverage Control group number	Schankanlagen Gruppennummer
Bill description	Rechnungsbeschreibung
Waiter group (that closed)	Kellnergruppe (der abgerechnet hat)
Waiter group (that opened)	Kellnergruppe (der geöffnet hat)
EFT tip	
Folio number	Folio Nummer
id	
Original price	Orginal Preis
Options count	Option Anzahl
Discounted?	Rabattiert?
Deposit number	Ablagerungszahl
Waiter group	Kellner Gruppe
Parts count	Teileanzahl
Part index	Teilenummer
Original single price	Orginaler Einzelpreis
Online booking system name	Online Buchung System Name
Deleted	Gelöscht
BO	
Invoice Date/Time	Rechnungs- Datum/Zeit
Settled waiter group	Feste Kellner-Gruppe
Discount. In case of percentage "%" sign added	Rabatt. Im Falle des hinzugefügten Prozentsatz-"%"-Zeichens
Absolute value of discount	Absoluter Wert des Rabattes
Invoice total	Gesamtrechnung
Voided orders	gelöschte Bestellung
Kitchen screen	Küchenmonitor
Time	Zeit
Not fired articles with separate course (simple kitchen screen)	Artikel mit "seperaten" Gang, die noch nicht genannt werden (um im Küchenmonitor verfogel zu können)
Discount payments	Abschlagszahlungen
In/out cash	Ein- /Auszahlung
In-out cash operations	Ein- /Auszahlung Vorgang
In/out Cash parameters	Ein- /Auszahlung Einstellungen
Supplier	Lieferant
Reason	Grund
Current In/Out cash operation	laufender Ein/Auszahlungs Vorgang
Beverage Control Turnover	Schankkontrolle Durchsatz
Action	Aktion
Table history	Tischhistorie
Date/time: low bound	Reihenfolge Tageszeit: kleinste zuerst
Date/time: high bound	Reihenfolge Tageszeit: größte zuerst
Order date/time	Bestellzeit
Orders history for current table	Bestellhistorie für den gegenwärtigen Tisch
Last orders	letzte Bestellung
Shows last ordered articles	Zeige letzen bestellten Artikel
Table history (version for print)	Tischhistorie (Druckversion)
Order of printing	Anordnung des Druckes
Unit price	Einheitspreis
Orders history for current table (version for print)	Bestellreihenfolge für ausgewählten Tisch (Druckversion)
Gross total	Brutto Summe
GT+	
GT	
GT-	
Shows gross payments total per PC	Zeige gesamte Zahlungen pro PC
Sold articles overall	Verkaufte Artikel insgesamt
Shows all sold articles, including menu items, excluding menu articles	Zeige alle verkauften Artikel, einschließlich Menüteile, Menüartikel ausschließend
Article notify areas	Artikel Bereich bekannt
Preparation area to notify	Vorbereitungsbereich bekannt geben
Purpose of notification	Zweck der Ankündigung
List of preparation areas to be notified for an article	Liste dessen gibt Vorbereitungsbereich für einen Artikel bekannt
Database options	
List of options, can be inserted into Articles	Liste von Optionen, kann in Artikel eingefügt werden
Available sales areas	Verfügbare Verkaufsbereiche
Sales area name	Verkaufsbereich Name
Sequence number	Sequenz Nummer
List of sales areas where article/option is available	Liste von Verkaufsbereichen wo Artikel/Option beinhaltet ist
Database courses	Datenbank Gänge
List of all courses in database	Liste der Gänge in Datenbank
Payzone Payment Receipts	Zahlungsbereich Rechnungeinnahmen
Original Payzone receipts data	Originale Zahlungsbereich Rechnungs Daten
Final articles for Combo-Promo	Feritge Artikel für Kombi-Promo
List of final articles for Combo-Promo	Liste der feritgen Artikel für Kombi-Promo
Articles in Combo-Promo	Artikel für Kombi-Promo
List of articles in Combo-Promo	Liste der Artikel für Kombi-Promo
Table info	Tisch Info
Order name	Bestellname
Waiter who initially opened the table	Kellner, der Tisch eröffnet hat
Time when table was initially opened	Zeit, wann der Tisch eröffnet wurde
Waiter who took the last order	Keller, der letzten Bestellung
Time when the last order was taken	Zeit, der letzten Bestellung
Time when "Next course" has been asked	Zeit, von "Nächster Gang" angefragt wurde
Cash drawer amounts	Kassenschublade Betrag
Cash drawer amount list	Kassenschublade-Betrag-Liste
Payment part info	Teilzahlungs Info
Parts quantity	Teilmenge
Part client	Kundenteil
Part total	Teile Gesamt
Driverthru orders	Durchläufer Bestellungen
Shows Drivethru orders	Zeige Durchläufer Bestellungen
Re-printed bills	Wiederholte Rechnungsdrucke
Date/Time of reprint	Datum/Zeit des wiederholten Rechnungsdruckes
List of re-printed bills	Liste der wiederholte Rechnungsdrucke
Barcode	
Future quantity	Zukunft Menge
Year	Jahr
Month	Monat
Week	Woche
First day of week	Erster Tag der Woche
Last day of week	Letzter Tag der Woche
From date	Vom Datum
Split bill number of parts	Teilrechnung Vorgangsnummer
Partial price	Vorgangs Preis
Operator ID	Anwender ID
SmartCuisine ID	
Text	
Option	
Purpose	beabsichtigen
Location Name	Lokal Name
Location Number	Lokal Nummer
Article info	Artikel Info
Article info (line 2)	Artikel Info (Zeile 2)
Negative total	Gesamtabzug
Reason of void	Stornogrund
Reason of return	Rückgabegrund
Is price manual	Ist manueller Preis
User group	Benutzer Gruppe
Department number	Artikelgruppe Nummer
Group number	Gruppen Nummer
Category number	Kategorie Nummer
(Card) Payzone: 2 bills	
(Card) Account type	
(Card) Terminal ID	
(Card) AID	
(Card) TVR	
(Card) TSI	
Chairs	Stühle
List of chairs, used in Order	Liste der Stühle, in Bestellreihenfolge
Chr.	Stu.
Client bookings	Kundenbuchungen
Cbk.	KBu.
Suppliers	Lieferant
List of suppliers	Liste der Lieferanten
Spl.	LLi
Future orders	Zukunftsbestellungen
No order	Keine Bestellung
List of orders in future	Liste der Zukunftsbestellungen
Ord.	Best.
Free options	Freie Option
List of free options	Liste der freien Optionen
Free.	Frei.
Void reasons	Stornogründe
List of void reasons	Liste der Stornogründe
Void rsn.	
Paym.	
Notes & Coins	
N&C	
Cmb art.	
Fin. cmb.	
Part info list	Teil Info Liste
Table/Drive-thru	Tisch/Durchläufer
List of booked tables	Liste der reservierten Tische
TBk.	
List of printed proformas	Liste der gedruckten Zwischenrechnungen
Prf.	
Issue	
Reload	
Void Issue	
Void Reload	
Void Deactivate	
Deactivate	
Rented	
In use	
Under repair	
Free	
After repair	
Not confirmed	
To	
Not started	
Waiting	
In process	
Awating pick up	
Finished	
Preparation	
Information	
Distribution	
Hand held device	
Stock location	
Item signature	
Regular item	
Modifier	
Single article	
Main menu	
Bonus	
Promo	
Menu item	
List of options related to printing article	
Internal ID	
Linked to stock	
Parent menu	
Bonus group	
Parent bonus article	
Weight	
Closed waiter's insurance	
Item type	
Option name	
Client ID	
Order integer number	
Transaction name	
Item kind	
Consolidate quantity	
Native department	
Native group	
Native category	
Messages	
Additional info	
Discount percentage	
Single purchase price	
TD	
%	
Service charge amount	
Vat on Service charge	
Service tax amount	
Service tax on Service charge	
Comments	
Transaction integer number	
Transaction ID	
Bill integer number	
Fiscal number	
Bill status	
Bill ID	
Client code	
Cash tip	
FDM: Production number	
FDM: Signature	
FDM: Manufacturing Code	
FDM: Signing Code	
FDM: Control Timestamp	
FDM: Ticket Counter	
FDM: Vsc Identification number	
FDM: PLU Data Hash	
Bill total	
Bill total VAT	
Bill total original price	
Bill open date/time	
Client balance	
SA client balance	
Active	
Reopened	
Voided	
Unknown	
Client ZIP code	
Invoice description	
Sales Area ID	
Last order date/time	
Service tax	
Closed by waiter	
Payment name	
Reprinted waiter	
Rental item status	
List with rental statuses	
Rental items on bill	
Rental ID	
Article name	
End date/time	
List of rental items on bill	
Bonus Articles	
Include in data set 'Articles' to see bonus articles	
Discount on bill total	
VAT	
Netto VAT	
VAT%	
List of discounts on bill total	
Bill service charge	
Service charge information	
Rental items in repair	
Repair reason	
Start date	
List of rental items in repair	
Rental history	
End date	
Daily stock adjustments	
List of daily stock actions	
Savepoints turnover	
Items transferred from other tables	
Add to data set 'Transactions' to see transferred items	
Cancelled articles	
List of cancelled articles	
Z/X Reports	
X-Report	
Origin	
Z/X Reports Printing History	
Export Task	
Backoffice	
POS	
KS articles awaiting pickup	
List of articles in KS awaiting pickup	
Fast lane	
Service charge details	
Service charge details report	
Gift Cards & Vouchers	
Gift Operations	
List of gift-card operations over the period	
Yes	
Smartcard deposits	
Smartcards deposits report data	
Kitchen screen statistics	
Started	
Sent to Pickup	
Complete	
Articles daily balance	
Voucher payments	
Database sales areas	
List of all sales areas in database	
Multi vouchers	
Voucher number	
Voucher barcode	
Voucher amount	
Bonus groups	
List of bonus groups	
Bonus parent articles	
List of bonus parent articles	
Voucher bill items	
Rest amount	
Voucher payment	
Voucher payment info	
Coupons	
Identity code type	
Coupon item number	
Coupon template number	
Coupon template name	
Coupon number	
Coupon name	
Single barcode?	
Fields for coupon(s) layout	
Single	
Multi	
QR code	
Previously ordered coupon items	
Coupon items, ordered in previous bills	
Coupons report	
Quantity of coupons made	
Quantity used	
State	
Ordered articles in coupon	
List of coupons	
Payment vouchers	
Paid amount	
Original amount	
Voucher payments in current bill	
Coupon articles	
Articles ordered with coupon	
Voided articles in Bill	
List of voided articles in Bill	
Complete meal	
FDM Belgium articles	
Kitchen screen order	
Account Turnover	
Account payments and deposits	
Order type	
Waiter insurance	
Phone 2	
Shared acount number	
Shared acount info	
Shared acount address 1	
Shared acount address 2	
Start article time	
End article time	
Last update date/time	
KS state	
Duration	
Expiration date	
Chair from	
Chair to	
New Group Name	
Group Name	
Creator	
Decrease reason	
Order description	
Message text	
Duration(hh:mm)	
Article HQ ID	
Future deposit amount	
Department HQ ID	
Pre-paid price	
Prepaid net VAT	
Pre-paid VAT	
Pre-paid kind	
savepoints amount	
Receiving waiter	
Original waiter first name	
Original waiter group	
Total free numeric 1	
Total free numeric 2	
FDM item type	
Day of week	
Daily stock amount	
Free num field 1	
Free num field 2	
Free text field A	
Free text field B	
Daily stock value 1	
Daily stock value 2	
Price for weight unit	
HQ ID	
PLU number	
Sold quantity	
Notes	
Free discount reason	
Tips currency	
Currency tips amount	
Client phone	
Old quantity	
Original quantity	
Percentage amount	
Service charge plan	
Currency amount	
Customer return amount	
Operation kind	
Parent item number	
Operation	
Internal name	
Masked PAN	
(Card) Merchant Id	
(Card) Cashback	
(Card) Misc Data	
(Card) Extra Field 6	
(Card) Extra Field 7	
(Card) Extra Field 8	
(Card) Extra Field 9	
(Card) Extra Field 10	
(Card) Extra Field 11	
(Card) Extra Field 12	
(Card) Extra Field 13	
(Card) Extra Field 14	
(Card) Extra Field 15	
(Card) Card type	
(Card) Card holder name	
(Hotel) Transaction number	
(Zapper) Payment UID	
(Zapper) Customer name	
Bookkeeping account number	
Card UID	
Initial	
Smartcard Group	
T2O Number	
(Smartcard) New Balance	
Take away?	
Time before countdown starts (minutes:seconds)	
Order time	
Start countdown time	
Remaining preparation time (minutes:seconds)	
Preparation time (minutes:seconds)	
Warning time (minutes:seconds)	
Kitchen screen status	
Separate?	
Free number 1	
Free number 2	
Total free number 1	
Total free number 2	
Price per weight unit	
On hold?	
Bonus?	
on hold	
Smart card	
Fast lane?	
Transfer table from	
Table info order	
Client's order history	
Shows order history per client	
All menu items	
All ordered menu items	
Article options	
Selected article options	
Rental items	
Rent start date/time	
Rent end date/time	
List of rented articles by Rental ID	
Undefined	
Not assigned	
Closed bills	
Waiter (who created first order)	
Reopened bill number	
Bill date & time	
Reopened bill date & time	
Waiter, who paid bill	
Bill client name	
Bill payer name	
Shows a list of closed bills	
Finalized	
Closed orders	
Order date & time	
Waiter, who created order	
Order client name	
Shows a list of closed orders	
Deposits	
Date & time	
User name	
Discount %	
Shows a list of deposits per business day	
Paid bills	
Has tips?	
Has Cash tips?	
Cash tips	
List to add tips to closed bills	
No	
Available rental articles	
Number of articles available for rent	
Articles in course	
Number of articles in courses of current transaction	
Client printed invoice	
Invoice date & time	
Invoice amount	
List of printed invoices	
Preparation time (minutes)	
Course time frame	
Kitchen screen overview	
Take away	
Purpose type	
List of articles for Kitchen screen	
Fast lane articles	
Waiter who ordered	
Kitchen screen detail	
Allergen names	
Allergens	
Elapsed time	
Fire course time	
Initial order time	
Finish time	
Actual preparation time	
Difference time	
Picked up time	
Detailed list of articles for Kitchen screen	
KS list completed articles	
Remaining preparation time	
Completed time	
List of completed articles for Kitchen screen	
Kitchen screen consolidate	
First order date/time	
Consolidate list of articles for Kitchen screen	
Awating time	
List of articles awaiting delivery	
KS delivered articles	
List of articles delivered to table	
KS pickup per article	
Kitchen screen counter	
Waiter, who opened bill	
Quantity of articles in course	
Number of KS articles in order	
Duration since kitchen told food is ready(Minutes:Seconds)	
List to inform waiter of pickup	
Charge Allowed	
Credit Limit	
Message Waiting	
List of hotel guests	
Choose guest from the list	
Room details	
Detailed information on room	
Client info	
Client contact	
Gift Card Operations List	
List of gift card operations	
CC Type	
Saved	
Course name	
Transaction	
Open waiter	
Order waiter	
Order items	
Articles quantity	
Article price	
Article vat	
Bill waiter	
Bill date/time	
Table transfer	
Transfer waiter	
Transfer manager	
Transfer date/time	
Transfer to - table number	
Void orders	
Void waiter	
Void date/time	
Void reason	
Reopen bills	
Reopen payments	
Proforma details	
Shows proforma details	
Kitchen screen preparation	
Prepartion list of articles for Kitchen screen	
Not paid amount	
Creation date	
On hold overview	
Remaining time	
List of articles for On Hold screen	
KS not confirmed articles	
List of not confirmed articles for Kitchen screen	
Block/Unblock reason	
Quantity of coupons used	
Expiration date/time	
User, who created coupon	
Coupon state	
Preparation area name	
Time block to	
Time slot to	
Deliverer	
Time start	
Time block	
Time slot	
Deliverer name	
Delivery time	
Selected rent barcodes	
Number of barcodes in rental	
List of chosen barcodes	
Card table control list	
Comment	
Registration time	
Card code	
Shows a list of card tables	
Reserved	
Active bill	
In blacklist	
Card table order	
Card table overview	
Consolidated card table overview list	
KS items to change course	
List KS items that will have the course changed	
Detail info	
KS selected consolidate list	
Adjust date	
Original value	
New value	
User who created voucher	
Remaining value	
Adjusted value	
Adjusted by user	
Voucher state	
Selected allergens	
Allergen	
Image	
All coupons	
Not used coupons	
New/Amount	
Blocked	
Expired	
Spent	
Stock balance	
Ordered quantity	
Client number	
Card number	
Account balance	
Shared account balance	
R	
Repeated?	
Repeated	
OIF number	
Upsellers	
List of upsellers	
Upsl.	
Pinf.	
Period-articles	
Period-articles periods	
PerArt.	
Article periods	
Available article periods	
Pers.	
Overdue rents	
List of not paid overdue rents	
Ov.Rent.	
Pre-paid groups	
Available amount	
List of pre-paid groups	
P/p gr.	
Pre-paid group articles	
List of pre-paid group articles	
P/p art.	
Preparation areas	
List of preparation areas to print order	
Prep.	
Articles for exchange to pre-paid	
Quantity to exchange	
List of articles in current order which can be exchanged to pre-paid	
P/P Exch.	
Pre-paid voucher groups	
List of prepaid voucher groups	
P/p vch	
Service charge plans	
List of service charge plans	
SC plan.	
Courses awaiting pickup	
Courses, awaiting pickup	
Pickup	
Separate courses	
Shows all separate courses in database	
Crs.	
Kitchen screen devices From...	
Shows all kitchen screen devices From..	
Ksd. From.	
Kitchen screen devices routings	
Shows kitchen screen devices routings	
Ks rts.	
Show all courses in DB	
DB Crs.	
Database groups	
Show all groups in DB	
Fg.	
Database categories	
Show all categories in DB	
Cat.	
Tables awaiting transfer acceptance	
Tables, awaiting transfer acceptation	
Tbl. Acc.	
Kitchen screen devices To...	
Shows all kitchen screen devices To..	
Ksd. To.	
Courses with KS-rerouting	
Shows courses that are rerouted in KS	
RR Crs.	
Articles ready for pickup	
Articles that can be picked up	
KS tran.	
Tap2Order Group Members	
List of clients in Tap2Order group on "Manage Group" screen	
T2O Mm.	
Special words	
List of special words	
SW.	
Filtered articles	
Sch Art.	
Flt. Art.	
Editing options	
List of editing options	
Ed. Opt.	
Kitchen screen active tables	
Shows active tables in Kitchen screen	
Act. tbl.	
Ordered options	
Options of current article	
Ord. Opt.	
Sorted tables	
List of sorted tables	
Srt. tbl.	
Kitchen screen page tables	
Shows one page tables in Kitchen screen	
PG. tbl.	
Delivered orders	
Dlv. Ord.	
Edited articles	
Edit articles	
Edt. Art.	
Article parent entities	
Options, included article	
Prn. Opt.	
Daily balance articles	
Daily balance	
Daily balance article list	
Dl.Art.	
Vouchers	
Voucher End date	
List of vouchers	
Vch.	
Waiter's reports	
Shows waiter's report list	
Wt. rep.	
Hand held devices	
List of active hand held devices	
HHT	
Orderman:	
Sales U/M	
List of sales unity of measure	
Sal.UM.	
Waiter's orders	
Waiter name	
Waiter number	
Show waiters & orders data	
Wt. Ord.	
Guest Discount Groups	
List of discount goups for current hotel guest	
Coup.	
KS consolidate article list	
Cons. Art.	
KS items in preparation	
KS articles in preparation	
Prep. Art.	
KS tables in preparation	
Table numbers	
Prep. tbl.	
Option list	
KS detail article list	
Dt.Art.	
Smart Cards Groups	
List of smart cards groups	
SCGr.	
Voucher values	
List of all existing voucher values	
Vch.V	
Common drawers	
Common drawer types	
Cmn.Dr.	
First	
Second	
None	
User login actions	
Usr.Act.	
Show General Manager	
Take away bookings	
Customer emails	
List of customer emails	
Eml.	
EFT Config Selection	
List of available EFT Configrations for current terminal	
EFT Cfg.	
Report articles	
List of articles to choose in reports	
Rep.Art.	
Report departments	
List of departments to choose in reports	
Rep.dep.	
Coupon templates	
Coupon template list	
Cpn.Tmp.	
Unlimited	
Used amount	
Cpn.	
Coupon items	
Rest items quantity	
List of coupon items	
Cp.	
Modified options	
List of modified options	
Mdf.Opt.	
Voucher/coupon departments	
List of voucher/coupon departments	
V.C.Dep.	
Ordered coupon items	
Show coupon items to order	
Ord.Cp.	
Historical coupons	
List of historical coupons	
Hst.Cpn.	
Take away orders	
List of active Take away orders	
City	
TA.Ord.	
Take away time blocks	
List of Take away time blocks	
TA.TB.	
Take away time slots	
List of Take away time slots	
TA.TS.	
Order quantity	
Article quantity	
Take away time blocks From	
Take away time blocks transfer from	
TA.TB.From	
Take away time blocks To	
Take away time blocks transfer to	
TA.TB.To	
Take away time slots From	
TA.TS.From	
Take away time slots To	
TA.TS.To	
Take away transfer orders	
List of active Take away orders for transfer	
TA.Tr.Or.	
Newyse Guests	
Available items	
List of all Newyse guests in resort	
Newyse Guest Items	
List of all items of the current Newyse guest	
N.Guest Items	
Promo suggestions	
Combo/Promo articles can be used in current order	
Prm.S.	
Alternate Promo articles	
Alt.Pr.	
No barcodes rentals	
List of rentals with no linked barcode	
NB.Rent.	
Rent repair reasons	
List of rentals repair reasons	
Rep.Rsn.	
Rent items	
List of barcodes for repaired rentals	
Rent.Itm.	
Newyse Distribution Methods	
List of all available Newyse distribution methods	
Distr. Methods	
Take away deliverers	
Number of orders	
Takeaway deliverer	
TA.PCK.	
Articles in Bill	
Currently chosen Bill article list	
Art.Bill	
Card tables booking	
List of card table bookings	
CTBk.	
DB Order types	
Show all order types in DB	
O.TP	
Adjusting vouchers	
List of adjusting vouchers	
Adj.Vch.	
List of allergens	
Alrg.	
Free option groups	
List of free option groups	
Free.Gr.	
Last ordered article	
Keeps last ordered article params	
Kitchen screen info	
Kitchen screen message	
Kitchen screen device number	
Kitchen screen device name	
Bills sort type	
Articles type	
KS general information	
KS table info	
Allergen picture	
Time left to finish	
Table area	
Time before first countdown starts	
Take away time slot color	
Take away time slot	
Time from first order	
Tile number	
Take away color	
Waiter who opened table	
Waiter who currently owns table	
Minutes since intial order	
Course color	
Waiter who made last order	
Last order date & time	
Active course	
Waiter who asked last Next course	
Last Next course date & time	
Minutes since last order	
Minutes since last Next course	
KS active table info	
KS detail progress	
KS complete table info	
Active time	
Waiter, who made last order	
Kitchen screen pagination	
Bills quantity	
Shows kitchen screen pagination info	
Voucher data	
Vouchers total	
Voucher value	
Voucher rest amount	
Barcode filter	
Vouchers state	
Single voucher data	
Restaurant covers capacity	
Minimal covers quantity	
Maximum covers quantity	
Current covers quantity	
Shows current restaurant covers capacity	
Restaurant tables capacity	
Minimal tables quantity	
Maximum tables quantity	
Current tables quantity	
Shows current restaurant tables capacity	
Current course	
Tap2 group name	
Course info	
Payment status	
Open amount	
Rental pickup	
Message	
Name filter	
Rent barcode	
New rent barcode	
New repair barcode	
Rental items filter	
Rent action	
Current rental pickup information	
Rental barcode status	
Return caption	
Prolong caption	
Repair caption	
Free rent article filter	
rent items amount	
Period units amount	
Period type	
Hours	
Days	
Vat number	
Zip code	
Reference	
Invoice e-mail(s)	
Last used date	
Deposit amount	
Enable save points	
Is active	
Currency tips	
Article weight	
Rent price	
Rent price period	
Rent price period value	
Edit article properties	
Option type	
Sales unity of measure	
Stock ingredient	
Stock supplier	
Stock recipe	
Edit article price	
Last chosen article	
Order in future schedule	
Periodicity	
Period count	
Schedule status	
Next OIF date/time	
Hour	
Day	
Not active	
Client not selected	
Balance data	
Shows data returned by balance if installed on PC	
Tap2Order	
Modified Group Name	
Card Holder Name	
Card Holder Group	
Data used by Tap2Order functionality	
EFT Payment Receipt	
Data used EFT "declined" receipt	
Pre-Auth result	
Total Amount	
Pre-Auth operation result	
Newyse customer	
Remaining voucher amount	
Customer name	
Input room number	
Customer ID	
Resort	
Reservation number	
Newyse customer information	
Gift card operations	
Operations from	
Operations till	
Input amount	
Fields required on Gift Card Operations screen	
Gift Card Operation Ticket	
Suffix	
(Card) Old Balance	
(Card) New Balance	
Data required for "Gift Operation" ticket	
Table Pre-Auth Info	
Total Pre-Auth	
Pre-Auth Amount Left	
New Pre-Auth Amount	
Pre-Auth information for current table	
Client filter	
Complete meal remainder total	
Complete meal total	
New complete meal quantity	
New complete meal price	
Filter table number	
Service charge vat	
Grand total	
Total excl. VAT	
EFT tips	
Total paid amount	
Service charge in percentages	
Tax amount	
Filter by table number	
Filter by client	
Filter by Note	
Filter by status	
New note	
New client contact	
New client info	
Smart Card Info	
UID	
Card Pricelevel	
Client Pricelevel	
(TTG) Validity	
(TTG) Patrons	
(TTG) Servings Per Patron	
(TTG) Creation Time	
(TTG) Expiration Time	
(TTG) Creator	
(TTG) Servings	
(TTG) Servings Limit	
(TTG) Card Type	
Displays Smart Card Information	
Current article name	
Current article quantity	
Daily stock amount 1	
Daily stock amount 2	
Daily article balance	
Tendered amount	
Smart card limit	
Smart card rest	
New order type	
Promo color	
Scanned coupon	
FDM: Extra1	
FDM: Extra2	
FDM: Transaction number	
Future deposit save point amount	
Service charge plan name	
OIF repeated?	
Native table number	
Total free num field 1	
Total V.A.T.	
Total excluded V.A.T.	
Total free num field 2	
Number of articles in transaction	
Waiter first name	
Waiter last name	
Save points amount	
Total discount value	
Initial waiter	
Initial waiter first name	
Initial waiter number	
Primary/Secondary Order group VAT	
Next future order date & time	
Future not paid	
New number of covers	
Close date & time	
FDM: Original Signature	
Free void reason	
Newyse: Customer Name	
Newyse: Customer ID	
Newyse: Reservation ID	
Newyse: Card Number	
Newyse: Reservation Number	
Newyse: Resort	
Newyse: Account Balance	
Not paid excluding SC	
Order phone	
Sum to return(Main currency)	
Combined table	
Cash EAN 13 barcode	
Total discount	
Note(Ticket Restaurant) value	
Proforma QR-code	
Old account balance	
Article count with save points	
Proforma number	
Failed pro forma number	
Split sum to pay	
Take away booking filter	
Kitchen screen from..	
Kitchen screen to..	
In/out cash account	
OBS account	
New cash drawer amount	
Article free text A	
Article free text B	
Labor/Cost	
OIF filter	
Current article period	
Final cash amount	
Quantity to take out of on hold	
Articles quantity/Discount value	
New tip amount(Main currency)	
New PSP tip amount	
Last received amount	
Pick up screen enabled?	
Pick up screen enabled (Non separate)?	
Last returned amount	
Article re-order mode	
Report terminal	
Report hand held terminal	
Report sales area	
Relative	
Absolute	
Exchange	
Classic Kitchen screen	
Professional Kitchen screen	
Waiter table	
Table %d	
Not defined	
KS consolidate	
Quantity of prepared items	
KS consolidate data	
Multi voucher data	
Vouchers quantity	
On Hold table info	
Pagination	
Page info	
Voucher bill	
Voucher bill info	
Customer email	
Customer email information	
Coupon template	
Printed amount	
Coupon block state	
Unlimited?	
Printing type	
Print quantity	
Coupon items quantity	
Barcode/QR code?	
Single code/Unique code?	
Coupon template data	
Not selected	
Time slot capacity	
Minimal KS orders number	
Maximum KS orders number	
Current KS orders number	
Shows time slot capacity	
Newyse Guests Overview	
First Name	
Middle Name	
Last Name	
Zip	
Reservation Id	
Resort Code	
Resort Name	
Zip Filter	
Room Filter	
Newyse Guests Overview Screen Fields	
All available	
Definitive Cancelled	
Declined	
Quota	
Optional	
Provisional	
Definitive	
Checked In	
Checked Out	
Card table info	
Smart card total limit	
New card limit	
New smart card	
Shows Card table reservation info	
No data	
Page quantity	
Current page number	
Card table overview info	
Table type color	
KS change course data	
Destination course	
Transfer quantity	
KS report table info	
General Manager values	
General manager progress	
Total turnover	
Total ordered	
Total paid	
Total unpaid	
Number of open tables	
Number of closed bills	
Average per Bill	
Average per Customer	
Number of customers	
Number of voids	
Total of voids	
Number of reopened bills	
Total of reopened bills	
Total of discounts	
Number of reserverations	
Number of printed reports	
Number of transfers	
Number of discounts	
Shows General Manager values	
General Manager chart	
Shows General manager chart	
Manager closed bills	
List of manager closed bills	
Manager open tables	
Bill name	
List of manager open tables	
Manager voids	
List of manager voids	
Manager discounts	
Original total	
List of manager discounts	
Manager transfers	
Table from	
Table to	
Number of articles	
List of manager transfers	
Manager turnover	
Daily turnover by department	
Manager ordered amount	
Amount ordered	
List of manager ordered articles	
Manager re-opened bills	
Original closing time	
List of manager re-opened bills	
Unknown Legal Report	
Manager printed reports	
Number of reports	
Report name	
List of manager reports	
Manager journal	
Stock locations	
List of stock locations	
St.loc.	
Stock ingredients	
On hand amount	
Extra info	
Ingredients & stock balance	
Ing.	
New ingredient price	
Current ingredient class	
Ingredient purchase price	
New invoice amount	
New physical amount	
New amount	
Transfer amount	
Normal location amount	
Search sample	
Unity measure type	
Ingredient display mode	
Default location U/M	
Default location amount	
Stock location transfer to	
Current ingredient	
Current amount	
Location U/M	
Purchase U/M	
Ingredients stock balance	
Location from U/M	
Location from amount	
Ingredient name	
Ingredient number	
Difference (On hand vs Normal)	
Need adjust ingredients	
Difference	
Usage U/M	
Ingredient class	
Ingredient class number	
Ingredients need to adjust	
Stock recipes	
Choose stock recipe	
Rec.	
Stock ingredient class	
Choose stock ingredient class	
Ing.Cl.	
Stock	
Edit ingredient properties	
Supplier min. amount	
Supplier order day of week	
Supplier delivery day of week	
Min. quantity	
Average purchase price	
Par. quantity	
Location normal amount	
Supplier item description	
Count U/M	
Counting location	
Contract price	
Ingredient properties	
Daily	
Count unity convertions	
Cnt. UM.	
Purchase unity convertions	
Prch. UM.	
Purchase Orders - not complete	
PO number	
PO name	
PO date	
PO time	
PO state	
PO.	
Ingredient document properties	
Item notes	
Supplier total amount	
Purchase order advice ingredient properties	
Purchase order properties	
Customer nr.	
Delivery date	
Purchase order advice properties	
Stock invoice properties	
Purchase Orders - ordered	
PO.FR.	
Physical count ingredients	
Counted amount	
Untity of measure	
Cnt.Ing.	
Multi Purchase order	
Multi Purchase order properties	
Transfered stock ingredients	
Location to amount	
Location to U/M	
Stock supplier items	
Supplier item U/M	
Sup.I.	
$FORMNAME	RestaurantPluginU
Need condiment	Brauche Kommentare
Need supplement	Brauche Extras
Select article	Wähle Artikel
Select department	Wähle Artikelgruppe
Go to table	Gehe zu Tisch
Need table split	Benötige getrennter Tisch
Need table transfer	Benötige Tischwechsel
Change article course	Ändere Artikelgang
Need combine table	Benötige zusammengelegte Tische
Need change course	Benötige Gangwechsel
Need table transfer by article	Benötige Tischwechsel pro Artikel
Need payment	Benötige Bezahlung
Send payment mode	Benötige Bezahlarten
Split Bill	Rechnung splitten
Change order name	Ändere Bestell Name
Need proforma	Benötige Zwischenrechnung
Need article message	Benötige Artikelnachricht
Z-Report can be sent to printer only	Z-Bericht kann nur gedruckt werden
Generate Z-Report	Erzeuge Z-Bericht
Goto paid bill	Gehe zu Rechnung bezahlen
Need control screen	Benötige Kontroll Bildschirm
Print bill	Drucke Rechnung
Re-open bill	Öffne Rechnung
Direct sales	Direkter Verkauf
Need select table	Wähle Tisch
Test	Test
Need discount	Benötige Rabatt
Apply fixed discount	Bestätige Euro Rabatt
Apply percent discount	Bestätige Prozent Rabatt
Paid articles	Bezahlte Artikel
Ordered articles	Bestellte Artikel
Open drawer	Lade auf
Need client	Benötige Kunden
Need bar balance	Benötige Bar Saldo
Tip handling	Trinkgeld Handling
Need HC guest info	Benötige HC Gast Info
Get HC guest	Erhalte HC Gast
Next menu	Nächstes Menu
Next course	Nächster Gang
Need deposit on account	Saldo auf Konto nicht ausreichend
Need print invoice	Benötige Rechnungsdruck
Undo menu item	Lösche Menu Artikel
Need preview invoice	Benötige Rechnungs Voransicht
Need reservation	Benötige Reservierung
Close current reservation	Schliesse jetzige Reservierung
Accept current reservation	Akzeptiere jetzige Reservierung
Need reservation list	Benötige Reservierungsliste
Enter tip amount	Trinkgeldbetrag eingeben
Modify client info	Ändere Kunden Info
Add new client	Neuen Kunden hinzufügen
Delete client	Lösche Kunden
OnCard PSP special function	
Need return articles	Benötige Retoure Artikel
Need menu message	Benötige Menu Nachricht
Need waiter report	Benötige Kellnerbericht
Report is printed on printer	Bericht ist gedruckt auf Drucker
Need stock control	Benötige Lager Kontrolle
Need article info	Benötige Artikel Info
End-Of-Day export (Hotel Concepts)	
You are now working in Direct Sales mode	Sie arbeiten jetzt im Direktverkaufmodus
You are now working in Normal Sales mode	Sie arbeiten jetzt im Tisch Modus
Need direct sales mode	Benötige Direkt - Verkaufs Modus
Need normal sales mode	Benötige Tisch - Verkaufs Modus
Need transfer waiter	Übertrage Kellner
Allow return articles	Erlaube Artikel Retoure
Need waiter from	Benötige Kellner von
Need waiter to	Benötige Kellner auf
Make table dirty	Markiere Tisch als schmutzig
Make table clean	Markiere Tisch als sauber
Show paid articles	Zeige Bbezahlte Artiikel
Need PSP special operation	Sie brauchen PSP spezielle Operation
Need invoice discount	Benötige Rechnungs Rabatt
Need predefined discount	Benötige voreingestellten Rabatt
Need service charge	Benötige Service Bertag
Need Shared account	Benötige geteilte Konten
Need payer name	Benötige Name des Zahlers
Split bill in a half	Rechnung in gleiche Teile splitten
Reset numbers	Nummern auf Null gestellt
End-Of-Day exported succesful	End-of-Day Export erfolgreich
The table is already opened.	Tisch ist bereits geöffnet.
Should the original waiter stay responsible?	Sollte ursprünglicher Kellner als der grundsätzliche behalten werden?
Goto order	Gehe zu Bestellung
Need undo all articles	Alle Artikel aktivieren
Need discount on article	Alle Artikel deaktivieren
Need kitchen screen	Küchenmonitor?
Apply fix discount to article	Fester Rabatt dem Artikel geben
Send discount reason	Zeige Rabattgrund
Need move table	Tische bewegen!
Reset tables postion	Setze Tischpositionen zurück
Need undo discount on article	Rabattstufe auf dem Artikel
Print predefined report	Drucken Sie vorher bestimmten Bericht
Need chair number	Benötige Stuhl Nummer
Need change sales area price	Benötige Verkausbereich Preis
PLU number confirm	PLU Nummer bestätigen
Go to next chair	Gehe nächsten Stuhl
Go to previous chair	Gehe vorheriver Stuhl
Need Beconet Control screen	Benötige Schankkontrolle Monitor
Beconet action	Schankanlgen Aktion
Need locker number	Benötige Schrank Nummer
Transfer to locker	Verschiene zum Schrank
Need book on client	Benötige Buchung auf Kunden
Need client bookings	Benötige Kundenbuchungen
Need table/drive-through bookings	Benötige Tisch/Durchläufer Buchungen
Need In/out cash	Benötige Ein-/Auszahlung
Need order in future	Benötige Zukunftsbestellung
Need order in future list	Benötige Zukunftsbestellungen Liste
Close order in future	Schliesse Zukunftsbestellung
Open order in future	Öffne Zukunftsbestellung
Delete order in future	Lösche Zukunftsbestellung
Go to order in future	Gehe zu Zukunftsbestellung
Search order in future using calendar	Suchen Sie Bestellung im zukünftigen Kalender
Need chair caption	Benötige Stuhlüberschrift
BarCode confirm	Barcode bestätigen
Need cash draw amount	Benötige Bargeld Betrag
Need change order in future date	Bitte Datum für Zukunftsbestellung ändern
Show table history	Zeige Tisch Historie
Is current order empty	Ist gegenwärtige Bestellung leer
Switch primary/secondary group VAT	Wechsel zwischen Vorängige/Untergeordnete MwSt Gruppe
Go to table part	Gehe zu Tisch Teil
Unlock proforma	Zwischenrechnung aufheben
Disable/Enable printing	De-/Aktiviere Ausdruck
Print table history	Drucke Tisch Historie
Show transaction structure	Zeige Transaktionsverlauf
Re-open bill and open table	Rechnung und Tisch wieder öffnen
Send void reason	Sende Stornogrund
Send note	Sende Notiz
Repeat last order	ZUrück zur letzten Bestellung
Split bill in equal parts	Teilrechnung zu gleichen Teilen
Pay all equal parts	Zahlt alle gleiche Teile
Pay payment part	Bezahlungs Teilzahlung
Need book table/Drivethru	Benötige Buchung Tisch/Durchläufer
Make proforma for all chairs	Mache alle Stuhl Zwischenrechnungen
Make proforma for all parts	Mache alle Teil Zwischenrechnungen
Change order description	Wechsel der Bestellbeschreibung
Is order description empty	Ist die Auftragsbeschreibung leer
The rent is overdue!	
Should customer pay extra?	
The rent is overdue. Should customer pay extra?	
Do you want to reset course to	
Please verify that the customer was born on or before	
Please enter customer's date of birth	
This is new card do you want to create?	
KS: Restart course preparation	
Switch single order vat level	
Switch voucher type	
Get whozz calling number	
Test KS ready	
Multi select order list items	
Whole day period	
Set take away whole day period	
Need take away deliverer	
Need take away order	
Need take away bookings	
Need take away time slot	
Need Take away transfer orders	
Cancel payments by current transaction	
Cancel selected article	
Cashdro extra function	
Change article period	
Change beverage auto-order mode	
Rearrange bar tables	
Need change article price	
Need change card table limit	
Need change course in KS	
Need PLU number	
Need change order type	
Need prolong voucher valid period	
Need rearrange KS tiles	
Print complete meal	
Previous time slot	
Print KS order	
Bill Extra Field %d	
Need edit PSP tips	
Need search article	
Need search PO	
Need select KS order	
Take Away order	
Need free allergen	
Test add dynamic discount	
Test undo dynamic discounts	
Need enable/disable articles	
Need free discount reason	
Apply discount group	
Need voucher screen	
Select table part	
Print Stock balance report	
Print physical count sheet	
Stock: Close operation day	
Need consolidate KS	
Create New TTG Card	
Need bar tables	
Option quantity	
Sort open tables	
Create coupon	
Current option number	
Set ingredient head filter	
Next time slot	
Set amount	
Set client shared account	
Set card table filter	
Need mark invoice as paid	
Need print coupon	
Undo last option	
Send reservation status	
Filter reservation list	
Need reset void limit	
KS: End article countdown	
KS: End item countdown	
KS: End course countdown	
Need coupons	
Need voucher value	
Modify supplier info	
EFT custom command	
Email invoice	
Email predefined report	
Add new user	
Authorize in Newyse by room number	
Authorize in Newyse	
Fast lane Bill ready	
Fast lane item ready	
Finish KS handling	
Finish KS course	
Need stock balance	
Need stock physical counting	
Need stock invoice	
Need stock purchase order	
Need PO stock invoice	
Need stock PO overview	
Need stock purchase order advise	
Need stock transfer	
Switch between ingredient and recipe	
Stock: Auto-adjust location	
Stock: Auto-adjust all items	
Stock: Change ingredient on hand amount	
Stock: Transfer ingredients from Location	
Stock: Auto-adjust item	
Make transaction copy	
Hide historical order	
Need transfer my tables to other waiter	
Transfer to client booking	
Put item to fast lane	
Stock: Switch ingredient list mode	
Show articles for exchange to pre-paid	
Show daily stocked articles	
Show fast lane	
Show historical order	
Show KS order history	
Show not daily stocked articles	
Show client order history	
Show pre-paid articles	
Need journal manager	
Choose payment currency	
Move KS item to next step	
KS: Start article countdown	
KS: Start item countdown	
Move KS course to next step	
KS: Start course countdown	
Start pending item in KS	
Start pending course in KS	
Stock: Add physical amount	
Stock: Create re-order PO	
Need stock document comments	
Need stock document reference	
Stock: Need search ingredient	
Stock: recieve items	
Need ingredient message	
Need ingredient price	
Stock: return items	
KS: Stop article countdown	
KS: Stop item countdown	
KS: Stop course countdown	
Stop auto-scroll all tables	
Need set Bill language	
Need number of covers(obsolete)	
Reset sales area	
Need users	
Need kitchen screen Pro	
Need active table details	
Need Pickup screen	
Need KS deliver items	
Need KS start preparation	
Need KS transfer by articles	
Undo all discounts on articles	
Need service charge plan	
Need suggest combo/promo	
Need new voucher value	
Enter number of covers	
Switch block/unblock coupon	
Need block/unblock coupon	
Set rent items state filter	
Bill take away	
Need modify menu item	
Need transfer booking to table	
Stock: Need ingredient general info	
Stock: Need ingredient locations	
Stock: Need ingredient suppliers	
Need article daily balance	
Need delete card tables	
Delete Smart card from blacklist	
Go to list page	
Search order in future using calendar Ex	
Need card table bookings	
Need Card table control	
Need Card table overview	
Need replace card table	
Need cash declaration	
Need enter cash tips	
Clear service charge plan	
Change fast lane sort type	
Select client by name	
Has bill promo	
Is bill paid	
Is bill printer disabled	
Is customer amount non zero	
Is Tap2Order Bill	
Is client On Invoice	
Switch coupons type	
Switch article rearrange mode	
Stock: Switch stock balance U/M	
Set Primary group VAT level	
Set Secondary group VAT level	
Switch Kitchen screen mode	
Newyse Guests Overview	
Stock: Update normal amount	
Use voucher remaining value	
Enable/Disable KS Pickup screen	
Enable/Disable KS Pickup screen (non separate)	
Skip Order In Future	
Clear parts covers	
Input Card	
Increase amount	
Request Payment by Code	
Re-send invoice	
Re-print last EFT receipt	
Show table info	
Activate all paid OIF	
Close all open tables	
Need pay by client account	
Clear menu item filter	
Set menu item filter	
Need pay vouchers	
Personal Log	
Put bill out of on Hold	
Put Smart card to blacklist	
Need rent pick-up	
Need redirect Kitchen screen	
Need re-enter physical amount	
Return from rent all barcodes	
Need rent	
Need rent repair	
Need return from rent	
Need rent prolong	
Need repair/return rent item	
Reset article order	
Reset current period	
Close fast lane	
Reset group VAT level	
Apply total discount(Obsolete, use "Apply Discount on Total")	
Deposit on Smart Card	
Turn Off auto service charge	
Ignore bonus price	
Need Smart Card Info	
Is waiter selected	
Complete all KS workflows	
Complete course	
Print course	
Send non separate courses to pickup	
KS return from delivered	
KS return from pickup	
KS return to pickup	
KS show delivered orders	
Switch Pickup bill mode	
Switch between KS ready and KS all	
Need change date range	
Need overdue rents	
Need check barcode status	
Need change status period	
Need change status quantity	
Need replace rent barcode	
Has order discounts	
Has order rental articles	
Is order name empty	
Is current balance equal to zero	
Need multi voucher	
Put article on hold	
Need filter Bills by client name	
Need filter by table number	
Need find rent client	
Check free rent items	
Create free purchase order	
Need order in future Ex	
Need articles out of on hold	
Need On Hold viewer	
Has order articles on hold	
Need show rent status	
Need slide table caption	
Increase article quantity	
Decrease article quantity	
Return all barcodes in rental action	
Return to OIF state	
Modify client card	
Professional kitchen screen	
Take away kitchen screen	
Show General Manager	
Take away bookings	
Put all articles out of hold	
Need article price	
Need article rearrangement	
Need articles Sales U/M	
Switch auto-scroll table caption	
Apply discount on total	
Need adjust vouchers values	
Need allergen info	
Need all suppliers	
Put all articles on hold	
Has order condition articles	
Has order condition articles(Only new items)	
Client selected in current order	
Need scan coupon	
Get room details	
Show last proforma QR code	
Load activated OIF	
Change schedule periods quantity	
Change stock purchase order	
Change schedule day of week	
Change Order in future schedule	
Change periodicity	
Change kitchen screen language	
Change kitchen screen sort type	
Undo last condiment/supplement	
Undo "on hold" single article	
Undo "on hold" articles	
Need edit articles	
Need edit ingredients	
Edit reservation client contact	
Edit reservation client info	
Edit reservation note	
Need enter customer amount	
Need pre-auth	
Confirm selection for bill	
Need accept transfer waiter	
Need balance weight	
Reset tip	
Refresh Labor/Cost coefficient	
Duplicate user	
Raise daily stock	
Print order	
Print order label	
Decrease amount	
Decrease daily stock	
Set daily stock	
Preparation	
Information	
Distribution	
Any purpose	
Change client date	
Change close bill type	
Change client card info	
Set client search field	
$FORMNAME	RestaurantSettingFram
Save settings	Speicher Einstellungen
Refresh settings	Einstellungen Aktualisieren
Transfer ticket layout	Übertrage Ticket Layout
Next course ticket layout	Nächster Gang Layout
Font	Schriftart
Color	Farbe
Size	Grösse
Format	Format
PC	PC
Mobile	Handy
Reservation font	Reservierungs Schriftart
Used color	Belegt Schriftart
Used border	Belegt Rand
Proforma color	Zwischenrechnung Farbe
Proforma border	Zwischenrechnung Rand
Slow color	Farbe Kein Service
Slow border	Rand kein Service
Slow interval, seconds	Kein Service Interval, Sekunden
Time	Zeit
Next order	Nächste Bestellung
Next bill	Nächste Rechnung
Next transaction	Nächste Transaktion
Print when transfer	Druck bei Übertragung
Z-Report	Z-Bericht
Numbers	Nummer
Tables	Tische
Tickets	Tickets
Bar balance	Bar Saldo
Watch bar balance in POS	Beobachte Bar Saldo in POS
Critical balance	Kritischer Saldo
Check interval, min	Rechnungs Intervall, min
Proforma	Zw-Rechnung
Stay in pro forma status	Keine Zw-Rechnung
Invoices	Rechnungen
Invoice number	Rechnung Nummer
Reservation	Reservierung
Minutes till start reservation	Abreiss Interval, min
Hotel Concepts End-Of-Day procedure	HotelConcepts Tagesabschluss Procedure
Page	Seite
Server one not available. Inital numbers not updated.	Server eins nicht verfügbar. Inital Nummer nicht aktuell.
Dirty color	Farbe Schmutzig
Dirty border	Rand Schmutzig
Server one not available. Inital numbers not updated	Server ein nicht verfügbar. Inital Nummer nicht aktuell
Purchase order ticket layout	Wareneinakuf Beleg Layout
Invoice ticket layout	Rechnungs Ticket Layout
Perform stock calculations	Lager Kalkulation durchführen
Re-open bill	Rechnung öffnen
days ago	Vorherige Tage
Allow reopen old bills	Erlaubt Rechnungen zu öffnen
Stock control	Lager Kontrolle
Allow reopen old bills for	Erlaube Rechnung zu öffnen für
central stock location	Zentrallager
Discount payment type	Rabatt-Zahlungstyp
Check Promo after order confirmation	Überprüfen Sie Promo, nach Bestellbestätigung
Max transaction number	Max Transaktionsnummer
Vat processing	MwSt Verarbeitung
Price includes V.A.T.	Preis inkl. MwSt
Price excludes V.A.T.	Preis ohne MwSt
Print timed articles on preparation ticket if is starts within	Drucke Zeitartikel, wenn Anfänge innerhalb
minutes	Minuten
Calculate gross total	Berechnen Brutto Summe
Need void/return reason	Will Storno/Rückgabe Grund
Round period to lower bound	Runde Zeitraum auf untere Grenze
Round period to upper bound	Runde Zeitraum auf obere Grenze
Time articles	Zeit Artikel
Order in future	Zukunftsbestellung
Print when order is confirmed	Drucke wenn Bestellung bestätigt ist
Print when order is activated	Drucke wenn Bestellung aktiviert ist
Init counting sheet	Ini. Inventurzählbeleg
Book on client	Buche auf Kunden
Table range for 'Book on client' feature	Tischereich für "Kundenbuchung" in Zukunft
to	zu
From	von
Save points discount reason	Rabatt Bonuspunkte Grund
Client has to earn this number of save points before save points can be used	Kunde muss die Anzahl der Bonuspunkte erst Sammel, bervor er sie verwenden kann
Work with save points	Arbeiten mit Bonuspunkte
Discount per bill	Rabatt pro Rechnung
Keep original waiter as responsible after transfer	Behalten Sie ursprünglichen Kellner als verantwortlich nach der Übertragung
Count sheet layout	Inventurzählbeleg Layout
Invoice includes payments of all types	Rechnung inkl. aller Zahlungsarten
Default invoice stock location	Standard Rechnung Lagerort
Direct Sales mode table	Tischnummer für Dierektverkauf ohne Tischnummer
Per PC	Pro Pc
Per waiter	Pro Kellner
Physical count report layout	Zählungsbericht Layout
Beverage Control	Schankanlage
Statistics refresh interval	Statistik setzt interval zurück
seconds	Sekunden
Print order when transfer to locker	Drucke Bestellung Übertrag zu Schrank
Table info	
Info lines	
Auto Order	
On waiter direct sales table	
On certain table	
Table Nr	
Table range for groups	
Table range for clients	
Nr	
Delay time frame for separate course start	
save point	
Separate courses	
Non separate courses	
Predefined	
Defined by unTill	
Manual	
Days	
Weeks	
Months	
Years	
Article barcode	
Bizerba	
* Feature is not licensed	
Last "till" time	
Last EOD execution time	
from 1 (transparent) till 255 (black)	
Brightness unselected orders	
Allergen image	
Load image...	
Split items by chairs	
Keep hotel guest after partial "Room" payment	
Voucher bill number	
Show options quantity	
Auto confirm default options	
Play sound on first Order	
Play sound on start Pending Order	
Hide options color	
Cross out pickup items in KS	
Cross out finished items	
No limit	
Limit	
Tips name	
Service charge name	
Color for cards not linked to a real table	
Color for cards linked to a real table	
Order only if Card is provided	
Keep menu items open after Combo/Promo confirmation	
Voucher number range	
Use common delay time frame	
Use article delay time frame	
Default number of Pickup lists	
Card table overview tiles number	
Pick up rented article on ordering	
Ask guest name on rent start	
Inactive Promo color	
Active Promo color	
Suggest replace articles with Combo/Promo during ordering	
Hide in POS	
Show in POS in alternate font	
Not active articles	
Active color timeslot(calculation KS 1 timeslot upfront)	
Second color timeslot(calculation KS 2 timeslots upfront)	
Kitchen screen take away settings	
Ordering type	
First create order	
First add client	
Free ordering	
Enable override orders	
Show Start && End time in Time slot captions	
Max nr. of orders per timeslot	
Table range for take away orders	
Period definition to be used	
Time slot nr. of minutes	
Don't allow Z-report generation until all users are clocked out	
Number of delivered orders	
Choose only clocked in waiters when transfer	
Allow negative ordering	
Use waiter's direct sales area	
Show question to use save points when minimum quantity is reached	
Maximum number of save points per transaction	
Use Payment Station barcode	
Email PO to supplier after creating	
Print PO after creating	
Set article color per department in overview screen	
Keep proforma state after reopening	
Waiter can reset course	
Keep selected department in Direct Sales	
Send message when article is not started after	
Send message when article was finished for more than	
Force popup Allergen info	
On Hold viewer tiles number	
Max history pages	
Don't show articles with zero prices during split by articles	
Combo/Promo ordering	
Check Promo by chairs	
Check Promo by transaction	
Article message length	
characters	
Consolidate courses	
Voucher valid period	
Voucher period type	
Don't charge save points on save point  payment	
Bill email subject	
Show items in orders with course color	
Don't wait KS finish to fire Next course	
Don't re-calculate price on Payment, when client changes	
Notification type	
By article	
By preparation/table area	
Take away color	
Barcode syntax	
General Manager timeout	
Ask if client wants bill by email	
Voucher department	
Vouchers	
Voucher type	
Block fire course at start	
Ignore manual SA price level on exceptions	
File	
Play	
Second slow interval, seconds	
Second slow color	
Second slow border	
Don't allow print Z-report when open tables exist	
Play sound on new Order	
Play sound on Next Course	
Play sound on added Order	
Play sound on new Fast Lane Order	
Play sound on Order Transfer	
Need specify OIF table	
Arrangement of article positions like	
Disallow make OIF in holidays	
Table range for Bar tables	
Max. number of active tables	
Age control	
Simple confirmation	
Need enter birth date	
Information color	
Distribution color	
Use pickup screen	
Don't use pickup screen	
New in Fast Lane	
Show fast lane for	
Active	
Not active	
Show fast lane every	
Simple Kitchen Screen	
Professional Kitchen Screen	
Allow to modify prices in stock invoice	
Calculate VAT on Service Charge	
Detail kitchen screen	
Overview kitchen screen	
Show message when course changes	
Don't allow clock out until all PSP tips are assigned	
Table Info presentation time:	
Blinked sign	
Additional info on article button	
Stock balance	
Daily stock balance	
Allow add options to confirmed articles	
Remove exceeding article from order	
No ordering unless a client is selected	
Only allow one-by-one menu ordering	
Ordering	
Disable auto confirm menu	
Check acccount balance when ordering	
Don't allow change client on Payment	
Check on expiration date on payment	
Client cards	
Pre-paid articles	
Exchange to p/p void reason	
On hold border	
Bar table name required	
Auto print on hold orders	
Ready pickup color	
Article color with allergen	
Accumulation mode	
Card field maximum length	
Client card deposit limit	
Automatic balance stock invoice	
Save Points	
Keep waiter responsable when Void/Transfer	
Apply report user filters to original waiter	
Extra daily stock fields	
Free text field A	
Free text field B	
Daily deadline time	
Table range for overdue rentals	
Rental	
Start using save points from	
Save point calculation	
Minimal save points required for discount	
When articles in an order are sent to different kitchen screens, only send to pickup when all are done	
Send to Pickup when overall done	
Send to Pickup single item	
Spend mode	
Discount per article manual	
Discount per article automatic	
Save point cost	
By bill total	
Next number	
Last Z-Report date	
Next proforma	
Bill waiter - same as Proforma waiter	
Transfer	
Transfer waiter needs acceptation	
Next period start date/time	
Print order when return	
Term client card validity	
In/Out Cash	
Bookkeeping account	
OBS account	
Tap2Order	
Automation user	
Disabled	
By Penko number	
By Smartcard UID	
Auto open table	
Always order to group for group members	
Kitchen screen	
Preparation status	
Not started	
Waiting	
Started	
Close to complete	
Complete	
Awaiting for pick up	
Serve food simultanously	
Detailed order presentation time	
Finished and Picked up	
Print preparation tickets	
Purchase order ticket	
Invoice ticket	
Payments	
EOD Period "from" time	
Table range for card orders	
Send bookkeeping instead of payment name to BillHandlers for EFT payments, when possible	
WAV files	
$FORMNAME	RestaurantTasksU
Clear sales	Data Clear
Export bookkeeping data	Export Buchführungsdaten
Clear accounts history	Lösche Konten/Kunde History
Import clients	Import Kunden
Change table area status	Tischbereichs Status ändern
Z-Report has not been done yet for selected date	Z-Bericht ist noch für das ausgewählte Datum gezogen worden
Clear Beverage Control balance	Lösche Schankkontrolle Vergleich
Clear Beverage Control turnover	Lösche Schankkontrolle Absatz
Clear save points	Lösche Bonuspunkte
Clear stock	lösche Lager
Clear in/out cash	Lösche Ein-/Auszahlung
Clear report numbers	Lösche Berichtsnummern
Import Smart Cuisine Data	
Clear beverage credit queue	
Close all Tap2Order groups	
Import Tap2Order Clients	
Syncronize Tap2Order Client Cards	
Import Clients From CSV	
End of day	
Clear all vouchers	
Email stock close day	
All tables must be closed before running this task	
Reset sales data	
Close all open tables	
Aggregates recalculation	
Aggregates removal	
$FORMNAME	RestaurantTreeFoldersU
Restaurant	Restaurant
Area	Bereiche
Person	Personen
Product	Produkte
Restaurant settings	Restaurant Einstellungen
Beverage Control	Schankkontrolle
Coupons	
$FORMNAME	RestaurantViewU
Restaurant settings	Restaurant Einstellungen
$FORMNAME	SalesAreaEntityManager
New Sales Area	Neuer Verkaufsbereich
Sales Area	Verkaufsbereiche
Manual	Manuell
Price	Preis
Exceptions	Ausnahmen
&1 Exceptions	&1 Ausnahmen
&2 Table Range	&2 Tisch Bereich
Range	Bereich
Close tables manually	Schliesse Tische manuell
Auto-accept reservations	Auto- Akzeptierung Reservierungen
Open only reserved tables	Öffne nur reservierte Tische
If this flag is checked, coming reservations are automatically accepted when the table is opened	Wenn dieses Feld markiert ist, kommende Reservierungen werden automatisch akzeptiert wenn Tisch geöffnet ist
Group VAT level	MwSt Ebenen Gruppe
Primary	Vorängig
Secondary	Untergeordnet
(not licensed)	
Standard %	
Service charge plan	
Automatically add Service Charge	
Only if number of covers is over	
No service charge	
$FORMNAME	SalesAreaExceptEmbManager
New Exception	Neue Ausnahme
Exception	Ausnahme
Period	Periode
Price	Preis
$FORMNAME	SalesRangeEmbEntityManager
From	Von
To	Bis
Sales area: Table range	Verkaufsbereich: Tischbereich
New sales area range	Neuer Verkaufsbereich Von-Bis
Sales area range	Verkaufbereich Von-Bis
$FORMNAME	SelectArticleParamsFram
Article	Artikel
$FORMNAME	SelectDepartmentParamsFram
Please select article	Bitte Artikel wählen
Department	Artikelgruppe
Please select department	Bitte Artikelgruppe wählen
Normal article	
Combo/Promo article	
Keep last articles	
$FORMNAME	SpecialWordsEntityManager
Special words	Spezial Wörter
Key name	Funktions Name
Internal	Bondruck
PC text	PC Text
RM text	Handy Text
Number	
$FORMNAME	SpecialWordsEmbEntityManager
New special word	Neues Spezial Wort
Special word	Spezial Wort
Key	Funktion
Name	Name
Internal	Bondruck
PC Text	PC Text
RM Text	Handy Text
$FORMNAME	SupplierContactsEmbManager
Position	
Phone	Telefon
Mobile	
Fax	
Email	
Contact Name	Kontakt Name
Supplier: Contacts	Lieferant: Kontakte
New Contact	Neuer Kontakt
Contact	Kontakt
Contacts	Kontakte
E-Mail	
$FORMNAME	SupplierEntityManager
New Supplier	Neuer Lieferant
Supplier	Lieferant
&1 General	&1 Allgemein
&2 Contacts	&2 Kontakte
Address Information	Adressen Info
Country	Ort
Address	Adresse
PC Text Options	PC Text Optionen
PC Text	PC texte
PC Font	PC Schriftart
Change &Font	Schrift wechseln
Phone	Telefon
E-Mail	
Fax	
Website	
Contacts	Kontakte
Suppliers	Lieferant
Supplier number	Lieferanten Nummer
Allow back order	Erlauben Sie rückgabe der Bestellung
Bookkeeping	Buchhaltung
Account Number	Konto-Nummer
Customer nr.	
Min. order amount	
Delivery day of week	
Order day of week	
PO reference template	
Invoice reference template	
Daily	
$FORMNAME	TableAreaEntityManager
New table area	Neuer Tischbereich
Table area	Tischbereich
Bill printer	Rechnungsdrucker
Bill layout	Rechnungslayout
mobile	Handy
Range	Bereich
Manual	Manuell
Active on start	Ativ bei Start
Period	Periode
&1 Range	&1 Bereich
Proforma printer	Zw-Rechnungsdrucker
Proforma layout	Zw-Rechnungs Layout
Number of covers	Gästeanzahl
Table number	Tischnummer
Bill Name	Beleg Name
Client Name	Kunden Name
Waiter who initially opened the table	Kellner, der am Anfang den Tisch öffnete
Time when table was initially opened	Zeit, der Tischeröffnung
Waiter who took the last order	Kellner der letzten Bestellung
Time when the last order was taken	Zeit der letzten Bestellung
Time when "Next course" has been asked	Zeit, wann der "nächste Gang" abgefragt wurde
Deposit printer	Ablagendrucker
Deposit layout	Ablagenlayout
Additional info to show on table	Füge Info auf Tisch hinzu
Order printers/Stock locations	Bestelldrucker/Lagerbereich
Limited (cannot be used together with Beconet location assigned to table area)	Beschränkt (Kann nicht zusammen mit der Schankbereich verwendet werden, die damit beauftragt ist, Lokal auf den Tisch zu legen)
Beverage Control	Schankkontrolle
Location	Bereich
Avaliable fields	Bereichsvorrat
Assigned fields	Bereichsvorrat
"Beverage Control Location will be discarded because it is incompatible with "Limited" flag. Continue?	"Getränk-Kontrollposition wird verworfen, weil es mit "der Beschränkten" Einstellung unvereinbar ist. Setzen Sie fort?
"Limited" flag will be turned off because it is incompatible with "Location". Continue?	"Beschränkte" Einstellung wird abgeschaltet, weil es mit "dem Bereich" inkompatibel ist. Fortsetzten?
Clear font	Lösche Schrift
Click this button to use font settings defined in screen designer	Klicken Sie auf diesen Knopf, um im Bildschirm-Designer definierte Schriftart-Einstellungen zu verwenden
Current course	
Tap2 group name	
Client phone	
Room number	
Course info	
Payment status	
Use default number of covers	
Allow zero covers	
No ordering unless a client is selected	
Use general setting	
Need client for table area	
No client is needed for table area	
Assign to specific devices	
&2 Print route/Stock locations	
&3 KS route	
&4 Options / Beverage Control	
&5 Devices	
Force entering number of covers(obsolete)	
Enter number of covers when table opens	
Enter table name when table opens	
Computers	
Pick up device	
Table Info	
PC font	
HHT font	
Up	
Down	
Remote hand terminals	
Ordermans	
$FORMNAME	TableNoFram
Table number	Tisch Nummer
$FORMNAME	TableOptionFram
Polygone	
Ellipse	
Rounded rectangle	Gerundetes Rechteck
Default table type	Voreingesteller Tisch Typ
$FORMNAME	TablePrintEmbEntityManager
Table area: Order printers	Tischbereich: Bondrucker
Preparation area	Produktionbon
Order printer	Bondrucker
Order layout	Bon Layout
Beep	Piep
Print	Druck
Preparation	Zubereitung
Language	Sprache
times	Zeiten
Stock location	Lagerort
New table area preparation route	Neuer Tischbereichs Vorbereitungsweg
Table area preparation route	Tischbereichs Vorbereitungsweg
Kitchen screen source	
Kitchen screen destination	
New table area KS route	
Table area KS route	
Exception period	
Disable kitchen screen	
$FORMNAME	TablePropFram
Table type	Tisch Typ
Curvature width of a round corner	Breite der Kurve einer runden Ecke
Curvature height of a round corner	Höhe der Kurve einer runden Ecke
Table Number	Tisch Nummer
Security	Sicherheit
Table	Tisch
Curvature radius of a round corner	Radius der Kurve einer runden Ecke
Number of covers	
$FORMNAME	UnityEntityManager
New Unity	Neue Einheit
Unity	Einheit
Unities	Einheiten
Purchasing	Einkauf
Counting	Zähler
Usage	Verbrauch
Conversion	Umwandlung
This unity conversion cannot be deleted	
This unity is used in purchase conversion rules and cannot be deleted	
This unity cannot be deleted	
$FORMNAME	UntillMainFiguresU
Table	Tisch
Data Meter	
$FORMNAME	UntilMainFiguresU
Table	Tisch
$FORMNAME	UserEntityExtender
Order	Bestellung
Close	Geschlossen
Tables	Tisch
Table areas	Tischbereich
Drawer	Schublade
Include table areas	Inclusiv Tischbereich
All	Alle
Own	Eigene
First	Erster
Second	Zweiter
&3 Order	&3 Bestellung
&4 Close	&4 Bezahlung
&5 Tables	&5 Tische
&6 Commission	&6 Provision
Own color	Eigene Farbe
Color settings	Farbeinstellungen
Used color	Farbe Belegt
Used border	Rand Belegt
Slow color	Farbe Kein Service
Slow border	Rand Kein Service
Proforma color	Farbe Zw-Rechnung
Proforma border	Farbe Zw-Rechnung
Calculation	Kalkulation
Table area	Tischbereich
Dirty color	Farbe schmutzig
Dirty border	Rand schmutzig
User with the same Beverage Control Number already exists	Benutzer mit der gleichen Schankanlagen Nummer existiert bereiits
&7 Beverage Control	&7 Schank Kontrolle
Direct Sales table number	Direktverkauf Tischnummer
Beverage Control	
Common drawer	
Check OIF on print Z-Report	
None	
Regular user	
Delivery user	
Take away role	
Don't allow clock out when there are open tables	
Don't print waiter report when there are open tables	
Don't show waiter in Waiter Orders dataset	
Allow waiter to transfer excluded articles	
Second slow color	
Second slow border	
Waiter ID	
$FORMNAME	WaiterEntityManager
Calculation	Kalkulation
$FORMNAME	VanDuijnenTicketDataSetParamsFram
Articles only	Nur Artikel
Groups only	Nur Gruppen
Both articles and groups	Beides Artikel und Gruppen
$FORMNAME	VDGroupsEntityManager
New Beverage Control group	Neue Schankanlagengruppe
Beverage Control group	Schankanlagengruppe
Beverage Control groups	Schankanlagengruppen
$FORMNAME	WaiterCalcEmbEntityManager
Commission	Provision
Calculation	Kalkulation
Waiter: Commission rate	Kellner: Service Rate
New waiter commission rate	Neuer Kellner Service Rate
Waiter commission rate	Kellner Service Rate
$FORMNAME	ZReportParamsFram
Layout	
No print mode (supported by some fiscal printers)	
Printer	
Report type	
Custom Printer	
Z-report	
X-report	
Sequential Sales area report	
$FORMNAME	AccountDepositsParamsFram
All items	Alle Artikel
Items with discounts only	Nur Artikel mit Rabatt
Items without discounts only	Nur Artikel ohne Rabatt
Discount parameter	
Deposit type	
Paid invoices	
Client deposits	
All vouchers	
Active vouchers	
Used vouchers	
Report shows all active items in DB (date range is not taken into account)	
$FORMNAME	AccountManager
New account	Neues Konto
Account	Konto
Name	Name
Number	Nummer
On invoice	Auf Rechnung
Last invoice	Letzte Rechnung
Change	Wechselgeld
Limit	Limit
Save points	Sparpunkte
Save amount	Spar Betrag
Shared accounts	Gemeinsame Konten
Address	Adresse
Shared account	Gemeinsames Konto
Invoice description	
Payment part	
General	
Linked clients	
Discounts	
Client, linked to this shared account	
Info	
with	
E-Mail	
Invoice e-mail(s)	
Partial payment	
Percentage amount	
Absolute amount	
General shared account	
Keep individual accounts	
$FORMNAME	ArticleFilterFram
Department	Atikelgruppe
Find	Finde
Category	
Show not linked to any sales area	
$FORMNAME	ClientFilterFram
Name	Name
Card	Karte
Filter	Filter
Number	Nummer
$FORMNAME	ClientStoredFram
Select caption fields	Wähle Beschreibungsfeld
Sort by:	
$FORMNAME	ExportDataTaskParamsParamsFram
Custom TILL: %s	Kunden bis: %s
Date	Datum
Time	Zeit
Use custom TILL date/time	Benutze Kunden bis Datum/Zeit
Custom FROM: %s	
Use custom FROM date/time	
$FORMNAME	NeedPredefinedDiscountFram
In percents	In Prozent
Fixed	Fest
Reason	Grund
Value	Wert
All	Alle
On department	Auf Artikelgruppe
On course	Auf Gang
Applied to all articles (order is saved)	Angewandt auf alle Artikel (wird mit Bestellung Bonuspunkte erteilt)
Applied to selected article	Angewandt auf den ausgewählten Artikel
Don't apply discount to	
price exceptions in period	
On group	
On category	
Free reason	
Fixed text	
Ask reason	
$FORMNAME	TNeedPredefinedDiscountFrame
Discount reason	Rabatt Grund
Department	Artikelgruppe
Course	Gang
$FORMNAME	NeedReturnFram
If specially allowed	Wenn speziell erlaubt
$FORMNAME	PaymentsTicketDataSetParamsFram
Cash	Bargeld
Card	Karte
Account	Konto
Room	Zimmer
Cheque	Scheck
Hash	Auslage
Only payments by selected kind	Nur Bezahlungsarten vom vorgewählten Mode
All payments	Alle Zahlungsarten
Payment kind	Zahlungsarten
Show only last payment	Zeige nur letzte Zahlungen
Service charge	
Discount	
Smart card	
Cashdro	
Newyse	
Gift card	
Prepaid card	
Voucher	
$FORMNAME	KernelDataSetsU
Waiter tables	Kellner Tische
List of tables belonging to waiter	Lister der Tische des Kellners
Payments info	Bezahl Info
List of payments related to bill	Liste der Bezahlungen der Rechnungen
Payment parts info	Zahlungsarten Info
List of payments related to payment part	Liste von mit dem Zahlungsart verbundenen Zahlungen
Restaurant message	Restaurant Nachricht
Error message	Fehler Nachricht
Database payments	
List of payments in database	
Database categories	
List of categories in database	
Database void reasons	
List of void reasons in database	
Database discount reasons	
List of discount reasons in database	
Last bill payments	
List of last bill payments	
KS routing courses	
List of chosen courses for KS routing	
Transaction details	
Vouchers	
Vouchers to pay	
Take away transferred items	
List of Take away bills to be transferred	
Take away articles in time range	
List of Take away articles in time range	
Consolidate Take away articles	
Take away articles in time range, consolidated by time range	
Take away orders	
List of Take away orders	
Adjusting vouchers	
List of vouchers to adjust values	
Open OIF number	
Table is busy message	
User name	
PC name	
HHT name	
Take away time slot	
Take away deliverer	
Take away?	
Next bill/proforma language	
Card tables total bill amount	
Card tables quantity	
Temporary message	
Number of Take away orders	
Number of Take away articles	
Document Signature	
Take away time block	
Current coupon filter	
Transfer order name To	
Transfer order name From	
Auto-order mode	
Manager report waiter	
Kitchen screen type	
Transfer chair to	
Customer birthday	
Current article filter	
Chosen course	
Kitchen screen route rule	
Table Info progress	
Current PO date/time filter	
Current PO filter	
Current item filter	
Additional ingredient info	
New ingredient price	
$FORMNAME	WaiterTableParamsFram
Transfer origin	Übertrage Original
Transfer result	Übertrage Resultat
List type	
$FORMNAME	BillOPU
Unknown operation	Unbekannte Operation
Ordered	Bestellt
Voided	Storniert
Transferred	Übertragen
Discounted	Rabatt
Returned	Zurückgegeben
Paid	Bezahlt
Reopened	Wieder geöffnet
Voided discounts	entferne Rabatte
Void article	
Void order	
Transfer	
Fixed discount	
Percentage discount	
Open discount	
Client changed	
Vat level changed	
$FORMNAME	ChangeTAStatusTaskParamsParamsFram
Make table area active	Aktiviere Tische
Make table area inactive	Inaktiviere Tische
Table area	Tischbereich
Change state to	Ändere Tisch Status
Active	Aktiv
Inactive	Inaktiv
Table area not selected	Tischbereich ist nicht selektiert
Close all open tables	
User	
$FORMNAME	PaymentEntityManager
Cash	Bargeld
Card	Karte
Account	Konto
Room	Zimmer
Cheque	Scheck
Hash	Auslage
Service charge	Service
$FORMNAME	PaymentsReportTicketDataSetParamsFram
Only service charge (SC)	Nur Service Betrag
Use real date/time	Verwenden Sie die richtige Tageszeit
Included SC/EFT tip also shown apart	
Included SC/EFT tip not shown apart	
Only EFT Tips	
Include voucher sales	
$FORMNAME	PrintZReportDlg
Layout	Layout
Printer	Drucker
Print Z-Report	Drucke Z-Bericht
From date	Von Datum
Default "Till" date and time (end of day)	Voreingestellt "Von" Datum/Zeit (Tagesende)
User defined "Till" date and time	Benutzerdefiniert "von" Datum und Zeit
Next	Nächste
Print X-Report	Drucke X-Bericht
Print per day	Druck pro Tag
$FORMNAME	ResetNumbersParamsFram
Select at least one item to reset	Wähle mindestens einen Artikel zum Zurücksetzen
Reset	Zurücksetzen
Bill numbers	Rechnungs Nummer
Order numbers	Bestell Nummer
Transaction numbers	Transaktions Nummer
$FORMNAME	RestaurantBOU
Z-Report:	Z-Bericht
Legal X was sent to bill printer	Rechtliche X wurde zum Rechnungsdrucker gesandt
Legal Z was sent to bill printer	Rechtliche Z wurde zum Rechnungsdrucker gesandt
$FORMNAME	RestaurantTestDataU
Coca-cola	
Pepsi	
Tuborg 0.5	
Sprite	
Soda	
Tea	Tee
Cola light	
Fanta	
Pepsi light	
Dr Pepper	
Fizzy drinks	Erfrischungsgetränke
Beers	Bier
Hot drinks	Heißgetränke
Without alcohol	Alkoholfrei
With alcohol	Alkoholisch
Bar	
John Smith	
Bill Gates	
Donnie Aaron	
Nicky Adriaan	
Grey Marius	
Kelcey Lucien	
Jack Damien	
Dmitri York	
400 Terhune Ave Paramus, NJ	
Seattle, WA	
Herentalsstraat 47	
Molsekiezel 193	
Bekegemstraat 37	
Cassiersstraat 19	
Cathilleweg 34A	
Naamsesteenweg 191	
Terhaegen 20	
Chaussee de Vleurgat 232	
2300 Turnhout	
3920 Lommel	
8490 Jabbeke	
2060 Antwerpen / Anvers	
3001 Leuven / Louvain	
4851 Plombieres	
1050 Ixelles / Elsene	
Visa	
Dinners	
Mastercard	
Cash	
Card	
USA	
Belgium	
(201) 599-0640	
(206) 522-4829	
014/416718	
011/546756	
050/811637	
03/2212700	
050/610132	
087/788305	
016/206578	
02/6445563	
(201) 599-0641	
<EMAIL>	
<EMAIL>	
<EMAIL>	
87189070200009070045500	
87189070200009070045501	
87189070200009070045502	
87189070200009070045503	
87189070200009070045504	
87189070200009070045505	
87189070200009070045506	
87189070200009070045507	
87189070200009070045508	
87189070200009070045509	
Account1	
Account2	
Account3	
Account4	
Account5	
Account6	
Account7	
Account8	
Account9	
Account10	
7412VA	
7415LC	
7423SB	
7433AJ	
A	
B	
F26	
F38	
DEVENTER	
DIEPENVEEN	
SCHALKHAAR	
Normal	
Kitchen	Küche
test discount reason	test Rabattgrund
Bar dispencer	
Downstairs dispencer	
Not accepted	Nicht akzeptiert
Accepted	Akzeptiert
Closed	Geschlossen
Taverne	
Terrace	
Bavaria	
Milk combination	Milchmischgetränke
Pepsico	
Santa House	
IBM	
Microsoft	
Parmalat	
Sun Interbrew	
Philips	
3409527-95-23098	
3245234-95-sv	
rewg556-45-56745	
2341234-95-56546	
2343434-rh-dbfgf	
3232435-95-45y43	
3246324-95-43456	
safe234-95-63456	
q345456-95-zdfb4	
4556768-95-sdvas	
Stock 1	Lager 1
Stock 2	Lager 2
North stock	Nord Lager
West stock	West Lager
Stock 3	Lager 3
Stock 4	Lager 4
Stock 5	Lager 5
Stock 6	Lager 6
Stock 7	Lager 7
Stock 8	Lager 8
Transfer out	Übertragung
Non retail sale	Kein Wiederverkauf
Employee consumption	Mitarbeiterverbrauch
Charity	Einladung
Promotion	Promo
Raw waste	Roh Vernichtet
Finished waste	Fertig Vernichtet
Other	anders
Invoice ********	
Adjustment ********	
Initial counting	
Adjustment 3t345	
Adjustment 3q45345	
Invoice 67656	
Vodka	
Rice	Reis
Beef	Fleisch
Pork	Schwein
Ketchup	
Tuborg	
Heineken	
Corn Oil	
Butter	
Coca_cola	
Price 1	Preis 1
Price 2	Preis 2
Price 3	Preis 3
Price 4	Preis 4
Price 5	Preis 5
Price 6	Preis 6
Price 7	Preis 7
Price 8	Preis 8
Price 9	Preis 9
Table 1	Tisch 1
Table 2	Tisch 2
Table 3	Tisch 3
Table 4	Tisch 4
Table 5	Tisch 5
Table 6	Tisch 6
Table 7	Tisch 7
Table 8	Tisch 8
Table 9	Tisch 9
Table 10	Tisch 10
Group	Gruppe
litre	
cl	
kg	
gram	
pound	Pfund
bottle	Flasche
box	
can	
lug	
pack	
Pub	Bar
Summer salad	Sommer Salat
Lasagne	
Pizza	
Cheesecake	Käsekuchen
Fried chicken wings	
Goulash	
Vinaigrette	
Roast beef	
Fish aspic	Fisch
Remi Martin	
Hennesy	
Milk	Milch
Beverage	Getränk
Meat	Fleisch
Breakfast	Früstück
Orfe	
Alcohol	
Dessert	
Soup	Suppe
Beer	Bier
Aperitif	
Kids drink	Kinder Getränke
Transfer between locations	Übertragung zwischen Positionen
Invoice 54545	
Invoice 45	
Invoice 3434	
Invoice 234	
lug divided 10 = kg	
box divided 5 = kg	
can divided 7 = kg	
box divided 20 = bottle	
liter multiplied 1 = kg	
gram multiplied 1000 = kg	
gram multiplied 330 = bottle	
cl multiplied 100 = liter	
gram multiplied 230 = each	
gram multiplied 30 = each	
Simple Par	
Periodic Par	
Coca cola	
Fish	
Steak	
Hamburger	
Stella Artois	
Apple juice	
Mocha	
Daily menu	
Special menu	
Dinner club	
Business lunch	
Meal of day	
Chef choice	
Spaghetti junior	
Chicken nuggets	
Hamburger & French Fries	
Cold Kitchen	
Hot kitchen	
Fast lane	
KS Cold kitchen 1	
KS Cold kitchen 2	
KS Hot kitchen	
KS Bar 1	
KS Bar 2	
KS Desserts	
KS Sushi	
KS Manager	
Coca	
Col	
Tub	
Tra	
ppist	
Vo	
Vod	
Vodk	
Tables take away	
Tables restaurant	
Tables terrace	
$FORMNAME	SplitBillPartFram
Bill Parts number	Rechnung Teil Nr.
$FORMNAME	ApostillRestSettingsDlg
Direct Sales mode table	Direktverkauf Tischwahl
Per PC	Pro PC
Per waiter	Pro Kellner
Apostill Restaurant Settings	Apostill Restaurant Einstellungen
Proforma	
Keep pro forma status	
$FORMNAME	ArtAndOptParamsFram
Show line for paid returned articles	Zeige Preise für Stornierte Artikel
Print option zero prices on Bill	Drucke auch 0 Preise auf Bon
Print main zero prices on Bill	
Print as HPH export	
$FORMNAME	ArticleBecoLocationsEmbEntityManager
Location	Lokal
Article: Locations	Artikel: Lokale
PLU	
Std.	
Alt.	
New location	Neues Lokal
PLU number	PLU Nummer
$FORMNAME	ArticleRentUnitEntityFram
New rental unit	Neue Mieteinheit
Rent unit	Mieteinheit
Barcode	
Comments	Kommentar
Article	
New price exception	
Price exception	
Period	
Price	
Rental price	
Rental periods number	
$FORMNAME	ArticleRentUntiEmbEntityManager
Barcode	
Comments	Kommentar
$FORMNAME	BecoGroupsEntityManager
New BeCo group	Neue BeCo Gruppe
BeCo group	BeCo Gruppe
BeCo Groups	BeCo Gruppen
$FORMNAME	BecoLocationsEntityManager
New BeCo location	Neuer BeCo Bereich
BeCo location	BeCo Bereich
From table	vom Tisch
To table	zum Tisch
BeCo Locations	BeCO Bereiche
$FORMNAME	BeconetActionParamsFram
Action	Aktion
Get size	gehe zu Größe
Set size	Setzte Größe
Get interval	gehe zu Zeitabstand
Set interval	benutze Zeitabstand
Read current article	Lesen Sie aktuelle Artikel
Read all articles	Lesen Sie alle Artikel
Set standard volume	Setze Standard Datenträger
Set alternative volume	Setze alternativen Datenträger
Enable selected article	Aktivieren Sie ausgewählte Artikel
Enable all articles	Aktivieren Sie alle Artikel
Disable selected article	Deaktivieren Sie ausgewählte Artikel
Disable all articles	Deaktivieren Sie alle Artikel
Open all BRT	Öffne alle BRT
Close all BRT	Schliesse alle BRT
Set standard to all articles	Standardeinstellung für alle Artikel
Set alternative to all articles	Alternativeinstellung für alle Artikel
Begin rinsing	Beginn Spülung
End rinsing	Beende Spülung
$FORMNAME	BeconetDatasetsU
n/a	
ok	
$FORMNAME	BLRBeconetU
Disabling article: %s	Deaktivieren Artikel: %s
off	aus
Enabling article: %s	Aktiviere Artikel: %s
on	an
Article has no alternative volume	Artikel hat keine alternative Zählung
The following articles were skipped because of no standard volume: %s	Die folgenden Artikel wurde aufgrund von keiner Standardzählung übersprungen: %s
The following articles were skipped because of no alternative volume: %s	Die folgenden Artikel wurde aufgrund von keiner Alternativzählung übersprungen: %s
Reading article: %s	Lese Artikel: %s
Setting alternative volume for article: %s	Setze alternative Zählung für Artikel: %s
Setting standard volume for article: %s	Setze standard Zählung für Artikel: %s
Disabling %s	
Enabling %s	
$FORMNAME	BLRBookClientU
Client bookings	Kundenbuchung
Table/Drive-through bookings	Tisch-/Durchläufer Buchung
Card table bookings	
Replace card in current reservation	
Set smart card limit	
Choose booking to transfer	
$FORMNAME	BLRDelayOrderU
Choose table for future order...	Wählen Sie den Tisch für künftige Bestellung ...
Choose order to activate...	Wählen Sie die Bestellung zum Aktivieren...
Choose date/time to search order in future	Wählen Sie die Tageszeit um die Bestellung zu suchen
Change order in future date/time	Ändere Tageszeit der künftigen Bestellung
The order is completely paid by %s Save points.	
The order is completely paid. Original deposit amount: %s. Remaining amount: %s	
OIF cannot be created in the past	
OIF cannot be created on Holiday.	
Void Order in future	
Change order in future schedule	
Choose the next date/time	
Choose date for Order in future	
There are open OIF warning	
$FORMNAME	BLRTableInfoU
Select table to show info	Tischauswahl für Info Anzeige
$FORMNAME	BLRVoidU
Enter number of articles to be voided	Anzahl der Artikel eingeben
Choose user for whom the void limit is reset	
$FORMNAME	ClearNumbersTaskParamsFram
This task is only meant for new installs and will clear ALL report numbers in this database including Z and X-report numbers. Are you sure ?	Diese Aufgabe ist nur für neue Installationen gedacht und wird den Berichtszähler in dieser Datenbank und Z-und X-Berichtszähler zurücksetzten. Sind Sie sicher?
By pressing "OK" you understand that all existing numbers will be set to zero and you will be responsible for any consequences of this action.	Durch Drücken von "OK" bestätigen Sie, dass alle bestehenden Nummern auf Null gesetzt werden, und Sie verantwortlich für alle Folgen dieser Aktion sind.
Software manufacturer unTill can never be held responsible for the consequences of this action.	Software Hersteller unTill kann nie verantwortlich für die Folgen dieses Handelns sein.
$FORMNAME	ComputersEntityExtender
BeCo Location	BeCo Bereich
Avaliable fields	Verfügbare Felder
Assigned fields	Zweckgebundene Felder
Additional info to show on table	Hinzufügen von Info in Tischanzeige
Fields	Felder
PC font	PC Schrift
HHT font	HHT Schrift
Additional info to show on table. Use DOWN button for adding more lines	
Default stock location	
Up	
Down	
$FORMNAME	DisablePrintParamsFram
Order	Bestellung
Bill	Beleg
$FORMNAME	FutureOrderScreenParamsFram
Select caption fields	Wähle Bezugs Felder
Sample font	
Paid OIF font	
$FORMNAME	ImpClntsFrm
Select target field	Wähle Ziel Feld
Target field	Ziel Feld
Source file not specified	Bezugsquellen Datei nicht festgelegt
Source	Bezugsquelle
XLS file	XLS Datei
Fields	Felder
From line	Von Zeile
To line	zu Zeile
Import clients	Importiere Kunden
Cancel	Abbruch
Go >>	
Added: %d records, updated: %d records	
$FORMNAME	InOutCashActionParamsFram
Action	Vorgang
Print ticket	Drucke Beleg
times	Zeiten
In cash	Einzahlung
Out cash	Auszahlung
$FORMNAME	InputNoteValueFram
Note/Coin value	Banknoten/Münzen Inhalt
$FORMNAME	InventoryItemFilterFram
Filter	
Name	
Location	
Group	
None	
Group & name	
Ingredients	
$FORMNAME	InvoicesMassModifyFram
"From" date/time is after "Till" date/time	"Von" dem Datum/Zeit ist größer als "Bis zu" dem Datum/Zeit
"Till" date/time is after current date/time	"Bis" Datum/Zeit größer ist als jetzt
$FORMNAME	KitchenScreenStoredParamsFram
Not consolidated	Nicht konsolidiert
Consolidated by article	konsolidiert mit Artikel
$FORMNAME	LastOrderParamsFram
Previous order number	Vorherige Bestellnummer
Tile number	
Article number	
$FORMNAME	MainFrm
Legal X:	gesetzmäßiger X:
Legal Z:	gesetzmäßiger Z:
Re-print Z-Report:	
Re-print sequential reports:	
Periodical-Z:	
$FORMNAME	NeedClientByNameParamsFram
Client name	Kunden Name
$FORMNAME	NeedFixDiscountFram
Transfer immediately	Wechseln Sie wirklich sofort über
Do transfer after action "Transfer to locker"	Wechseln Sie wirklich über nach der Handlung "Wechseln zum Schrank über"
Per item	Mach ein Artikel
Common discount	Allgemeiner Rabatt
$FORMNAME	OrderedArticlesParamsFram
Normal	
Ordered in future	Bestellung in Zukunft
Only bonused articles	
$FORMNAME	PayedArticlesParamsFram
All articles	Alle Artikel
Only negative quantity articles	Nur negative Artikelmenge
Only upsellers	
Show consolidate aliases	
Only main articles	
Show single Menu/Combo/Promo article	
Show Menu/Combo/Promo items	
Show Main Menu/Combo/Promo and items	
Originally ordered	
Don't show reopen history	
All articles, excluding Hash	
Only Hash articles	
Only positive quantity articles	
Included discounts on bill total	
$FORMNAME	PayzoneDataParamsFram
Succeed transaction	Verfolgen Sie Transaktion
Tip approvement	Trinkgeldnachtrag
Rejected transaction	Zurückgewiesene Transaktion
Customer copy	Kundenkopie
Merchant copy	Handelskopie
$FORMNAME	PromoEntityManager
Add articles	Artikel hinzufügen
Add existing group of choices	Fügen Sie vorhandene Gruppe von der Auswahl hinzu
New Promo	Neue Promotion
Combo-Promo article	Kombinations- Promo Artikel
Name	
Available	verfügbar
Don't suggest using the combo/promo, when items are ordered	
General	
Additional	
Alternate article	
Alternate alias	
If this article presents in an order, together with the combo, the Combo/Promo name on the bill will be replaced by Alternate alias. Prices of Combo/Promo and Alternate article will be summarized.	
Create group of choices(available for this combo/promo only)	
PC view	
HHT view	
Orderman view	
Number	
Department	
$FORMNAME	PUAArticleEmbEntityManager
Article	Artikel
Articles sequence	Artiklefolge
Name	
Number	Nummer
Upseller: Articles	
New upseller	
Upseller	
$FORMNAME	PUAAvailEmbEntityManager
Sales area	Verkausbereich
Upseller: Available	
$FORMNAME	PUAEntityManager
New PUA-Group	Neue PUA Gruppe
PUA	
Name	
Available	verfügbar
Articles	
Upsellers	
Upseller	
$FORMNAME	ReopenOrderFram
Type of orders	Bestelltyp
First	Erstes
Last	Letztes
Next	Nächstes
Previous	Vorheriges
Entered number	Nummer eingeben
Any status	
Not accepted	
Accepted	
Closed	
$FORMNAME	SendVoidReasonFram
Reason	Grund
Void reason	Stornogrund
$FORMNAME	ShowTransactionDialog
Waiter	Kellner
Transaction number	Vorgangsnummer
Bill Number	Rechnungsnummer
PC name	PC Name
Client	Kunde
Transaction open date/time	Vorgang geöffnet Datum/Zeit
Payment date/time	Bezahlt Datum/Zeit
Transaction close date/time	Vorgang geschlossen Datum/Zeit
Real payment date/time	Tatsächlich bezahlt Datum/Zeit
Search parameters	Suche Einstellungen
Table number	
Order Number	
$FORMNAME	SmartCuisineImportTaskParamsFram
Default Department	Standard Artikelgruppen
Default Course	Standard Kurs
Source File	Quelldatei
Default Preparation Area	Standard Vorbereitungsbereich
Default Sales Area	Standard Verkaufsbereich
Default Purpose	Standard Zweck
$FORMNAME	SPTurnoverEntityManager
Amount	Menge
New save points turnover record	Neue Bonuspunkte Umsatzspeicherung
Save points turnover record	Bonuspunkte Umsatzspeicherung
Save points turnover	Bonuspunkte Umsatz
User name	Benutzername
Quantity	Menge
Date/Time	Datum/Zeit
Comments	Kommentar
$FORMNAME	TableHistoryParamsFram
Articles ordered per minute	Artikelbestellungen pro Minute
Articles ordered per hour	Artikelbestellungen pro Stunde
Articles ordered per day	Artikelbestellungen pro Tag
$FORMNAME	TestFrm
Server one not available. Inital numbers not updated.	Server eins nicht verfügbar. Inital Nummer nicht aktuell.
$FORMNAME	TipAmountFram
Printer	Drucker
Ticket layout	Beleg Layout
$FORMNAME	TPUAEntityManager
You cannot delete this upseller. There are Articles(s) linked to it.	
$FORMNAME	TSendDiscountReasonFramee
Discount reason	Rapattgrung
$FORMNAME	UntillScreenU
You are trying to print a Z-report in the future. Z-report will not be printed.	Sie versuchen einen Druck Z-Bericht in der Zukunft. Z-Bericht wird nicht gedruckt.
$FORMNAME	VDAliasesEntityManager
Translate from	Übertrage von
Translate to	Übertrage zu
A list of comma-separated PLU numbers	Eine Liste über alle PLU Nummern (Kommagetrennt)
New Beverage Control Alias	Neue Schank Kontroll alias
Beverage Control Alias	Schank Kontroll alias
Wrong "Translate To" syntax	Falsche "Übertragung zur" Syntax
Beverage Control Aliases	Schank Kontroll aliases
$FORMNAME	VDReceiptsEmbEntityManager
Article	Artikel
Count	Anzahl
$FORMNAME	VDReceiptsEntityManager
Article	Artikel
Count	Anzahl
Beverage Control recipe	Schankanlagenrezeptur
$FORMNAME	VoidReasonEntityManager
Void reason	Stornogrund
Void reasons	Stornogründe
$FORMNAME	CountFrequencyEntityManager
Frequency	Frequenz
New count frequency	Neuer Zählerstand
Count frequency	Zählerstand
Overrules all other count frequencies	Exklusiv aller anderen Zählerstände
Count frequencies	Zählerstände
Exclusive	
$FORMNAME	FIFOStackEmbEntityManager
Date	Datum
Quantity	Menge
Unit price	Stückpreis
Value	Betrag
$FORMNAME	InventoryClassEntityFram
Class	Klasse
Group	Gruppe
Inventory item defaults	Inventurstückzahl Standard
Count frequency	Zählerstand
New ingredient class	Neue Zutat-Klasse
$FORMNAME	InventoryClassEntityManager
Ingredient class	Zutaten Klasse
Ingredient classes	Zutaten Klassen
Group	Gruppe
Class	Klasse
Ingredient locations	Zutaten Bereich
$FORMNAME	InventoryItemEntityManager
Number	Nummer
Name	
Class	Klasse
Frequency	Häufigkeit
Default yield	Standard Ertrag
Active	Aktiv
General	
Value	Preis
Usage U/M	Verbrauch U/M
Minimum	
Maximum	
Stock unit price range	Lager Einheits Preis
Average price	Durchschnitts Preis
On hand	Ist Bestand
Units	Einheiten
FIFO Stack	FIFO Block
Items	Artikel
Item locations	Artikel Lokal
Substitute	Ersatz
Supplier items	Lieferant Artikel
New ingredient	Neue Zutat
Ingredient	Zutat
Ingredient locations	Zutaten Bereich
Supplier items link	Lieferant Artikel link
All	Alle
Ingredients	Zutaten
Group name	Gruppen Name
FIFO cost	FIFO Kosten
LIFO cost	LIFO Kosten
Average cost	Durchschnitts Kosten
Expired date	Ungültiges Datum
Count frequency	Zählerstand
Normal amount	
Show...	
From	
To	
Articles	
Barcode	
Turnover	
Ingredient class	
Par quantity	
Supplier	
Simple Par	
Periodic Par	
$FORMNAME	InventoryLocationEmbEntityManager
Location	Bereich
Shelf	Ablage
Purchase U/M	Einkauf U/M
Stock location	Lager Bereich
Count U/M	Zähler U/M
Default	
The ingredient cannot be deleted because it is not depleted on this stock location	
Normal amount	
$FORMNAME	InventoryLocationEntityFram
New ingredient location	Neuer Zutaten Bereich
Ingredient location	Zutaten Bereich
$FORMNAME	InventoryLocationEntityManager
Location	Bereich
Shelf	Ablage
Count U/M	Zähler U/M
Periodic normal amounts	
Normal amount	
Round up	
$FORMNAME	InventorySubstEmbEntityManager
Substitute	Ersatz
Operator	Bearbeiter
Factor	Faktor
Priority	Vorrang
Ingredient substitution	Zutat-Ersatz
You cannot select the same ingredient as a substitute	Sie können nicht die gleiche Zutat als Ersatz-Zutat wählen
You cannot select the same recipe as a parent recipe	Sie können nicht die gleiche Rezeptur als Ersat-Rezeptur wählen
$FORMNAME	InventorySubstEntityFram
New substitute	Neue Ersatz-Zutat
Substitute	Ersatz-Zutat
$FORMNAME	InventorySubstEntityManager
Substitute	Ersatz-Zutat
Conversion	Umwandlung
Priority	Vorrang
$FORMNAME	PurchaseOrderEntityManager
Print order	Drucke Bestellung
Supplier	Lieferant
Type	Typ
Order date	Bestelldatum
Required by	Gefordert von
Reference	Bezug
Open order	Öffne Bestellung
Return of goods	Rückkehr von Waren
General	allgemein
Order items	Bestell Artikel
New Purchase Order	Neue Einkauf Bestellung
Purchase Order	Einkauf Bestellung
Date	Datum
Conducted	ausgeführt
Show all PO	Zeige alle Einkauf Bestellung
Show only opened PO	Zeige nur offene Einkauf Bestellung
Number	
Ordered	
Partially received	
Fully received	
Processed	
$FORMNAME	PurchaseOrderItemsEmbEntityManager
Supplier item	Lieferant Artikel
Ingredient	Bestandteil
Quantity	Menge
Purchase order items	Einkaufs Bestellung Artikel
Order date	Bestell Datum
Purchase U/M	Einkauf U/M
Price	Preis
Last price	letzter Preis
$FORMNAME	PurchaseOrderItemsEntityFram
New purchase order item	Neuer Einkaufs Bestellung von Artikel
Purchase order item	Einkaufs Bestellung von Artikel
$FORMNAME	PurchaseOrderItemsEntityManager
Ingredient	Bestandteil
Supplier item	Lieferant Artikel
Quantity	Menge
Purchase U/M	Einkauf U/M
Price	Preis
Notes	Notiz
$FORMNAME	RecipeEntityFram
Cut	Ausschneiden
Copy	Kopie
Paste	Einfügen
Bold font	Dicke Schrift
Italic font	Schräge Schrift
Underline font	Unterstrichene Schrift
Align right	Rechts ausrichten
Print recipe description	Drucke die Rezeptur
Align center	Miitig ausrichten
Align left	Links ausrichten
Recipe	Rezeptur
$FORMNAME	RecipeEntityManager
Name	
Group	Gruppe
Units produced	Einheiten erzeugt
Usage U/M	Verbrauch U/M
General	
Recipe items	Rezeptur Artikel
New recipe	Neue Rezeptur
Recipe	Rezeptur
Number	Nummer
$FORMNAME	RecipeItemsEmbEntityManager
Sequence number	Vorgangsnummer
Recipe item	Rezeptur Artikel
Usage U/M	Verbrauch U/M
Amount used	Menge wählen
Override yield	Beeinflussung Ausbeute
Remark	Hinweis
Ingredient	Zutat
Base recipe	Basis Rezeptur
Recipe items	Rezept-Bestandteil
Number	Nummer
Item name	Artikelname
$FORMNAME	RecipeItemsEntityFram
New recipe item	Neues Rezept-Bestandteil
Recipe item	Rezept-Bestandteil
$FORMNAME	RecipeItemsEntityManager
Item type	Artikelart
Ingredient	Zutat
Recipe	Rezeptur
Remark	Hinweis
Seq number	Reihenfolgenummer
Item	Artikel
Override yield	Beeinflussung Ausbeute
Default yield	Standard Ausbeute
Amount used	Betrag verwendet
U/M	
$FORMNAME	ReorderWizardU
Finish	Feritg
Cancel	Abbruch
Back	Zurück
Next	Nächste
Select supplier	Wähle Lieferant
Choose supplier for purchase order	Wählen Sie Lieferant zur Bestellung
You can choose one supplier and one purchase order or choose "All" then the Wizard will create set of Purchase orders of all needed ingredients	Sie können einen Lieferanten und eine Bestellung wählen oder "Alle" dann wählen, dass der Assitent von Bestellungen aller erforderlichen Zutaten schaffen wird
Supplier	Lieferant
Order date	Bestell Datum
Required date	benötigt Datum
Please enter purchase order references and change amounts where needed	Gehen Sie bitte in Bestellungsvorlagen und geben Sie den Änderungsbeträge, wenn erforderlich, ein
Reference	Bezug
Description	Beschreibung
$FORMNAME	StockAdjEntityManager
Show items used in invoices	Zeige Artikel von Rechung
Do not show items used in invoices	Zeige nicht Artikel von Rechung
Remark	Hinweis
Type	Typ
Adjustment date	Abgleichsdatum
Reference	Bezug
General	
Adjustment items	Ablgeichsartikel
Stock location from	von Lagerort
New adjustment	Neuer Abgleich
Adjustment	Abgleich
Stock location	Lagerort
Adjustment type	Abgelichstyp
Location from	vom Bereich
Location to	zum Bereich
Non retail sale	kein Einzelhandelsverkauf
Employee consumption	Mitarbeiterverbrauch
Charity	Einladung
Promotions	Promos
Raw Waste	Rohabfall
Finished Waste	Abfall Fertigware
Other	Anders
Transfer between locations	Übertragung zwischen Bereiche
Physical Stock Count	Tatsächlicher Lagerbestand
Date	Datum
Conducted	ausgeführt
Conduct adjustments	Abegeglichen
Stock adjustment	Lagerabgleich
Show all adjustments	Zeige alle Abgleiche
Show only opened adjustments	Zeige nur offene Abgleiche
Conduct physical counting	Tatsächliche Zählung ausführen
Close purchase order	
Number	
None	
$FORMNAME	StockAdjItemsEmbEntityManager
Ingredient	Zutat
Quantity	Menge
Adjustment items	Artikel abgleichen
Unity	Einheit
Remark	Hinweis
$FORMNAME	StockAdjItemsEntityFram
New stock adjustment item	Neuer Lager Artikelabgleich
Stock adjustment item	Lager Artikelabgleich
$FORMNAME	StockAdjItemsEntityManager
Ingredient	Zutat
Quantity	Menge
Usage U/M	Verbrauch U/M
Remark	Hinweis
Ingredient barcode	
$FORMNAME	StockCloseDayEntityManager
Close day	Schliesse Tag
Date	Datum
$FORMNAME	StockCostCorrectionItemManager
Date	Datum
Invoice	Rechnung
On hand	Ist Bestand
Unit price	Einzelpreis
New unit price	Neuer Einzelpreis
Variance	Abweichung
id_cost_item	id_kosten-artkel
id_stock_invoice_item	id_lager_rechnung_Artikel
Spent	verbraucht
$FORMNAME	StockCostEntityManager
Show spent invoices	Zeige Verbrauchsrechnung
Remark	Hinweis
Ingredient	Zutat
Correction date	Datum korrigieren
Reference	Bezug
Stock locations	Lagerort
Average cost	Durschnittskosten
On hand	Ist Bestand
Usage U/M	Verbrauch U/M
Value	Betrag
FIFO stack	FIFO Lager
New cost correction	Neue Kostenkorrektur
Cost correction	Kostenkorrektur
Date	Datum
Conducted	ausgeführt
Conduct cost correction	Berechnungskorrektur durchführen
Stock cost correction	Lagerberechnung durchführen
Show all cost corrections	Ziege alle Berechnungskorrekturen
Show only opened cost corrections	Ziege nur offene Berechnungskorrektur
Number	
Current values	
$FORMNAME	StockDatasetsU
Stock balance	Lagerabgleich
Ingredient class	Zutaten Klasse
Category number	Kategorie Nummer
Ingredient	Zutat
Amount	Betrag
Value	Wert
Average cost	Durchschnittskosten
Last cost	Letzte Kosten
Ingredient number	Zutaten Anzahl
Usage U/M	Verbrauch U/M
Active	Aktiv
Stock location	Lagerort
Contains current stock balance	Enthält gegenwärtigen Lagerabgleich
Stock invoices	Lager Rechnung
Invoice type	Rechnungstyp
Invoice date	Rechnungsdatum
Supplier	Lieferant
Supplier number	Lieferanten Nummer
Reference	Bezug
Balanced	Abgleich
Total	Gesamt
Freight	Fracht
Tax	Steuer
Other payments	Andere Bezahlungen
Invoice amount	Rechnungsbetrag
Non inventory	Nicht Warenbestand
User	Benutzer
Invoices list for the period	Rechnungsliste für Zeitraum
PO invoice	PO Rechnung
Invoice	Rechnung
Transfer in	Übertrage zu
Cash payouts	Bargeld ausgezahlt
Adjustments	Anpassung
false	falsch
true	richtig
Stock adjustments	Lager Anpassung
Adjustment type	Anpassungs Typ
Stock location from	von Lagerort
Stock location to	zu Lagerort
Adjustment date	Anpassungsdatum
Transfer-to unit	Anpassung-zu Einheit
Adjustments list for the period	Anpassungsliste für Zeitraum
Transfer out	Übertragung
Non retail sale	Nicht Einzelhandelsumsatz
Employee consumption	Mitarbeiterverbrauch
Charity	Einladung
Promotion	Promo
Raw waste	Roh vernichtet
Finished waste	Fertig vernichtet
Other	anders
Transfer between locations	Übertragung zwischen Bereiche
Purchase orders	Bestellung
Purchase order type	Bestellungstyp
Purchase order date	Bestellungsdatum
Required date	Bedarfstermin
Purchase order list for the period	Bestellungsliste für Zeitraum
Open order	Offen Bestellungen
Return of goods	Warenrücksendung
Stock turnover	Lagerumschlag
Turnover date/time	Umsschlags Tageszeit
Comment	Kommentar
Contains stock turnover data for period	Behälter Warenrücksendungdaten für Zeitraum
Stock invoice items	Lagerrechung Artikel
Supplier item	Lieferant Artikel
Invoice reference	Rechnungsbezug
Notes	Notiz
Purchase U/M	Einkauf U/M
Stock adjustment items	Lager Einstellungen Artikel
Adjustment reference	Bezüge einstellen
Purchase order items	Bestellung Artikel
Order reference	Bestellreferenz
Units of measure	Einheiten der Masse
multiplied	mal
devided	geteilt
Physical count sheet	Muster Inventurzählbeleg
Count frequency	Zählfrequenz
Date	Datum
Stock locaction	Lagerbereich
Physical amount	Tatsächlicher Bertag
Theoretical amount	Theoritscher Betrag
Variance	Abweichung
Count U/M	Anzahl U/M
Physical amount (Usage U/M)	Tatsächlicher Umsatz (Verbrauch U/M)
Physical counting data	Tatsächliche Zählung
Stock reports	Lagerbericht
Suppliers	Lieferant
Name	
Address	Adresse
Country	Land
Phone	Tel
Fax	
E-mail	
Website	
Contains suppliers	Kontakt Lieferanten
Recipe cost	Rezeturpreis
Recipe	Rezeptur
Recipe number	Rezepturnummer
Group	Speisengruppe
Units produced	Eiheiten produziert
Unity of measure	Einheit der Maßnahme
Calculation ingredients amount and prices for recipes	Berechnungszutat-Betrag und Preise für Rezepte
Reorder report	Nachbestellungs Bericht
On hand	Ist Bestand
Quantity to order	Menge zum Bestellen
Reorder type	Nachbestellungs Typ
Lists quantities of inventory items needed to meet the requirements of anticipated sales	Listenmengen von Warenbestand-Sachen mussten den Anforderungen von vorausgesehenen Verkäufen entsprechen
Sales and cost	Verkäufe und Kosten
Department number	Artikelgruppennummer
Article number	Arikelnummer
Sold quantity	Verkaufte Menge
Total sold	Gesamt Verkauft
Theoretical cost	Theoritisch Verkauft
Gross Profit	Bruttogewinn
Usage U/M of ingredient	Verbrauch U/M von Zutat
Pos department number	Kassen Artikelgruppennummer
Pos article number	Kassen Artiklenummer
Sold ingredients quantity	Verkaufte Zutat-Menge
Total ingredients sold	Gesamt Zutatenverkauf
Theoretical ingredient cost (calculated based on supplies)	Theoretische Kosten der Zutat (rechnet basiert auf den Bedarf)
Sales and Cost stock report	Verkäufe und Lagerkosten Bereicht
Ingredients	Zutat
Number	Nummer
Class	Klasse
Default yield	Standard Produktion
Reorder minimium	minimum Wiederbestellen
Reorder par	gleich Wiederbestellen
Contains ingredients	Container Zutat
Initial count sheet	Initailisieren Inventurzählbeleg
Location	Bereich
Shelf code	Absatzcode
Shows ingredients to be initially counted	Zeige am Anfang aufzuzählende Zutaten
Recipe items	Rezepturartikel
Sequence number	Bearbeitungs Nummer
Type	Typ
Item number	Artikelnummer
Item name	Artikelname
Amount used	Betrag verwendet
U/M conversion	U/M Umwandlung
Cost	Kosten
Last unit cost	Letzte Stückkosten
Yield	Ertrag
I	
R	
yes	Ja
no	Nein
Count sheet	Inventurzählung
Cost of sales report	Die Kosten der umgesetzten Bericht
Ingredient class number	Zutat Klassifizierung
Opening stock amount	Anfangsbestand Betrag
Opening stock value	Öffner Lagerwert
Closing stock amount	Endbestand Betrag
Closing stock value	Schliesse Lagerwert
Purchased amount	Einkaufs Betrag
Purchased value	Einkaufswert
Adjusted amount	Ausgleichs Betrag
Adjusted value	Ausgleichswert
Sold amount	Verkaufs Betrag
Sold value	Verkaufwert
Sold stock ingredients	Lagerverkauf Zutat
Single sales price	Einzelner Abgabepreis
Parent menu	
Order ID	
Supplier article number	
Stock unity of measure	
Single stock price	
Total price	
Price	
Par amount	
On hand (rounded)	
On hand in stock u/m	
Ordered today	
Supplier description	
Conduct date & time	
Adjustment date & time	
POS auto adjustment	
Customer nr.	
Day of week	
Summary stock turnover	
On hand amount	
Contains summary stock turnover for period	
Last price	
Invoice items for stock invoice	
Purchase U/M convertion	
Total stock value	
Theoretical amount(usage U/M)	
Sale price	
Simple Par	
Periodic Par	
V.A.T.	
Supplier name	
Day of week quantity	
Unknown	
Inital count items	
Date/time	
Stock history turnover	
Operation date/time	
Operation description	
Show turnover history by ingredients	
Adjustment	
Inital counting	
Article	
Order	
Stock articles	
Article name	
Sales UM	
Articles, linked to ingredients	
Counting U/M	
Not adjusted	
All ingredients	
Common stock information	
Stock edit ingredients	
Edt. Ing.	
Stock: Order proposal	
Stock: list of ingredients to re-order	
Stock: Invoice items	
Ordered quantity	
received quantity	
Backorder	
Rest	
Stock: list of ingredients for invoice	
Stock: Modified order proposal	
Stock: list of ingredients in PO	
Stock: Free order proposal	
Stock: list of ingredients in free PO	
Stock edit supplier	
PC text	
V.A.T. number	
Discount	
Delivery day	
Order day	
Min. order amount	
Modified stock supplier data	
Stock: Supplier contacts	
Position	
Mobile phone	
Stock: list of supplier contacts	
Supplier contact data	
Modified supplier contact data	
Stock: Purchase orders	
PO number	
PO reference	
PO state	
Stock: list of Purchase orders	
Stock: order proposal overview	
$FORMNAME	StockLocationEntityManager
Ingredient class	Zutaten Klasse
New stock location	Neuer Lagerbereich
Stock location	Lagerbereich
Stock unit	Lagereinheit
Stock_number	Lager_Nummer
Printer	
Ticket	
You cannot delete location. The balance on this stock location is not zero for one or more ingredients	
$FORMNAME	StockInitCountEntityManager
Initial counting	Die Anfangszählung
Class name	Klassenname
Ingredient	Zutat
Quantity	Menge
Unit cost(Usage U/M)	Stückkosten (Verbrauch U/M)
Usage U/M	Verbrauch U/M
Unit cost(Count U/M)	Einheitskosten(Anzahl U/M)
Count U/M	Anzahl U/M
Conduct initial counting	Anfangszählung durchführen
$FORMNAME	StockInitCountFilterFram
Stock location	Lagerbereich
Filter	
Name	
None	
Group & name	
$FORMNAME	StockInvoiceEntityManager
Supplier	Lieferant
Type	Typ
Invoice date	Rechnungs Datum
Reference	Bezug
General	
Invoice items	Artikelrechnung
Invoice total	Rechnung Gesamt
Invoice amount	Rechnugsbetrag
Line items total	Positionssumme
Freight	Fracht
Tax	Steuer
Disbursed	Ausgezahlt
Remaining	restlich
Other	anders
Non Inventory	Nicht Warenbestand
Balanced	Ausgeglichen
Print invoice	Drucke Rechnung
Stock location	Lager Bereich
New invoice	Neue Rechnung
Invoice	Rechnung
Purchase order	Bestellung
Invoice type	Bestelltyp
Invoice total amount	Rechnungs Gesamtbetrag
PO Invoice	PO Rechnung
Transfers in	
Cash payouts	Barauszahlung
Adjustments	Anpassung
Conducted	Dirigiert
Conduct invoices	Verhalten Rechnungen
Stock invoice	Lager Rechnung
Show all invoices	Zeige alle Rechnungen
Show only open invoices	Zeige alle offene Rechnungen
Number	
Adjustment	
Initial count	
Auto initial count	
Date	
$FORMNAME	StockInvoiceItemsEmbEntityManager
Expired date	Ungültiges Datum
Supplier item	Lieferant Artikel
Ingredient	Zutat
Quantity	Menge
Price	Preis
Invoice items	Rechnungsposten
Purchase U/M	Einkauf U/M
Last price	Letzter Preis
Received	
Backorder	
Add item from purchase order sheet	
Add item from re-order report	
$FORMNAME	StockInvoiceItemsEntityFram
New stock invoice item	Neue Lager Rechnungsposten
Stock invoice item	Lager Rechnungsposten
$FORMNAME	StockInvoiceItemsEntityManager
Ingredient	Zutat
Supplier item	Lieferant Artikel
Quantity	Menge
Purchase U/M	Einkauf U/M
Price	Preis
Notes	Anhang
Expiration date	Verfallsdatum
$FORMNAME	StockOpenDayEntityManager
Date	Datum
Ingredients	Zutat
Open	Öffne
Close	Schliessen
day	Tag
Day already opened	Tag öffnete sich bereits
Open day	Öffne Tag
$FORMNAME	StockOpenDayItemEntityManager
Number	Nummer
Ingredient	Zutat
On hand quantity	Ist Bestands Menge
On hand value	Ist Bestandswert
Usage U/M	Verbrauch U/M
$FORMNAME	StockPhysicalCountEntityManager
Frequency	Abstand
Physical counting on this date and this stock location already exists	Das physische Zählen an diesem Datum und diesen Bereich besteht bereits
Physical count entry	Tatsäche Zahl beginnen
Date	Datum
Count frequency	Zählerstand
Stock location	Lagerort
Conducted	ausgeführt
Counting items	Zähler Artikel
Adjustment...	Einstellungen...
New count period	Neuer Zählabstand
Count period	Zähl abstand
None	
Ingredient Class & Name	
Sort by	
Print count sheet	
Print count report	
... for selected item	
... for all items	
$FORMNAME	StockPhysicalCountFram
Some values are changed, but not saved. Ignore changes?	Einige Werte werden geändert, aber nicht gesichert. Ignorieren Sie Änderungen?
$FORMNAME	StockPhysicalCountItemEntityManager
Location	Bereich
Number	Nummer
Ingredient	Zutat
Quantity	Menge
Count U/M	Anzahl U/M
Quantity(Usage U/M)	Menge (Verbrauch U/M)
On hand	Ist Bestand
Variance	Abweichung
Usage U/M	Verbrauch U/M
Class	
Stock price	
$FORMNAME	StockReorderEntityManager
Class	Klasse
Ingredient	Zutat
On hand	Ist Bestand
$FORMNAME	StockTreeFoldersU
Stock	Lager
Product	Produkt
Documents	Dokumente
Person	
Actions	Akltionen
$FORMNAME	StockUnitsEntityManager
New stock unit	Neuer Lagerartikel
Stock unit	Lagerartikel
Stock units	Lagerartikel (mehr)
$FORMNAME	SuppliersFilterFram
Find	Finde
$FORMNAME	UnitsConvEmbEntityManager
Multiplied	Multipliziert
Divided	Dividiert
Usage U/M	Verbrauch U/M
Unity conversion	Einheit Umwandlung
Default	Standard
Operator	Bearbeiter
Factor	Faktor
$FORMNAME	UnityConvEntityFram
New unit conversion	Neue Einheiten Umrechnung
Unit conversion	Umrechnung von Einheiten
Unit of measure	Masseinheit
Operator	Bearbeiter
Factor	Faktor
Default	Standard
Round up	
$FORMNAME	VendorInventoryEntityFram
New supplier item	Neue Lieferanten Artikel
Supplier Item	Lieferant Artikel
$FORMNAME	VendorInventoryEntityManager
Supplier	Lieferant
Item number	Artikelnummer
Purchase U/M	Einkauf U/M
Active	Aktiv
Preferred item	Bevorzugter Artikel
Supplier article number	
Contract price	
$FORMNAME	VendorItemEmbEntityManager
Supplier	Lieferant
Name	
Purchase U/M	Einkauf U/M
Supplier items	Lieferant Artikel
Number	Nummer
Item name	Artikelname
Price	
Pref.	
$FORMNAME	AccountHistoryParamsFram
Include menu items	
$FORMNAME	AddCardsFrm
Add Cards Into Database	
Set Balance	
Close	
Init Prepaid Card	
Swipe cards one by one to add them to the database	
Card Reader is not configured for this PC	
Prepaid Penko Card	
Not a Prepaid Penko Card	
Not a Penko Card	
Deposit made to card	
Client with this card UID already exists	
Card skipped	
Card with the Tap2Order Number %d already exists in database. Deactivate it?	
Swipe card again to apply	
Card with the same UID already exists in database. Deactivate it?	
Card added to DB	
$FORMNAME	AggregatesRecalculationTaskParamsFram
SYSDBA password	
$FORMNAME	AirDiscountsEntityManager
Discounts	
Order	
Discount Type	
Discount Reason	
All items	
Selected items	
Discount Value	
Value Type	
Percentage	
Fixed	
Exclude Period	
Reason	
Free Reason Type	
Free Reason Text	
Fixed text	
Ask Reason	
Constraints	
Applied To	
Department	
Course	
Group	
Category	
Appearance	
Background Color	
Font Color	
$FORMNAME	AirPaymentFunctionsEntityManagerU
Payment Functions	
Order	
Button Text	
Button Code	
Split by item	
Split equally	
Split by course	
Split by seat	
Split by part	
Add client	
Reverse split	
Add tips	
Add payer	
$FORMNAME	AllergenEntityFram
Alternate picture	
$FORMNAME	AllergenEntityManager
This number is reserved for built-in allergen	
Allergen	
Predefined	
Custom	
Icon	
Alt.icon	
Kind	
New allergen	
$FORMNAME	ArticleBarcodesEntityFram
New article barcode	
Article barcode	
Barcode	
Comments	
$FORMNAME	ArticleBarcodesEntityManager
There is another article - "%s" with the same barcode	
Barcode	
Comments	
$FORMNAME	ArticleBonusArticleEmbEntityManager
Number	
$FORMNAME	ArticleBonusEmbEntityManager
Bonus group	
Article: Bonus groups	
Max qty	
New article bonus group	
Article bonus group	
Max quantity	
Prices	
Article	
$FORMNAME	ArticleBonusPriceEmbEntityManager
Currency	
Price	
$FORMNAME	ArticleDGEmbEntityManager
Discount group	
Article	
Discount value	
$FORMNAME	ArticleFreeOptionEmbEntityManager
Number	
Option	
New free option group	
Free option group	
$FORMNAME	ArticleKSNotifyEmbEntityManager
Kitchen screen	
Kitchen screen purpose	
Article: KS notify	
KS notification	
Stage number	
Stage name	
Preparation time	
KS workflow stage	
Warining level	
KS workflow stage item	
$FORMNAME	ArticleOptDefaultEntityFram
New default article	
Default article	
$FORMNAME	ArticlePriceExclEmbEntitymanager
Period	
Price	
$FORMNAME	ArticleRentPriceEmbEntityManager
Quantity	
Price	
$FORMNAME	ArticleRentPriceEntityFram
New rental period price	
$FORMNAME	BalanceSmartCardParamsFram
Payment mode for negative deposit ticket	
Custom invoice layout	
$FORMNAME	BarCodesU
PLU not found: %s	
$FORMNAME	BillsParamsFram
Paid and voided bills	
Reopened and not closed bills	
$FORMNAME	BLAlgRentU
Enter rental start and end date/time	
Pick up rental item	
You are going to return all rental barcodes. Are you sure?	
One or more items are overdue	
Overdue rentals	
Check rental status	
Rented items status	
$FORMNAME	BLRAlgCardTableU
Card tables control	
Please scan client card or choose table from list	
Select Card table order to delete	
Scan card to delete bills	
Card tables overview	
Enter blacklist reason	
$FORMNAME	BLRAlgPromoU
Choose combo/promo	
Choose combo/promo item	
$FORMNAME	BLRAlgStockBalanceU
You are going to reopen bill with number %s. Are you sure?	
You are going to create %s vouchers with amount %s. Are you sure?	
Item %s has been adjusted. Old quantity %s New quantity %s	
One or more items that have an insufficient quantity in central stock, were not adjusted	
One or more items were not adjusted. Insufficient quantity in stock	
You are going to make an adjustment for all items in all locations. Are you sure?	
You are going to make an adjustment for all items in location "%s". Are you sure?	
You are going to make an adjustment for item "%s". Are you sure?	
Transfer adjustment number %s with %s ingredients is created.	
$FORMNAME	BLRCardOpsU
Gift Card Operations	
Gift Card Operation	
Void operation?	
Balance: %s	
Input Gift Card Amount	
$FORMNAME	BLRDailyArticleBalanceU
Enter article daily stock balance	
$FORMNAME	BLRentU
Print rent ticket	
$FORMNAME	BLRGeneralManagerU
Restaurant General Manager	
Restaurant General Journal	
$FORMNAME	BLRKitchenScreenU
Start with latest ordered	
Start with latest opened	
Start with least preparation time leftover	
Start with oldest ordered	
Start with oldest opened	
Start with oldest pickup	
Start with latest pickup	
Ascending manually sorted	
Descending manually sorted	
Unknown sorting	
Simple kitchen screen	
Kitchen screen Pro	
Message from user	
Consolidate kitchen screen	
Detailed kitchen screen	
KS pickup history screen	
KS pickup screen	
Redirect kitchen screen	
Redirect kitchen screen management	
Pickup per article screen	
Pickup per article	
Kitchen screen message	
Rearrange kitchen screen tiles	
Delivered orders	
Select articles to change course	
All ordered articles	
Articles prepared for current and previous courses	
Articles to be prepared for upcoming course	
Articles of same order prepared in other places	
All ordered articles and picked up articles	
$FORMNAME	BLRSpecDiscountsU
Enter discount reason	
Enter free void reason	
$FORMNAME	BLRTakeAwayU
Maximum orders (%d) in time slot %s reached. Are you sure you want to override?	
Take away order	
Take away orders	
Transfer take away orders	
Selected time slot %s is already full. Override?	
The order is being prepared now. Continue transfer?	
Assign deliverer to take away orders	
$FORMNAME	BLRVouchersU
Not paid	
Paid	
Spent	
Blocked	
New voucher's value	
Please scan valid barcode	
$FORMNAME	BLSysU
Non-printed Z-Report %d successful	
Unable to generate report. Some databases not synchronized: %s	
$FORMNAME	BonusArticleEmbEntityManager
The bonus group can't be added: article "%s"   already belongs to bonus group "%s"	
Article	
Bonus group: Articles	
New bonus article	
Bonus article	
Prices	
Name	
Number	
$FORMNAME	BonusArticlePriceEmbEntityManager
Currency	
Price	
$FORMNAME	BonusGroupEntityManager
New bonus group	
Bonus group	
Name	
&1 General	
&2 Availability	
Articles	
Available	
Bonus groups of parent articles	
Parent articles	
Bonus groups	
$FORMNAME	BonusParentArticleEmbEntityManager
Article	
Max. quantity	
Open articles	
$FORMNAME	CardTableParamsFram
Registered	
Ordered	
Blacklist	
$FORMNAME	CashdroSpecialFuncFram
Function	
Charge	
Return	
Charge Return	
Remove cash drawer	
Change	
$FORMNAME	ChangeAutoOrderModeFram
On waiter's direct sales	
On certain table	
$FORMNAME	ClientDepositDlg
Payment	
Make Deposit	
Increase	
Decrease	
Set balance	
$FORMNAME	ClientDGEmbEntityManager
Article	
Discount	
Article: Discount	
$FORMNAME	ClientEmailU
Get client e-mail	
$FORMNAME	ClientSearchParamsFram
Client name	
Client card number	
Client phone number	
Internal number	
Client Name/Card/Phone	
Predefined order	
Choose next course	
Course name	
$FORMNAME	ClientSharedEmbEntityManager
Number	
Is active	
Name	
Address	
$FORMNAME	CloseAllTablesTaskParamsFram
Payment type not selected	
User not selected	
$FORMNAME	CloseBillTypeFram
Bills without tips	
Bills with tips	
All closed bills	
$FORMNAME	Common
Allergy	
$FORMNAME	CouponEntityFram
Coupon ticket %s is printed on printer %s	
$FORMNAME	CouponEntityManager
Block	
Unlimited	
Identificator type	
Barcode	
QR code	
Start date/time	
End date/time	
Single code	
Unique code	
Notes	
Coupon template	
Validation period	
Coupon type	
Coupons quantity	
New code	
Re-generate code	
Export...	
Nr of Coupons to be printed	
Print	
General	
Area/View	
Coupon	
Coupons	
Spent date	
Start date	
End date	
User	
State	
Unblock	
Price level for turnover calculation	
Max articles quantity	
Price	
New coupon template	
$FORMNAME	CouponsFilterFram
Name	
$FORMNAME	CouponssTicketDataSetParamsFram
Hide expired coupons	
$FORMNAME	CouponTemplateEntityManager
Price level	
Coupon templates	
Max articles quantity	
Price	
$FORMNAME	CptArticleEmbEntityManager
New coupon article	
Coupon article	
$FORMNAME	CreateTTGCardParamsFram
Default Group	
$FORMNAME	CustomerEmailEntityManager
E-mail	
Email	
Customer emails	
$FORMNAME	CustomerEmailFilterFram
E-mail	
Filter	
$FORMNAME	DailyStockTurnoverParamsFram
Normal report	
Last daily stock transaction	
Kitchen screen device	
$FORMNAME	DailyStockU
Enter decrease reason	
$FORMNAME	DepartmentMassModifyingDlg
Mass modifying	
New group	
New supplement	
New condiment	
New age group	
New available	
Change PC appearance	
Change HHT appearance	
Change Orderman appearance	
Change default view	
Change color	
Change font	
Change alt. font	
Change group	
Change supplement	
Change condiment	
Change age group	
Change available	
&1 General	
&2 Appearance	
$FORMNAME	DiscountGroupEntityFram
New discount group	
Discount group	
Name	
&1 General	
Articles	
&2 Linked clients	
Clients, linked to discount group	
Discount groups, linked to Client	
Barcode	
Only the cheapest article discounted	
Discount reason	
$FORMNAME	DiscountGroupEntityManager
Discount reason	
Discount groups	
New discount group	
Discount group	
$FORMNAME	EndOfDayParamsFram
Params	
$FORMNAME	ExpClntsFrm
Destination file not specified	
Exported: %d records	
Destination	
XLS file	
Fields	
Export clients	
Cancel	
Go >>	
$FORMNAME	ExportU
Z-Report	
$FORMNAME	GiftCardPaymentIdFram
Payment type	
Print receipt	
$FORMNAME	HotelConceptsU
Ok, consolidated mode	
Ok, detailed mode	
Not in house	
No credit	
Overlimit	
Guest not found	
$FORMNAME	ImportClientsTaskParamsFram
Source File	
Local Path	
Remote Path	
Smart Cards Group	
Source	
Local	
FTP	
FTP Properties	
Delete file after import	
Update clients by name	
$FORMNAME	ImportT2OClientsTaskParamsFram
Smart Cards Group	
Source File	
$FORMNAME	iTessoU
Nothing to export in EOD	
No reservation for EOD Room=%d	
$FORMNAME	KSChangeCourseParamsFram
Show changed with color	
$FORMNAME	KSConsSelecteStoredParamsFram
Mode	
Detailed	
Consolidated	
Only next course articles	
All articles	
Current active articles	
Pending articles	
Data type	
Show courses with color	
Take away course	
$FORMNAME	KSCounterEntityFram
Number of lists on screen	
Show options	
Consolidate by chair	
Show items with Information purpose	
Split by courses	
$FORMNAME	KSCounterEntityManager
name	
number	
Pickup device	
$FORMNAME	KSCounterParamsFram
Pickup device	
$FORMNAME	KSDetailsParamsFram
List type	
Actual item list	
Voided item list	
$FORMNAME	KSEntityManager
Type	
Number of lists	
Kitchen screen	
Kitchen screens	
$FORMNAME	KSOrderParamsFram
Items state	
$FORMNAME	KSOverviewParamsFram
Show option quantity	
$FORMNAME	KSParamsItemsFram
Kitchen screen device	
Hide options	
Items type	
All items	
Active items	
Not active items	
Show options	
$FORMNAME	KSSortTypeParamFram
Start with latest ordered	
Start with oldest ordered	
Order date/time	
Open date/time	
Pickup date/time	
Language	
Pick up device	
Sort by field	
Articles to show	
Purpose	
$FORMNAME	KSWFStageEntityFram
Notifications	
Default preparation time	
Default warning level	
New KS workflow stage	
KS workflow stage	
$FORMNAME	KSWFStageItemEmbEntityManager
Kitchen screen	
Kitchen screen purpose	
$FORMNAME	KSWFStageItemEntityManager
Name	
KS workflow stage	
KS workflow template	
$FORMNAME	KSWFTemplateEntityFram
Workflow stages	
New KS workflow template	
KS workflow template	
Workflow stage	
New KS workflow template item	
KS workflow template item	
$FORMNAME	KSWFTemplateItemEmbEntityManager
KS workflow stage	
Stage number	
Stage name	
$FORMNAME	LookupCardFrm
Card Reader is not configured for this PC	
Swipe card	
Swipe again please	
Card error. Swipe again please	
$FORMNAME	MenuFilterParamsFram
Departments	
Groups	
Courses	
$FORMNAME	MinMaxDSParamsFram
All items	
Hours of service items	
$FORMNAME	MustHaveOptScreenParamsFram
Option set number	
$FORMNAME	NeedPrintKSOrderFram
Printer	
Layout	
$FORMNAME	NeedReopenBillFram
Undo payments	
Keep re-open manager	
Keep original waiter	
$FORMNAME	NewyseU
Payment is not available with this card	
Connecting to Newyse	
Payment cancelled	
Not enough money on customer account	
Sending Data to Newyse	
Service Charge	
Forbidden partial payment with Newyse. Use split bill	
Another card has been used in current order	
Reading Newyse Products	
Reading Card Information	
Select Customer	
No customers found for this card	
$FORMNAME	OnHoldInfoParamsFram
Tile number	
Show items in course color	
$FORMNAME	OpenDrawerParamsFram
Common drawer	
Personal drawer	
My personal drawer	
Comments	
$FORMNAME	OpenTablePartActionParamsFram
Table part	
$FORMNAME	OptionArticlePricesEntityFram
New option article price	
Option article price	
Profit rate	
$FORMNAME	OptionMassModifyFram
Mass modifying	
New available	
Change available	
$FORMNAME	OptionWithEmbEntityManager
Price	
$FORMNAME	OrderTypeEntityFram
New order type	
Order type	
$FORMNAME	OrderTypeEntityManager
Order type	
Order types	
$FORMNAME	PAKSNotifyEmbEntityManager
Kitchen screen	
PA: KS notify	
TA: KS notify	
$FORMNAME	PaymentActionParamsFram
Undefined	
Ask user	
Predefined	
Payment type	
Table area	
Only tables, opened by current user	
All tables	
Close what	
Extra fields	
$FORMNAME	PaymentByRequestParamsFram
Driver	
$FORMNAME	PaymentCurrencyFram
Tile number	
Currency	
$FORMNAME	PeriodArticleEntityManager
New article related period	
Article related period	
Period	
Article	
Number	
Pair "%s" - "%s" already exists	
Articles related periods	
$FORMNAME	PersonalLogActionParamsFram
Message	
$FORMNAME	PLPrepaidAriclesU
Current order contains pre-paid articles for other card	
$FORMNAME	PosAlgU
Deposit %s %s made to unTill Smart Card. Available: %s %s	
Deposit %s %s made to Penko Smart Card. Available: %s %s	
Deposit %s %s made to TTG Smart Card. Available: %s %s	
Amount is too large	
Not enough money on smart card	
Unable to store deposit on unTill smart card	
This operation only available for "on-invoice" client	
Smartcard already exists in database	
Unsupported reader model	
$FORMNAME	PosPayWithSCCardFrm
Penko credit block not recognized	
Not a Penko-prepaid card. Check card settings in database	
Not enough money on card	
Internal Error	
Not a Penko card. Check card settings in database	
Internal Error. See except.log and smartcards.log files for details	
Unknown card	
Function not available for shared accounts	
Card read error	
Card write error	
Swipe card	
$FORMNAME	PrepaidArticlesEmbEntityManager
Article	
Add article to pre-paid articles group	
Modify pre-paid group article	
Article already included in prepaid group	
Prepaid group articles: items	
Add article(s)	
Add combo article(s)	
Articles sequence	
$FORMNAME	PrepaidArticlesEntityManager
Group ID	
Articles in this group	
Remaining value articles	
For voucher amount	
Negative for payment	
Pre-paid articles group	
Pre-paid article groups	
Pre-paid article group	
$FORMNAME	PrintCouponsDlg
Print coupon dialog	
All	
Not used	
New	
Items	
$FORMNAME	QuantityIncDecFram
Cash	
Credit card	
Select payment	
Day of week	
Single article	
All articles in course	
Increase value	
Decrease value	
Overdue	
Ending in period	
$FORMNAME	RentActionFram
Return	
Repair	
Prolong	
$FORMNAME	RentalGroupEntityManager
New rental group	
Rental group	
Rental groups	
New rental period value	
Rental period value	
Period values	
New rent price period	
Rent price period	
$FORMNAME	RentalPeriodItemsEmbEntityManager
Value	
$FORMNAME	RentalPeriodsEntityManager
Rental price table	
Rental price tables	
$FORMNAME	RentFilterFram
Status	
All items	
$FORMNAME	RentRepairReasonEntityManager
Reason	
Barcode	
New reason	
Rent repair reasons	
Rent repair reason	
$FORMNAME	RentStoredParamsFram
Items by article	
Overdue items	
Close to finish	
Select dataset type	
$FORMNAME	RePrintZReportDialog
Print Z-Report	
Sales area	
Re-print	
By number	
By date	
till	
$FORMNAME	RePrintZReportDlg
Reports to be re-printed: %d	
Printer	
Number of reports printed: %d	
$FORMNAME	ReservationFilterFram
Status	
$FORMNAME	ResetCardFrm
Reset Smartcard	
New Balance	
Close	
Card Reader is not configured for this PC	
Swipe card to reset it	
Storno	
Deposit	
$FORMNAME	ResetOrderArticlesParamFram
All departments/sales areas	
Current department/sales area	
$FORMNAME	ResortBarTablesFram
Sort bar tables by	
Table numbers	
Table names	
Open dates	
Order names	
$FORMNAME	RestaurantInfoDSU
How many courses show on screen	
Allergen info	
Show as images	
Show as text	
Ignore state colors	
Show course once per course	
Show order time once per course	
Coupons	
List of coupons	
Coupon avaliable	
List of coupon sales areas	
Coupon notify	
List of coupon preparation areas	
$FORMNAME	RestaurantScreenDSU
All	
Free reason	
OIF Calendar	
OIF calendar days	
OIF Cal.	
Bill/Proforma languages	
Used bill/proforma languages	
Lng.	
Void limited users	
Last reset date	
List of users limited Voids permission	
Vl.Usr.	
Coupon print type	
Cpn.Prn.	
City names	
Cit.	
Table number	
Open date/time	
Client	
Table name	
Bill not paid total	
$FORMNAME	RestaurantSingleDSU
There are other barcodes in the last rent action	
E-mail	
$FORMNAME	RGMDatasetU
from	
to	
$FORMNAME	RGMDSParamsFram
Chart type	
Total ordered	
Total turnover	
Total payments	
Number of open tables	
Number of closed bills	
Average per Bill	
Number of voids	
Total of voids	
Number of discounts	
Total of discounts	
Number of reopens	
Total of reopens	
Number of reservations	
Number of transfers	
Average per customer	
Number of printed reports	
All items	
Grouped by hours	
$FORMNAME	SalesAreaDataSetParamsFram
Show "All sales areas" button	
$FORMNAME	SCDepositDlg
Payment	
Smart Card Deposit	
Date	
Amount	
$FORMNAME	SCEmbGroupsEntityManager
This smart group already added	
Name	
Card type	
$FORMNAME	SCGEmbEntityFram
New smart card group	
Smart card group	
$FORMNAME	SCGroupsEntityManager
New Smart Cards Group	
Smart Cards Group	
Kind	
Keyword	
Password	
Card type	
Data Security	
Prepaid Sector Security	
TTG Settings	
Supplier password	
unTill password	
Read Key	
Write Key	
Payment Mode	
Patrons	
Servings per Patron	
Validity, hrs	
0 - Unlimited	
Add Cards...	
Lookup Card...	
It is required to apply changes before adding cards. Continue?	
Card not found	
Smart Cards Groups	
$FORMNAME	ScheduleKindParamFram
Periodicity	
Voucher screen mode	
Create new voucher	
Show paid vouchers	
Show expired vouchers	
Adjustment type	
General	
New item	
New supplier	
Invoice	
Reorder	
$FORMNAME	SCPlanEntityManager
Service charge plan	
Service charge plan Name	
Item list is empty	
Service charge plans	
Number	
Name	
Service charge plan item	
Service charge related item	
Service charge amount	
$FORMNAME	SCPlanItemFram
New service charge plan item	
Service charge plan item	
Number	
Item	
Amount	
$FORMNAME	SCPlanItemsEntityManager
Number	
Amount(%)	
Item	
id_item	
Service charge plan item	
$FORMNAME	ScreenImageEntityManager
Comments	
$FORMNAME	SelectPrinterFram
Printer	
$FORMNAME	SendChangeMessage
New Course on table "%d" is fired !	
New Fast Lane item comes !	
New items come !	
New Bill on table "%d" comes !	
The following articles from course) "%s" on table "%d" changed:	
$FORMNAME	SendOverudeKSMsg
Course "%s" on table %d is waiting %d minutes for start preparation	
Course "%s" on table %d finished %d minutes ago	
$FORMNAME	SetSAParamsFram
Shared account	
$FORMNAME	ShowTransactionParamsFram
Transaction to show	
Not specified	
Current transaction	
Current order	
Current bill	
$FORMNAME	SmartCardDepositsU
SC Deposit print task	
$FORMNAME	SmartCardGroupsParamsFram
All Kinds	
Selected Kind	
$FORMNAME	SmartCardsEmbManager
TTG card balanced	
Unable to write TTG card	
Unable to read card data	
TTG card reactivated	
Not a TTG pre-paid card	
Error reading pre-paid Penko card	
Error writing pre-paid Penko card	
Not a Pre-paid Penko card	
Unable to store deposit on unTill smart card	
Unable to store deposit on Penko Smart Card	
Price	
UID	
Tap2Order Nr	
Client	
Card with the same UID already exists in database. Deactivate it?	
Smart Cards	
Card with the same UID already exists in database. Deactivate this card?	
Smart Card	
Read Card Balance	
Balance	
Turnover	
From	
Till	
TTG Kind	
Till Now	
Deposit	
Payment	
Update	
Reset Card	
Date/Time	
Kind	
Amount	
Initial	
Are you sure you want to reset the card balance?	
$FORMNAME	SortedTableDataSetParamsFram
Set Active background color	
Set Active font color (Used table color)	
$FORMNAME	SortOpenTableParamsFram
Sort field	
Original order	
Table	
Waiter	
Total	
Number of covers	
Original waiter	
$FORMNAME	TableAreaFontFormatDlg
Time format	
$FORMNAME	TableFontFram
Not defined	
$FORMNAME	TableKSEmbEntityManager
KS source	
KS destination	
Exception period	
$FORMNAME	TakeAwayActionParamsFram
First create order	
First add client	
$FORMNAME	TakeAwayMinMaxParamsFram
Data type	
Actual	
Potential	
$FORMNAME	TArticleDGEmbEntityManager
Article	
$FORMNAME	TArticleDGEntityFrame
New article	
Article	
Discount value	
$FORMNAME	TArticleFreeOptionEmbEntityManager
Option	
$FORMNAME	TArticleOptDefaultEmbEntityManager
Article	
$FORMNAME	TArticleParentsDataSet
Main price	
Option	
Bonus article	
$FORMNAME	TBLRTransferWaiter
Transfer of table:	
is rejected by	
Please accept table transfer(s)	
$FORMNAME	TCashDrawerTicketFrame
Cash	
Credit card	
Select payment	
All payments	
$FORMNAME	TClientDGEmbEntityManager
Discount group	
$FORMNAME	TClientDGEntityFram
New discount group	
Discount group	
$FORMNAME	TClientDGItemsEmbEntityManager
Discount group	
$FORMNAME	TClientDGItemsEntityFrame
New article	
Article	
Discount value	
New e-mail	
Email	
E-mail	
$FORMNAME	TCoursesDataSetParamsFrame
Only changeable courses	
All courses	
Only separated courses	
Article free options	
Option group free options	
Show options	
Show in Caption	
Show as Extra text	
Items in use	
Items in repair	
Sort by name	
Sort by Date/Time	
Show period name	
Show time range	
Show free reason	
$FORMNAME	TEditPriceArticleDataSet
Article price	
Option article price	
Bonus article price	
$FORMNAME	TEmailReportTicketFrame
From	
Till	
$FORMNAME	TInvoicesEmbEntityManager
Mail service is not configured	
Sending emails failed. Check network settings and recipient address	
$FORMNAME	TKSEntityFrame
Overview screen	
Detail screen	
Dashboard screen	
Consolidate screen	
Start with latest	
Start with lowest preparation time	
Start with oldest	
Manually ascending order	
Manually descending order	
Course number	
Article name	
Department name	
$FORMNAME	TKSPickupHistoryParamsFrame
Page number	
$FORMNAME	TMultiSelectItemsParamsFrame
All items	
Select by chair	
$FORMNAME	TransactionsParamsFram
All transactions	
Only paid transactions	
Only Orders in Future	
No conditions	
Only with Voids	
Only with Discounts	
$FORMNAME	TSCPlanEntityFrame
Number	
Name	
Category	
Group	
Department	
New Service charge plan	
Entire bill	
$FORMNAME	TShowOrderHistoryFrame
Limit result by	
History period	
One day	
One week	
One month	
One year	
All items	
$FORMNAME	TStatisticMeterPropFrame
Dataset name	
Sample text	
Show Arrow	
Low capacity color	
High capacity color	
Coloring type	
Radial gradient	
Sectors	
Meter type	
Linear	
Circle	
Arrow color	
Statistic meter	
$FORMNAME	TVoucherScreenParamsFrame
Only paid vouchers	
Only not paid vouchers	
$FORMNAME	UserEntityManager
Smartcard UID	
Reference	
Language	
Zip Code	
City	
VAT No	
$FORMNAME	VoidedArtParamsFram
Include voided orders	
$FORMNAME	VoucherEntityManager
Vouchers	
Barcode	
Value	
Creation date	
Used date	
Expiration date	
Remaining value	
Voucher:	
$FORMNAME	VoucherseFilterFram
Barcode	
Active	
Blocked	
Not paid	
Expired	
$FORMNAME	WaiterOrderScreenParamsFram
None	
User name	
User number	
$FORMNAME	XZReportsParamsFram
Show what	
Z-Reports	
X-Reports	
Sequential Sales area report	
$FORMNAME	BLRAlgStockTransferU
Transfer ingredient amount	
$FORMNAME	BLRReorderU
Item price	
Create stock order proposal	
Purchase order "%s" has been successfully created	
Confirm proposal	
Receive goods	
Purchase Order number:	
Purchase Order is in attachment	
Purchase order "%s" has been successfully received	
Confirm goods receivements	
Change stock purchase order	
Purchase order "%s" has been changed successfully.	
Update stock order	
Create stock purchase order	
The following Purchase orders have been successfully created:	
Confirm multi proposal	
Add not preffered items	
$FORMNAME	BLRStockArticleBalanceU
Enter ingredient Stock balance	
Enter ingredient physical amount	
Create Purchase order	
Ingredient editing	
Choose stocking location	
$FORMNAME	BLRSupplier
You are going to delete supplier %s. Are you sure?	
$FORMNAME	ChangeAmountFram
Quantity	
$FORMNAME	InventoryArticlesEmbEntityManager
Article N	
Article name	
Sales unity	
unity	
$FORMNAME	InvoiceItemFilterFram
Filter	
Supplier	
None	
Supplier & date	
$FORMNAME	PeriodIngNormalEmbEntityManager
Period	
Normal value	
$FORMNAME	PeriodParEmbEntityManager
Period	
Par value	
$FORMNAME	PeriodParEntityFram
New period par	
Period par	
$FORMNAME	PeriodParEntityManager
Normal amount	
Period	
Par amount	
$FORMNAME	StockCloseDayManager
There is a more recent close day document	
$FORMNAME	TStockInvoiceEntityFrame
Purchase order	
$FORMNAME	TStockInvoiceEntityManager
Stock calculations not performed on this PC	
$FORMNAME	TStockInvoiceItemsEntityFrame
Please select ingredient first	
$FORMNAME	TurnoverEmbEntityManager
Stock location	
Date	
Amount	
Unity	
Unit price	
Comments	
