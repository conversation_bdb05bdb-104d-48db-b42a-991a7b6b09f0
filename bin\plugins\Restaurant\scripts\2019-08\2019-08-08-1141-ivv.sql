create table ABG_SM_PRICES(
    id u_id,
    id_ARTICLE_BONUS_PRICES bigint,
    id_size_modifier_item bigint,
    price		decimal(17,4),	
    is_active 		smallint,
    IS_ACTIVE_MODIFIED 	timestamp,
    IS_ACTIVE_MODIFIER 	varchar(30),
    constraint ABG_SM_PRICES_pk primary key (id),
    constraint ABG_SM_PRICES_fk1 foreign key (ID_ARTICLE_BONUS_PRICES) references ARTICLE_BONUS_PRICES(id),
    constraint ABG_SM_PRICES_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on ABG_SM_PRICES to untilluser;
commit;
execute procedure register_sync_table_ex('ABG_SM_PRICES', 'b', 1);
commit;
execute procedure register_bo_table('ABG_SM_PRICES', 'id_ARTICLE_BONUS_PRICES', 'ARTICLE_BONUS_PRICES');
commit;
execute procedure register_bo_table('ABG_SM_PRICES', 'id_size_modifier_item', 'size_modifier_item');
commit;
