unit NUEnvironmentU;

interface
uses TFTranslation, Classes, SysUtils, TntCompatibilityU, ProgressU, SyncObjs, ClassesU;

type

  ENUException = class(Exception);

  ENUTranslatedException = class(Exception)
  private
    FMessage: WideString;
    procedure SetMessage(const Value: WideString);
  public
    property  Message: WideString read FMessage write SetMessage;
    constructor Create(AMessage: WideString);
  end;

  TEnvironment = class
  private
    FLang: TTranslationBase;
    FLangLock: TCriticalSection;
    FApplicationName: String;
  public
    constructor Create(AApplicationName: String);
    destructor  Destroy; override;
    function    GetLangList: TTntStringList;
    procedure   FreeLangList(List: TTntStringList);
    procedure   RaiseException(AMessage: String); overload;
    procedure   OnExcept(Sender: TObject; E: Exception);
    procedure   RaiseException(AMessage: String; const Args: array of const); overload;
    function    Translate(FormName: String; Template: String; Comment: String=''): WideString;
    function    TranslateLabel(FormName: String; Template: String; Comment: String=''): WideString;
    procedure   SetLang(LangId: String);
    property    ApplicationName: String read FApplicationName;
    function    ReadSetting(Section, Name: String; Default: String=''): String; overload;
    function    ReadSetting(Section, Name: String; Default: Integer=0): Integer; overload;
    procedure   WriteSetting(Section, Name: String; Value: String); overload;
    procedure   WriteSetting(Section, Name: String; Value: Integer); overload;
  end;

  TShowProgress = class(TInterfacedObject, IShowProgress)
  private
    procedure   ShowProgress(AName: WideString=''; ACurrent: Integer=0; ATotal: Integer=0);
    procedure   HideProgress;
  public
    constructor Create;
    destructor  Destroy; override;
    procedure   CheckTerminated;
    procedure   SetCaption(ACaption: WideString);
  end;

  function CreateProgress :IShowProgress;
  
var
  Plugin: TEnvironment;

implementation
uses Forms, StrUtils, UntillLogsU, Dialogs, IniFiles, Controls;

function CreateProgress :IShowProgress;
begin
  Result := TShowProgress.Create;
end;

{ TShowProgress }
var
  CounterShowProgress   : Integer;

procedure TShowProgress.CheckTerminated;
begin
end;

constructor TShowProgress.Create;
begin
  Inc(CounterShowProgress);
  if CounterShowProgress = 1 then
    ShowProgress;
end;

destructor TShowProgress.Destroy;
begin
  Dec(CounterShowProgress);
  if CounterShowProgress = 0 then
    HideProgress;
  inherited;
end;

procedure TShowProgress.HideProgress;
begin
  Screen.Cursor := crDefault;
end;

procedure TShowProgress.SetCaption(ACaption: WideString);
begin

end;

procedure TShowProgress.ShowProgress;
begin
  Screen.Cursor := crHourGlass;
end;

{ TEnvironment }

constructor TEnvironment.Create(AApplicationName: String);
begin
  FLang:=TTranslationBase.Create(nil);
  FApplicationName:=AApplicationName;
  FLangLock:=TCriticalSection.Create;
end;

destructor TEnvironment.Destroy;
begin
  FreeAndNil(FLang);
  FreeAndNil(FLangLock);
  inherited;
end;

function TEnvironment.GetLangList: TTntStringList;
var sr:TSearchRec;
    path:string;
    langid: String;
begin

  result:=TTntStringList.Create;

  path:=ExtractFileDir(Application.ExeName)+'\';
  if (FindFirst( path+'untillsms*.lng',faAnyfile,sr)<>0) then exit;

  repeat
    if (pos('-',sr.Name)>0) then continue;
    langid:=copy(sr.Name,length(sr.Name)-length('.lng')-3,4);
    if length(langid) < 4 then continue;
    result.AddObject(LangNameById(ShortString(langid)), TString.Create(langid));
  until FindNext(sr)<>0;
  FindClose(sr);
end;

procedure TEnvironment.RaiseException(AMessage: String);
begin
  raise ENUException.Create(AMessage);
end;

procedure TEnvironment.OnExcept(Sender: TObject; E: Exception);
var ws: WideString;
begin
  ExceptionLog.WriteExceptionInfo(E, '');
  if E is ENUException then
    ws:=Self.Translate('exceptions', ENUException(e).Message)
  else
    ws:=E.Message;
  MessageDlg(ws, mtError, [mbOk], 0);
end;

procedure TEnvironment.RaiseException(AMessage: String; const Args: array of const);
begin
  raise ENUException.CreateFmt(AMessage, Args);
end;

procedure TEnvironment.SetLang(LangId: String);
begin
//  {$ifdef UNTILL_RELEASE}
  if LangId <> DEFAULT_LANG_ID then
    FLang.LoadFromFile(ExtractFileDir(Application.ExeName)+'\untillsms'+LangId+'.lng')
  else
    FLang.Clear;
//  {$endif}
end;

function TEnvironment.Translate(FormName, Template, Comment: String): WideString;
begin
  FLangLock.Enter;
  try
    result:=FLang.Translate(Template);
  finally
    FLangLock.Leave;
  end;
end;

function TEnvironment.TranslateLabel(FormName, Template, Comment: String): WideString;
begin
  result:=Self.Translate(FormName, Template, Comment)+':';
end;

function TEnvironment.ReadSetting(Section, Name: String; Default: String=''): String;
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    result:=ini.ReadString(Section, Name, Default)
  finally
    FreeAndNil(ini);
  end;
end;

procedure TEnvironment.FreeLangList(List: TTntStringList);
var i: integer;
begin
  for i:=0 to List.Count-1 do
    if assigned(List.Objects[i]) then
      List.Objects[i].Free;
  FreeAndNil(List);
end;

function TEnvironment.ReadSetting(Section, Name: String; Default: Integer): Integer;
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    result:=ini.ReadInteger(Section, Name, Default)
  finally
    FreeAndNil(ini);
  end;
end;

procedure TEnvironment.WriteSetting(Section, Name, Value: String);
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    ini.WriteString(Section, Name, Value)
  finally
    FreeAndNil(ini);
  end;
end;

procedure TEnvironment.WriteSetting(Section, Name: String; Value: Integer);
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    ini.WriteInteger(Section, Name, Value)
  finally
    FreeAndNil(ini);
  end;
end;

{ ENUTranslatedException }

constructor ENUTranslatedException.Create(AMessage: WideString);
begin
  FMessage:=AMessage;
end;

procedure ENUTranslatedException.SetMessage(const Value: WideString);
begin
  FMessage := Value;
end;

initialization
  Plugin:=TEnvironment.Create('UntillSMS');
  Application.OnException:=Plugin.OnExcept;
  UntillLogsU.ApplicationName:='UntillSMS';

finalization
  FreeAndNil(Plugin);

end.

