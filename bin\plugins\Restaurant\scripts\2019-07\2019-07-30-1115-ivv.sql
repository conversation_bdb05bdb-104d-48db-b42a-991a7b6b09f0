create table OPTION_ARTICLE_SM_PRICES(
    id u_id,
    ID_OPTION_ARTICLE_PRICES 	bigint,
    id_size_modifier_item	bigint,
    price		decimal(17,4),	
    is_active 		smallint,
    IS_ACTIVE_MODIFIED 	timestamp,
    IS_ACTIVE_MODIFIER 	varchar(30),
    constraint OPTART_SM_PRICES_pk primary key (id),
    constraint OPTART_SM_PRICES_fk1 foreign key (ID_OPTION_ARTICLE_PRICES) references OPTION_ARTICLE_PRICES(id),
    constraint OPTART_SM_PRICES_fk2 foreign key (id_size_modifier_item) references size_modifier_item(id)
);
commit;
grant all on OPTION_ARTICLE_SM_PRICES to untilluser;
commit;
execute procedure register_sync_table_ex('OPTION_ARTICLE_SM_PRICES', 'b', 1);
commit;
execute procedure register_bo_table('OPTION_ARTICLE_SM_PRICES', 'id_size_modifier_item', 'size_modifier_item');
commit;
execute procedure register_bo_table('OPTION_ARTICLE_SM_PRICES', 'ID_OPTION_ARTICLE_PRICES', 'OPTION_ARTICLE_PRICES');
commit;
