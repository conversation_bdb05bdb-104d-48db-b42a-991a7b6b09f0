unit ClientEntityFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, EntityFram, StdCtrls, UntillComboBoxU,
  UntillButtonU, ExtCtrls,PNGImage, JPEG, RestaurantPluginU,
  UntillSelectBoxU, ComCtrls, CountryEntityManager, CurrencyEntityManager,
  UntillPageControl, UntillPanelU, UntillSpinEditU, ExtDlgs, PriceEntityManager,
  UntillCheckBoxU, ClPriceExceptionEmbManager, ShellApi,
  UntillDateTimePickerU, Buttons, UntillRealEdit, DepositEmbEntityManagerU,
  TagReadersU, AccountManager, ExtraFieldValuesEmbManagerU, SPTurnoverEntityManager,
  TntCompatibilityU, ClientDGEmbEntityManager, CheckLst, UntillCheckListBoxU,
  ClientSmartcardsEmbManagerU, ClientCardEmbManager, UntillMemoU;

type
  TClientEntityFrame = class(TEntityFrame)
    OpenPictureDialog: TOpenPictureDialog;
    pcClient: TUntillPageControl;
    ts1: TTntTabSheet;
    lblName: TTntLabel;
    lblCountry: TTntLabel;
    lblAddress: TTntLabel;
    lblInfo: TTntLabel;
    lblNumber: TTntLabel;
    lblAddressInformation: TBlockHeading;
    lblReference: TTntLabel;
    lblZipCode: TTntLabel;
    lblLanguage: TTntLabel;
    lblCity: TTntLabel;
    edtName: TTntEdit;
    usbCountry: TUntillSelectBox;
    memAddress: TUntillMemo;
    memInfo: TUntillMemo;
    seNumber: TUntillSpinEdit;
    dtpDateOfBirth: TUntillDateTimePicker;
    chbActive: TUntillCheckBox;
    dtpCreationDate: TUntillDateTimePicker;
    dtpExpirationDate: TUntillDateTimePicker;
    edtReference: TTntEdit;
    edtZipCode: TTntEdit;
    cmbLanguage: TUntillComboBox;
    edtCity: TUntillComboBox;
    lblCreationDate: TCheckBox;
    lblExpirationDate: TCheckBox;
    lblDateOfBirth: TCheckBox;
    ts2: TTntTabSheet;
    lblPhone: TTntLabel;
    lblFax: TTntLabel;
    lblEmail: TTntLabel;
    lblWebsite: TTntLabel;
    lblCode: TTntLabel;
    lblContacts: TBlockHeading;
    lblAdditional: TBlockHeading;
    lblPhone2: TTntLabel;
    lblInvoiceEmail: TTntLabel;
    lblVatNo: TTntLabel;
    edtPhone: TTntEdit;
    edtFax: TTntEdit;
    edtEmail: TTntEdit;
    edtWebsite: TTntEdit;
    edtCode: TTntEdit;
    edtPhone2: TTntEdit;
    edtInvoiceEmail: TTntEdit;
    edtVatNo: TTntEdit;
    pnlFdmFrance: TPanel;
    lblSiretNumber: TTntLabel;
    lblNafNumber: TTntLabel;
    edtSiretNumber: TTntEdit;
    edtNafNumber: TTntEdit;
    ts3: TTntTabSheet;
    lblLimit: TTntLabel;
    lblAccount: TTntLabel;
    lblSavePoints: TTntLabel;
    lblSaveAmount: TTntLabel;
    btnErasePicture: TUntillSpeedButton;
    lblLastInvoice: TTntLabel;
    lblAccountNumber: TTntLabel;
    lblInvoiceName: TTntLabel;
    chbOnInvoice: TUntillCheckBox;
    chbChange: TUntillCheckBox;
    dtpLastInvoiceDate: TUntillDateTimePicker;
    dtpLastInvoiceTime: TUntillDateTimePicker;
    usbLimCurrency: TUntillSelectBox;
    usbAccCurrency: TUntillSelectBox;
    btnLoadPicture: TUntillButton;
    panPicture: TUntillPanel;
    iPicture: TTntImage;
    seSaveAmount: TUntillSpinEdit;
    reLimit: TUntillRealEdit;
    reAccount: TUntillRealEdit;
    chkAccount: TUntillCheckBox;
    usbAccount: TUntillSelectBox;
    seSavePoints: TTntEdit;
    btnSpTurnover: TUntillButton;
    chkDisableSP: TUntillCheckBox;
    edtInvoiceName: TTntEdit;
    ts4: TTntTabSheet;
    lblPromotion: TTntLabel;
    lblExceptions: TBlockHeading;
    chbPrice: TUntillCheckBox;
    usbPrice: TUntillSelectBox;
    usbPromotion: TUntillSelectBox;
    panExceptions: TTntPanel;
    chbExempted: TUntillCheckBox;
    chbBusiness: TUntillCheckBox;
    ts5: TTntTabSheet;
    ts6: TTntTabSheet;
    panDeposits: TTntPanel;
    tsGarbageCollection: TTntTabSheet;
    lblDatum: TTntLabel;
    lblPostCode: TTntLabel;
    lblHuisNummer: TTntLabel;
    lblHuisLetter: TTntLabel;
    lblHuistoevoeging: TTntLabel;
    lblPlaatsnaam: TTntLabel;
    lblFreeVisits: TTntLabel;
    lblPricePrefix: TTntLabel;
    dtpDatum: TUntillDateTimePicker;
    edtPostCode: TTntEdit;
    edtHuisNummer: TTntEdit;
    edtHuisLetter: TTntEdit;
    edtHuistoevoeging: TTntEdit;
    edtPlaatsnaam: TTntEdit;
    edtFreeVisits: TTntEdit;
    edtPricePrefix: TTntEdit;
    chbCombinePrice: TUntillCheckBox;
    tsExtra: TTntTabSheet;
    lblAccNumber: TTntLabel;
    lblBookkeeping: TBlockHeading;
    panExtraFields: TTntPanel;
    edtAccountNumber: TTntEdit;
    tsDiscounts: TTabSheet;
    pnlDiscounts: TTntPanel;
    tbCards: TTabSheet;
    pnlSmartcards: TPanel;
    lblSmartcards: TBlockHeading;
    pnlCards: TPanel;
    lblCards: TBlockHeading;
    dtpCreationDateReal: TUntillDateTimePicker;
    rbCreationDate: TTntLabel;
    lblAccountNo: TTntLabel;
    edtAccountNo: TTntEdit;
    lblChamberNo: TTntLabel;
    edtChamberNo: TTntEdit;
    procedure usbCountryButtonClick(Sender: TObject);
    procedure usbAccCurrencyButtonClick(Sender: TObject);
    procedure usbLimCurrencyButtonClick(Sender: TObject);
    procedure btnLoadPictureClick(Sender: TObject);
    procedure usbPriceButtonClick(Sender: TObject);
    procedure usbPromotionButtonClick(Sender: TObject);
    procedure chbPriceClick(Sender: TObject);
    procedure edtWebsiteMouseMove(Sender: TObject; Shift: TShiftState; X,
      Y: Integer);
    procedure edtWebsiteClick(Sender: TObject);
    procedure edtWebsiteChange(Sender: TObject);
    procedure btnErasePictureClick(Sender: TObject);
    procedure chbOnInvoiceClick(Sender: TObject);
    procedure chkAccountClick(Sender: TObject);
    procedure usbAccountButtonClick(Sender: TObject);
    procedure btnSpTurnoverClick(Sender: TObject);
    procedure chkDisableSPClick(Sender: TObject);
    procedure edtZipCodeChange(Sender: TObject);
    procedure lblDateOfBirthClick(Sender: TObject);
    procedure lblCreationDateClick(Sender: TObject);
    procedure lblExpirationDateClick(Sender: TObject);
  private
    { Private declarations }
    emaillink,
    wwwlink     : boolean;
    procedure ResetImageControl(ImageControl: TImage);
  public

    bmp   : TBitmap;
    png   : TPNGImage;
    jpg   : TJPEGImage;

    emDG                  : TClientDGEmbEntityManager;
    emSPTurn              : TSPTurnoverEntityManager;
    emAccounts            : TAccountManager;
    emCountries           : TCountryEntityManager;
    emCurrency            : TCurrencyEntityManager;
    emPrices              : TPriceEntityManager;
    embmExceptions        : TClPriceExceptionEmbManager;
    embmDeposits          : TDepositEmbEntityManager;
    embmCards             : TClientCardEmbManager;
    embmExtraFields       : TExtraFieldValuesEmbManager;
    embmSmartCards        : TClientSmartcardsEmbManager;
    procedure   InitPicture;
    constructor Create(AOwner: TComponent); override;
    procedure   SetDateEnabled(dtp: TUntillDateTimePicker; AEnabled: Boolean);
    destructor  Destroy; override;
    procedure   TranslateStrings; override;
    { Public declarations }

  end;

var
  ClientEntityFrame: TClientEntityFrame;

implementation
uses commonu, ImageUtilsU, TntUtilsU,UntillEquU, RestaurantCommonStringsU,
  UntillAppU, UntillLogsU, ClientEntityManager, LookupCardFrm, ClientsU, LangU,
  CleanCashU, KernelSettingsU, ZipCodeU, DateUtils, ClientMandatoryFieldsU;
{$R *.dfm}

{ TClientEntityFrame }

constructor TClientEntityFrame.Create(AOwner: TComponent);
var i   : integer;
begin
  inherited;
  pnlFdmFrance.Visible := CleanCashU.IsFrenchFdmEnabledInLicense(UntillDB);

  bmp   := TBitmap.Create;
  png   := TPNGImage.Create;
  jpg   := TJPEGImage.Create;
  emaillink:=false;
  wwwlink:=false;
  emAccounts:= TAccountManager.Create(Self, UntillDB);
  emCountries:=TCountryEntityManager.Create(Self, UntillDB);
  emCurrency:=TCurrencyEntityManager.Create(Self, UntillDB);

  emPrices:=TPriceEntityManager.Create(Self, UntillDB);
  embmExceptions :=TClPriceExceptionEmbManager.Create(Self, Self.View.Manager, Self.ObjectId);
  embmExceptions.CreateListFrame(panExceptions);

  embmSmartCards:=TClientSmartcardsEmbManager.Create(Self, Self.View.Manager, Self.ObjectId);
  embmSmartCards.CreateListFrame(pnlSmartcards);

  emDG :=TClientDGEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId, false);
  emDG.CreateListFrame(pnlDiscounts);

  embmDeposits:=TDepositEmbEntityManager.Create(Self, Self.View.Manager, Self.ObjectId);
  embmDeposits.CreateListFrame(panDeposits);

  embmCards  := TClientCardEmbManager.Create(Self, Self.View.Manager, Self.ObjectId);
  embmCards.CreateListFrame(pnlCards);

  if (NewEntity)  then
    embmExtraFields:=TExtraFieldValuesEmbManager.Create(Self, Self.View.Manager, 0, CLIENTS_EXTRA_FIELDS)
  else
    embmExtraFields:=TExtraFieldValuesEmbManager.Create(Self, Self.View.Manager, StrToInt64Def(View.Manager.ClientDataSet.FieldByName('id_extra_field_values').AsString, 0), CLIENTS_EXTRA_FIELDS);
  embmExtraFields.CreateListFrame(panExtraFields);

   pcClient.TabIndex := 0;
  _DrawButtonGlyph(btnErasePicture, Plugin.GetImageFileName( 'delete.ico' ));

  if not UntillAppU.GarbageCollectionMode then
    tsGarbageCollection.TabVisible:=false;

  dtpDateOfBirth.DateTime := GetDefaultClientBirthdate();

  cmbLanguage.Clear;
  for i:=0 to LANGCOUNT-1 do
  	cmbLanguage.Items.Add(Languages[i].Name);

end;

destructor TClientEntityFrame.Destroy;
begin
  FreeAndNil(bmp);
  FreeAndNil(png);
  FreeAndNil(jpg);
  FreeAndNil(emDG);
  emCountries.Free;
  emCurrency.Free;
  embmExceptions.Free;
  embmDeposits.Free;
  emPrices.Free;
  FreeAndNil(emAccounts);
  FreeAndNil(embmExtraFields);
  inherited;
end;

procedure TClientEntityFrame.InitPicture;
begin
  btnErasePicture.Enabled := (not bmp.empty) or (not png.empty) or (not jpg.empty);
end;

procedure TClientEntityFrame.lblDateOfBirthClick(Sender: TObject);
begin
  inherited;
  SetDateEnabled(dtpDateOfBirth, lblDateOfBirth.Checked);
end;

procedure TClientEntityFrame.lblExpirationDateClick(Sender: TObject);
begin
  inherited;
  SetDateEnabled(dtpExpirationDate, lblExpirationDate.Checked);
end;

procedure TClientEntityFrame.usbCountryButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emCountries.ShowModal(usbCountry.Value);
  if res>0 then with usbCountry do begin
    Value:=res;
    Text := emCountries.GetWideStringById('name',res);
  end;
  usbCountry.SetFocus;
end;

procedure TClientEntityFrame.usbAccCurrencyButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emCurrency.ShowModal(usbAccCurrency.Value);
  if res>0 then with usbAccCurrency do begin
    Value:=res;
    Text := emCurrency.GetWideStringById('name',res);
  end;
  usbAccCurrency.SetFocus;
end;

procedure TClientEntityFrame.usbLimCurrencyButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emCurrency.ShowModal(usbLimCurrency.Value);
  if res>0 then with usbLimCurrency do begin
    Value:=res;
    Text := emCurrency.GetWideStringById('name',res);
  end;
  usbLimCurrency.SetFocus;
end;

procedure TClientEntityFrame.ResetImageControl(ImageControl : TImage);
var SaveBrush : TBrush;
begin
  ImageControl.Picture.Assign(nil);
  SaveBrush := TBrush.Create;
  try
    SaveBrush.Assign(ImageControl.Canvas.Brush);
    ImageControl.Canvas.Brush.Color := clBtnface;
    ImageControl.Canvas.FillRect(Rect(0,0,ImageControl.Width, ImageControl.Height));
    ImageControl.Canvas.Brush.Assign(SaveBrush);
  finally
    FreeAndNil(SaveBrush);
  end;
end;

procedure TClientEntityFrame.SetDateEnabled(dtp: TUntillDateTimePicker;
  AEnabled: Boolean);
begin
	dtp.Enabled := AEnabled;
  if AEnabled then begin
  	dtp.Format := FormatSettings.ShortDateFormat;
    if YearOf(dtp.Date) < 1900 then
    	dtp.Date := Now;
  end else begin
  	dtp.Format := ' ';
    dtp.DateTime := 0;
  end;
end;

procedure TClientEntityFrame.btnLoadPictureClick(Sender: TObject);
var ar    : TPolyArray;
begin
  if not OpenPictureDialog.Execute then exit;
  if OpenPictureDialog.FileName  = '' then exit;

   bmp.Assign(nil);
   png.Assign(nil);
   jpg.Assign(bmp);
   ResetImageControl(iPicture);
   if LowerCase(ExtractFileExt(OpenPictureDialog.FileName))='.bmp' then begin
     bmp.LoadFromFile(OpenPictureDialog.FileName);
     if bmp.empty then exit;

     ar := GetImgRect(iPicture.width, iPicture.height, bmp.Width, bmp.Height);
     FastDrawBitmap(iPicture.Canvas,bmp,ar);
   end else if LowerCase(ExtractFileExt(OpenPictureDialog.FileName))='.png' then begin
     png.LoadFromFile(OpenPictureDialog.FileName);

     if png.empty then exit;

     ar := GetImgRect(iPicture.width, iPicture.height, png.Width, png.Height);
     png.Draw(iPicture.Canvas, Rect(ar[0].X, ar[0].Y, ar[2].X, ar[2].Y) );
   end else if (LowerCase(ExtractFileExt(OpenPictureDialog.FileName))='.jpg')
    or (LowerCase(ExtractFileExt(OpenPictureDialog.FileName))='.jpeg') then begin
     jpg.LoadFromFile(OpenPictureDialog.FileName);

     if jpg.empty then exit;

     ar := GetImgRect(iPicture.width, iPicture.height, jpg.Width, jpg.Height);
     iPicture.Canvas.StretchDraw(Rect(ar[0].X, ar[0].Y, ar[2].X, ar[2].Y), jpg);
   end;

end;


procedure TClientEntityFrame.usbPriceButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  with usbPrice, emPrices do begin
    res:=ShowModal(Value);
    if res>0 then begin
      Value:=res;
      Text := GetWideStringById('name',res);
    end;
    SetFocus;
  end;
end;

procedure TClientEntityFrame.usbPromotionButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  with usbPromotion, emPrices do begin
    res:=ShowModal(Value);
    if res>0 then begin
      Value:=res;
      Text := GetWideStringById('name',res);
    end;
    SetFocus;
  end;
end;

procedure TClientEntityFrame.chbPriceClick(Sender: TObject);
begin
  inherited;
  if chbPrice.Checked then begin
    usbPrice.Enabled:=true;
    lblExceptions.Enabled:=true;
    RecursiveChangeEnabled(panExceptions, true);
    lblPromotion.Enabled:=true;
    usbPromotion.Enabled:=true;
  end else begin
    usbPrice.Enabled:=false;
    lblExceptions.Enabled:=false;
    RecursiveChangeEnabled(panExceptions, false);
    lblPromotion.Enabled:=false;
    usbPromotion.Enabled:=false;
  end;
end;

procedure TClientEntityFrame.lblCreationDateClick(Sender: TObject);
begin
  inherited;
  SetDateEnabled(dtpCreationDate, lblCreationDate.Checked);
end;

procedure TClientEntityFrame.TranslateStrings;
begin
  inherited;

  if NewEntity then
    Caption:=Plugin.Translate('ClientEntityManager','New Client')
  else
    Caption:=Plugin.TranslateLabel('ClientEntityManager','Client')+edtName.Text;

  lblAccNumber.Caption := Plugin.Translate('ClientEntityManager', 'Account Number');
  lblBookkeeping.Caption := Plugin.Translate('ClientEntityManager', 'Bookkeeping');
  lblSmartcards.Caption := Plugin.Translate('ClientEntityManager', 'Smartcards');
  lblCards.Caption := Plugin.Translate('ClientEntityManager', 'Cards');
  rbCreationDate.Caption := strCreationDate;

  lblInvoiceName.Caption := Plugin.Translate('ClientEntityManager', 'Invoice description');
  chbExempted.Caption := Plugin.Translate('ClientEntityManager', 'Exempted');
  chbBusiness.Caption := Plugin.Translate('ClientEntityManager', 'Business/Company');
  chkDisableSP.Caption := Plugin.Translate('ClientEntityManager', 'Enable save points');
  chkAccount.Caption := Plugin.Translate('ClientEntityManager', 'Shared account');
  ts1.Caption:=Plugin.Translate('ClientEntityManager', '&1 General');
  tbCards.Caption:=Plugin.Translate('ClientEntityManager', '&2 Cards');
  ts2.Caption:=Plugin.Translate('ClientEntityManager', '&3 Contacts');
  ts3.Caption:=Plugin.Translate('ClientEntityManager', '&4 Personal');
  ts4.Caption:=Plugin.Translate('ClientEntityManager', '&5 Price');
  ts5.Caption:=Plugin.Translate('ClientEntityManager', '&6 Promotions');
  ts6.Caption:=Plugin.Translate('ClientEntityManager', '&7 Deposits');
  tsExtra.Caption:='&'+IntToStr(tsExtra.TabIndex+1)+' '+Plugin.Translate('ClientEntityManager', 'Extra');
  tsGarbageCollection.Caption:='&'+IntToStr(tsGarbageCollection.TabIndex+1)+' '+Plugin.Translate('ClientEntityManager', 'Garbage collection');
  tsDiscounts.Caption:='&'+IntToStr(tsDiscounts.TabIndex+1)+' '+Plugin.Translate('ClientEntityManager', 'Discounts');

  lblName.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Client Name');
  lblNumber.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Number');
  lblAddressInformation.Caption:=Plugin.Translate('ClientEntityManager', 'Address Information');
  lblCountry.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Country');
  lblAddress.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Address');
  lblPhone.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Phone');
  lblPhone2.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Phone 2');
  lblFax.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Fax');
  lblEmail.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'E-mail');
  lblInvoiceEmail.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Invoice e-mail(s)');
  lblWebsite.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Website');
  lblContacts.Caption:=Plugin.Translate('ClientEntityManager', 'Contacts');
  lblAdditional.Caption:=Plugin.Translate('ClientEntityManager', 'Additional');
  lblCode.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Code');
  lblDateOfBirth.Caption:=Plugin.Translate('ClientEntityManager', 'Date Of Birth');
  lblLimit.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Limit');
  lblAccount.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Account');
  lblSavePoints.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Save Points');
  lblSaveAmount.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Save Amount');
  lblPromotion.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Promotion');
  lblExceptions.Caption:=Plugin.Translate('ClientEntityManager', 'Exceptions');
  lblInfo.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Info');
  lblLastInvoice.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Last invoice');
  chbChange.Caption:=Plugin.Translate('ClientEntityManager', 'Change');
  lblCreationDate.Caption:=Plugin.Translate('ClientEntityManager', 'Creation date');
  lblExpirationDate.Caption:=Plugin.Translate('ClientEntityManager', 'Expiration date');
  chbOnInvoice.Caption:=Plugin.Translate('ClientEntityManager', 'On invoice');
  btnLoadPicture.Caption:=Plugin.Translate('ClientEntityManager', 'Load &Picture');
  chbPrice.Caption:=Plugin.Translate('ClientEntityManager', 'Price');
  btnErasePicture.Hint:=Plugin.Translate('ClientEntityManager', 'Erase picture');
  lblDatum.Caption:=Plugin.Translate('ClientEntityManager', 'Date','Garbage collection client attributes');
  lblPostCode.Caption:=Plugin.Translate('ClientEntityManager', 'Postcode','Garbage collection client attributes');
  lblHuisNummer.Caption:=Plugin.Translate('ClientEntityManager', 'House number','Garbage collection client attributes');
  lblHuisLetter.Caption:=Plugin.Translate('ClientEntityManager', 'House letter','Garbage collection client attributes');
  lblHuistoevoeging.Caption:=Plugin.Translate('ClientEntityManager', 'House number addition','Garbage collection client attributes');
  lblPlaatsnaam.Caption:=Plugin.Translate('ClientEntityManager', 'Place','Garbage collection client attributes');
  lblFreeVisits.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Free visits');
  lblPricePrefix.Caption:=Plugin.TranslateLabel('ClientEntityManager', 'Price prefix');
  chbCombinePrice.Caption:=Plugin.Translate('ClientEntityManager', 'Combine prices');
  chbActive.Caption:=Plugin.Translate('ClientEntityManager', 'Active');
  btnSpTurnover.Caption := Plugin.Translate('ClientEntityManager', 'SP turnover...');
  lblReference.Caption := Plugin.TranslateLabel('UserEntityManager', 'Reference');
  lblLanguage.Caption := Plugin.TranslateLabel('UserEntityManager', 'Language');
  lblZipCode.Caption := Plugin.TranslateLabel('UserEntityManager', 'Zip Code');
  lblCity.Caption := Plugin.TranslateLabel('UserEntityManager', 'City');
  lblVatNo.Caption := Plugin.TranslateLabel('UserEntityManager', 'VAT No');
  lblSiretNumber.Caption := Plugin.TranslateLabel('ClientEntityManager','Siret Number');
  lblNafNumber.Caption := Plugin.TranslateLabel('ClientEntityManager','NAF Number');
  lblAccountNo.Caption := Plugin.TranslateLabel('ClientEntityManager', 'Account number');
  lblChamberNo.Caption := Plugin.TranslateLabel('ClientEntityManager', 'Chamber of Commerce');
end;

procedure TClientEntityFrame.edtWebsiteMouseMove(Sender: TObject;
  Shift: TShiftState; X, Y: Integer);
var cc:TControlCanvas;
begin
  inherited;
  cc:=TControlCanvas.Create;
  try
    cc.Control:=edtWebsite;
    if (x>=2)and(x<=2+cc.TextWidth(edtWebsite.text))
    and(y>=2)and(y<=2+cc.TextHeight(edtWebsite.text))
    and(wwwlink) then begin
      edtWebsite.Cursor:=crHandPoint;
    end else
      edtWebsite.Cursor:=crDefault;
  finally
    cc.free;
  end;
end;

procedure TClientEntityFrame.edtZipCodeChange(Sender: TObject);
var code : string;
    list : TStringList;
    i    : Integer;
begin
  inherited;
  code :=  Uppercase( Trim(edtZipCode.Text ));
  edtCity.Clear;
  edtCity.Text := '';
  if Length( code ) > 3 then begin
    list := TStringList.Create;
    try
      GetCityByZipCodes(UntillDB, code, list);
      if list.count=0 then exit;
      for i := 0 to Pred(list.count) do begin
        if edtCity.Text='' then
          edtCity.Text := list[i];
        edtCity.items.Add(list[i]);
      end;
      try
        edtCity.setFocus;
        SendMessage(edtCity.Handle, CB_SHOWDROPDOWN, 1, 0);
      except
      end;
    finally
      FreeAndNil(list);
    end;
  end;
end;

procedure TClientEntityFrame.edtWebsiteClick(Sender: TObject);
var link:string;
begin
  inherited;
  if edtWebSite.cursor=crHandPoint then begin
    link:=edtWebSite.Text;
    if trim(edtWebSite.Text)<>'' then
      ShellExecute(Application.Handle,'open',pChar(link),NIL,NIL,SW_SHOWNORMAL);
  end;
end;

procedure TClientEntityFrame.edtWebsiteChange(Sender: TObject);
begin
  inherited;
  if IsWWWLink(edtWebsite.text) then begin
    edtWebsite.Font.Color:=clHotLight;
    edtWebsite.Font.Style:=[fsUnderline];
    wwwlink:=true;
  end else begin
    edtWebsite.Font.Color:=clWindowText;
    edtWebsite.Font.Style:=[];
    wwwlink:=false;
  end;
end;

procedure TClientEntityFrame.btnErasePictureClick(Sender: TObject);
begin
  inherited;
  iPicture.Picture.Bitmap.Width:=0;
  iPicture.Picture.Bitmap.Height:=0;
  iPicture.Update;
  iPicture.AutoSize:=true;
  InitPicture;
end;

procedure TClientEntityFrame.chbOnInvoiceClick(Sender: TObject);
begin
  inherited;
  reLimit.Enabled:=not chbOnInvoice.Checked;
  lblLimit.Enabled:=not chbOnInvoice.Checked;

  RecursiveChangeEnabled(panDeposits, (not chbOnInvoice.Checked));

  lblLastInvoice.Enabled:= (chbOnInvoice.Checked)and(chbChange.Checked);
  dtpLastInvoiceDate.Enabled:= (chbOnInvoice.Checked)and(chbChange.Checked);
  dtpLastInvoiceTime.Enabled:= (chbOnInvoice.Checked)and(chbChange.Checked);
  chbChange.Enabled:=chbOnInvoice.Checked;
end;

procedure TClientEntityFrame.chkAccountClick(Sender: TObject);
begin
  inherited;
  if not chkAccount.checked then begin
    lblAccountNumber.Caption := '';
    usbAccount.text := '';
  end;
  tsDiscounts.TabVisible := not chkAccount.checked;
  usbAccount.Enabled  := chkAccount.checked;
end;

procedure TClientEntityFrame.chkDisableSPClick(Sender: TObject);
begin
  inherited;
  btnSpTurnover.Enabled := chkDisableSP.checked;
end;

procedure TClientEntityFrame.usbAccountButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=emAccounts.ShowModal(usbAccount.Value);
  if res>0 then with usbAccount do begin
    Value:=res;
    Text := emAccounts.GetWideStringById('name',res);
  end;
  lblAccountNumber.Caption := '(' + Plugin.TranslateLabel('ClientEntityManager','Number') + ' ' + emAccounts.GetWideStringById('number',res) + ')';
  usbAccount.SetFocus;
end;

procedure TClientEntityFrame.btnSpTurnoverClick(Sender: TObject);
begin
  inherited;
  emSPTurn := TSPTurnoverEntityManager.Create(Self, UntillDB, Self.ObjectId);
  try
    emSPTurn.ShowModal(0);
    seSavePoints.text := IntToStr(TClientEntityManager(self.View.Manager).GetSavePoints(Self.ObjectId));
  finally
    FreeAndNil(emSPTurn);
  end;
end;

end.
