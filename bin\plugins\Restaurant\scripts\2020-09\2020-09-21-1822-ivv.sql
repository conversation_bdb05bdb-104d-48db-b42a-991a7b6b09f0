set term !!;
create or alter procedure fix_service_charge_202009
as 
declare variable id_payment_sc bigint;
declare variable id_bill bigint;
declare variable dsc decimal(17,8);
begin
    select first 1 id from payments where payments.kind=6 and payments.is_active=1 into :id_payment_sc;
    if (:id_payment_sc=0) then exit;

    for
      select distinct bill.id, bill.discount
        from bill
        where coalesce(bill.service_charge,0)>0 and coalesce(bill.discount,0)>0 and coalesce(bill.discount,0)<>1.00
        into :id_bill, :dsc do begin

        update pbill_payments set
            price = price * (1.00 - :dsc),
            customer_amount = customer_amount * (1.00 - :dsc),
            c_price = c_price * (1.00 - :dsc),
            c_customer_amount = c_customer_amount * (1.00 - :dsc)
        where price>0 and id_payments=:id_payment_sc and id_pbill in (select id from pbill where id_bill=:id_bill);
    end
end
!!
commit
!!
grant execute on procedure fix_service_charge_202009 to untilluser
!!
commit
!!
set term ;!!


