create table KS_messages (
    id u_id,
    id_bill bigint,
    id_untill_users bigint,
    placed timestamp, 
    msg_text varchar(1024), 
    constraint KS_messages_pk primary key (id),
    constraint KS_messages_fk2 foreign key (id_untill_users) references untill_users (id),
    constraint KS_messages_fk3 foreign key (id_bill) references bill (id)
);
commit;
grant all on KS_messages to untilluser;
commit;
execute procedure register_sync_table_ex('KS_messages', 'p', 1);
commit;

create table KS_messages_viewed (
    id u_id,
    id_ks_messages bigint,
    id_ks bigint,
    id_untill_users bigint,
    constraint KS_messages_viewed_pk primary key (id),
    constraint KS_messages_viewed_fk1 foreign key (id_KS_messages) references KS_messages (id),
    constraint KS_messages_viewed_fk2 foreign key (id_ks) references kitchen_screens (id),
    constraint KS_messages_viewed_fk3 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on KS_messages_viewed to untilluser;
commit;
execute procedure register_sync_table_ex('KS_messages_viewed', 'p', 1);
commit;


