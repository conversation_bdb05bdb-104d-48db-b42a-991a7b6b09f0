alter table SETTINGS add dummy_flag smallint;
commit;

set term !! ;
CREATE OR ALTER TRIGGER bill_upd_trigger FOR BILL
active before update position 1
as
declare variable df smallint;
declare variable cnt smallint;
declare variable tcnt smallint;
begin
    select first 1 coalesce(dummy_flag,0) from settings into :df;
    if (df = 0) then begin
        if ((new.close_datetime is null) and (new.isactive=1) and (new.pbill_number is null)) then begin
            select count(*) from transferred_bills where transferred_bills.id_bill_result = new.id  into :tcnt;
            select count(*) from bill where id = new.id and close_datetime is not null and isactive=0 into :cnt;
            if ((cnt>0) and (tcnt=0)) then begin
                exception e_untill 'impossible to activate closed bill ' || new.id  ;
            end
        end
    end
end
!!
commit
!!
set term ; !!


