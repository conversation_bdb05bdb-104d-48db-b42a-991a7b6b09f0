create table order_item_allergens (
    id u_id,
    id_order_item bigint,
    id_menu_item bigint,
    id_allergens bigint,
    constraint oia_pk primary key (id),
    constraint oia_fk1 foreign key (id_order_item) references order_item(id),
    constraint oia_fk2 foreign key (id_menu_item) references menu_item(id),
    constraint oia_fk3 foreign key (id_allergens) references allergens(id)
);
commit;
grant all on order_item_allergens to untilluser;
commit;
execute procedure register_sync_table_ex('order_item_allergens', 'p', 0);
commit;


