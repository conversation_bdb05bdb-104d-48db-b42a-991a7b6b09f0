create table ks_wf_template (
    id u_id,
    name   varchar(100),
    description   varchar(250),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint kswft_pk primary key (id)
);
commit;
grant all on ks_wf_template to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_template', 'b', 1);
commit;
execute procedure register_bo_table('ks_wf_template', '', '');
commit;

