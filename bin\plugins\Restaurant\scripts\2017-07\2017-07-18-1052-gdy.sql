SET TERM ^ ;
CREATE OR ALTER procedure GETORDERITEMOSIG_AKS (
    ID_ORDERS bigint,
    ID_ORDER_ITEM bigint)
returns (
    OSIG varchar(1024))
as
begin
  execute procedure GETORDERITEMOSIG(:id_order_item, :id_orders) returning_values(:osig);
  suspend;
end^
SET TERM ; ^
commit;

CREATE OR ALTER VIEW ORDERED_OSIG(
    ID_ORDER_ITEM,
    OSIG)
AS
select order_item.ID, (select osig from GETORDERITEMOSIG(order_item.ID, order_item.ID_ORDERS))
from order_item;

CREATE OR ALTER VIEW ORDERED_OSIG_AKS(
    ID_ORDER_ITEM,
    OSIG)
AS
select order_item.ID, (select osig from GETORDER<PERSON>EMOSIG_AKS(order_item.ID, order_item.ID_ORDERS))
from order_item;

COMMIT;