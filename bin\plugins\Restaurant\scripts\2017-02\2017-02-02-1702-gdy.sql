CREATE INDEX AKS_SENT_TICKETS_IDX3 ON AKS_SENT_TICKETS (ID_TRANSFERRED_BILLS);
COMMENT ON COLUMN AKS_SENT_TICKETS.KIND IS 'NOT USED';
DROP INDEX AKS_SENT_TICKETS_IDX2;
-- unset 'not null' for id_ticket
ALTER TABLE AKS_SENT_TICKETS DROP ID_TICKET;
ALTER TABLE AKS_SENT_TICKETS ADD ID_TICKET INTEGER;
COMMENT ON COLUMN AKS_SENT_TICKETS.ID_TICKET IS 'NOT USED';
COMMIT;

-- just for recompile. Joins on order_item.id_menu = menu_item.id_menu = ORDERED_MI_OSIG.id_menu_item 
-- causes order_item full scan, i.e. index on order_item.id_menu is skipped
SET TERM ^ ;
CREATE OR ALTER procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable SEAT integer;
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text,
         order_item.CHAIR_NUMBER
         from menu_item
         join order_item on order_item.id_menu = menu_item.ID_MENU
  where menu_item.id >= :id_menu_item and order_item.QUANTITY > 0
  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text, :seat
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || 'mi' || kind || seat;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || '_' || id_articles;
    end
    idx = idx + 1;
  end
  suspend;
end;
^
SET TERM ; ^
COMMIT;

SET TERM ^ ;
CREATE OR ALTER procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint)
returns (
    OSIG varchar(1024))
as
begin
  execute procedure GETORDERITEMOSIG(:id_order_item) returning_values(:osig);
  suspend;
end;
^
SET TERM ; ^
COMMIT;
