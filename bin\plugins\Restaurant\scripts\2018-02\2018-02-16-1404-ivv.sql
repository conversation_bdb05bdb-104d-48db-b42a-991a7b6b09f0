create table ks_workflow_options (
	id 						u_id,
	id_KS_WORKFLOW_ITEMS 	bigint,	
	ID_ARTICLES				bigint,	
	text 					varchar(100),
    constraint kswo_pk 		primary key (id),
	constraint kswo_fk1 	foreign key (id_KS_WORKFLOW_ITEMS) references KS_WORKFLOW_ITEMS(id),
	constraint kswo_fk2 	foreign key (ID_ARTICLES) references ARTICLES(id)
);
commit;
grant all on ks_workflow_options to untilluser;
commit;
execute procedure register_sync_table_ex('ks_workflow_options', 'p', 1);
commit;


