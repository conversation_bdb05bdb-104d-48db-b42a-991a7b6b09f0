set term ^ ;
create or alter procedure get_first_available_caller
(id_group bigint, after_code varchar(50)) 
returns (id_caller bigint, code varchar(50), barcode varchar(50))
as 
declare variable ordering_mode integer;
declare variable number_distr integer;
declare variable c varchar(100);
declare variable o varchar(30);
declare variable x varchar(100);
declare variable stmt varchar(700);
begin    
    select ordering_mode, number_distr from caller_groups where id=:id_group into :ordering_mode, :number_distr;
    if (:ordering_mode = 1) then begin
        o = ' order by cast(code as numeric)';
    end else begin
        o = ' order by code';
    end
    if (coalesce(:after_code, '') != '') then begin
        c = ' and code > :after_code';
    end else begin
        c = '';
    end
    if (:number_distr = 1) then begin
        x = ' and not exists(select id_caller from caller_locks lock where lock.id_caller=callers.id and (current_timestamp - lock.lock_ts < 120))';
    end else begin
        x = '';
    end
    stmt = 'select first 1 id, code, barcode from callers where id_caller_groups='|| :id_group || ' and is_active=1 and id not in (' ||
        ' select r.id_callers from CAL_ORDERS r, callers c, bill b, orders o' ||
        ' where r.ID_ORDERS = o.id and r.inactive is null and o.ID_BILL = b.id and c.id = r.ID_CALLERS' ||
        ' and ((r.CLOSE_DATETIME is null) or (b.CLOSE_DATETIME is null)) ' || x ||
        ' )' || c || o;
    execute statement stmt into :id_caller, :code, :barcode;

    if (:number_distr = 1 and coalesce(:id_caller,0)!=0) then begin
        insert into caller_locks(id_caller, lock_ts) values (:id_caller, current_timestamp);
    end

    suspend;
end^
set term ; ^
commit;
