SET TERM ^ ;
CREATE OR ALTER procedure GET_TPAPI_TRANSACTION_ORDERS (
    AID_BILL bigint,
    ADATE_FROM timestamp,
    ADATE_TO timestamp,
    AID_SALES_AREA bigint,
    APAID smallint)
returns (
    ID_ORDERS bigint,
    ORD_DATETIME timestamp,
    PCNAME varchar(50),
    NUMBER varchar(50),
    ID_UNTILL_USERS bigint,
    ID_SALES_AREA bigint,
    GN_CODE varchar(50))
as
declare variable SQL_STR varchar(1024);
begin

  sql_str = ' select distinct orders.id, orders.ord_datetime, orders.pcname, orders.number || coalesce(orders.suffix,''''), orders.id_untill_users, orders.id_sales_area';
  sql_str = sql_str || ', callers.code gn_code';
  sql_str = sql_str || ' from orders';
  sql_str = sql_str || ' left outer join cal_orders on cal_orders.id_orders = orders.id ';
  sql_str = sql_str || ' left outer join callers on callers.id = cal_orders.id_callers ';
  if (apaid=1) then begin
    sql_str = sql_str || ' inner join order_item oi on oi.id_orders = orders.id ';
    sql_str = sql_str || ' inner join sold_articles sa on sa.id_order_item = oi.id ';
    sql_str = sql_str || ' inner join pbill pb on pb.id = sa.id_pbill ';
    sql_str = sql_str || ' where pb.pdatetime between :d1 and :d2 and pb.id_bill=:id_bill';
  end else
    sql_str = sql_str || ' where orders.ord_datetime between :d1 and :d2 and orders.id_bill=:id_bill';
  sql_str = sql_str || ' union all   ';
  sql_str = sql_str || ' select pbill.id, pbill.pdatetime, pbill.pcname, pbill.number || coalesce(pbill.suffix,''''), pbill.id_untill_users, pbill.id_sales_area, '''' gn_code ';
  sql_str = sql_str || ' from pbill';
  sql_str = sql_str || ' join PBILL_RETURN on PBILL_RETURN.id_pbill=pbill.id';
  sql_str = sql_str || ' where pbill.pdatetime between :d1 and :d2 and pbill.id_bill=:id_bill';
  for
    execute statement (sql_str) (id_bill:=aID_BILL, d1:=aDATE_FROM, d2:=aDATE_TO)
        into :id_orders, :ord_datetime, :pcname, :number, :id_untill_users, :id_sales_area, :gn_code
  do
    suspend;
end
^
SET TERM ; ^
commit;
