-- Recreate procedure GET_PASTEL_TURNOVER

SET TERM !! ;
ALTER PROCEDURE GET_PASTEL_TURNOVER (
    from_dt timestamp,
    to_dt timestamp)
returns (
    amount decimal(17,4),
    amount_vat decimal(17,4),
    dname varchar(50),
    snumber integer,
        sname varchar(50))
as
begin
  for
select Sum(order_item.price*sold_articles.quantity*sold_articles.sa_coef) amount,
Sum(order_item.vat*sold_articles.quantity*sold_articles.sa_coef) amount_vat,
sales_area.name sname, sales_area.number snumber, department.name dname 
from order_item 
inner join orders on orders.id=order_item.id_orders 
inner join sales_area on orders.id_sales_area=sales_area.id 
inner join bill on bill.id=orders.id_bill and bill.pbill_number is null 
inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id ) 
inner join pbill on (pbill.id = sold_articles.id_pbill) and (pbill.pdatetime>=:from_dt and pbill.pdatetime<:to_dt) 
join articles on articles.id = order_item.id_articles 
join department on department.id = articles.id_departament 
Group by 3,4,5 
union all 
select Sum(pbill_item.price*pbill_item.quantity) amount, 
Sum(pbill_item.vat*pbill_item.quantity) amount_vat, 
sales_area.name sname, sales_area.number snumber, department.name dname 
from pbill_item 
inner join pbill on pbill.id=pbill_item.id_pbill and (pbill.pdatetime>=:from_dt and pbill.pdatetime<:to_dt) 
inner join sales_area on pbill.id_sales_area=sales_area.id 
inner join bill on bill.id=pbill.id_bill and bill.pbill_number is null 
join pbill_return on pbill_return.id_pbill=pbill.id 
join articles on articles.id = pbill_item.id_articles 
join department on department.id = articles.id_departament 
Group by 3,4,5 
union all 
select Sum(coalesce(pbill_item.quantity, sold_articles.quantity*sold_articles.sa_coef) * menu_item.price * menu_item.quantity) amount,
Sum(coalesce(pbill_item.quantity, sold_articles.quantity*sold_articles.sa_coef)* menu_item.vat * menu_item.quantity) amount_vat,
coalesce(sa_r.name, sa_oi.name) sname, coalesce(sa_r.number, sa_oi.number) snumber, department.name dname 
from menu_item 
left outer join order_item 
inner join orders on orders.id=order_item.id_orders 
inner join bill b1 on b1.id=orders.id_bill and b1.pbill_number is null 
inner join sold_articles sold_articles on (sold_articles.id_order_item = order_item.id ) 
inner join pbill pb_sa on (pb_sa.id = sold_articles.id_pbill) and (pb_sa.pdatetime>=:from_dt and pb_sa.pdatetime<:to_dt) 
inner join sales_area sa_oi on orders.id_sales_area=sa_oi.id 
on order_item.id_menu = menu_item.id_menu 
left outer join pbill_item 
inner join pbill pb_r on (pb_r.id = pbill_item.id_pbill) and (pb_r.pdatetime>=:from_dt and pb_r.pdatetime<:to_dt) 
inner join bill b2 on b2.id=pb_r.id_bill and b2.pbill_number is null 
inner join sales_area sa_r on pb_r.id_sales_area=sa_r.id 
on pbill_item.id_menu = menu_item.id_menu 
join articles on articles.id = order_item.id_articles 
join department on department.id = articles.id_departament 
Group by 3,4,5
into :amount, :amount_vat, :sname, :snumber, :dname
  do begin
     suspend;
  end
end;
!!
SET TERM ; !!

COMMIT;
