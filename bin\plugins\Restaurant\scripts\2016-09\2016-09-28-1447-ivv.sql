create table kitchen_screens_link (
    id u_id,
    id_ks_from bigint,
    id_ks_to bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint ksl_pk primary key (id),
    constraint ksl_fk1 foreign key (id_ks_from) references kitchen_screens(id),
    constraint ksl_fk2 foreign key (id_ks_to) references kitchen_screens(id)
);
commit;
grant all on kitchen_screens_link to untilluser;
commit;
execute procedure register_sync_table_ex('kitchen_screens_link', 'b', 1);
commit;
execute procedure register_bo_table('kitchen_screens_link', 'id_ks_from', 'kitchen_screens');
commit;


