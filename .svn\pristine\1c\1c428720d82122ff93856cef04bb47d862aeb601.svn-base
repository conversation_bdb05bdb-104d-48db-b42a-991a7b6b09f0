unit StreamUtilsTestU;

interface
uses TestFramework;
type

  TStreamUtilsTest = class(TTestCase)
  public
    procedure SetUp; override;
    procedure TearDown; override;
  published
    procedure TestGetSCBPLU;
  end;

  function Suite: ITestSuite;

implementation
uses SysUtils, Classes, DateUtils, Windows, StreamUtilsU;

function Suite: ITestSuite;
begin
  Result := TTestSuite.Create(TStreamUtilsTest);
end;

{ TStreamUtilsTest }

procedure TStreamUtilsTest.SetUp;
begin
  inherited;
end;

procedure TStreamUtilsTest.TearDown;
begin
  inherited;
end;

procedure TStreamUtilsTest.TestGetSCBPLU;
var plu: String;
begin
  plu := GetSCBPLU(1, 'Pannekoek IJs&Chocola', 5.30, 'b', FormatSettings);
  assert(plu = '0001PANNEKOEKIJSCHOCOLA 00000530B');
end;


end.
