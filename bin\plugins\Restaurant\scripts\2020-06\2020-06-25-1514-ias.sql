alter table stock_balance add version int;
commit;

create generator gen_stock_balance_version;
commit;

create index stock_balance_version_index on stock_balance version;
commit;

set term ^ ;

create or alter trigger STOCK_TURNOVER_INS_UPD_TRIG for stock_turnover
after insert
as
declare cnt integer;
declare id_od bigint;
declare newbalance decimal(17,4);
begin
  if ((deleting) or (updating)) then
    exit;
  
  select count(*) from stock_balance where id_inventory_item=new.id_inventory_item and id_stock_locations=new.id_stock_locations into :cnt;

  if (:cnt = 0) then
    insert into stock_balance (id_inventory_item, amount, price, id_stock_locations, version) values (new.id_inventory_item, new.quantity, new.standart_price * new.quantity, new.id_stock_locations, gen_id(gen_stock_balance_version, 1));
  else begin
    update stock_balance set amount=amount + new.quantity where id_inventory_item = new.id_inventory_item and id_stock_locations=new.id_stock_locations;
    update stock_balance
       SET price = AMOUNT * (select sum(QUANTITY * STANDART_PRICE)/sum(QUANTITY)
         FROM stock_turnover st1
         Where st1.ID_INVENTORY_ITEM=stock_balance.ID_INVENTORY_ITEM
         and st1.QUANTITY<>0 and st1.entity_type in (1,2) 
         and id_inventory_item = new.id_inventory_item
         having Sum(QUANTITY)<>0),
         version = gen_id(gen_stock_balance_version, 1)
    where id_inventory_item = new.id_inventory_item;
  end

  select first 1 id from stock_open_day where conducted=0 order by sdatetime desc into :id_od;
  if (:id_od > 0) then
    update stock_open_day set conducted=1 where id=:id_od;

end^
commit^

CREATE OR ALTER TRIGGER STOCK_TURNOVER_UPD_TRIG FOR STOCK_TURNOVER
BEFORE UPDATE

as
declare old_price decimal(17,4);
declare qty double precision;
declare id_loc bigint;
declare id_inv bigint;
begin
  if ((deleting) or (inserting)) then
    exit;
  
  select standart_price, quantity, id_stock_locations, id_inventory_item from stock_turnover where id = new.id into :old_price, :qty, :id_loc, :id_inv;
  update stock_balance set price=price + (new.standart_price-:old_price) * :qty, version = gen_id(gen_stock_balance_version, 1) where id_stock_locations=:id_loc and id_inventory_item=:id_inv;
end
^
commit^
SET TERM ; ^


