set term !! ;
CREATE or ALTER PROCEDURE FIX_BLACK_SCREEN_BUTTONS
as
declare variable ser_srvone int;
declare variable ser_my int;
begin
	select first 1 ser from sync_db where is_active=1 and server_no=1 into :ser_srvone;
	select first 1 ser_sync_db from untill_db into :ser_my;
	if (ser_srvone=ser_my) then begin
	
      insert into article_button_setting(id_articles, id_screen_groups, color, font_name, font_size, font_attr, font_color)
        select articles.id, '9999999991',articles.hht_color,articles.hht_font_name,articles.hht_font_size,
        articles.hht_font_attr,articles.hht_font_color from articles where is_active=1 and 
        not exists(select * from article_button_setting a where a.id_articles=articles.id and id_screen_groups='9999999991');

      insert into article_button_setting(id_articles, id_screen_groups, color, font_name, font_size, font_attr, font_color)
        select articles.id, '9999999990',articles.pc_color,articles.pc_font_name,articles.pc_font_size,
        articles.pc_font_attr,articles.pc_font_color from articles where is_active=1 and 
        not exists(select * from article_button_setting a where a.id_articles=articles.id and id_screen_groups='9999999990');

	  insert into dep_button_setting(id_department, id_screen_groups, color, font_name, font_size, font_attr, font_color)
        select department.id, '9999999991',department.hht_color,department.hht_font_name,department.hht_font_size,
        department.hht_font_attr,department.hht_font_color from department where is_active=1 and 
        not exists(select * from dep_button_setting a where a.ID_DEPARTMENT=department.id and id_screen_groups='9999999991');


      insert into dep_button_setting(id_department, id_screen_groups, color, font_name, font_size, font_attr, font_color)
        select department.id, '9999999990',department.pc_color,department.pc_font_name,department.pc_font_size,
        department.pc_font_attr,department.pc_font_color from department where is_active=1 and 
        not exists(select * from dep_button_setting a where a.ID_DEPARTMENT=department.id and id_screen_groups='9999999990');

	end
end
!!
commit
!!
grant execute on procedure FIX_BLACK_SCREEN_BUTTONS to untilluser
!!
commit
!!
set term ; !!

execute procedure FIX_BLACK_SCREEN_BUTTONS;
commit;


