set term !! ;
create or alter procedure FIX_AKS_QUEUE_PURGE
as
begin
    if (exists(select 1 from rdb$relations where rdb$relation_name = 'T_TDBQUEUES')) then
        execute statement 'delete from T_TDBQUEUES where T_TDBQUEUES.QUEUE_ID = (select id from T_TNAMEDDBID where nname = ''eu.untill.ubl.aks.syncer.AKSQSyncer'');';
end
!!
commit
!!
grant execute on procedure FIX_AKS_QUEUE_PURGE to untilluser
!!
commit
!!
set term ; !!

execute procedure FIX_AKS_QUEUE_PURGE;
commit;
