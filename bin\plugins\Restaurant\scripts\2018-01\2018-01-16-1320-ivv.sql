create table ta_bills (
	id 				u_id,
	id_bill 		bigint,	
	timeblock_start timestamp,
	timeblock_end   timestamp,
	timeslot_start  timestamp,
	timeslot_end    timestamp,
	number 			integer, 
	FAILUREDNUMBER 	integer, 
	SUFFIX 			varchar(3),
    constraint ta_bills_pk primary key (id),
	constraint ta_bills_fk1 foreign key (id_bill) references bill(id)
);
commit;
grant all on ta_bills to untilluser;
commit;
execute procedure register_sync_table_ex('ta_bills', 'p', 1);
commit;


