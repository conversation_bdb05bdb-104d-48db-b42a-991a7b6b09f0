SET TERM ^ ;

CREATE OR ALTER TRIGGER STOCK_DOC_INS_TRIG FOR STOCK_DOC
ACTIVE AFTER INSERT POSITION 0
as
declare datetime timestamp;
declare stock_on smallint;
declare id_bill bigint;
begin
  if ((deleting) or (updating)) then
    exit;
  
  select stock_on from restaurant_vars into :stock_on;
  if (:stock_on=0) then exit;

  if (not (new.id_stock_cost_correction is null)) then begin
    select correction_date from stock_cost_correction where id = new.id_stock_cost_correction into :datetime;
    insert into stock_correction_queue (id_cost_correction, correction_datetime) values (new.id_stock_cost_correction, :datetime);
  end
  if (not (new.id_pbill is null)) then begin
    select pdatetime from pbill where id = new.id_pbill into :datetime;
    insert into stock_pbill_queue (id_pbill, pdatetime) values (new.id_pbill, :datetime);
  end
  if (not (new.id_orders is null)) then begin
    select ord_datetime,id_bill from orders where id = new.id_orders into :datetime,:id_bill;
    insert into stock_orders_queue (id_orders, ord_datetime) values (new.id_orders, :datetime);
  end
  if (not (new.id_stock_invoice is null)) then begin
    select invoice_date from stock_invoice where id = new.id_stock_invoice into :datetime;
    insert into stock_invoice_queue (id_invoice, inv_datetime) values (new.id_stock_invoice, :datetime);
  end
  if (not (new.id_stock_adjustment is null)) then begin
    select adjustment_date from stock_adjustment where id = new.id_stock_adjustment into :datetime;
    insert into stock_adjustment_queue (id_adjustment, adj_datetime) values (new.id_stock_adjustment, :datetime);
  end
end
^

SET TERM ; ^
