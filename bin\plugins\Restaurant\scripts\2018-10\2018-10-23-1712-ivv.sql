create table card_table_blacklist (
    id u_id,
    id_smartcards bigint,
    id_untill_users bigint,
    bl_dt timestamp,
    pcname varchar(50),
    bl_reason varchar(100),
    constraint card_table_blacklist_pk primary key (id),
    constraint card_table_blacklist_fk1 foreign key (id_smartcards) references smartcards (id),
    constraint card_table_blacklist_fk2 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on card_table_blacklist to untilluser;
commit;
execute procedure register_sync_table_ex('card_table_blacklist', 'p', 1);
commit;


