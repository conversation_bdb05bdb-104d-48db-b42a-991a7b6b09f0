ALTER TABLE AKS_SENT_TICKETS ADD ID_TRANSFERRED_BILLS BIGINT;
ALTER TABLE AKS_ITEM_ENTRIES ADD ID_BILL_CURRENT BIGINT;
COMMENT ON COLUMN AKS_ITEM_ENTRIES.ID_ARTICLES IS 'NOT USED';
COMMENT ON COLUMN AKS_ITEM_ENTRIES.ID_MENU_ARTICLES IS 'NOT USED';
UPDATE AKS_ITEM_ENTRIES SET ID_ARTICLES = NULL, ID_MENU_ARTICLES = NULL;
COMMIT;

SET TERM ^ ;
CREATE OR ALTER procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable SEAT integer;
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text,
         order_item.CHAIR_NUMBER from menu_item join order_item on order_item.id_menu = menu_item.ID_MENU
  where menu_item.id >= :id_menu_item and order_item.QUANTITY > 0
  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text, :seat
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || 'mi' || kind || seat;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || '_' || id_articles;
    end
    idx = idx + 1;
  end
  suspend;
end
^
SET TERM ; ^
COMMIT;

SET TERM ^ ;
CREATE OR ALTER procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable SEAT integer;
declare variable MENU_ITEM_OSIG varchar(1024);
declare variable ID_MENU_ITEM bigint;
declare variable ID_BILL bigint;
declare variable ID_MENU bigint;
begin
  execute procedure GETORDERITEMOSIG(:id_order_item) returning_values(:osig);

  select order_item.ID_MENU, order_item.CHAIR_NUMBER,  orders.ID_BILL from order_item
    join orders on orders.id = order_item.id_orders
    where order_item.id = :id_order_item into :id_menu, :seat, :id_bill;

  osig = :seat || osig;

  for select menu_item.ID from menu_item where menu_item.ID_MENU = :id_menu
    and menu_item.rowbeg = 1 into :id_menu_item do begin
    EXECUTE procedure GETMENUITEMOSIG(:id_menu_item) returning_values(:menu_item_osig);
    osig = osig || '/' || menu_item_osig;
  end
  suspend;
end;
^
SET TERM ; ^
COMMIT;


