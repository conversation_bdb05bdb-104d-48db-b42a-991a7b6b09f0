create table coupon_block (
    id u_id,
    id_coupons bigint,
    id_untill_users bigint,
    cpn_action_dt timestamp,
    cpn_action smallint,
    pc_name varchar(50),
    reason varchar(100),
    constraint cpnb_pk primary key (id),
    constraint cpnb_fk1 foreign key (id_coupons) references coupons(id),
    constraint cpnb_fk2 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on coupon_block to untilluser;
commit;
execute procedure register_sync_table_ex('coupon_block', 'p', 1);
commit;


alter table coupons add cpn_state smallint;
commit;

