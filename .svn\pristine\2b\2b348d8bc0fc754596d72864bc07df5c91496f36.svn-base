unit CustomerEmailEntityFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, EntityFram, StdCtrls, CommonStringsU,
  TntCompatibilityU, ExtCtrls, UntillPanelU, RestaurantPluginU, UntillMemoU;

type
  TCustomerEmailEntityFrame = class(TEntityFrame)
    pnlBonugGroup: TUntillPanel;
    lblEmail: TTntLabel;
    lblDesc: TTntLabel;
    edtEmail: TTntEdit;
    edtDesc: TUntillMemo;
  private
    { Private declarations }
  public
    { Public declarations }
    procedure TranslateStrings; override;
  end;

implementation
uses CommonU, EmbEntityManagerU;

{$R *.dfm}

{ TArticleBonusEntityFrame }

procedure TCustomerEmailEntityFrame.TranslateStrings;
begin
 inherited;
 if NewEntity then
   Caption:=Plugin.Translate('TClientDGItemsEntityFrame','New e-mail')
 else
   Caption:=Plugin.TranslateLabel('TClientDGItemsEntityFrame','Email') + edtEmail.Text;
 lblEmail.Caption:=Plugin.TranslateLabel('TClientDGItemsEntityFrame','E-mail');
 lblDesc.Caption:=StrDescCaption;
end;

end.
