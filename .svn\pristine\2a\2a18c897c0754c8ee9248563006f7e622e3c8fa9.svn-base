unit KSCounterEntityFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, EntityFram, UntillSelectBoxU, UntillSpinEditU, StdCtrls,
  TntCompatibilityU, ExtCtrls, UntillPanelU, RestaurantPluginU, UntillComboBoxU,
  UntillCheckBoxU, UntillGroupBoxU, TicketEntityManager, PrinterEntityManager,
  UntillRadioButtonU;

type
  TKSCounterEntityFrame = class(TEntityFrame)
    UntillGroupBox1: TUntillGroupBox;
    lblName: TTntLabel;
    lblNumber: TTntLabel;
    lblListCount: TTntLabel;
    edtListCount: TUntillSpinEdit;
    usNumber: TUntillSpinEdit;
    chkConsByChair: TUntillCheckBox;
    edtName: TTntEdit;
    chkShowOptionsPickup: TUntillCheckBox;
    chkSplitByCourses: TUntillCheckBox;
    usbKSprinter: TUntillSelectBox;
    usbKSticket: TUntillSelectBox;
    lblKSprinter: TTntLabel;
    lblKSticket: TTntLabel;
    chkShowInformation: TUntillCheckBox;
    lblType: TUntillGroupBox;
    rbRegular: TUntillRadioButton;
    rbExtended: TUntillRadioButton;
    chbShowPending: TUntillCheckBox;
    chkFinished: TUntillCheckBox;
    procedure usbKSticketButtonClick(Sender: TObject);
    procedure usbKSprinterButtonClick(Sender: TObject);
    procedure rbExtendedClick(Sender: TObject);
    procedure rbRegularClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    tem : TTicketEntityManager;
    pem : TPrinterEntityManager;
    procedure TranslateStrings; override;
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

implementation

uses EntityManagerU, CommonU;

{$R *.dfm}

{ TKSCounterEntityFrame }

constructor TKSCounterEntityFrame.Create(AOwner: TComponent);
begin
  inherited;
  tem:=TTicketEntityManager.Create(Self, UntillDB);
  pem:=TPrinterEntityManager.Create(Self, UntillDB);
end;

destructor TKSCounterEntityFrame.Destroy;
begin
  FreeAndNil(tem);
  FreeAndNil(pem);
  inherited;
end;

procedure TKSCounterEntityFrame.rbExtendedClick(Sender: TObject);
begin
  inherited;
  chbShowPending.Visible := rbExtended.Checked;
  chkFinished.Visible    := rbExtended.Checked;
end;

procedure TKSCounterEntityFrame.rbRegularClick(Sender: TObject);
begin
  inherited;
  chbShowPending.Visible := rbExtended.Checked;
  chkFinished.Visible    := rbExtended.Checked;
end;

procedure TKSCounterEntityFrame.TranslateStrings;
begin
  inherited;
  if NewEntity then
    Caption := Plugin.Translate('OptionArticleEmbEntityManager','New Pickup device')
  else
    Caption := Plugin.TranslateLabel('OptionArticleEmbEntityManager','Pickup device') + ' ' + edtName.Text;

 lblName.Caption              := Plugin.TranslateLabel('OptionArticleEmbEntityManager','Name');
 lblNumber.Caption            := Plugin.TranslateLabel('OptionArticleEmbEntityManager','Number');
 lblListCount.Caption         := Plugin.TranslateLabel('KSCounterEntityFram','Number of lists on screen');
 chkShowOptionsPickup.Caption := Plugin.Translate('KSCounterEntityFram','Show options');
 chkConsByChair.Caption       := Plugin.Translate('KSCounterEntityFram','Consolidate by chair');
 chkShowInformation.Caption   := Plugin.Translate('KSCounterEntityFram','Show items with Information purpose');
 chkSplitByCourses.Caption    := Plugin.Translate('KSCounterEntityFram','Split by courses');
 lblKSticket.Caption          := Plugin.TranslateLabel('CourseEntityManager','Pickup ticket');
 lblKSprinter.Caption         := Plugin.TranslateLabel('CourseEntityManager','Pickup printer');
 lblType.Caption              := Plugin.TranslateLabel('CourseEntityManager','Screen type');
 rbRegular.Caption            := Plugin.Translate('CourseEntityManager','Regular');
 rbExtended.Caption           := Plugin.Translate('CourseEntityManager','Extended');
 chbShowPending.Caption       := Plugin.Translate('CourseEntityManager','Show pending articles');
 chkFinished.Caption       := Plugin.Translate('CourseEntityManager','Cross out items');
end;

procedure TKSCounterEntityFrame.usbKSprinterButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=pem.ShowModal(usbKSPrinter.Value);
  if res>0 then with usbKSPrinter do begin
    Value:=res;
    Text:=pem.GetWideStringById('name',res);
  end;
  usbKSPrinter.SetFocus;
end;

procedure TKSCounterEntityFrame.usbKSticketButtonClick(Sender: TObject);
var res:Int64;
begin
  inherited;
  res:=tem.ShowModal(usbKSTicket.Value);
  if res>0 then with usbKSTicket do begin
    Value:=res;
    Text:=tem.GetWideStringById('name',res);
  end;
  usbKSTicket.SetFocus;
end;

end.
