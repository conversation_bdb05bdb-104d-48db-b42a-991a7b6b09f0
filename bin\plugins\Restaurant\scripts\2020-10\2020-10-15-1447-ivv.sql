create table smartcard_table_cards(
    id u_id,
    id_smartcard_tables bigint,	 
    id_smartcards     bigint,
    rfid_code varchar(100),
    is_active             smallint,
    IS_ACTIVE_MODIFIED         timestamp,
    IS_ACTIVE_MODIFIER         varchar(30),
    constraint smtc_pk primary key (id),
    constraint smtc_fk0 foreign key (id_smartcards) references smartcards(id),
    constraint smtc_fk1 foreign key (id_smartcard_tables) references smartcard_tables(id)
);
commit;
grant all on smartcard_table_cards to untilluser;
commit;
execute procedure register_sync_table_ex('smartcard_table_cards', 'b', 1);
commit;
execute procedure register_bo_table('smartcard_table_cards', 'id_smartcards', 'smartcards');
execute procedure register_bo_table('smartcard_table_cards', 'id_smartcard_tables', 'smartcard_tables');
commit;
