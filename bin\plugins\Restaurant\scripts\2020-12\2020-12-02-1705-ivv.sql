alter table kitchen_screens add alter_minutes integer;
commit;

create table kitchen_screen_alert_periods(
    id u_id,
    id_kitchen_screens bigint,	 
    id_periods	       bigint,
    is_active             smallint,
    IS_ACTIVE_MODIFIED         timestamp,
    IS_ACTIVE_MODIFIER         varchar(30),
    constraint ksap_pk primary key (id),
    constraint ksap_fk0 foreign key (id_kitchen_screens) references kitchen_screens(id),
    constraint ksap_fk1 foreign key (id_periods) references periods(id)
);
commit;
grant all on kitchen_screen_alert_periods to untilluser;
commit;
execute procedure register_sync_table_ex('kitchen_screen_alert_periods', 'b', 1);
commit;
execute procedure register_bo_table('kitchen_screen_alert_periods', 'id_kitchen_screens', 'kitchen_screens');
execute procedure register_bo_table('kitchen_screen_alert_periods', 'id_periods', 'periods');
commit;

