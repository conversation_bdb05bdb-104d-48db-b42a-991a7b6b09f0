create table ks_workflow_ks (
    id u_id,
    ID_KS_WORKFLOW bigint,
    ID_KS_MAIN bigint,
    ID_KS_SUB bigint,
    ID_untill_users bigint,
    datetime timestamp,
    constraint kswks_pk primary key (id),
	constraint kswks_FK1 foreign key (ID_KS_MAIN) references kitchen_screens(id),
	constraint kswks_FK11 foreign key (ID_KS_SUB) references kitchen_screens(id),
	constraint kswks_FK2 foreign key (ID_KS_WORKFLOW) references KS_WORKFLOW(id),
	constraint kswks_FK3 foreign key (ID_untill_users) references untill_users(id)
	);
commit;
execute procedure register_sync_table_ex('ks_workflow_ks', 'p', 1);
commit;
grant all on ks_workflow_ks to untilluser;
commit;

