CREATE TABLE card_active (
	id u_id,
    	ID_SMARTCARDS bigint,
	active_dt timestamp,
    	id_untill_users bigint,
        pcname varchar(50),
        state  integer,
    constraint card_active_pk primary key (id),
    constraint card_active_fk1 foreign key (ID_SMARTCARDS) references SMARTCARDS (id),
    constraint card_active_fk2 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on card_active to untilluser;
commit;
execute procedure register_sync_table_ex('card_active', 'p', 1);
commit;

