create table SmartCardOrderGroups (
    id u_id,
    id_SMARTCARDS bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint SmartCardOrderGroups_PK primary key (id),
    constraint SmartCardOrderGroups_FK1 foreign key (id_SMARTCARDS) references SMARTCARD_GROUPS(id)
);
commit;
grant all on SmartCardOrderGroups to untilluser;
commit;
execute procedure register_sync_table_ex('SmartCardOrderGroups', 'b', 1);
commit;
execute procedure register_bo_table('SmartCardOrderGroups', 'id_SMARTCARDS', 'SMARTCARD_GROUPS');
commit;

