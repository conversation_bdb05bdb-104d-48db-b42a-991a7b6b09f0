set term !! ;
create or alter procedure fill_price_vat
as 
  declare variable VAT_TYPE smallint;
begin
        
    select first 1 coalesce(VAT_TYPE,0) from RESTAURANT_VARS INTO VAT_TYPE;
    execute procedure set_logging_cs;

    if (:VAT_TYPE = 0) then begin
        update order_item set price_vat=price, price_no_vat = price - vat where price is not null;
        update MENU_ITEM set price_vat=price, price_no_vat = price - vat where price is not null;
        update PBILL_ITEM set price_vat=price, price_no_vat = price - vat where price is not null;
        update ACCOUNTS_MENU_ITEM set price_vat=price, price_no_vat = price - vat where price is not null;
        update CANCEL_ORDER_ITEM set price_vat=price, price_no_vat = price - vat where price is not null;
        update ORDER_ITEM_SIZES set price_vat=price, price_no_vat = price - vat where price is not null;
    end else begin    
        update order_item set price_vat=price+vat, price_no_vat = price where price is not null;
        update MENU_ITEM set price_vat=price+vat, price_no_vat = price where price is not null;
        update PBILL_ITEM set price_vat=price+vat, price_no_vat = price where price is not null;
        update ACCOUNTS_MENU_ITEM set price_vat=price+vat, price_no_vat = price where price is not null;
        update CANCEL_ORDER_ITEM set price_vat=price+vat, price_no_vat = price where price is not null;
        update ORDER_ITEM_SIZES set price_vat=price+vat, price_no_vat = price where price is not null;
    end
    execute procedure set_logging_on;

end;

!!
commit
!!

execute procedure fill_price_vat;
!!

set term ; !!
