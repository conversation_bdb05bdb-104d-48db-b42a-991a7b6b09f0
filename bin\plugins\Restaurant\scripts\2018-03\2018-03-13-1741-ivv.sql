SET TERM !! ;

create or alter procedure GET_TPAPI_TRANSACTION_OI (
    aID_ORDERS bigint,
    aDATE_FROM timestamp,
    aDATE_TO timestamp,
    apaid smallint)
returns (
    id bigint,
    id_articles bigint,
    kind smallint,
    sa_coef double precision,
    qty integer,
    quantity integer,
    menu_quantity integer,
    price decimal(17,4),
    discount double precision,
    vat double precision,
    original_price decimal(17,4),
    text varchar(50),
    vat_percent decimal(17,4),
    hq_id varchar(50),
    id_menu bigint,
    void_id bigint,
    discount_name varchar(50),
    free_comments varchar(250))
as
declare variable sql_str varchar(1024);
begin

   if (apaid=1) then begin
     sql_str = ' select oi.id, oi.id_articles, oi.kind, sa.sa_coef, sa.quantity qty, oi.quantity, oi.menu_quantity, oi.price,'
        ||  ' b.discount discount, oi.vat, oi.original_price, oi.text, oi.vat_percent,'
        ||  ' a.hq_id, null void_id, dr.name discount_name, di.free_comments, oi.id_menu '
        || ' from order_item oi '
        || ' inner join articles a on a.id = oi.id_articles '
        || ' inner join sold_articles sa on sa.id_order_item = oi.id '
        || ' inner join pbill pb on pb.id = sa.id_pbill '
        || ' inner join bill b on b.id = pb.id_bill '
        || ' left outer join discounted_items di on di.id_order_item=oi.id '
        || ' left outer join discount_reasons dr on dr.id = di.discount_reason '
        || ' where '
        || ' oi.id_orders=:id_order '
        || ' and a.article_hash = 0 '
        || ' and pb.pdatetime between :d1 and :d2';
     sql_str = sql_str || ' union all';
     sql_str = sql_str || ' select oi.id, oi.id_articles, oi.kind, 1.00 sa_coef, oi.quantity qty, oi.quantity, oi.menu_quantity, oi.price,'
        ||  ' null discount_name, oi.vat, oi.original_price, oi.text, oi.vat_percent, '
        ||  ' a.hq_id, null void_id, 0 discount,  null free_comments, oi.id_menu '
        || ' from pbill_item oi '
        || ' inner join articles a on a.id = oi.id_articles '
        || ' inner join pbill pb on pb.id = oi.id_pbill '
        || ' inner join PBILL_RETURN on pb.id = PBILL_RETURN.id_pbill '
        || ' inner join bill b on b.id = pb.id_bill '
        || ' where oi.id_pbill=:id_order '
        || ' and pb.pdatetime between :d1 and :d2';
     for execute statement (sql_str) (id_order:=aID_ORDERS, d1:=aDATE_FROM, d2:=aDATE_TO)
        into :id, :id_articles,:kind,:sa_coef,:qty,:quantity,:menu_quantity,:price,:discount,:vat,
             :original_price, :text, :vat_percent, :hq_id, :void_id, :discount_name, :free_comments, :id_menu
    do suspend;
  end else begin
    sql_str = ' select oi.id, oi.id_articles, oi.kind, 1.00 sa_coef, oi.quantity qty, oi.quantity, oi.menu_quantity, oi.price,'
       ||  ' b.discount discount, oi.vat, oi.original_price, oi.text, oi.vat_percent, '
       ||  ' a.hq_id, vi.id void_id, dr.name discount_name, di.free_comments, oi.id_menu '
       ||  ' from order_item oi '
       ||  ' inner join articles a on a.id = oi.id_articles and a.article_hash = 0 '
       ||  ' inner join orders o on o.id = oi.id_orders '
       ||  ' inner join bill b on o.id_bill = b.id '
       ||  ' left outer join discounted_items di on oi.id = di.id_order_item '
       ||  ' left outer join discount_reasons dr on dr.id = di.discount_reason '
       ||  ' left outer join voided_items vi on vi.id_order_item=oi.id '
       ||  ' where oi.id_orders=:id_order';
     sql_str = sql_str || ' union all';
     sql_str = sql_str || ' select oi.id, oi.id_articles, oi.kind, 1.00 sa_coef, oi.quantity qty, oi.quantity, oi.menu_quantity, oi.price,'
        ||  ' null discount_name, oi.vat, oi.original_price, oi.text, oi.vat_percent, '
        ||  ' a.hq_id, null void_id, 0 discount,  null free_comments, oi.id_menu '
        || ' from pbill_item oi '
        || ' inner join articles a on a.id = oi.id_articles '
        || ' inner join pbill pb on pb.id = oi.id_pbill '
        || ' inner join PBILL_RETURN on pb.id = PBILL_RETURN.id_pbill '
        || ' inner join bill b on b.id = pb.id_bill '
        || ' where oi.id_pbill=:id_order ';
    for execute statement (sql_str) (id_order:=aID_ORDERS)
        into :id, :id_articles, :kind, :sa_coef, :qty, :quantity, :menu_quantity, :price,:discount,:vat,
             :original_price, :text, :vat_percent, :hq_id, :void_id, :discount_name,:free_comments, :id_menu
    do suspend;
  end
end
!!
commit
!!

grant execute on procedure GET_TPAPI_TRANSACTION_OI to untilluser!!

SET TERM ; !!



