[Start]
**************************************************
TimeStamp=10.09.2018 16:55
Interval=0;30.12.1899
[Class]
Name=TBLMessageKeyboardString
TimeStamp=10.09.2018 16:55
Interval=1.22685232781805E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=10.09.2018 16:55
Interval=1.32870336528867E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=11
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=10.09.2018 16:55
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=11
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=10.09.2018 16:55
Interval=8.86573980096728E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=2
id=5000000120
id_department=5000000120
[Class]
Name=TBLRMsgArticle
TimeStamp=10.09.2018 16:55
Interval=2.61226887232624E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=25000097600
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgArticle
TimeStamp=10.09.2018 16:55
Interval=2.07407356356271E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=25000090675
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:55
Interval=1.50000050780363E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>    <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
                             END OF ORDER                                       

<<<Pos ticket
>>>Pos ticket
Printer=Keuken Printer: Ticket=Order-kueken
                         ORDER-KUEKEN                                           
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
KeukenPU       Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>    <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
 1      Separate1KeukenPU    1,70     1,70   0,30    1,40 21,0%         1,40    
                             3,40            0,59                               
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:55
Interval=1.88657359103672E-5;30.12.1899 0:00:01
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:55
Interval=1.29976906464435E-5;30.12.1899 0:00:01
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:55
Interval=1.27314706332982E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:56
Interval=0.000151516200276092;30.12.1899 0:00:13
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKSPickupInfoDataSet
1;Separate1KeukenPU2;0;1
1;Separate1KeukenPU;0;1

<<<Dataset: TKSPickupInfoDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKSPickupInfoDataSet
Fields=quantity;article;purpose;wf_status
StoredParams=
[Class]
Name=TBLMessageCancel
TimeStamp=10.09.2018 16:56
Interval=0.00015983796765795;30.12.1899 0:00:13
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRNeedKitchenScreenPro
TimeStamp=10.09.2018 16:56
Interval=1.12384223029949E-5;30.12.1899
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=2
billTimeType=ksbtNormal
ksnumber=2
[Class]
Name=TBLRMsgNeedDetailedKS
TimeStamp=10.09.2018 16:56
Interval=7.1875037974678E-6;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=2
billTimeType=ksbtNormal
TableItemNumber=1
[Class]
Name=TBLRMsgEndCountDown
TimeStamp=10.09.2018 16:56
Interval=2.12152735912241E-5;30.12.1899 0:00:01
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=0
[Class]
Name=TBLRMsgMoveKSWFNextStep
TimeStamp=10.09.2018 16:56
Interval=2.31484591495246E-7;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:56
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=0
[Class]
Name=TBLRMsgNeedDetailedKS
TimeStamp=10.09.2018 16:56
Interval=2.42245369008742E-5;30.12.1899 0:00:02
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=2
billTimeType=ksbtNormal
TableItemNumber=1
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:56
Interval=8.80786683410406E-6;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:56
Interval=2.31484591495246E-7;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:56
Interval=0.000302372682199348;30.12.1899 0:00:26
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
StateData=
>>>Dataset: TKitchenScreenDetailDataSet
1;Separate1KeukenPU;Separate1;Preparation;2
1;Separate1KeukenPU2;Separate1;Information;1

<<<Dataset: TKitchenScreenDetailDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TKitchenScreenOverviewDataSet
DSName=TKitchenScreenDetailDataSet
Fields=quantity;article;course;purpose;ks_status
StoredParams=
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:56
Interval=2.21412046812475E-5;30.12.1899 0:00:01
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:57
Interval=9.32291659410112E-5;30.12.1899 0:00:08
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:57
Interval=3.16087971441448E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:57
Interval=1.04169885162264E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:57
Interval=0.000146284721267875;30.12.1899 0:00:12
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Dataset: TKitchenScreenCounterDataSet

<<<Dataset: TKitchenScreenCounterDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKitchenScreenCounterDataSet
Fields=tableno;quantity
StoredParams=
[Start]
**************************************************
TimeStamp=10.09.2018 16:58
Interval=0;30.12.1899
[Class]
Name=TBLRNeedKitchenScreenPro
TimeStamp=10.09.2018 16:58
Interval=7.01388489687815E-6;30.12.1899
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=2
billTimeType=ksbtNormal
ksnumber=2
[Class]
Name=TBLRMsgNeedDetailedKS
TimeStamp=10.09.2018 16:58
Interval=7.14120687916875E-6;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=2
billTimeType=ksbtNormal
TableItemNumber=1
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:58
Interval=1.54282388393767E-5;30.12.1899 0:00:01
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:58
Interval=1.83564843609929E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:58
Interval=1.03819402283989E-5;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:58
Interval=2.31712983804755E-5;30.12.1899 0:00:02
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:58
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:58
Interval=0.000161817129992414;30.12.1899 0:00:13
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKSPickupInfoDataSet
1;Separate1KeukenPU2;1;0
1;Separate1KeukenPU;2;0

<<<Dataset: TKSPickupInfoDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKSPickupInfoDataSet
Fields=quantity;article;wf_status;purpose
StoredParams=
[Class]
Name=TBLRMsgKSFinishItemByIdBill
TimeStamp=10.09.2018 16:58
Interval=0.000105868057289626;30.12.1899 0:00:09
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_bill=25000097768
[Class]
Name=TBLSMsgBeforeLayerVisibility
TimeStamp=10.09.2018 16:58
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLSMsgAfterLayerVisibility
TimeStamp=10.09.2018 16:58
Interval=0;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLRMsgKSCompleteBill
TimeStamp=10.09.2018 16:58
Interval=6.01851934334263E-6;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:58
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:58
Interval=1.40856500365771E-5;30.12.1899 0:00:01
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:58
Interval=1.39814801514149E-5;30.12.1899 0:00:01
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:58
Interval=1.27314706332982E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:59
Interval=0.000137766204716172;30.12.1899 0:00:11
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKSPickupInfoDataSet
1;Separate1KeukenPU2;1

<<<Dataset: TKSPickupInfoDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKSPickupInfoDataSet
Fields=quantity;article;wf_status
StoredParams=
[Class]
Name=TBLMessageCancel
TimeStamp=10.09.2018 16:59
Interval=5.85300949751399E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:59
Interval=3.74421288142912E-5;30.12.1899 0:00:03
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLMessageCancel
TimeStamp=10.09.2018 16:59
Interval=1.4247685612645E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRNeedKitchenScreenPro
TimeStamp=10.09.2018 16:59
Interval=8.34490492707118E-6;30.12.1899
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=2
billTimeType=ksbtNormal
ksnumber=1
[Class]
Name=TBLRMsgNeedDetailedKS
TimeStamp=10.09.2018 16:59
Interval=5.68287214264274E-6;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=2
billTimeType=ksbtNormal
TableItemNumber=1
[Class]
Name=TBLRMsgEndCountDown
TimeStamp=10.09.2018 16:59
Interval=7.41898111300543E-6;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=0
[Class]
Name=TBLRMsgMoveKSWFNextStep
TimeStamp=10.09.2018 16:59
Interval=3.47223249264061E-7;30.12.1899
Exception=
StateGUID={D0F8CEB1-253F-4686-8660-721BEE730C32}
StateName=TBLRKSDetailPro
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:59
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D7FCF664-4D8F-4408-9469-6BEDC12F0244}
StateName=TBLRKitchenScreenPro
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:59
Interval=9.38657467486337E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:59
Interval=1.27546300063841E-5;30.12.1899 0:00:01
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:59
Interval=2.60763918049634E-5;30.12.1899 0:00:02
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:59
Interval=2.31477315537632E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:59
Interval=0.000144652782182675;30.12.1899 0:00:12
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKitchenScreenCounterDataSet
1;1

<<<Dataset: TKitchenScreenCounterDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKitchenScreenCounterDataSet
Fields=tableno;quantity
StoredParams=
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 16:59
Interval=0.000178599533683155;30.12.1899 0:00:15
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKSPickupInfoDataSet
1;Separate1KeukenPU2;3

<<<Dataset: TKSPickupInfoDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKSPickupInfoDataSet
Fields=quantity;article;wf_status
StoredParams=
[Class]
Name=TBLRMsgKSFinishItemByIdBill
TimeStamp=10.09.2018 16:59
Interval=4.21180520788766E-5;30.12.1899 0:00:03
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_bill=25000097768
[Class]
Name=TBLSMsgBeforeLayerVisibility
TimeStamp=10.09.2018 16:59
Interval=2.1990854293108E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLSMsgAfterLayerVisibility
TimeStamp=10.09.2018 16:59
Interval=0;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLRMsgKSCompleteBill
TimeStamp=10.09.2018 16:59
Interval=7.23380071576685E-6;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=10.09.2018 16:59
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgKSPickup
TimeStamp=10.09.2018 16:59
Interval=2.59490698226728E-5;30.12.1899 0:00:02
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
id_ksc=25000090642
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 16:59
Interval=7.32638727640733E-6;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 16:59
Interval=1.04169885162264E-7;30.12.1899
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
FieldCount=1
Value=True
[Class]
Name=TBLMsgPrintScreenDataset
TimeStamp=10.09.2018 17:00
Interval=9.34953714022413E-5;30.12.1899 0:00:08
Exception=
StateGUID={20A6CABC-E00F-4288-A105-93A687645707}
StateName=TBLRKSPickUp
StateData=
>>>Dataset: TKSPickupInfoDataSet

<<<Dataset: TKSPickupInfoDataSet
<<<<<EOSD
FieldCount=5
CollectExtraData=False
DSClassName=TRestaurantInfoDataSet
DSName=TKSPickupInfoDataSet
Fields=article;quantity
StoredParams=
[Class]
Name=TBLMessageCancel
TimeStamp=10.09.2018 17:00
Interval=4.27083359682001E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=10.09.2018 17:00
Interval=8.06712341727689E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=11
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=10.09.2018 17:00
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=11
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=10.09.2018 17:00
Interval=9.3171329353936E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=10.09.2018 17:00
Interval=2.26851989282295E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    1                                                                
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>    <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
 1      Separate1KeukenPU    1,70     1,70   0,30    1,40 21,0%         1,40    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        3,40        3,40        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 17:00
Interval=6.8171284510754E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 17:00
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=10.09.2018 17:00
Interval=2.34027756960131E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
[Start]
**************************************************
TimeStamp=10.09.2018 17:02
Interval=0;30.12.1899
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=10.09.2018 17:02
Interval=1.28703686641529E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=10.09.2018 17:02
Interval=1.0416260920465E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestKSPro
TimeStamp=10.09.2018 17:02
Interval=6.21643557678908E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=7
ActiveFirst=False
KSNotifType=0
KSSingleInCourse=False
Overall=False
turnOnPrint=False
UsePUNon=False
Value=False
