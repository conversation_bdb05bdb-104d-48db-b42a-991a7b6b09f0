create table transfer_waiter (
    id u_id,
    id_bill bigint,
    id_untill_users bigint,
	id_alter_user bigint,
    op_datetime timestamp, 
    state integer, 
    constraint transfer_waiter_pk primary key (id),
    constraint transfer_waiter_fk1 foreign key (id_bill) references bill (id),
    constraint transfer_waiter_fk2 foreign key (id_untill_users) references untill_users (id),
    constraint transfer_waiter_fk3 foreign key (id_alter_user) references untill_users (id)
);
commit;
grant all on transfer_waiter to untilluser;
commit;
execute procedure register_sync_table_ex('transfer_waiter', 'p', 1);
commit;

