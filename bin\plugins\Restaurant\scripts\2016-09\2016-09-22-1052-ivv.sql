create table ks_cooked (
    id u_id,
    id_ks bigint,
    item_sign varchar(1024),
    quantity integer,
    original_quantity integer,
    id_untill_users bigint,
    dt timestamp,
    constraint ks_cooked_pk primary key (id),
    constraint ks_cooked_fk1 foreign key (id_ks) references KITCHEN_SCREENS (id),
    constraint ks_cooked_fk2 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on ks_cooked to untilluser;
commit;
execute procedure register_sync_table_ex('ks_cooked', 'p', 1);
commit;

create table ks_cooked_spent (
    id u_id,
    id_ks_cooked bigint,
    id_ks_wf_item bigint,
    quantity_spent integer,
    id_untill_users_spent bigint,
    dt_spent timestamp,
    constraint ks_cooked_spent_pk primary key (id),
    constraint ks_cooked_spent_fk1 foreign key (id_ks_cooked) references ks_cooked (id),
    constraint ks_cooked_spent_fk2 foreign key (id_ks_wf_item) references KS_WORKFLOW_ITEMS (id),
    constraint ks_cooked_spent_fk3 foreign key (id_untill_users_spent) references untill_users (id)
);
commit;
grant all on ks_cooked_spent to untilluser;
commit;
execute procedure register_sync_table_ex('ks_cooked_spent', 'p', 1);
commit;

