set term !!;
CREATE OR ALTER procedure ROLLBACK_PSP_TIPS (
    ID_PBILL_PAYMENTS bigint,
    ID_PBILL bigint)
as
declare variable AMOUNT decimal(17,4);
begin
  select sum(amount) from psp_tips where id_pbill=:id_pbill and id_pbill_payments=:id_pbill_payments into :amount;
  if (coalesce(:amount,0) = 0) then exit;
  insert into psp_tips(id_pbill, id_pbill_payments, amount) values (:id_pbill, :id_pbill_payments, -:amount);
end
!!
commit
!!
grant execute on procedure rollback_psp_tips to untilluser
!!
commit
!!
set term ;!!
