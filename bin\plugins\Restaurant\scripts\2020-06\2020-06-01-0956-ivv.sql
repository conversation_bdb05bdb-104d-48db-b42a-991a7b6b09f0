create table kitchen_screens_substations (
    id u_id,
    id_ks_main bigint,
    id_ks_sub bigint,
    KS_SUB_NUMBER integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint kss_pk primary key (id),
    constraint kss_fk1 foreign key (id_ks_main) references kitchen_screens(id),
    constraint kss_fk2 foreign key (id_ks_sub) references kitchen_screens(id)
);
commit;
grant all on kitchen_screens_substations to untilluser;
commit;
execute procedure register_sync_table_ex('kitchen_screens_substations', 'b', 1);
commit;
execute procedure register_bo_table('kitchen_screens_substations', 'id_ks_main', 'kitchen_screens');
commit;



