CREATE OR ALTER VIEW COI_SIZE_PRICE(
    id_cancel_order_item,
    ART_SIZE_NAME)
AS
select cancel_order_item.id id_cancel_order_item,
    (case when coalesce(size_modifier_item.smi_name,'')='' then articles.name else size_modifier_item.smi_name || ' ' || articles.name end) art_size_name
from cancel_order_item
join articles on articles.id=cancel_order_item.id_articles
left outer join size_modifier_item on size_modifier_item.id=cancel_order_item.id_size_modifier_item;
commit;

grant all on COI_SIZE_PRICE to untilluser;
commit;
                         