create table user_report_groups (
    id u_id,
    id_untill_users  bigint,
    id_report_groups bigint,
    urg_number integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint urpg_pk primary key (id),
    constraint urpg_fk1 foreign key (id_untill_users) references untill_users(id),
    constraint urpg_fk2 foreign key (id_report_groups) references report_groups(id)
);
commit;
grant all on user_report_groups to untilluser;
commit;
execute procedure register_sync_table_ex('user_report_groups', 'b', 1);
commit;
execute procedure register_bo_table('user_report_groups', 'id_untill_users', 'untill_users');
commit;
execute procedure register_bo_table('user_report_groups', 'id_report_groups', 'report_groups');
commit;



