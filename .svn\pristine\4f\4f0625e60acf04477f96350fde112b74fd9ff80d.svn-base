inherited GroupPanelPropFrame: TGroupPanelPropFrame
  Width = 475
  Height = 410
  ExplicitWidth = 475
  ExplicitHeight = 410
  object pnlBorder: TUntillPanel
    Left = 3
    Top = 2
    Width = 470
    Height = 406
    BevelInner = bvRaised
    BevelOuter = bvNone
    TabOrder = 0
    BorderColor = clBlack
    object gbBorder: TUntillGroupBox
      Left = 14
      Top = 52
      Width = 441
      Height = 157
      TabOrder = 1
      object lblBorderColor: TTntLabel
        Left = 15
        Top = 15
        Width = 102
        Height = 15
        AutoSize = False
        Caption = 'Border color:'
        WordWrap = True
      end
      object lblBorderPlace: TTntLabel
        Left = 15
        Top = 127
        Width = 102
        Height = 15
        AutoSize = False
        Caption = 'Border position:'
        WordWrap = True
      end
      object cbPenColor: TTntColorComboBox
        Left = 132
        Top = 12
        Width = 173
        Height = 20
        Style = [cbStandardColors, cbExtendedColors, cbCustomColor]
        ItemHeight = 14
        TabOrder = 0
        OnClick = cbPenColorClick
        OnExit = cbPenColorExit
        ColorValue = clBlack
      end
      object gbVertical: TUntillGroupBox
        Left = 222
        Top = 38
        Width = 202
        Height = 77
        Caption = 'Vertical border'
        TabOrder = 2
        object lblBorderVWidth: TTntLabel
          Left = 18
          Top = 23
          Width = 141
          Height = 15
          AutoSize = False
          Caption = 'Width:'
          WordWrap = True
        end
        object edtVWidth: TUntillSpinEdit
          Left = 116
          Top = 20
          Width = 57
          Height = 21
          ParentBackground = False
          TabStop = True
          TabOrder = 0
          Alignment = taLeftJustify
          Value = 25
          MaxValue = 25
          MinValue = 1
          MaxLength = 3
          OnChange = edtVWidthChange
        end
        object chkVGradient: TUntillCheckBox
          Left = 18
          Top = 48
          Width = 93
          Height = 17
          Caption = 'Gradient fill'
          TabOrder = 1
          OnClick = chkVGradientClick
        end
      end
      object gbHorizontal: TUntillGroupBox
        Left = 15
        Top = 38
        Width = 201
        Height = 77
        Caption = 'Horizontal border'
        TabOrder = 1
        object lblBorderHWidth: TTntLabel
          Left = 18
          Top = 24
          Width = 141
          Height = 15
          AutoSize = False
          Caption = 'Width:'
          WordWrap = True
        end
        object edtHWidth: TUntillSpinEdit
          Left = 116
          Top = 19
          Width = 57
          Height = 21
          ParentBackground = False
          TabStop = True
          TabOrder = 0
          Alignment = taLeftJustify
          Value = 25
          MaxValue = 25
          MinValue = 1
          MaxLength = 3
          OnChange = edtVWidthChange
        end
        object chkHGradient: TUntillCheckBox
          Left = 18
          Top = 48
          Width = 93
          Height = 17
          Caption = 'Gradient fill'
          TabOrder = 1
          OnClick = chkHGradientClick
        end
      end
      object cmbBorderPlace: TUntillComboBox
        Left = 136
        Top = 124
        Width = 169
        Height = 21
        Style = csDropDownList
        TabOrder = 3
        OnChange = cmbBorderPlaceChange
        ColorActive = clBlack
      end
    end
    object chkWithBorder: TUntillCheckBox
      Left = 14
      Top = 21
      Width = 81
      Height = 17
      Caption = 'Border'
      Checked = True
      State = cbChecked
      TabOrder = 0
      OnClick = chkWithBorderClick
    end
    object UntillGroupBox1: TUntillGroupBox
      Left = 16
      Top = 216
      Width = 214
      Height = 105
      Caption = 'Horizontal alignment'
      TabOrder = 2
      Visible = False
      object rbHAbsolute: TUntillRadioButton
        Left = 23
        Top = 24
        Width = 113
        Height = 17
        Caption = 'Absolute size'
        Checked = True
        TabOrder = 0
        TabStop = True
        OnClick = rbHAbsoluteClick
      end
      object rbHPercent: TUntillRadioButton
        Left = 23
        Top = 48
        Width = 113
        Height = 17
        Caption = 'Relative size'
        TabOrder = 1
        OnClick = rbHAbsoluteClick
      end
      object rbHFree: TUntillRadioButton
        Left = 23
        Top = 72
        Width = 113
        Height = 17
        Caption = 'Free size'
        TabOrder = 2
        OnClick = rbHAbsoluteClick
      end
    end
    object UntillGroupBox2: TUntillGroupBox
      Left = 236
      Top = 216
      Width = 218
      Height = 105
      Caption = 'Vertical alignment'
      TabOrder = 3
      Visible = False
      object rbVAbsolute: TUntillRadioButton
        Left = 23
        Top = 24
        Width = 113
        Height = 17
        Caption = 'Absolute size'
        Checked = True
        TabOrder = 0
        TabStop = True
        OnClick = rbHAbsoluteClick
      end
      object rbVPercent: TUntillRadioButton
        Left = 23
        Top = 48
        Width = 113
        Height = 17
        Caption = 'Relative size'
        TabOrder = 1
        OnClick = rbHAbsoluteClick
      end
      object rbVFree: TUntillRadioButton
        Left = 23
        Top = 72
        Width = 113
        Height = 17
        Caption = 'Free size'
        TabOrder = 2
        OnClick = rbHAbsoluteClick
      end
    end
  end
end
