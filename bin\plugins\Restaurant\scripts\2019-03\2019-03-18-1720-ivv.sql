create table articles_allergens (
    id u_id,
    id_articles bigint,
    id_allergens bigint,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint articles_allergens_pk primary key (id),
    constraint articles_allergens_fk1 foreign key (id_articles) references articles(id),
    constraint articles_allergens_fk2 foreign key (id_allergens) references allergens(id)
);
commit;
grant all on articles_allergens to untilluser;
commit;
execute procedure register_sync_table_ex('articles_allergens', 'b', 1);
commit;
execute procedure register_bo_table('articles_allergens', 'id_allergens', 'allergens');
commit;
execute procedure register_bo_table('articles_allergens', 'id_articles', 'articles');
commit;
