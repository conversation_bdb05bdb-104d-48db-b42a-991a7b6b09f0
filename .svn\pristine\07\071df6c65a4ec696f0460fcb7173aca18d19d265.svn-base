unit BLRMainU;

interface

uses
 Classes, Windows, UrlNotifierU, BLRBillU, BLRReservationU, UntillDBU, BLAlgU, BLRAlgU, ActiveTableU, Contnrs,
 ClassesU, BLRCardOpsU, SCPlanU, UBLU, UBLProxyU, RestaurantPluginU, TableAreaU, SingleArticleU,
 OnHoldManagerU, BLSysU, OrderItemU, BLRTakeAwayU, KSTypesU, SyncObjs, Generics.Collections,
 TableLinksU;

const
  URL_BLR_CHANGED = 'blrchanged';
type

  TTableNumberAndPart = record
    TableNumber: Integer;
    TablePart: String;
  end;

  TTableLink = class
    tableTo : Integer;
    id_sa   : Int64;
    LastHostUpdate : TDatetime;
  end;

  IUntillPosScreenPager = interface
    ['{5f617d9e-b0ae-492f-b071-d823636f3b84}']
    function GetMaxPageCount : Integer;
    function GetCurPageNum   : Integer;
  end;

  BLRSalesKind = (skByTable, skDirect);
  TBillStatus  = ( blsNone, blsActive, blsReopened, blsVoided, blsCredit, blsTransfer );
  TMenuFilterType = (mftDeps, mftGroups, mftCourses);

  TUEFilterMenuItems     = class(TUrlEvent);
  TOpenBarTablesEvent    = class(TUrlEvent);

  TUnlockTableResultCode = (unlrNone, unlrYes, unlrNo);
  TUnlockTableResult = class
  private
    answerlst : TDictionary<string, TUnlockTableResultCode>;
    FLock     : TCriticalSection;
  public
    function pop(aguid : String) : TUnlockTableResultCode;
    procedure push(aguid : String; Value: TUnlockTableResultCode);
    constructor Create;
    destructor Destroy; override;
  end;

  TBillTotal = record
    total    : Currency;
    discount : Currency;
    vat      : double;
    payment  : Currency;
  public
    procedure Clear;
  end;

  TMenuItemFilter = class
  private
    Fdeps    : TObjectList;
    Fgroups  : TObjectList;
    Fcourses : TObjectList;
  public
    property deps    : TObjectList read fdeps;
    property groups  : TObjectList read Fgroups;
    property courses : TObjectList read fcourses;

    procedure AddItem(id : Int64; filterType: TMenuFilterType);
    function IsEmpty : boolean;
    procedure Clear;
    constructor Create;
    destructor Destroy; override;
  end;

  TBlrTable = class
  private
    FNumber: Integer;
    FCovers: Integer;
  public
    property Number: Integer read FNumber;
    property Covers: Integer read FCovers;
    constructor Create(aTableNo: Integer; aCovers: Integer);
  end;

  TBlrTableList = class(TObjectList)
  private
    function GetItems( Index: integer ): TBlrTable;
  public
    function RegisterTable(aTableNo, aCovers : Integer) : Integer;
    procedure UnRegisterTable(aTableNo: Integer);
    function FindItem(aTableno : Integer) : Integer;
    property Items[Index: integer]: TBlrTable read GetItems;
  end;

  TBlrPaymentInfo = class
  private
    FAmount   : Currency;
    FC_amount : Currency;
    Fid_payment : Int64;
  public
    property Amount   : Currency read FAmount;
    property C_amount : Currency read FC_Amount;
    property id_payment : Int64 read fid_payment;
    constructor Create(aAmount, aC_amount : Currency; aid_payment : Int64);
  end;

  TBlrPaymentList = class(TObjectList)
  private
    function GetItems( Index: integer ): TBlrPaymentInfo;
  public
    property Items[Index: integer]: TBlrPaymentInfo read GetItems;
  end;

  TControlBillData = record
  private
    FClientID     : Int64;
    FClientFilter : String;
    FTableNumber  : Integer;
    FBillNumber   : String;
    FBillStatus   : TBillStatus;
    FBillID       : Int64;
    procedure SetBillNumber(const Value: String);
    procedure SetBillStatus(const Value: TBillStatus);
    procedure SetBillID(const Value: Int64);
    procedure SetTableNumber(const Value: Integer);
    procedure SetClientFilter(const Value: string);
    procedure SetClientID(const Value: Int64);
  public
    property ClientID     : Int64 read FClientID write SetClientID;
    property TableNumber : Integer read FTableNumber write SetTableNumber;
    property ClientFilter : string read FClientFilter write SetClientFilter;
    property BillNumber  : String read FBillNumber write SetBillNumber;
    property BillStatus  : TBillStatus read FBillStatus write SetBillStatus;
    property BillID      : Int64 read FBillID write SetBillID;
    procedure Clear;
  end;

  TInfoAliveEvent  = class(TUrlEvent);
  TPMResetParams  = class(TUrlEvent);
  TDetailAliveEvent  = class(TUrlEvent);
  TKSMessageAliveEvent= class(TUrlEvent);

  TBLRestaurant = class (TComponent, IUrlListener)
  private
    FTableLinker : TObjectDictionary<Integer, TTableLink>;
    FTableMover  : TObjectDictionary<Integer, TSize>;
    FIgnoreUnlockTable : boolean;
    FTestVisaExcept : boolean;
    FStockBalanceLocationID : Int64;
    FLastOrderedArticleList : TRSingleArticleDataList;
    FBaseStep : TBLRRestaurant;
    FmenuFilter : TMenuItemFilter;
    FBarTableSorting : TBarTableSort;
    FArticleDiscountReason : String;
    FArticleDiscountReasonID: Int64;
    FChosenCourse : Int64;
    FTempAllowVoidBillID : Int64;
    FCurrentKSRouteID : Int64;
    FDetailedAliveProgress : Integer;
    FKSMessageAliveProgress : Integer;
    FControlBillData : TControlBillData;
    FControlOrderData : TControlBillData;
    FLastOIFID  : Int64;
    FNewDelayOrderDateTime  : TDateTime;
    FNextDelayOrderDateTime : TDateTime;
    FIsLastBillPaid : boolean;
    FCurPayerName   : String;
    FTableList      : TBlrTableList;
    FLastPaymentList: TBlrPaymentList;
    FCashDrawUserID : Int64;
    FClockInDT      : TDatetime;
    FLaborCost      : Double;
    FLastCashAmount : Currency;
    FNeedReturnToDirectSales: Boolean;
    FRearDisplayLastPrint : TDatetime;
    FRearScreenSaverPrinted : boolean;
    FTransferFromDirectSales: Integer;
    FInfoAliveProgress : Integer;
    FRGMProgress: Integer;
    FArticleVoidReason: String;
    FOnHoldManager    : TOnHoldManager;
    FRearSecondArticlePrinted: boolean;
    FtaBookingBillData : TTABookingBillData;
    FTransferToDirectSales: Integer;

    function GetIsCondimentNeeded: boolean;
    function GetIsSupplementNeeded: boolean;
    // returns table Part by id_bill
    procedure SetReportType(const Value: TBLRReportType);
    procedure SetNeedReturnToDirectSales(const Value: Boolean);
    procedure SetLaborCost(const Value: Double);
    procedure SetLastCashAmount(const Value: Currency);
    function CheckNeedCashDrawer(ClockInDT : TDatetime) : boolean;
    procedure SetCashDrawUserID(const Value: Int64);
    procedure SetClockInDT(const Value: TDatetime);
    procedure SetRearDisplayLastPrint(const Value: TDatetime);
    procedure SetRearScreenSaverPrinted(const Value: boolean);
    procedure SetCurPayerName(const Value: String);
    procedure SetIsLastBillPaid(const Value: boolean);
    procedure SetTransferFromDirectSales(const Value: Integer);
    procedure SetNewDelayOrderDateTime(const Value: TDateTime);
    procedure SetNextDelayOrderDateTime(const Value: TDateTime);
    procedure SetLastOIFID(const Value: Int64);
    procedure SetInfoAliveProgress(const Value: Integer);
    procedure SetDetailedAliveProgress(const Value: Integer);
    procedure SetCurrentKSRouteID(const Value: Int64);
    procedure SetKSMessageAliveProgress(const Value: Integer);
    procedure SetTempAllowVoidBillID(const Value: Int64);
    procedure SetPaymentCurrency(const Value: Int64);
    procedure SetChosenCourse(const Value: Int64);
    procedure SetArticleDiscountReason(const Value: String);
    procedure SetArticleDiscountReasonID(const Value: Int64);
    procedure SetBarTableSorting(const Value: TBarTableSort);
    procedure SetRGMProgress(const Value: Integer);
    procedure SetArticleVoidReason(const Value: String);
    procedure SetStockBalanceLocationID(const Value: Int64);
    function NeedPrintSecondPrinter: boolean;
    procedure SetTransferToDirectSales(const Value: Integer);
  protected
    //Global
    FCompSalesArea: int64; //Computer Sales Area
    FSalesKind: BLRSalesKind;
    FReportType : TBLRReportType;
    FPaymentCurrency : Int64;

    //Active Bill
    ActiveBillTable: integer;
    ActiveBillPart: string;
    IsActiveBillTablePartNeeded: boolean;
    FActiveBill:TBLRBill;
    procedure OnUrlEvent(url: string; ue: TUrlEvent);
    procedure SetActiveBill(Value:TBLRBill);
  public
    LastTotal          : Currency;
    LastReceivedAmount : Currency;
    LastReturnedAmount   : Currency;
    LastTrNumber       : String;

    Billa, Billb, Billc, Billd, Bille, Billf, TransferOriginal, TransferResult, TempOrder, ShadowBill : TBLRBill;
    ActiveReservs :
    TBLRReservations;
    CardOps: TBLRCardOps;

    constructor Create(AOnwer: TComponent); override;
    destructor Destroy; override;

    property TableLinker : TObjectDictionary<Integer, TTableLink> read FTableLinker;
    property TableMover  : TObjectDictionary<Integer, TSize> read FTableMover;
    property IgnoreUnlockTable : boolean read FIgnoreUnlockTable;
    property taBookingBillData : TTABookingBillData read FtaBookingBillData;
    property OnHoldManager  : TOnHoldManager read FOnHoldManager;
    property TestVisaExcept : boolean read FTestVisaExcept write FTestVisaExcept;
    property StockBalanceLocationID : Int64 read FStockBalanceLocationID write SetStockBalanceLocationID;
    property LastOrderedArticleList : TRSingleArticleDataList read FLastOrderedArticleList;
    property ActiveBill  : TBLRBill read FActiveBill write SetActiveBill;
    property BaseStep : TBLRRestaurant read FBaseStep; // First step, created in POS
    property menuFilter : TMenuItemFilter read FmenuFilter;
    property BarTableSorting : TBarTableSort read FBarTableSorting write SetBarTableSorting;
    property ArticleDiscountReason   : String read FArticleDiscountReason write SetArticleDiscountReason;
    property ArticleVoidReason   : String read FArticleVoidReason write SetArticleVoidReason;
    property ArticleDiscountReasonID : Int64 read FArticleDiscountReasonID write SetArticleDiscountReasonID;
    property ChosenCourse : Int64 read FChosenCourse write SetChosenCourse;
    property TempAllowVoidBillID : Int64 read FTempAllowVoidBillID write SetTempAllowVoidBillID;
    property CurrentKSRouteID : Int64 read FCurrentKSRouteID write SetCurrentKSRouteID;
    property DetailedAliveProgress: Integer read FDetailedAliveProgress write SetDetailedAliveProgress;
    property RGMProgress : Integer read FRGMProgress write SetRGMProgress;
    property KSMessageAliveProgress : Integer read FKSMessageAliveProgress write SetKSMessageAliveProgress;
    property ControlBillData : TControlBillData read FControlBillData write FControlBillData;
    property ControlOrderData : TControlBillData read FControlOrderData write FControlOrderData;
    property PaymentCurrency : Int64 read FPaymentCurrency write SetPaymentCurrency;
    property LastOIFID  : Int64 read FLastOIFID write SetLastOIFID;
    property NewDelayOrderDateTime  : TDateTime read FNewDelayOrderDateTime write SetNewDelayOrderDateTime;
    property NextDelayOrderDateTime  : TDateTime read FNextDelayOrderDateTime write SetNextDelayOrderDateTime;
    property IsLastBillPaid : boolean read FIsLastBillPaid write SetIsLastBillPaid;
    property CurPayerName   : String read FCurPayerName write SetCurPayerName;
    property RearScreenSaverPrinted   : boolean read FRearScreenSaverPrinted write SetRearScreenSaverPrinted;
    property RearSecondArticlePrinted : boolean read FRearSecondArticlePrinted write FRearSecondArticlePrinted;
    property RearDisplayLastPrint : TDatetime read FRearDisplayLastPrint write SetRearDisplayLastPrint;
    property BLRTableList   : TBlrTableList read FTableList;
    property LastPaymentList: TBlrPaymentList read FLastPaymentList;
    property CashDrawUserID : Int64 read FCashDrawUserID write SetCashDrawUserID;
    property ClockInDT      : TDatetime read FClockInDT write SetClockInDT;
    property LastCashAmount : Currency read FLastCashAmount write SetLastCashAmount;
    property LaborCost : Double read FLaborCost write SetLaborCost;
    property NeedReturnToDirectSales : Boolean read FNeedReturnToDirectSales write SetNeedReturnToDirectSales;
    property TransferFromDirectSales : Integer read FTransferFromDirectSales write SetTransferFromDirectSales;
    property TransferToDirectSales : Integer read FTransferToDirectSales write SetTransferToDirectSales;
    property InfoAliveProgress : Integer read FInfoAliveProgress write SetInfoAliveProgress;
    //Internal functions/properties
    property ReportType : TBLRReportType read FReportType write SetReportType;
    property CompSalesArea: int64 read FCompSalesArea;
    //Restaurant functions/properties
    property  SalesKind: BLRSalesKind read FSalesKind;
    procedure SendOptionGlb(id_option, id_article: int64; bFreeOptions: boolean=false);
    procedure SendArticleGlb(id: int64; artType :TArticleType=atNormal);
    procedure SendDiscountReasonID(id: int64);
    procedure SendVoidReasonID(id: int64);
    procedure SendBillInput(s: string);
    procedure SendCancel;
    procedure SendPriceCancel;
    procedure SendTable(tableno: integer; SalesArea :Int64; TableName :WideString;
      Native_Table : Integer; aNumberOfCovers : Integer);
    procedure SendUndo;
    property IsCondimentNeeded: boolean read GetIsCondimentNeeded;
    property IsSupplementNeeded: boolean read GetIsSupplementNeeded;
    function IsOrderNeeded: boolean;
    function IsPriceNeeded: boolean;
    function IsOptionsNeeded: boolean;
    function IsTableNeeded: boolean;
    procedure SendOpenBarTableEvent;
    procedure BillOk;
    procedure PriceOk;
    procedure AddSupplement;
    procedure AddCondiment;
    function GetBillIDByNumber(BillNumber: String; bNeedUpdate : Boolean = true): String;
    function GetBillIDByFiscalNumber(DocumentId: String): String;
    function GetBillStatus(pbill_status: Integer; pBillClosed : boolean): Integer;
    procedure GetServiceAmount(id_bill: Int64;
      var sc_amount, scvat_amount: double);
    function AllTablesClosed: boolean;
    procedure InitMenuFilter(msg : TBLRMsgPutMenuFilter);
    function IsTableOverview : boolean;

    procedure GetItemServiceAmount(
      id_order_item: Int64; id_menu_item: Int64;
      var sc_amount, scvat_amount: double);

  end;
var
  blr                : TBLRestaurant;
  CurrentTable       : Integer;
  OpenTableSortField : String;
  SortOpenTablesDecs : boolean;
  UnlockTableResult  : TUnlockTableResult;

procedure SendDataChange(URL: String; FieldName:String = '');
function GetLockPCName() : String;
procedure SendDataAsync( atn : Integer; atp :String;
  aData : string; apcName : String );
procedure SaveOrderDataToUBL;
function  GetOpenedUserPC(aUserId : Int64; tableno : Integer;
  var UserName : string; var PCName : String; var hht : string) : boolean;
procedure CheckUserOrderPrivileges(AUserID : Int64; tableno : Integer);
procedure CheckUserClosePrivileges(AUserID : Int64; tableno : Integer);
function  GetSalesAreaID(tableno : Integer) : Int64;
function  IsBillClosed(wt : IWTran; aid_bill: Int64): boolean;
procedure GetWaiterByName(name : string; var id_waiters: Int64; var pwd: String);
function  GetFinalCashAmount(dt : TDatetime; PCName : string) : Currency;
procedure BLRMainPosIniFinit(bInit: boolean);
function  GetTotalSales(db : TUntillDB; FromDT, EndDT : TDatetime): Currency;
procedure CheckOpenTables(UntillDB : TUntillDB; uid : Int64; otType : TRCheckOpenTable);
function GetTableNoFromBill( wt : IWTran; aid_bill : Int64 ) : Integer;
function GetTableNoFromBillEx( wt : IWTran; aid_bill : Int64 ) : TTableNumberAndPart;
procedure CheckTableNotActive(tableno : Integer);
function GetClientNameByBill(db : TUntillDB; id_bill : Int64) : String;
function GetReopenBillStatus(id_pbill : Int64) :TBillStatus;
function GetBillTotal( aid_bill : Int64 ) : TBillTotal;
procedure GetBillNrByPBPID ( aid_pbp : Int64;
  var aid_pbill : int64; var aPbillNumber : String );
function GetOrCreatePOSArticle( aName, aCaption: String;
  avat : Currency; avat_sign : string ) : Int64;
function OtherBillSameClientExist(aid_clients : Int64) : boolean;
function GetOptionNum(aid_article_options : Int64) : Integer;
procedure DisableArtMem(Fid_article, fid_department, fid_sa : Int64; fenable : boolean);
function BuildArtEnableKey(id_art, id_dep, id_sa : Int64): string;

implementation

uses UntillPosU, sysutils, BLREqu, BLRPrintU, POSAlgU, BlrBillTypesU,
  UntillAppU,  ProfilerU, BLRDiscountsU, UntillLogsU, DBRestaurantTableU,
  UntillScreenU, BLRVoidU, DataControlsU, DateUtils, BLRAlgCardTableU,
  RestaurantCashDatasetsU, UTF8U, CommonU, BLRCashDrawAmountU, Math,
  CurrencyU, AccessControlU, BLRWaitersU, TaxesU, KitchenScreenDataFeederU,
  KSDataProFeederU, KSManagerU, ClockInOutU, BLRAlgCombineTablesU,
  BLRAlgChangeOrderCourseU, BLRDelayOrderU, BLRTableInfoU, CacheTableAreasU,
  MemTableU, NamedVarsU, Base64U, WaitersU, RetranslatorU, PaymentsU,
  PosAsyncU, BlrMainAsyncOpsU, BLRAlgReopenBillU, BLRArticleU,
  BLRAlgSizeModifier, BLRAlgTransferU;

function GetLockPCName() : String;
var hh : string;
begin
  result := upos.POSComputerName;
  if upos.UntillDB.IsRemote then
    result := result + '- remote(' + ThisComputerName + ')';
  hh := trim(upos.UntillDB.HandHeldId);
  if length(hh)>0 then
    result := result + '(' + hh + ')'
end;

procedure SendDataChange(URL: String; FieldName:String = '');
var
  e: TPMDataChangedEvent;
begin
  e := TPMDataChangedEvent.Create(0, FieldName, uetUpdate);
  try
    UPos.Un.SendEvent(URL, e)
  finally
    FreeAndNil(e);
  end;
end;

function GetOptionNum(aid_article_options : Int64) : Integer;
begin
  result := CacheOPTs.GetOPTData( aid_article_options );
end;

function getLastPaidData( atn : Integer; atp :String ) : string;
var q : IIBSQL;
    UBLParams : INamedVars;
begin
  result := '';
  q := upos.UntillDB.GetPreparedIIbSql('select first 1 bill.id, pbill.pcname '
    + ' from bill '
    + ' left outer join pbill on pbill.id_bill=bill.id'
    + ' where bill.open_datetime > :dt and bill.close_datetime is not null and bill.isactive=0'
    + ' and bill.tableno=:tn and bill.table_part=:tp'
    + ' order by bill.close_datetime desc, pbill.pdatetime desc');
  q.q.ParamByName('dt').AsDateTime := IncDay(upos.GetPOSNow, -1);
  q.q.ParamByName('tn').AsInteger  := atn;
  q.q.ParamByName('tp').AsString   := atp;
  q.ExecQuery;

  if q.eof then exit;

  UBLParams       := TNamedVars.Create;
  UBLParams.Add('id_lastbill', q.q.FieldByName('id').asString);
  UBLParams.Add('lastbillPC', q.q.FieldByName('pcname').asString);
  result := String(NamedVarsToString(UBLParams));

end;

procedure SendDataAsync( atn : Integer; atp :String;
  aData : string; apcName : String );
var task :TAsyncSendOrderTask;
begin
  task:=TAsyncSendOrderTask.Create;
  task.dbguid    := upos.UntillDB.GetGuid;
  task.tableno   := atn;
  task.tablepart := atp;
  task.pcname    := apcName;
  if trim(aData)='' then
    aData := getLastPaidData(atn, atp);
  task.UBLOrderData := aData;
  PosAsyncTasksThread.AddTask( task );
end;

procedure SaveOrderDataToUBL;
var ubl     : TUBLConnection;
    UBLParams : INamedVars;
    ms      : TMemoryStream;
    msmenu  : TMemoryStream;
    strData : String;
    sumTotal : Currency;
begin

  if not Assigned(blr) then exit;
  if not Assigned(blr.ActiveBill) then exit;

  ubl := UBLU.GetUBL(upos.UntillDB);

  if not assigned(ubl) then exit;
  if blr.ActiveBill.State = bsSaved then exit;

  UBLParams       := TNamedVars.Create;
  with blr.ActiveBill do begin

    ms     := nil;
    msmenu := nil;
    try
      ms     := TMemoryStream.Create;
      msmenu := TMemoryStream.Create;
      items.SerializeNewRows(ms);
      if (ms.Size=0) then begin

        // send data to server one asynchronously
        SendDataAsync( tableno, table_part, '', GetLockPCName );

      end else begin
        sumTotal := items.Total + menu_items.Total;
        UBLParams.Add('id_clients', id_clients);
        UBLParams.Add('id_courses', id_courses);
        UBLParams.Add('group_vat_level', group_vat_level);
        UBLParams.Add('chair_number', CurrentChairNumber);
        UBLParams.Add('chair_name', CurrentChairName);
        UBLParams.Add('number_of_covers', NumberOfCovers);
        UBLParams.Add('order_name', order_name);
        UBLParams.Add('id_bill', id_bill);
        UBLParams.Add('id_sa', SalesArea);
        UBLParams.Add('total', sumTotal);
        ms.Seek(0,0);
        UBLParams.Add('item_size',ms.Size);
        UBLParams.Add('items', Base64U.StreamToBase64(ms));

        menu_items.SerializeNewRows(msmenu);
        if msmenu.Size=0 then
          UBLParams.Add('menu_size',0)
        else begin
          msmenu.Seek(0,0);
          UBLParams.Add('menu_size',msmenu.Size);
          UBLParams.Add('menu_items',  Base64U.StreamToBase64(msmenu));
        end;
      end;
      strData := String(NamedVarsToString(UBLParams));

      SendDataAsync( tableno, table_part, strData, GetLockPCName );

      if assigned(WaitersProvider) then
        WaitersProvider.SetWaiterActiveData(upos.GetUserID, upos.UserNameInfo.Username,
        	upos.UntillDB.GetUserNumber(upos.GetUserID), tableno,
          blr.ActiveBill.open_datetime, blr.ActiveBill.NotPaid);
    finally
      FreeAndNil(ms);
      FreeAndNil(msmenu);
    end;

  end;
  SendDataChange(URL_BILL_CHANGED);
end;

procedure CheckTableNotActive(tableno : Integer);
var iq : IIBSQL;
begin
  iq := upos.Untilldb.GetPreparedIIbSql('select count(*) from bill where tableno=:tableno and isactive=1 and close_datetime is not null');
  iq.q.Params[0].asInteger := tableno;
  iq.ExecQuery;

  if iq.Fields[0].asInteger>0 then
    plugin.RaisePosException('Table is active','CheckTableNotActive');
end;

procedure GetBillNrByPBPID ( aid_pbp : Int64;
  var aid_pbill : int64; var aPbillNumber : String );
var qSel : IIBSQL;
begin
  aid_pbill := 0;
  aPbillNumber := '';
  qSel := upos.Untilldb.GetPreparedIIbSql('select first 1 pb.id, pb.number, pb.suffix  '
    + ' from pbill pb'
    + ' join pbill_payments pbp on pb.id = pbp.id_pbill'
    + ' where pbp.id=:id');
  qSel.q.Params[0].asInt64 := aid_pbp;
  qSel.ExecQuery;

  if qSel.eof then exit;

  aid_pbill    := StrToInt64Def(qSel.q.fieldByName('id').asString,0);
  aPbillNumber := trim(qSel.q.fieldByName('number').asString + qSel.q.fieldByName('suffix').asString);
end;

function GetClientNameByBill(db : TUntillDB; id_bill : Int64) : String;
var iq : IIBSQL;
begin
  result := '';
  iq := upos.Untilldb.GetPreparedIIbSql('select clients.name from clients '
    + ' join bill on bill.id_clients=clients.id'
    + ' where bill.id=:id_bill');
  iq.q.Params[0].asInt64 := id_bill;
  iq.ExecQuery;

  if not iq.eof then
    result := iq.q.fields[0].asString;
end;

function GetFinalCashAmount(dt : TDatetime; PCName : string) : Currency;
var iq : IIBSQL;
begin
  Result := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select sum(amount) from in_out_cash where pcname=:pcname and datetime<:dt');
  iq.q.Params[0].asString   := UTF8_Encode(PCname);
  iq.q.Params[1].asDatetime := dt;
  iq.ExecQuery;
  if iq.eof then exit;
  Result := iq.fields[0].asCurrency;
end;

procedure CheckAllPSPTips;
var q, qq, iq : IIBSQL;
    sqlStr : String;
    d1, d2 : TDatetime;
    FromClock, ToClock: TDatetime;
    bClockedOut : Boolean;
    idpbill, idpayments, idpbillpayments : Int64;
    price : Currency;
begin

  if not POSRestaurantSettings.Settings.ExtraSettings.NeedAllTips then exit;
  q := upos.UntillDB.GetPreparedIIbSql('select user_clock_in from untill_users where id=:id');
  q.q.ParamByName('id').asInt64 := upos.GetUserID;
  q.ExecQuery;
  if q.Eof then  exit;

  if q.fields[0].AsInteger=1 then begin
    bClockedOut := UserClocked(upos.UntillDB, upos.GetUserID, FromClock, ToClock);
    if FromClock>0 then
      d1 := FromClock;
    if not bClockedOut then
      d2 := ToClock;
  end;

  GetRange(upos.GetPOSNow, d1, d2);
  sqlStr := 'select distinct p.id ' +
          ' from pbill p' +
          ' inner join PBILL_PAYMENTS pbp on pbp.id_pbill= p.id' +
          ' inner join PAYMENTS pt on pbp.id_payments=pt.id and pt.kind=1' +
          ' where p.pdatetime between :d1 and :d2 and p.id_untill_users=:uid ' +
          ' and not exists (select * from neg_pbill np where np.id_pbill=p.id)';
  qq := upos.UntillDB.GetPreparedIIbSql(sqlStr);
  qq.q.ParamByName('d1').asDatetime := d1;
  qq.q.ParamByName('d2').asDatetime := d2;
  qq.q.ParamByName('uid').asInt64 := upos.GetUserID;
  qq.ExecQuery;

  iq := upos.UntillDB.GetPreparedIIbSql('select count(*) from psp_tips  ' +
  ' where id_pbill_payments in (select id from pbill_payments where id_pbill=:id_pbill)');
  while not  qq.eof do begin
    idpbill := StrToInt64Def(qq.Fields[0].asString,0);
    if CanProcessTips(idpbill, idpayments, idpbillpayments, price) then begin
      iq.Close;
      iq.q.Params[0].AsInt64 := idpbill;
      iq.ExecQuery;
      if iq.Fields[0].asInteger=0 then
        plugin.RaisePosException('You cannot clock out. Not all PSP tips are assigned.', 'CheckAllPSPTips' );
    end;
    qq.Next;
  end;
end;

function GetTableNoFromBillEx( wt : IWTran; aid_bill : Int64 ) : TTableNumberAndPart;
var qSel : IIBSQL;
    bcommit : boolean;
begin
//    profenter('GetTableNoFromBillEx');
  result.TableNumber :=0;
  result.TablePart := '';

  bcommit := false;
  if wt=nil then begin
    wt := upos.UntillDB.getWTran;
    bcommit := true;
  end;

  qSel := wt.GetPreparedIIbSql('select tableno, table_part from bill where id=:id_bill');
  qSel.q.ParamByName('id_bill').asInt64          := aid_bill;
  qSel.ExecQuery;
  if qSel.eof then exit;
  result.TableNumber := qSel.Fields[0].AsInteger;
  result.TablePart := qSel.Fields[1].AsTrimString;
  if bCommit then wt.commit;
end;

function GetTableNoFromBill( wt : IWTran; aid_bill : Int64 ) : Integer;
begin
//    profenter('GetTableNoFromBill');
  result := GetTableNoFromBillEx(wt, aid_bill).TableNumber;
end;

procedure CheckOpenTables(UntillDB : TUntillDB; uid : Int64; otType : TRCheckOpenTable);
begin
  if TableAreaU.DoCheckOpenTables(UntillDB, uid, otType) then
    plugin.RaisePosException('You cannot execute action as you still have tables open. Transfer them to another waiter or close the bills.','CheckOpenTables');
end;

function BuildArtEnableKey(id_art, id_dep, id_sa : Int64): string;
begin
  result := IntToStr(id_art) + '.' + IntToStr(id_dep) + '.' + IntToStr(id_sa);
end;

procedure DisableArtMem(Fid_article, fid_department, fid_sa : Int64; fenable : boolean);
var keystr: string;
    uc     : TUBLConnection;
begin
  uc := GetUBL(upos.UntillDB);
  keystr := BuildArtEnableKey(Fid_article, fid_department, fid_sa);
  if fenable then begin
    uc.Soap.disbaledart_deleteData(uc.SessionID, keystr);
  end else begin
    uc.Soap.disbaledart_saveData(uc.SessionID, keystr);
  end;
end;

function SO_CheckTableOpened(db : TUntillDB; tableno : Integer; UserId : Int64; hhtname : String; bOwnUser : boolean;
   var id_user : Int64; var PCName :String; var hht : String) : boolean;
var ubl  : TUBLConnection;
    OTI  : TOpenTableItem;
begin
  result := false;
  ubl := UBLU.GetUBL(upos.UntillDB);
  OTI  := ubl.Soap.activetable_getTableItem(ubl.SessionID, tableno, 'a');
  if OTI.lockState = 1 then begin
    if (bOwnUser and (OTI.userId<>UserID)) then begin
      result  := true;
      id_user := OTI.userId;
      PCName  := OTI.pcName;
      hht     := OTI.pcName;
    end;
  end;
end;

function GetOpenedUserPC(aUserId : Int64; tableno : Integer;
  var UserName : string; var PCName : String; var hht : string) : boolean;
var id_user : Int64;
begin
  result := false;
  UserName := '';
  PCName   := '';
  hht      := '';
  if SO_CheckTableOpened(upos.UntillDB, tableno, aUserId, upos_.UntillDB.HandHeldId, true, id_user, PCName, hht) then begin
    result := true;
    PCName   := UpperCase(pcname);
    try
      UserName := GetUserName(upos.Untilldb,id_user);
    except
      UserName := plugin.TranslatePOS('BLRAlgU', 'Uknown user');
    end;
    if trim(hht)<>'' then
      hht      := hht
    else
      hht      := '';
  end;
end;

function GetTotalSales(db : TUntillDB; FromDT, EndDT : TDatetime): Currency;
var iq : IIBSQL;
begin
  Result := 0;
  iq := db.GetPreparedIIbSql(
    ' select Sum(pbill_item.price * sold_articles.SA_COEF * pbill_item.quantity * (1-coalesce(bill.discount,0))) price ' +
    ' from pbill_item  ' +
    ' join pbill on pbill.id = pbill_item.id_pbill and pbill.pdatetime between :reportfromdatetime_gw and :reporttilldatetime_gw ' +
    ' join pbill_return on pbill_return.id_pbill=pbill.id ' +
    ' join bill on bill.id = pbill.id_bill and bill.close_datetime is not null ' +
    ' join articles on pbill_item.id_articles=articles.id ' +
    ' where not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id) and ' +
    '     articles.article_hash = 0 and coalesce(articles.tip,0) = 0 and ' +
    '     not ((coalesce(pbill_item.price * pbill_item.quantity, 0) = 0) and ((pbill_item.rowbeg = 1) and (coalesce(articles."SENSITIVE",0) = 1))) and ' +
    '    not ((coalesce(pbill_item.price * pbill_item.quantity, 0) = 0) and ((pbill_item.rowbeg <> 1) and (coalesce(articles.sensitive_option,0) = 1))) ' +
    ' union all ' +
    ' select Sum(price * coalesce(sold_articles.SA_COEF,1) * coalesce(sold_articles.quantity,order_item.quantity) * (1-coalesce(bill.discount,0))) price ' +
    ' from order_item ' +
    ' join orders on orders.id = order_item.id_orders ' +
    ' join sold_articles on sold_articles.id_order_item = order_item.id ' +
    ' join pbill on pbill.id = sold_articles.id_pbill and pbill.pdatetime between :reportfromdatetime_gw and :reporttilldatetime_gw ' +
    ' join bill on bill.id = orders.id_bill ' +
    ' join articles on order_item.id_articles=articles.id ' +
    ' where not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id) and ' +
    '     articles.article_hash = 0 and coalesce(articles.tip,0) = 0 and ' +
    '     not ((coalesce(order_item.price * order_item.quantity, 0) = 0) and ((order_item.rowbeg = 1) and (coalesce(articles."SENSITIVE",0) = 1))) and ' +
    '     not ((coalesce(order_item.price * order_item.quantity, 0) = 0) and ((order_item.rowbeg <> 1) and (coalesce(articles.sensitive_option,0) = 1))) ' +
    ' union all ' +
    ' select Sum(menu_item.price * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer)* coalesce(sold_articles.SA_COEF,1) * coalesce(sold_articles.quantity,order_item.quantity)) price ' +
    ' from menu_item ' +
    ' join order_item on order_item.id_menu = menu_item.id_menu ' +
    ' join orders on orders.id = order_item.id_orders ' +
    ' join bill on bill.id = orders.id_bill ' +
    ' join sold_articles on sold_articles.id_order_item = order_item.id ' +
    ' join pbill on pbill.id = sold_articles.id_pbill and pbill.pdatetime between :reportfromdatetime_gw and :reporttilldatetime_gw ' +
    ' join articles on articles.id = menu_item.id_articles ' +
    ' where not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id) and ' +
    '     articles.article_hash = 0 and coalesce(articles.tip,0) = 0 and ' +
    '     not ((coalesce(order_item.price * order_item.quantity, 0) = 0) and ((order_item.rowbeg = 1) and (coalesce(articles."SENSITIVE",0) = 1))) and ' +
    '     not ((coalesce(order_item.price * order_item.quantity, 0) = 0) and ((order_item.rowbeg <> 1) and (coalesce(articles.sensitive_option,0) = 1))) ' +
    ' union all ' +
    ' select Sum(menu_item.price * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer) * pbill_item.quantity) price ' +
    ' from menu_item ' +
    ' join pbill_item on pbill_item.id_menu = menu_item.id_menu ' +
    ' join pbill on pbill.id = pbill_item.id_pbill and pbill.pdatetime between :reportfromdatetime_gw and :reporttilldatetime_gw ' +
    ' join pbill_return on pbill_return.id_pbill=pbill.id ' +
    ' join bill on bill.id = pbill.id_bill ' +
    ' join articles on articles.id = menu_item.id_articles ' +
    ' where not exists (select * from neg_pbill where neg_pbill.id_pbill=pbill.id) and ' +
    '     articles.article_hash = 0 and coalesce(articles.tip,0) = 0 and ' +
    '     not ((coalesce(pbill_item.price * pbill_item.quantity, 0) = 0) and ((pbill_item.rowbeg = 1) and (coalesce(articles."SENSITIVE",0) = 1))) and ' +
    '     not ((coalesce(pbill_item.price * pbill_item.quantity, 0) = 0) and ((pbill_item.rowbeg <> 1) and (coalesce(articles.sensitive_option,0) = 1))) ');
  iq.q.ParamByName('reportfromdatetime_gw').asDatetime   := FromDT;
  iq.q.ParamByName('reporttilldatetime_gw').asDatetime   := EndDT;
  iq.ExecQuery;
  While not iq.eof do begin
    Result := Result + iq.fields[0].asCurrency;
    iq.next;
  end;
end;

function  IsBillClosed(wt : IWTran; aid_bill: Int64): boolean;
var q       : IIBSQL;
    sqlStr  : string;
begin
  result := true;
  sqlStr := 'select close_datetime from bill where id=:id';
  if assigned(wt) then
    q := wt.GetPreparedIIbSql(sqlStr)
  else
    q := upos.UntillDB.GetPreparedIIbSql(sqlStr);
  q.q.Params[0].AsInt64 := aid_bill;
  q.ExecQuery;

  if q.eof then exit;
  result := not q.q.Fields[0].IsNull;
end;

procedure GetWaiterByName(name : string; var id_waiters: Int64; var pwd: String);
var iq : IIBSQL;
begin
  pwd := '';
  id_waiters := 0;
  iq := upos.UntillDB.GetPreparedIIbSql('select id, USER_POSCODE from untill_users where name=:name');
  iq.q.Params[0].asString := UTF8_Encode(name);
  iq.ExecQuery;
  if iq.eof then exit;

  id_waiters := StrToInt64Def(iq.fields[0].asString,0);
  pwd := trim(iq.fields[1].asString);
end;

function GetSalesAreaID(tableno : Integer) : Int64;
var
  q : IIBSQL;
begin
  q := upos.UntillDb.GetPreparedIIbSql('select sales_range.id_sales_area '
    + ' from sales_range '
    + ' join sales_area on sales_area.id=sales_range.id_sales_area and sales_area.is_active=1'
    + ' where sales_range.is_active=1 '
    + ' and Range_From <= :t and Range_To >= :t');
	q.q.Params[0].AsInteger := tableno;
  q.execQuery;
  if q.eof then
    Plugin.RaisePosException('Sales area for table number %d not defined',[tableno],'BlrMain.GetSalesAreaID');

  Result := q.q.fields[0].asInt64;
end;

procedure BLRMainPosIniFinit(bInit: boolean);
begin
  if(bInit) then begin
    {$ifndef REPORTGEN}
    blr := TBLRestaurant.Create(nil);
    blr.FBaseStep := TBLRRestaurant.Create(POSAlg, nil);
    {$endif}
    UnlockTableResult  := TUnlockTableResult.Create;
  end else begin
    {$ifndef REPORTGEN}
    FreeAndNil(UnlockTableResult);
    blr.FBaseStep := nil;
    FreeAndNil(blr);
    {$endif}
  end;
end;

{ TBLRestaurant }

procedure TBLRestaurant.BillOk;
begin
  POSAlg.SendOK;
end;

constructor TBLRestaurant.Create;
var
db: TUntillDB;
begin
  inherited Create(nil);

  FTableLinker := TObjectDictionary<Integer, TTableLink>.Create;
  FTableMover  := TObjectDictionary<Integer, TSize>.Create;
  FIgnoreUnlockTable := Plugin.GetSetting('common','IgnoreUnlockTable',0)=1;
  ftaBookingBillData := TTABookingBillData.Create;
  FRearSecondArticlePrinted := true;
  FTestVisaExcept := false;
  FStockBalanceLocationID := 0;
  FLastOrderedArticleList := TRSingleArticleDataList.Create;
  FmenuFilter := TMenuItemFilter.Create;
  FBarTableSorting := btsNumber;
  PosRestaurantSettings.Settings.POSKSMode := TPOSKSMode(PosRestaurantSettings.Settings.ExtraSettings.KSType);
  if (PosRestaurantSettings.Settings.POSKSMode = pkmClassic) then begin
    KitchenScreenDataFeederU.Enable;
    KSDataProFeederU.KSProDisable;
  end else begin
    KitchenScreenDataFeederU.Disable;
    KSDataProFeederU.KSProEnable;
  end;
  FTempAllowVoidBillID    := -1;
  FCurrentKSRouteID       := 0;
  FChosenCourse           := 0;
  FControlBillData.Clear;
  FControlOrderData.Clear;
  NewDelayOrderDateTime   := 0;
  NextDelayOrderDateTime  := 0;
  FCurPayerName           := '';
  FRearDisplayLastPrint   := 0;
  FRearScreenSaverPrinted := false;
  FCashDrawUserID         := 0;
  FClockInDT              := 0;
  FLaborCost              := 0;
  LastTotal               := 0;
  LastTrNumber            := '';
  LastCashAmount          := 0;
  LastReceivedAmount      := 0;
  LastReturnedAmount      := 0;
  FReportType             := rtNormal;
  NeedReturnToDirectSales := False;
  TransferFromDirectSales := 0;
  TransferToDirectSales   := 0;
  db := upos.UntillDB;

  Self.FCompSalesArea := GetComputerSalesArea(db);
  Self.FSalesKind := skByTable;

  ActiveBill := TBLRBill.Create(Self, upos.UntillDB);
  Billa := TBLRBill.Create(Self, upos.UntillDB);
  Billb := TBLRBill.Create(Self, upos.UntillDB);
  Billc := TBLRBill.Create(Self, upos.UntillDB);
  Billd := TBLRBill.Create(Self, upos.UntillDB);
  Bille := TBLRBill.Create(Self, upos.UntillDB);
  Billf := TBLRBill.Create(Self, upos.UntillDB);
  TransferOriginal := TBLRBill.Create(Self, upos.UntillDB);
  TransferResult := TBLRBill.Create(Self, upos.UntillDB);
  TempOrder    := TBLRBill.Create(Self, upos.UntillDB);
  ShadowBill   := TBLRBill.Create(Self, upos.UntillDB);
  upos.un.RegisterListener(URL_POS_GENERAL, Self);
  upos.un.RegisterListener(URL_CLOCK_OUT, Self);
  upos.un.RegisterListener(URL_POS_READMESSAGE, Self);
  upos.un.RegisterListener(URL_RES_TABLE_INFO, Self);
  upos.un.RegisterListener(URL_POS_GENERAL_CLEAR, Self);

  ActiveReservs:=TBLRReservations.Create(self, upos.UntillDB);

  CardOps := TBLRCardOps.Create(self, upos.UntillDB);

  FTableList       := TBLRTableList.Create;
  FLastPaymentList := TBlrPaymentList.Create;

  FOnHoldManager := TOnHoldManager.Create( self );


end;

destructor TBLRestaurant.Destroy;
begin

  upos.un.UnRegisterListener(URL_CLOCK_OUT, Self);
  upos.un.UnRegisterListener(URL_POS_GENERAL, Self);
  upos.un.UnRegisterListener(URL_POS_READMESSAGE, Self);
  upos.un.UnRegisterListener(URL_RES_TABLE_INFO, Self);
  upos.un.UnRegisterListener(URL_POS_GENERAL_CLEAR, Self);

  FreeAndNil( FTableList );
  FreeAndNil( FLastPaymentList );
  FreeAndNil( FmenuFilter );
  FreeAndNil( FLastOrderedArticleList );
  FreeAndNil( ftaBookingBillData );
  FreeAndNil( FTableLinker );
  FreeAndNil( FTableMover );

  inherited;
end;

procedure TBLRestaurant.InitMenuFilter(msg: TBLRMsgPutMenuFilter);
  procedure AddItemsToFilter(str : String; mf : TMenuFilterType);
  var strList : TStringList;
      i : Integer;
  begin
    strList := TStringList.Create;
    try
      StrSplitEx(str, strList, ';');
      if strList.count > 0 then begin
        for i :=0 to Pred(strList.count) do begin
          if StrToInt64Def(strList.Strings[i],0)>0 then
            fmenuFilter.AddItem(StrToInt64Def(strList.Strings[i],0),mf);
        end;
      end;
    finally
      FreeAndNil(strList);
    end;
  end;
begin
  if not assigned(menuFilter) then exit;

  menuFilter.Clear;
  if (msg.depStr<>'') then
    AddItemsToFilter(TBLRMsgPutMenuFilter(msg).depStr, mftDeps);

  if (msg.groupStr<>'') then
    AddItemsToFilter(TBLRMsgPutMenuFilter(msg).groupStr, mftGroups);

  if (msg.courseStr<>'') then
    AddItemsToFilter(TBLRMsgPutMenuFilter(msg).courseStr, mftcourses);

end;

function TBLRestaurant.AllTablesClosed : boolean;
var q : IIBSQL;
begin
  q := upos.UntillDB.GetPreparedIIbSql('select count(*) from bill '
      + ' join ks_workflow on ks_workflow.id_bill=bill.id '
      + ' and not wf_status in (:kswsFinished, :kswsPreFinished) '
      + ' where isActive=1 or close_datetime is null '
      + ' and not exists (select * from delay_bills where delay_bills.id_bill=bill.id)');
  q.q.ParamByName('kswsFinished').asInteger := Ord(kswsFinished);
  q.q.ParamByName('kswsPreFinished').asInteger := Ord(kswsPreFinished);
  q.ExecQuery;

  result := (q.fields[0].AsInteger=0);
end;

function TBLRestaurant.IsTableOverview: boolean;
begin
  result := (PosAlg.CurrentStateGUID = TBLRRestaurant.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetAutoScrollTable.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetSlideCaptionTable.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRTableSplit.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetTransferTableFrom.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetTransferTableTo.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRCombineTables.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetTableToChangeCourse.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetTableToReserve.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetMoveTable.GetStaticGUID);
  result := result or (PosAlg.CurrentStateGUID = TBLRGetTableForDelay.GetStaticGUID);
  result := result or ((PosAlg.CurrentStateGUID = TBLRTableInfoScreen.GetStaticGUID) and (TableInfoTableNo=0));
  result := result or ((PosAlg.CurrentStateGUID = TBLNeedResInfoTable.GetStaticGUID) and (TableResInfoTableNo=0));
end;

function TBLRestaurant.IsTableNeeded: boolean;
begin
  Result := POSALg.CanAcceptMessage(TBLRMsgTableNo);
end;

function TBLRestaurant.NeedPrintSecondPrinter : boolean;
begin
  result := false;
  if POSRestaurantSettings.Settings.RearSecondArticleLayout=0 then exit;
  if POSRestaurantSettings.Settings.RearSecondArticledelaySec=0 then exit;
  result := true;
end;

procedure TBLRestaurant.OnUrlEvent(url: string; ue: TUrlEvent);
begin
  if ue is TPMClearChangeFlags then begin
    ActiveBill.ClearChangeFlags;
    Billa.ClearChangeFlags;
    Billb.ClearChangeFlags;
    Billc.ClearChangeFlags;
    Billd.ClearChangeFlags;
    Bille.ClearChangeFlags;
    Billf.ClearChangeFlags;
    ShadowBill.ClearChangeFlags;
    TransferOriginal.ClearChangeFlags;
    TransferResult.ClearChangeFlags;
    TempOrder.ClearChangeFlags;

    if not RearSecondArticlePrinted
      and (SecondsBetween(Now, RearDisplayLastPrint) >= POSRestaurantSettings.Settings.RearSecondArticledelaySec) then begin
      if NeedPrintSecondPrinter then
        PrintSecondRearDisplay(ActiveBill);
      FRearSecondArticlePrinted := true;
    end;
    if RearDisplayLastPrint=0 then
      RearDisplayLastPrint := Now;
    if not FRearScreenSaverPrinted and
      (SecondsBetween(Now, RearDisplayLastPrint)>PosRestaurantSettings.Settings.ScreenSaverMin * 60) and
      (PosRestaurantSettings.Settings.RearScreenSaver > 0) and
      (PosRestaurantSettings.Settings.ScreenSaverMin > 0) then begin
      PrintRearDisplay(nil, false, false);
    end;
  end else if ue is TAfterReadMessageEvent then begin
    if SameText(ue.sInfo, 'TBLRAcceptTransfer') then begin
      TBLRConfirmTransfer.Create(PosAlg, nil, ue.iInfo )
    end else if SameText(ue.sInfo, 'TBLRRejectTransfer') then begin
      TBLRTransferWaiterWithConfirm.Create(PosAlg, nil,
        POSRestaurantSettings.Settings.ExtraSettings.TransferUsersClock, ue.iInfo)
    end;
  end else if ue is TBeforeClockOutEvent then begin
    CheckOpenTables(upos.UntillDB, upos.UserID, cotClockOut);
    upos.CheckNeedCashDeclaration( cdtOut, upos.UserID );
    CheckAllPSPTips;
  end else if ue is TClockInChangedEvent then begin
    if not CheckNeedCashDrawer(StrToDateTime(TClockInChangedEvent(ue).sInfo)) then
      upos.CheckNeedCashDeclaration( cdtIn, upos.UserID );
  end else if SameText(URL, URL_RES_TABLE_INFO) then begin
    blr.ActiveReservs.LoadByTable( TableResInfoTableNo );
  end;
end;

function TBLRestaurant.CheckNeedCashDrawer(ClockInDT : TDatetime) : boolean;
var iq  : IIBSQL;
    bNeedCashDrawer : boolean;
    stepForClockIn : TBLAlgStep;
begin
  result := false;
  if not assigned(upos_) then exit;
  if upos.UserId = GUEST_DEFAULT_USER_ID then exit;

  iq := upos.Untilldb.GetPreparedIIBSQl('select NeedStartCash from waiters where id_untill_users=:id');
  iq.q.Params[0].asInt64 := upos.UserId;
  iq.ExecQuery;

  if iq.eof then exit;
  bNeedCashDrawer := (iq.Fields[0].asInteger=1);
  if not bNeedCashDrawer then exit;

  Blr.ActiveBill.LoadFromActiveBill(nil, ORDER_EMPTY_TABLE, ORDER_MIN_TABLEPART,0, odltNormal, 0, 0);
  FCashDrawUserID := upos.GetUserID;
  FClockInDT      := ClockInDT;
  stepForClockIn := posalg.GetCurrentStep.GetFirstLogoffAfterClock;
  if not assigned(stepForClockIn) then
    stepForClockIn := posalg.GetFirstStepInStack;
  TBLSGetCashDrawAmount.Create(POSAlg, stepForClockIn, true,
    plugin.TranslatePOS('BLRAlgU', 'Cash draw amount'),
    NUMERIC_STATE_CASH_DRAW,0, ctCash, 0, true);
  result := true;
end;

procedure TBLRestaurant.PriceOk;
begin
  POSAlg.SendOK;
end;

procedure TBLRestaurant.SendArticleGlb(id: int64; artType :TArticleType=atNormal);
var msg:TBLRMsgArticle;
begin
  msg := TBLRMsgArticle.Create;
  msg.id := id;
  msg.articleType := artType;
  POSAlg.SendMessage(msg);
end;

procedure TBLRestaurant.SendBillInput(s: string);
begin
  if s = '@undo' then begin
    Self.SendUndo;
    Exit;
  end;
  if s = '@cancel' then begin
    Self.SendCancel;
    Exit;
  end;

  POSAlg.SendInput(s);

end;

procedure TBLRestaurant.SendCancel;
begin
  POSAlg.SendCancel;
end;

procedure TBLRestaurant.SendPriceCancel;
begin
  POSAlg.SendCancel;
end;

procedure TBLRestaurant.SendTable(tableno: integer; SalesArea :Int64;
  TableName :WideString; native_table : Integer; aNumberOfCovers : Integer );
var msg: TBLRMsgTableNo;
begin
  msg := TBLRMsgTableNo.Create;
  msg.tableno := tableno;
  msg.SalesArea := SalesArea;
  msg.TableName := TableName;
  msg.native_table   := native_table;
  msg.NumberOfCovers := aNumberOfCovers;
  POSAlg.SendMessage(msg);
  if PosAlg.GetCurrentGUID = TBLREditOrder.GetStaticGuid then begin
    msg := TBLRMsgTableNo.Create;
    msg.tableno := tableno;
    msg.SalesArea := SalesArea;
    msg.TableName := TableName;
    msg.native_table := native_table;
    msg.NumberOfCovers := aNumberOfCovers;
    POSAlg.SendMessage(msg);
  end;
end;

procedure TBLRestaurant.SendUndo;
begin
  POSAlg.SendUndo;
end;

procedure TBLRestaurant.SendOptionGlb(id_option, id_article: int64; bFreeOptions: boolean=false );
var msg:TBLRMsgOption;
begin
  msg := TBLRMsgOption.Create;
  msg.id_option := id_option;
  msg.id_article := id_article;
  msg.free_Option := bFreeOptions;
  POSAlg.SendMessage(msg);
end;

procedure TBLRestaurant.AddCondiment;
begin
  POSAlg.SendMessage(TBLRMsgAddCondiment.Create);
end;

procedure TBLRestaurant.AddSupplement;
begin
  POSAlg.SendMessage(TBLRMsgAddSupplement.Create);
end;

function TBLRestaurant.GetBillIDByFiscalNumber(DocumentId: String): String;
var q  : IIBSQL;
begin

  Result := '';
  if DocumentId = '' then exit;
  q := upos.UntillDB.GetPreparedIIbSql('select p.id, coalesce(np.r_type,10) pbill_status, bill.pbill_number, ' +
    ' bill.close_datetime, p.number ' +
    ' from pbill p' +
    ' join bill on p.id_bill = bill.id '+
    ' left outer join neg_pbill np on np.id_pbill = p.id' +
    ' left outer join fiscal_numbers on fiscal_numbers.id_pbill = p.id' +
    ' left outer join fiscal_numbers_ex on fiscal_numbers_ex.id_pbill = p.id' +
    ' where p.id = (select first 1 pp.id from pbill pp  ' +
       ' left join neg_pbill npp on npp.id_pbill = pp.id ' +
       ' where pp.id_bill=p.id_bill and  pp.number=p.number ' +
         ' and coalesce(pp.failurednumber,0)=coalesce(p.failurednumber,0) ' +
         ' and coalesce(pp.suffix,'''')=coalesce(p.suffix,'''') and coalesce(npp.r_type,0)=0 ' +
         ' order by pp.real_datetime desc, pp.id desc)  ' +
    ' and (fiscal_numbers.doc_id=:doc_id or fiscal_numbers_ex.doc_id=:doc_id) ' +
    ' order by p.pdatetime desc, p.number desc'
  );
  q.q.ParamByName('doc_id').asString := DocumentId;
  q.ExecQuery;
  if not q.eof then
    Result := q.q.fields[0].asString;

  ControlBillData.BillStatus  :=
    TBillStatus(GetBillStatus(q.q.fieldByName('pbill_status').asInteger,
      q.q.fieldByName('pbill_number').asString <> q.q.fieldByName('number').asString));

  RefresPaidBillhDS(4);
end;

function TBLRestaurant.GetBillIDByNumber(BillNumber : String; bNeedUpdate : Boolean = true) : String;
var
  q  : IIBSQL;
  r : String;
  s : String;
  Param1, Param2 : String;
begin

  Result := '';
  if BillNumber = '' then exit;
  r := BillNumber;
  s := '';
  while length(r) > 0 do begin
    s := copy(r,Length(R),1);
    if not IsNumeric(s) then Break;
    Delete(r, Length(R),1);
  end;
  Param1 := '';
  Param2 := '';
  if r = '' then begin
    Param1 := BillNumber;
  end
  else begin
    Param1 := copy(BillNumber, Length(R) + 1, Length(BillNumber) -  Length(R)) ;
    Param2 := r;
  end;
  q := upos.UntillDB.GetIIBSQL;

  q.q.SQl.Clear;
  q.q.SQl.Add('select p.id, coalesce(np.r_type,10) pbill_status, bill.pbill_number, ' +
    ' bill.close_datetime, p.number ' +
    ' from pbill p' +
    ' join bill on p.id_bill = bill.id '+
    ' left outer join neg_pbill np on np.id_pbill = p.id' +
    ' where p.id = (select first 1 pp.id from pbill pp  ' +
       ' left join neg_pbill npp on npp.id_pbill = pp.id ' +
       ' where pp.id_bill=p.id_bill and coalesce(npp.r_type,0)=0 ' +
       ' order by pp.real_datetime desc, pp.id desc) and '
  );
  if Param1 <> '' then begin
    q.q.SQl.Add(' p.number = ' + Param1 + ' and ');
  end else
    q.q.SQl.Add(' p.number is null and ');
  if Trim(Param2) <> '' then begin
    q.q.SQl.Add(' p.suffix = ''' + Param2 + '''');
  end else
    q.q.SQl.Add( ' p.suffix is null ');

  q.q.SQl.Add( ' and not p.number is null order by p.pdatetime desc, p.number desc');
  q.ExecQuery;
  if not q.eof then begin
    Result := q.q.fields[0].asString;
    ControlBillData.BillStatus  :=
      TBillStatus(GetBillStatus(q.q.fieldByName('pbill_status').asInteger,
        q.q.fieldByName('pbill_number').asString <> q.q.fieldByName('number').asString));
  end;
  q.Close;

  if bNeedUpdate then
    RefresPaidBillhDS(4);
end;

function TBLRestaurant.GetBillStatus(pbill_status: Integer;
  pBillClosed: boolean): Integer;
begin
  result := Ord(blsActive);
  if (pbill_status=0) then begin
    result := Ord(blsVoided)
  end else if (pbill_status=1) then
    result := Ord(blsReopened);
end;

function GetReopenBillStatus(id_pbill : Int64) :TBillStatus;
var qSel : IIBSQL;
begin
//  blsNone, blsActive, blsReopened, blsVoided, blsCredit, blsTransfer
  result := blsActive;
  qSel := upos.Untilldb.GetPreparedIIbSql('select coalesce(r_type,10) pbill_status, bill.close_datetime,'
    + ' pbill.number, bill.pbill_number '
    + ' from neg_pbill '
    + ' join pbill on pbill.id = neg_pbill.id_pbill'
    + ' join bill on pbill.id_bill = bill.id '
    + ' where id_pbill=:id');
  qSel.q.Params[0].asInt64 := id_pbill;
  qSel.ExecQuery;

  if qSel.eof then exit;

  if not qSel.q.fieldByName('close_datetime').IsNull and (qSel.q.fieldByName('pbill_status').asInteger=1) then
    result := blsActive
  else
    result :=
      TBillStatus(blr.GetBillStatus(qSel.q.fieldByName('pbill_status').asInteger,
        qSel.q.fieldByName('pbill_number').asString <> qSel.q.fieldByName('number').asString));
end;

function TBLRestaurant.GetIsCondimentNeeded: boolean;
begin
  Result := false;
  if POSAlg.IsStepInStack(TBLSGetPrnErrCorrection.GetStaticGuid) then Exit;
  Result := (ActiveBill.State = bsCondiment);
end;

function TBLRestaurant.GetIsSupplementNeeded: boolean;
begin
  Result := false;
  if POSAlg.IsStepInStack(TBLSGetPrnErrCorrection.GetStaticGuid) then Exit;
  Result := (ActiveBill.State = bsSupplement);
end;

function TBLRestaurant.IsOrderNeeded: boolean;
begin
  Result := false;
  if POSAlg.IsStepInStack(TBLSGetPrnErrCorrection.GetStaticGuid) then Exit;
  Result := POSAlg.IsStepInStack(TBLREditOrder.GetStaticGuid);
end;

function TBLRestaurant.IsPriceNeeded: boolean;
begin
  Result := POSAlg.IsStepAtTheTop(TBLRGetPrice.GetStaticGuid);
end;

function TBLRestaurant.IsOptionsNeeded: boolean;
begin
  Result := false;
  if POSAlg.IsStepInStack(TBLSGetPrnErrCorrection.GetStaticGuid) then Exit;
  Result := POSAlg.IsStepInStack(TBLRGetOption.GetStaticGuid);
end;

procedure TBLRestaurant.SendDiscountReasonID(id: int64);
var
  msg : TBLRMsgDiscountReason;
begin
  msg := TBLRMsgDiscountReason.Create;
  msg.id := id;
  POSAlg.SendMessage(msg);
end;

procedure TBLRestaurant.SendVoidReasonID(id: int64);
var
  msg : TBLRMsgVoidReason;
begin
  msg := TBLRMsgVoidReason.Create;
  msg.id := id;
  POSAlg.SendMessage(msg);
end;

procedure TBLRestaurant.GetServiceAmount(id_bill : Int64;
  var sc_amount : double;
  var scvat_amount : double);
begin
  TaxesU.GetServiceAmount(upos.UntillDB, id_bill, PosRestaurantSettings.Settings.ExtraSettings.VATSC, upos.MainCurrency.Round,
    sc_amount, scvat_amount);
end;

procedure TBLRestaurant.GetItemServiceAmount(
  id_order_item : Int64;
  id_menu_item : Int64;
  var sc_amount, scvat_amount: double );
begin

  TaxesU.GetItemServiceAmount(upos.UntillDB, id_order_item, id_menu_item,
    PosRestaurantSettings.Settings.ExtraSettings.VATSC, nil, 2,
    sc_amount, scvat_amount);
end;

function IsUserTableOwner(AUserID: Int64; tableno: Integer) : Boolean;
var
    qbill : IIBSQL;
    bCond : boolean;
begin
  qbill:= upos.UntillDB.GetPreparedIIBSQL('select ID_UNTILL_USERS, id_alter_user from bill ' +
    ' where tableno =:tableno and IsActive=1 '); //PLAN (BILL INDEX (BILL_IDISACTIVE))');
  qbill.q.Params[0].asString := IntToStr(tableno);
  qbill.ExecQuery;
  if qbill.eof then
    Result := true
  else begin
    Result := False;
    if qbill.q.fields[1].asInt64 = 0 then
       bCond := (qbill.q.fields[0].asInt64 = AUserID)
    else
       bCond := (qbill.q.fields[1].asInt64 = AUserID);
    while not qbill.eof do begin
      if bCond then begin
       Result := True;
       Break;
      end;
      qbill.Next;
    end;
  end;
end;

procedure CheckUserOrderPrivileges(AUserID: Int64; tableno: Integer);
var qq : IIBSQL;
    bOK : Boolean;
    idTableArea :Int64;
    ita: ITableArea;
    wcd : TCacheWaiter;
begin
  //Prohibit the Guest user to open tables
  //https://dev.untill.com/projects/#!498864

  if not Plugin.IsTesterModeEnabled then
  if POSRestaurantSettings.Settings.Sales_Kind <> skmDirect then
    if upos.UserId = GUEST_DEFAULT_USER_ID then
      Plugin.RaisePosException('Please log in','BLRMainU.CheckUserOrderPrivileges');

  wcd := CacheWaiters.GetCacheWaiterData( AUserId );
// if User he has privileges "ALL" ?
  bOK := false;
  if assigned(wcd) then
    bOK := (wcd.ORDER_TABLES_TYPE_ID = 0);
  if not bOK then begin
    // Check if user is an owner of this table ?
    bOK := IsUserTableOwner( AUserId, tableno );
  end;

// if User pass previous checks, maybe he is restricted by Table Area ?
  if bOK then begin
    ita := GetCacheTableArea(SysDateTimeToLocal(UPos.GetPosNow), tableno, upos.POSComputerName);
    if (ita <> nil) then
      idTableArea := ita.Id
    else
      idTableArea := 0;
    qq := upos.UntillDB.GetPreparedIIBSQL('select a.id_table_area ' +
      ' from WAITER_TABLE_AREA a ' +
      ' join TABLE_AREA t on t.id=a.id_table_area and t.is_active=1 ' +
      ' join waiters b  on a.id_waiters = b.id ' +
      ' where b.ID_UNTILL_USERS =:idUser and a.is_active=1 ');
    qq.q.Params[0].asString := IntToStr(AUserId);
    qq.ExecQuery;
    if not qq.eof then begin
      bOK := False;
      while not qq.eof do begin
        if idTableArea = qq.q.fields[0].asInt64 then begin
          bOK := true;
          Break;
        end;
        qq.next;
      end;
    end;
  end;
  if not bOK then
    Plugin.RaisePosException('You cannot work with table number "%s"',[IntToStr(Tableno)],'BLRMainU.CheckUserOrderPrivileges');
end;

procedure CheckUserClosePrivileges(AUserID: Int64; tableno: Integer);
var bOK : Boolean;
    ita: ITableArea;
    cw : TCacheWaiter;
begin
  // Check if user is an owner of this table ?
  bOK := IsUserTableOwner( AUserId, tableno );
// if User is not an owner , maybe he has privileges "ALL" ?
  cw := nil;
  if assigned(CacheWaiters) then
    cw := CacheWaiters.GetCacheWaiterData(AUserId);
  if not bOK then begin
    if assigned(cw) then
      bOK := (cw.CLOSE_TABLES_TYPE_ID = 0)
  end;
// if User pass previous checks, maybe he is restricted by Table Area ?
  if bOK then begin
    ita := GetCacheTableArea(SysDateTimeToLocal(UPos.GetPosNow), tableno,upos.POSComputerName);
    if (ita <> nil) then begin
      if assigned(cw) then
        bOK := cw.TableAreaExists( ita.Id )
    end;
  end;
  if not bOK then
    Plugin.RaisePosException('You cannot close table number "%s"',[IntToStr(Tableno)],'TBLRestaurant.CheckUserOrderPrivileges');
end;

procedure TBLRestaurant.SetRearDisplayLastPrint(const Value: TDatetime);
begin
  FRearDisplayLastPrint := Value;
end;

procedure TBLRestaurant.SetRearScreenSaverPrinted(const Value: boolean);
begin
  FRearScreenSaverPrinted := Value;
end;

procedure TBLRestaurant.SetReportType(const Value: TBLRReportType);
begin
  FReportType := Value;
end;

procedure TBLRestaurant.SetRGMProgress(const Value: Integer);
begin
  FRGMProgress := Value;
end;

procedure TBLRestaurant.SetStockBalanceLocationID(const Value: Int64);
begin
  FStockBalanceLocationID := Value;
end;

procedure TBLRestaurant.SetNeedReturnToDirectSales(const Value: Boolean);
begin
  FNeedReturnToDirectSales := Value;
end;

procedure TBLRestaurant.SetNewDelayOrderDateTime(const Value: TDateTime);
begin
  FNewDelayOrderDateTime := Value;
end;

procedure TBLRestaurant.SetNextDelayOrderDateTime(const Value: TDateTime);
begin
  FNextDelayOrderDateTime := Value;
end;

procedure TBLRestaurant.SetPaymentCurrency(const Value: Int64);
begin
  FPaymentCurrency := Value;
end;

procedure TBLRestaurant.SetTempAllowVoidBillID(const Value: Int64);
begin
  FTempAllowVoidBillID := Value;
end;

procedure TBLRestaurant.SetTransferFromDirectSales(const Value: Integer);
begin
  FTransferFromDirectSales := Value;
end;

procedure TBLRestaurant.SetTransferToDirectSales(const Value: Integer);
begin
  FTransferToDirectSales := Value;
end;

procedure TBLRestaurant.SetLaborCost(const Value: Double);
begin
  FLaborCost := Value;
end;

procedure TBLRestaurant.SetLastCashAmount(const Value: Currency);
begin
  FLastCashAmount := Value;
end;

procedure TBLRestaurant.SetLastOIFID(const Value: Int64);
begin
  FLastOIFID := Value;
end;

procedure TBLRestaurant.SetActiveBill(Value: TBLRBill);
begin
  FActiveBill:=Value;
  if assigned(Value) and (Value.id_bill > 0) then
    TThread.Synchronize(nil,
      procedure
      var
        ue: TPMDataChangedEvent;
      begin
        ue := TPMDataChangedEvent.Create(0, '', uetUpdate);
        try
          upos.un.SendEvent(URL_BLR_CHANGED, ue);
        finally
          FreeAndNil(ue);
        end;
      end);
end;

procedure TBLRestaurant.SetArticleDiscountReason(const Value: String);
begin
  FArticleDiscountReason := Value;
end;

procedure TBLRestaurant.SetArticleDiscountReasonID(const Value: Int64);
begin
  FArticleDiscountReasonID := Value;
end;

procedure TBLRestaurant.SetArticleVoidReason(const Value: String);
begin
  FArticleVoidReason := Value;
end;

procedure TBLRestaurant.SetBarTableSorting(const Value: TBarTableSort);
begin
  FBarTableSorting := Value;
  SendOpenBarTableEvent;
end;

procedure TBLRestaurant.SendOpenBarTableEvent;
var ue : TOpenBarTablesEvent;
begin
  ue := TOpenBarTablesEvent.Create(0, '', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, ue);
  finally
    FreeAndNil(ue);
  end;
end;

procedure TBLRestaurant.SetCashDrawUserID(const Value: Int64);
begin
  FCashDrawUserID := Value;
end;

procedure TBLRestaurant.SetChosenCourse(const Value: Int64);
begin
  FChosenCourse := Value;
end;

procedure TBLRestaurant.SetClockInDT(const Value: TDatetime);
begin
  FClockInDT := Value;
end;

procedure TBLRestaurant.SetCurPayerName(const Value: String);
begin
  FCurPayerName := Value;
end;

procedure TBLRestaurant.SetCurrentKSRouteID(const Value: Int64);
begin
  FCurrentKSRouteID := Value;
end;

procedure TBLRestaurant.SetInfoAliveProgress(const Value: Integer);
var ue :TInfoAliveEvent;
begin
  FInfoAliveProgress := Value;
  ue := TInfoAliveEvent.Create(0,'InfoAliveProgress', uetUpdate);
  try
    upos.SendEvent('InfoAliveProgress', ue);
  finally
    FreeAndnil(ue);
  end;
end;

procedure TBLRestaurant.SetDetailedAliveProgress(const Value: Integer);
var ue :TDetailAliveEvent;
begin
  FDetailedAliveProgress := Value;
  ue := TDetailAliveEvent.Create(0,'DetailedAliveProgress', uetUpdate);
  try
    upos.SendEvent('DetailedAliveProgress', ue);
  finally
    FreeAndnil(ue);
  end;
end;

procedure TBLRestaurant.SetIsLastBillPaid(const Value: boolean);
begin
  FIsLastBillPaid := Value;
end;

procedure TBLRestaurant.SetKSMessageAliveProgress(const Value: Integer);
var ue : TKSMessageAliveEvent;
begin
  FKSMessageAliveProgress := Value;
  ue := TKSMessageAliveEvent.Create(0,'KSMessageAliveProgress', uetUpdate);
  try
    upos.SendEvent('KSMessageAliveProgress', ue);
  finally
    FreeAndnil(ue);
  end;
end;

{ TBlrTableList }

function TBlrTableList.FindItem(aTableno: Integer): Integer;
var i : Integer;
begin
  result := -1;
  for i := 0 to Pred(count) do
    if items[i].FNumber=aTableNo then begin
      result := i;
      exit;
  end;
end;

function TBlrTableList.GetItems(Index: integer): TBlrTable;
begin
  Result := TBlrTable(inherited Items[Index]);
end;

function TBlrTableList.RegisterTable(aTableNo, aCovers: Integer): Integer;
var itemIndex : Integer;
begin
  itemIndex := FindItem(aTableno);
  if itemIndex = -1 then begin
    result := Add(TBLRTable.Create(aTableNo, aCovers));
    exit;
  end;
  result := itemIndex;
end;

procedure TBlrTableList.UnRegisterTable(aTableNo: Integer);
var itemIndex : Integer;
begin
  itemIndex := FindItem(aTableno);
  if itemIndex > -1 then begin
    self.Delete(itemIndex);
    self.Pack;
  end;
end;

{ TBlrTable }

constructor TBlrTable.Create(aTableNo, aCovers: Integer);
begin
  FNumber := aTableNo;
  FCovers := aCovers;
end;

{ TControlBillData }

procedure TControlBillData.Clear;
begin
  FClientID     := 0;
  fClientFilter := '';
  FTableNumber  := 0;
  FBillNumber   := '';
  FBillStatus   := blsNone;
  FBillID       := 0;
end;

procedure TControlBillData.SetBillID(const Value: Int64);
begin
  FBillID := Value;
end;

procedure TControlBillData.SetBillNumber(const Value: String);
begin
  FBillNumber := Value;
end;

procedure TControlBillData.SetBillStatus(const Value: TBillStatus);
begin
  FBillStatus := Value;
end;

procedure TControlBillData.SetClientFilter(const Value: string);
begin
  FClientFilter := Value;
end;

procedure TControlBillData.SetClientID(const Value: Int64);
begin
  FClientID := Value;
end;

procedure TControlBillData.SetTableNumber(const Value: Integer);
begin
  FTableNumber := Value;
end;

{ TBlrPaymentList }

function TBlrPaymentList.GetItems(Index: integer): TBlrPaymentInfo;
begin
  Result := TBlrPaymentInfo(inherited Items[Index]);
end;

{ TBlrPayment }

constructor TBlrPaymentInfo.Create(aAmount, aC_amount: Currency; aid_payment: Int64);
begin
  FAmount     := aAmount;
  FC_amount   := aC_amount;
  Fid_payment := aid_payment;
end;

{ TMenuItemFilter }
procedure TMenuItemFilter.AddItem(id: Int64; filterType: TMenuFilterType);
var ItemList : TObjectList;
begin
  if filterType=mftDeps then
    ItemList := fdeps
  else if filterType=mftGroups then
    ItemList := fgroups
  else
    ItemList := fcourses;

  ItemList.Add(TInt64.Create(id));
end;

procedure TMenuItemFilter.Clear;
begin
  Fdeps.Clear;
  Fgroups.Clear;
  Fcourses.Clear;
end;

constructor TMenuItemFilter.Create;
begin
  Fdeps    := TObjectList.Create;
  Fgroups  := TObjectList.Create;
  Fcourses := TObjectList.Create;
end;

destructor TMenuItemFilter.Destroy;
begin
  FreeAndNil(Fdeps);
  FreeAndNil(Fgroups);
  FreeAndNil(Fcourses);
  inherited;
end;

function TMenuItemFilter.IsEmpty: boolean;
begin
  result := (Fgroups.count=0) and (Fdeps.count = 0) and (Fcourses.count = 0);
end;

function GetBillTotal( aid_bill : Int64 ) : TBillTotal;
var qSelOrder : IIBSQL;
    qSelPBill : IIBSQL;
    strSQL    : String;
begin
  result.Clear;

  strSQL := 'select sum(order_item.quantity * order_item.price * (1-coalesce(bill.discount,0)) )'
    + ' ,sum(order_item.quantity * (original_price - order_item.price * (1-coalesce(bill.discount,0))) ) '
    + ' ,sum(order_item.quantity * order_item.vat * (1-coalesce(bill.discount,0)) ) '
    + ' from order_item '
    + ' join orders on orders.id=order_item.ID_orders '
    + ' join bill on orders.id_bill = bill.ID '
    + ' where bill.id=:id_bill '
  + ' union all '
    + ' select sum(menu_item.price * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer) * order_item.quantity)'
    + ' ,sum((menu_item.original_price - menu_item.price * (1-coalesce(bill.discount,0))) * cast(abs(menu_item.quantity) as integer) * order_item.quantity) '
    + ' ,sum(menu_item.vat * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer) * order_item.quantity) '
    + ' from menu_item '
    + ' join order_item on order_item.id_menu=menu_item.id_menu'
    + ' join orders on orders.id=order_item.ID_orders '
    + ' join bill on orders.id_bill = bill.ID '
    + ' where bill.id=:id_bill '
  + ' union all '
    + ' select sum(pbill_item.quantity * pbill_item.price * (1-coalesce(bill.discount,0)) ) '
    + ' ,sum(pbill_item.quantity * (original_price - pbill_item.price * (1-coalesce(bill.discount,0))) ) '
    + ' ,sum(pbill_item.quantity * pbill_item.vat * (1-coalesce(bill.discount,0)) ) '
    + ' from pbill_item '
    + ' join pbill on pbill.id=pbill_item.ID_pbill '
    + ' join PBILL_RETURN on pbill.id=PBILL_RETURN.ID_pbill '
    + ' join bill on pbill.id_bill = bill.ID '
    + ' where bill.id=:id_bill'
  + ' union all '
    + ' select sum(menu_item.price * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer) * pbill_item.quantity) '
    + ' ,sum((menu_item.original_price - menu_item.price * (1-coalesce(bill.discount,0))) * cast(abs(menu_item.quantity) as integer) * pbill_item.quantity) '
    + ' ,sum(menu_item.vat * (1-coalesce(bill.discount,0)) * cast(abs(menu_item.quantity) as integer) * pbill_item.quantity) '
    + ' from menu_item '
    + ' join pbill_item on pbill_item.id_menu=menu_item.id_menu'
    + ' join pbill on pbill.id=pbill_item.ID_pbill '
    + ' join PBILL_RETURN on pbill.id=PBILL_RETURN.ID_pbill '
    + ' join bill on pbill.id_bill = bill.ID '
    + ' where pbill.id=:id_bill';
  qSelOrder := upos.UntillDB.GetPreparedIIbSql(strSQL);
  qSelOrder.q.ParamByName('id_bill').asInt64 := aid_bill;
  qSelOrder.ExecQuery;

  if not qSelOrder.eof then
  while not qSelOrder.eof do begin
    result.total    := result.total    + qSelOrder.fields[0].asCurrency;
    result.discount := result.discount + qSelOrder.fields[1].asCurrency;
    result.vat      := result.vat      + qSelOrder.fields[2].asFloat;
    qSelOrder.Next;
  end;

  strSQL := 'select sum( price ) - sum( pbill.tips ) '
    + ' from pbill_payments'
    + ' join pbill on pbill.id=pbill_payments.id_pbill'
    + ' join PAYMENTS on pbill_payments.id_payments=PAYMENTS.id and PAYMENTS.kind<>:payment_sc'
    + ' where pbill.id_bill=:id_bill';
  qSelPbill := upos.UntillDB.GetPreparedIIbSql(strSQL);
  qSelPbill.q.ParamByName('id_bill').asInt64 := aid_bill;
  qSelPbill.q.ParamByName('payment_sc').asInteger := PAYMENT_SC;
  qSelPbill.ExecQuery;
  if not qSelPbill.eof then

  result.payment  := qSelPbill.fields[0].asCurrency;
end;

{ TBillTotal }

procedure TBillTotal.Clear;
begin
  total    := 0;
  discount := 0;
  vat      := 0;
  payment  := 0;
end;

function GetOrCreatePOSArticle( aName, aCaption: String;
  avat : Currency; avat_sign : string ) : Int64;
var qSelArt, qArt, qSelPrice, qPrice, qSelSA, qInsSA  : IIBSQL;
    cInfo : TCurrencyInfo;
    wt    : IWTran;
begin

  qSelArt := upos.UntillDB.GetPreparedIIbSql('select id from articles '
    + ' where pos_article_type=:patDiscount and lower(name)=:name and is_active=1');
  qSelArt.q.ParamByName('name').AsString  := lowercase(aname);
  qSelArt.q.ParamByName('patDiscount').AsInteger := Ord(patDiscount);
  qSelArt.ExecQuery;

  if not qSelArt.eof then begin
    result := StrToInt64Def(qSelArt.Fields[0].AsString,0);
    exit;
  end;

  wt := upos.UntillDB.getWTran;
  result := wt.GetID;
  qArt   := wt.GetPreparedIIbSql('insert into articles (id, article_number, name, '
    + ' INTERNAL_NAME, pc_text, rm_text, oman_text, barcode, id_departament, '
    + ' article_manual, article_hash, hideonhold, time_active, control_active, '
    + ' id_courses, pos_article_type, is_active) values '
    + '(:id, :number, :name, :INTERNAL_NAME, :INTERNAL_NAME, :INTERNAL_NAME, :INTERNAL_NAME,'
    + ' :barcode, :id_departament, 1, 0, 0, 0, 0, :id_courses, :patDiscount, 1)');
  qArt.q.ParamByName('id').AsInt64                := result;
  qArt.q.ParamByName('number').asInteger          := GetMaxArticleNumber(upos.UntillDB) + 1;
  qArt.q.ParamByName('name').asString             := aName;
  qArt.q.ParamByName('INTERNAL_NAME').asString    := aCaption;
  qArt.q.ParamByName('id_departament').asInt64    := GetFirstDepartmentId(upos.UntillDB);
  qArt.q.ParamByName('id_courses').AsInt64        := GetFirstCourseId(upos.UntillDB);
  qArt.q.ParamByName('patDiscount').asInteger     := Ord(patDiscount);
  qArt.ExecQuery;

  cInfo := GetUntillMainCurrency(upos.UntillDB);

  qSelPrice := wt.GetPreparedIIbSql('select id from prices where is_active=1');
  qSelPrice.ExecQuery;
  while not qSelPrice.Eof do begin
    qPrice := wt.GetPreparedIIbSql('insert into article_prices'
      + ' (id_prices, id_currency, id_articles, price, '
      + ' use_group_vat, vat, VAT_SIGN, is_active) values '
      + '(:id_prices, :id_currency, :id_articles, :price, '
      + ' :use_group_vat, :vat, :VAT_SIGN, 1)');

    qPrice.q.ParamByName('id_prices').AsInt64        := qSelPrice.FieldByName('id').AsInt64;
    qPrice.q.ParamByName('id_currency').AsInt64      := cInfo.Id;
    qPrice.q.ParamByName('id_articles').AsInt64      := result;
    qPrice.q.ParamByName('price').AsCurrency         := 0;
    qPrice.q.ParamByName('vat').AsCurrency           := avat;
    qPrice.q.ParamByName('VAT_SIGN').AsString        := avat_sign;
    qPrice.q.ParamByName('use_group_vat').AsInteger  := 0;
    qPrice.q.ExecQuery;

    qSelPrice.next();
  end;


  qSelSA := wt.GetPreparedIIbSql('select id from sales_area where is_active=1');
  qSelSA.ExecQuery;
  while not qSelSA.Eof do begin
    qInsSA := wt.GetPreparedIIBSQL( 'insert into ARTICLE_AVAILABLE (ID_SALES_AREA, ID_ARTICLES, IS_ACTIVE, limited) values '+
                                    '(:ID_SALES_AREA, :ID_ARTICLES, 1,0)');
    qInsSA.q.ParamByName('ID_SALES_AREA').AsInt64 := StrToInt64Def(qSelSA.q.fields[0].asString,0);
    qInsSA.q.ParamByName('id_articles').AsInt64   := result;
    qInsSA.ExecQuery;
    qSelSA.next();
  end;

  wt.commit;

  //Update Cache article prices
  CacheArticleBOData( result );
end;

function OtherBillSameClientExist(aid_clients : Int64) : boolean;
var q : IIBSQL;
begin
  result := false;
  if aid_clients <= 0 then exit;

  q := upos.UntillDB.GetPreparedIIBSQL('select count(*) from bill '
    + ' where close_datetime is null and isActive=1'
    + ' and not exists (select * from delay_bills where delay_bills.id_bill=bill.id)'
    + ' and not exists (select * from ta_bills where ta_bills.id_bill=bill.id)'
    + ' and id_clients =:id_clients');
  q.q.ParamByName('id_clients').asInt64 := aid_clients;
  q.ExecQuery;

  result := q.q.fields[0].asInteger >1;
end;

{ TAsyncUnlockTableEventTask }
constructor TUnlockTableResult.Create;
begin
  answerlst := TDictionary<string, TUnlockTableResultCode>.Create;
  FLock     := TCriticalSection.Create;
end;

destructor TUnlockTableResult.Destroy;
begin
	FreeAndNil(FLock);
  FreeAndNil(answerlst);
  inherited;
end;

function TUnlockTableResult.pop(aguid : String): TUnlockTableResultCode;
var value : TUnlockTableResultCode;
begin

	result := unlrNone;
  if aguid = '' then exit;

	FLock.Enter;
  try
    if answerlst.TryGetValue(aguid, value) then
      result := value;
    answerlst.Clear;
  finally
  	FLock.Leave;
  end;
end;

procedure TUnlockTableResult.push(aguid : String; Value: TUnlockTableResultCode);
begin
  if aguid = '' then exit;
	FLock.Enter;
  try
    answerlst.AddOrSetValue(aguid, value);
  finally
  	FLock.Leave;
  end;
end;

end.

