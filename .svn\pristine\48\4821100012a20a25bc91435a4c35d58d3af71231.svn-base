unit TableParentsU;

interface

uses
  Windows, Classes, SysUtils, DBClient, UntillDBU;

type
  TFieldCacheItem = class
  private
    FParentTableName: string;
    procedure SetParentTableName(const Value: string);
  public
    property ParentTableName: string read FParentTableName write SetParentTableName;
  end;

  TFieldsCache = class( TStringList )
  private
    function GetFieldCacheItem( Index: integer ): TFieldCacheItem;
  public
    function AddFieldCache( const sFieldName: string ): TFieldCacheItem;
    procedure Clear; override;
    function ItemByFieldName( const sFieldName: string ): TFieldCacheItem;
    property Items[ Index: integer ]: TFieldCacheItem read GetFieldCacheItem;
  end;

  TTableCacheItem = class
  private
    FFieldsCache: TFieldsCache;
    FTableName: string;
    procedure SetTableName(const Value: string);
  public
    constructor Create;
    destructor Destroy; override;
    function GetParentName( const sFieldName: string ): string;
    property TableName: string read FTableName write SetTableName;
  end;

  TTablesCache = class( TStringList )
  public
    function AddTableCache( const sTableName: string ): TTableCacheItem;
    procedure Clear; override;
    function ItemByTableName( const sTableName: string ): TTableCacheItem;
  end;

  TParentTableFinder = class
  private
    FTablesCache: TTablesCache;
    FTablesLoaded: boolean;
    FUntillDB: TUntillDB;
    procedure SetUntillDB(const Value: TUntillDB);
    procedure CheckSystemData;
  public
    property UntillDB: TUntillDB read FUntillDB write SetUntillDB;
    constructor Create;
    destructor Destroy; override;
    function ParentTableName( const sTableName, sFieldName: string ): string;
    function ParentTableExists(const sTableName: string; ParentList: TFieldsCache): boolean;
  end;

implementation

uses
  Controls, Forms, Variants, Dialogs, DB, IB, IBSQL, IBQuery, IBTable;

{ TFieldCacheItem }

procedure TFieldCacheItem.SetParentTableName(const Value: string);
begin
  FParentTableName := Value;
end;

{ TFieldsCache }

function TFieldsCache.GetFieldCacheItem(Index: integer): TFieldCacheItem;
begin
  Result:= Objects[ Index ] as TFieldCacheItem
end;

function TFieldsCache.AddFieldCache(const sFieldName: string): TFieldCacheItem;
begin
  Result:= TFieldCacheItem.Create;
  AddObject( sFieldName, Result )
end;

procedure TFieldsCache.Clear;
var
  ij: integer;
begin
  for ij:= 0 to Count - 1 do
    Objects[ ij ].Free;
  inherited
end;

function TFieldsCache.ItemByFieldName(const sFieldName: string): TFieldCacheItem;
var
  iPos: integer;
begin
  iPos:= IndexOf( sFieldName );
  if iPos >= 0 then
    Result:= Objects[ iPos ] as TFieldCacheItem
  else
    Result:= nil
end;

{ TTableCacheItem }

constructor TTableCacheItem.Create;
begin
  inherited;
  FFieldsCache:= TFieldsCache.Create;
  FFieldsCache.CaseSensitive:= false
end;

destructor TTableCacheItem.Destroy;
begin
  FFieldsCache.Free;
  inherited
end;

procedure TTableCacheItem.SetTableName(const Value: string);
begin
  FTableName := Value;
end;

function TTableCacheItem.GetParentName(const sFieldName: string): string;
var
  FieldItem: TFieldCacheItem;
begin
  FieldItem:=  FFieldsCache.ItemByFieldName( sFieldName );
  if FieldItem <> nil then
    Result:= FieldItem.ParentTableName
  else
    Result:= EmptyStr
end;

{ TTablesCache }

function TTablesCache.AddTableCache( const sTableName: string ): TTableCacheItem;
begin
  Result:= TTableCacheItem.Create;
  AddObject( sTableName, Result )
end;

procedure TTablesCache.Clear;
var
  ij: integer;
begin
  for ij:= 0 to Count - 1 do
    Objects[ ij ].Free;
  inherited;
end;

function TTablesCache.ItemByTableName(const sTableName: string): TTableCacheItem;
var
  iPos: integer;
begin
  iPos:= IndexOf( sTableName );
  if iPos >= 0 then
    Result:= Objects[ iPos ] as TTableCacheItem
  else
    Result:= nil
end;

{ TParentTableFinder }

constructor TParentTableFinder.Create;
begin
  inherited;
  FTablesCache:= TTablesCache.Create;
  FTablesCache.CaseSensitive:= false
end;

destructor TParentTableFinder.Destroy;
begin
  FTablesCache.Free;
  inherited;
end;

procedure TParentTableFinder.SetUntillDB(const Value: TUntillDB);
begin
  FUntillDB := Value;
end;

procedure TParentTableFinder.CheckSystemData;
var
  Query: IIBSql;
  sTableName: string;
  TableItem: TTableCacheItem;
begin
  assert( assigned(FUntillDB));
  assert( FUntillDB.IsConnected );  
  if not FTablesLoaded and FUntillDB.IsConnected then begin
    Query := FUntillDB.GetIIBSql;
    Query.SetText( 'select r.rdb$relation_name table_from,iseg.rdb$field_name field_from,' +
                   'ind2.rdb$relation_name table_to ' +
                   'from ' +
                   'rdb$relations r,rdb$indices ind,rdb$index_segments iseg,rdb$ref_constraints rc,' +
                   'rdb$indices ind2,rdb$relation_fields rf,rdb$fields f ' +
                   'where ' +
                   'ind.rdb$relation_name=r.rdb$relation_name and (not ind.rdb$foreign_key is null) and ' +
                   '(iseg.rdb$index_name=ind.rdb$index_name) and (rc.rdb$constraint_name=ind.rdb$index_name ) and ' +
                   '(ind2.rdb$index_name=rc.rdb$const_name_uq) and (rf.rdb$relation_name = r.rdb$relation_name ) and ' +
                   '(rf.rdb$field_name = iseg.rdb$field_name ) and (f.rdb$field_name = rf.rdb$field_source ) and ' +
                   '(f.rdb$field_type = 16 ) ' +
                   'order by ' +
                   'r.rdb$relation_name' );
    Query.ExecQuery;
    sTableName:= EmptyStr;
    TableItem:= nil;
    while not Query.EOF do begin
      if Trim( Query.q.FieldByName( 'table_from' ).AsString ) <> EmptyStr then begin
        if TrimRight( Query.q.FieldByName( 'table_from' ).AsString ) <> sTableName then begin
          sTableName:= TrimRight( Query.q.FieldByName( 'table_from' ).AsString );
          TableItem:= FTablesCache.AddTableCache( sTableName )
        end;
        TableItem.FFieldsCache.AddFieldCache( TrimRight( Query.q.FieldByName( 'field_from' ).AsString ) ).ParentTableName:=
          TrimRight( Query.q.FieldByName( 'table_to' ).AsString )
      end;
      Query.Next
    end;
    FTablesLoaded:= true
  end
end;

function TParentTableFinder.ParentTableExists(const sTableName: string; ParentList: TFieldsCache): boolean;
var
  TableItem: TTableCacheItem;
  ij: integer;
begin
  Assert( Assigned( ParentList ) );
  Result:= false;
  CheckSystemData;
  TableItem:=  FTablesCache.ItemByTableName( sTableName );
  if TableItem <> nil then begin
    ParentList.Clear;
    for ij:= 0 to TableItem.FFieldsCache.Count - 1 do
      ParentList.AddFieldCache( TableItem.FFieldsCache[ ij ] ).ParentTableName:= TableItem.FFieldsCache.Items[ ij ].ParentTableName;
    Result:= true
  end
end;

function TParentTableFinder.ParentTableName(const sTableName, sFieldName: string): string;
var
  TableItem: TTableCacheItem;
begin
  CheckSystemData;
  TableItem:=  FTablesCache.ItemByTableName( sTableName );
  if TableItem <> nil then
    Result:= TableItem.GetParentName( sFieldName )
  else
    Result:= EmptyStr
end;

end.
