create table table_area_department (
  id u_id,
  id_table_area bigint,
  id_department bigint,
  is_active smallint,
  is_active_modified timestamp,
  is_active_modifier varchar(30),
  constraint table_area_department_pk primary key (id),
  constraint table_area_department_fk1 foreign key (id_table_area) references table_area(id),
  constraint table_area_department_fk2 foreign key (id_department) references department(id)
);
commit;
grant all on table_area_department to untilluser;
commit;
execute procedure register_sync_table_ex('table_area_department', 'b', 1);
commit;
execute procedure register_bo_table('table_area_department', 'id_table_area', 'table_area');
commit;
execute procedure register_bo_table('table_area_department', 'id_department', 'department');
commit;

