SET TERM ^ ;



CREATE OR ALTER TRIGGER STOCK_TURNOVER_INS_UPD_TRIG FOR STOCK_TURNOVER
ACTIVE AFTER INSERT POSITION 0
as
declare cnt integer;
declare id_od bigint;
declare newbalance decimal(17,4);
begin
  if ((deleting) or (updating)) then
    exit;
  
  select count(*) from stock_balance where id_inventory_item=new.id_inventory_item and id_stock_locations=new.id_stock_locations into :cnt;

  if (:cnt = 0) then
    insert into stock_balance (id_inventory_item, amount, price, id_stock_locations) values (new.id_inventory_item, new.quantity, new.standart_price * new.quantity, new.id_stock_locations);
  else begin
    update stock_balance set amount=amount + new.quantity where id_inventory_item = new.id_inventory_item and id_stock_locations=new.id_stock_locations;
    update stock_balance
       SET price = AMOUNT * (select sum(QUANTITY * STANDART_PRICE)/sum(QUANTITY)
         FROM stock_turnover st1
         Where st1.ID_INVENTORY_ITEM=stock_balance.ID_INVENTORY_ITEM
         and st1.QUANTITY>0 and st1.entity_type=2 and id_inventory_item = new.id_inventory_item)
    where id_inventory_item = new.id_inventory_item;
  end

  select first 1 id from stock_open_day where conducted=0 order by sdatetime desc into :id_od;
  if (:id_od > 0) then
    update stock_open_day set conducted=1 where id=:id_od;

end
^


SET TERM ; ^
