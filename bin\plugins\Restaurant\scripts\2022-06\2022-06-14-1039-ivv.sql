SET TERM ^ ;

create or alter procedure FIX_KS_1000_ITEMS
as
begin
      delete from ks_workflow_options where ks_workflow_options.id_ks_workflow_items in (select id from ks_workflow_items where quantity=0 and not exists(select * from ks_workflow_void where id_ks_workflow_items=ks_workflow_items.id));
      delete from KS_WORKFLOW_ITEMS_ALLERGENS where KS_WORKFLOW_ITEMS_ALLERGENS.ID_KS_WORKFLOW_ITEMS in (select id from ks_workflow_items where quantity=0 and not exists(select * from ks_workflow_void where id_ks_workflow_items=ks_workflow_items.id));
      delete from ks_cooked_spent where ks_cooked_spent.id_ks_wf_item in (select id from ks_workflow_items where quantity=0 and not exists(select * from ks_workflow_void where id_ks_workflow_items=ks_workflow_items.id));
      delete from ks_workflow_items where quantity=0 and not exists(select * from ks_workflow_void where id_ks_workflow_items=ks_workflow_items.id);
      delete from ks_wf_counters where id_wf in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from KS_WF_STEPS where id_ks_workflow in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from ks_workflow_ks where id_ks_workflow in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from KITCHEN_SCREEN_READY where id_ks_workflow in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from ks_messages_viewed where id_ks_messages in (select id from KS_MESSAGES where id_wf in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id)));
      delete from KS_MESSAGES where id_wf in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from KS_WORKFLOW_LINK where id_ks_workflow in (select id from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id));
      delete from ks_workflow where not exists(select * from ks_workflow_items where id_ks_workflow=ks_workflow.id);
end^

^
grant execute on procedure FIX_KS_1000_ITEMS to SYSDBA
^
grant execute on procedure FIX_KS_1000_ITEMS to untilluser
^
commit
^
SET TERM ; ^
commit;

