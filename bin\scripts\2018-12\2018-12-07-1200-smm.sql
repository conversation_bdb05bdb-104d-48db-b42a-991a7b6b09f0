create table payment_bookkeeping (
    id u_id,
    id_payments bigint,
    id_bookkeeping bigint,
    kind smallint,
    key varchar(50),
    val varchar(50),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint payment_bookkp_pk primary key (id),
    constraint payment_bookkp_fk1 foreign key (id_payments) references payments(id),
    constraint payment_bookkp_fk2 foreign key (id_bookkeeping) references bookkeeping(id)
);
commit;
grant all on payment_bookkeeping to untilluser;
commit;
execute procedure register_sync_table_ex('payment_bookkeeping', 'b', 1);
commit;
execute procedure register_bo_table('payment_bookkeeping', 'id_payments', 'payments');
commit;



