alter table coupon_payments add id_option_parent bigint,
    add constraint cppm_fk4 foreign key (id_option_parent) references order_item(id);
commit;

CREATE OR ALTER VIEW GETCPPPRICE(
    ID_ORDER_ITEM,
    PRICE)
AS
select oi.id id_order_item,
       cp.coupon_template_price / (select coalesce(sum(oi1.quantity * oi1.original_price),1)
        from order_item oi1
        join coupon_payments cp1 on cp1.id_order_item = oi1.id
        where cp1.id_option_parent = cp.id_option_parent and oi1.rowbeg = 0)
    from order_item oi
    left outer join coupon_payments cp on cp.id_order_item = oi.id
    left outer join coupon_items cpi on cpi.id = cp.id_coupon_items
    left outer join coupons cpn on cpn.id = cpi.id_coupons
    where (cast(CURRENT_Date as timestamp) + (CURRENT_time - cast(CURRENT_TIMESTAMP - f_getsystime() as time)) /3600/24 > cpn.end_dt)
         or (cpi.used_dt is not null) or (cpn.single_barcode=0);
;
commit;
grant all on GETCPPPRICE to untilluser;
commit;
