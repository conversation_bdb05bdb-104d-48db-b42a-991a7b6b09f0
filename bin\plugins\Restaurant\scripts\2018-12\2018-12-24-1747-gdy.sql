set term !! ;
create or alter procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint,
    ID_ORDERS bigint)
returns (
    OSIG varchar(1024))
as
declare variable ID_COURSES varchar(20);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable TEXT varchar(20);
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select order_item.rowbeg, cast(order_item.kind as char(1)), 
         cast(coalesce(order_item.id_articles, 0) as varchar(20)), order_item.text,
         cast(coalesce(order_item.ID_COURSES, 0) as varchar(20))
  from order_item where order_item.id_orders=:ID_ORDERS and order_item.id >= :id_order_item
  into
       :rowbeg, :kind, :id_articles, :text, :id_courses
       do begin
    if (idx>0 and rowbeg=1) then break;
    if (id_articles='0') then begin
      osig = osig || kind || text;
    end else begin
      osig = osig || kind || id_articles;
    end
    if (id_courses <> '0') then
        osig = osig || id_courses;
    idx = idx + 1;
  end
  suspend;
end;
!!

create or alter procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024))
as
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable ID_COURSES varchar(20);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  for
  select menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text,
         cast(coalesce(menu_item.ID_COURSES, 0) as varchar(20))
         from menu_item
         join order_item on order_item.id_menu = menu_item.ID_MENU
  where menu_item.id >= :id_menu_item and order_item.QUANTITY > 0
  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text, :id_courses
       do begin
    if (idx>0 and rowbeg=1) then break;
    osig = osig || 'mi' || kind;
    if (id_articles='0') then begin
      osig = osig || text;
    end else begin
      osig = osig || '_' || id_articles;
    end
    if (id_courses <> '0') then
        osig = osig || '_' || id_courses;
    idx = idx + 1;
  end
  suspend;
end;
!!
set term ; !!

commit;
