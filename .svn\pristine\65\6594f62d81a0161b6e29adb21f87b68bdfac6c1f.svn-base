[Start]
**************************************************
TimeStamp=05.04.2016 10:48
Interval=0.000678067131957505;30.12.1899 0:00:58
[Class]
Name=TBLRGMTestClearSales
TimeStamp=05.04.2016 10:48
Interval=9.43518462008797E-5;30.12.1899 0:00:08
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=2958133.********;02.02.9999 11:45:20
[Class]
Name=TBLMsgResetDate
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
ShowException=True
[Class]
Name=TBLRMsgSetZReportData
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
ZDateTime=38019.**********;02.02.2004 8:45:20
ZNumber=1
[Class]
Name=TBLRMsgVanDuijnenClearStatistics
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLGChangeExactCurrentDate
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
dt=-0.****************;30.12.1899 2:00:00
[Class]
Name=TBLRGMTestClearAccountsHistory
TimeStamp=05.04.2016 10:48
Interval=2.31484591495246E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
tillDate=2958133.********;02.02.9999 11:45:20
[Class]
Name=TBLRGMTestSetInvoiceNumber
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
NewNumber=1
[Class]
Name=TBLRGMsgSaveTestSettings
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=3
BookOnClientAcsending=False
SkipSyncCheck=True
TracePspRequests=False
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance_common set amount=0
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update accounts set last_invoice='12/30/1899 00:00:00'
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update clients set last_invoice='12/30/1899 00:00:00'
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance set amount=0
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance set amount=-1200 where id_clients=**********
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance set amount=0.0 where id_clients=**********
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance set amount=1000 where id_clients=**********
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update account_balance set amount=-120 where id_clients=***********
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update SETTINGS set BOOKKP_NUMBER=1
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update clients set LAST_PERCENT_DISCOUNT=0
[Class]
Name=TBLRGMTestDeleteAllInvoices
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLSMsgNeedReports
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMsgSetOrderPosition
TimeStamp=05.04.2016 10:48
Interval=8.*************9E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=4
DatasetName=TReportParamsInfoDataSet
KeepMultiPosition=False
NewPos=0
OrderType=
[Class]
Name=TBLSMsgNeedClearReportParameter
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMsgSetOrderPosition
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=4
DatasetName=TReportParamsInfoDataSet
KeepMultiPosition=False
NewPos=1
OrderType=
[Class]
Name=TBLSMsgNeedClearReportParameter
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMsgSetOrderPosition
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=4
DatasetName=TReportParamsInfoDataSet
KeepMultiPosition=False
NewPos=2
OrderType=
[Class]
Name=TBLSMsgNeedClearReportParameter
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=4.62961907032877E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update settings set fromtime='01/01/2000 10:00:00'
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update settings set totime='01/01/2000 20:00:00'
[Class]
Name=TBLRGMTestAllowReopen
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
ReopenDays=0
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update settings set fromtime='01/01/2000 10:00:00'
[Class]
Name=TBLRGMTestExecSQL
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Text=update settings set totime='01/01/2000 20:00:00'
[Class]
Name=TBLRGMTestSetAllowAnyPaymentTypesInInvoice
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Allow=False
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=3.47215973306447E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=5
[Class]
Name=TBLRMsgNeedClient
TimeStamp=05.04.2016 10:48
Interval=2.89352465188131E-6;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLMessageClear
TimeStamp=05.04.2016 10:48
Interval=4.74530679639429E-7;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=05.04.2016 10:48
Interval=3.47223249264061E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:48
Interval=1.157408405561E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgSetSPMode
TimeStamp=05.04.2016 10:48
Interval=6.94446498528123E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
DiscountPaymentID=0
spmode=1
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=05.04.2016 10:48
Interval=2.31477315537632E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TBLRMsgSetVatType
TimeStamp=05.04.2016 10:48
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=3
ST=0;30.12.1899
VatSC=False
vt=vtIncluded
[Class]
Name=TBLMessageKeyboardString
TimeStamp=05.04.2016 10:48
Interval=4.81018505524844E-5;30.12.1899 0:00:04
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=1.10069449874572E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLRMsgNeedClient
TimeStamp=05.04.2016 10:48
Interval=1.2060183507856E-5;30.12.1899 0:00:01
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLRMsgClient
TimeStamp=05.04.2016 10:48
Interval=7.88195029599592E-6;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=1
id=25000089990
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=05.04.2016 10:48
Interval=4.05671235057525E-5;30.12.1899 0:00:03
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=1
Value=True
[Class]
Name=TBLRMsgClient
TimeStamp=05.04.2016 10:48
Interval=9.8402779258322E-5;30.12.1899 0:00:08
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=1
id=25000089990
[Class]
Name=TBLMessageOk
TimeStamp=05.04.2016 10:48
Interval=6.49305729893968E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgArticle
TimeStamp=05.04.2016 10:48
Interval=1.28587998915464E-5;30.12.1899 0:00:01
Exception=
StateGUID={28055024-DD33-45F3-9C75-7B134A3AE6CA}
StateName=TBLRGetPrice
FieldCount=9
articleType=atNormal
id=5000000463
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=05.04.2016 10:48
Interval=8.34490492707118E-6;30.12.1899
Exception=
StateGUID={28055024-DD33-45F3-9C75-7B134A3AE6CA}
StateName=TBLRGetPrice
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=05.04.2016 10:48
Interval=3.36805533152074E-6;30.12.1899
Exception=
StateGUID={28055024-DD33-45F3-9C75-7B134A3AE6CA}
StateName=TBLRGetPrice
FieldCount=1
s=0
[Class]
Name=TBLMessageOk
TimeStamp=05.04.2016 10:48
Interval=3.35647928295657E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=05.04.2016 10:48
Interval=9.50231333263218E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=05.04.2016 10:48
Interval=1.33333378471434E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 222 a   Waiter:   Peter 02.02.2004 11:45                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Desserts---------------------                               
 1      <USER> <GROUP>        10,00    10,00   1,74    8,26 21,0%         8,26    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Account     3,00        3,00        0,00                                        
Account     7,00        7,00        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=***********
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Account
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=05.04.2016 10:48
Interval=3.22222185786813E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLMsgSetDate
TimeStamp=05.04.2016 10:48
Interval=3.82523139705881E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
DateTime=38019.**********;02.02.2004 11:47:20
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=5.09259552927688E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLRMsgNeedClient
TimeStamp=05.04.2016 10:48
Interval=1.76273169927299E-5;30.12.1899 0:00:01
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:48
Interval=2.07407429115847E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:48
Interval=1.3564815162681E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgNeedSA
TimeStamp=05.04.2016 10:48
Interval=8.57638951856643E-6;30.12.1899
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
FieldCount=0
[Class]
Name=TBLRMsgSA
TimeStamp=05.04.2016 10:48
Interval=1.39120311359875E-5;30.12.1899 0:00:01
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
FieldCount=1
id=25000089952
[Class]
Name=TBLRMsgNeedPrintInvoice
TimeStamp=05.04.2016 10:48
Interval=6.49305729893968E-6;30.12.1899
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
StateData=
>>>Pos ticket
Printer: A4; Ticket: 15000038425
Invoice                                                                1
--------------------------------------------------------------------------------
From:          01.01.2003  00:00:00                                             
Till:          02.02.2004  11:47:20                                             
--------------------------------------------------------------------------------
Printed by:    Peter                             02.02.2004 11:47               
================================================================================
Leonid on Invoice                                                               
  02.02.2004 11:45                                                              
                    Peter                  222 a                                
     1          3,00           Banana Split             Desserts      3,00      
                                                            Total 3,00          

<<<Pos ticket
<<<<<EOSD
FieldCount=1
CustomLayout=0
[Class]
Name=TBLRMsgNeedMarkInvoice
TimeStamp=05.04.2016 10:48
Interval=5.78703475184739E-6;30.12.1899
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=0
[Class]
Name=TBLSMsgBeforeLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=0
[Class]
Name=TBLSMsgAfterLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=05.04.2016 10:48
Interval=9.03935142559931E-6;30.12.1899
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=**********
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:48
Interval=1.22916680993512E-5;30.12.1899 0:00:01
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
FieldCount=0
[Class]
Name=TBLSMsgBeforeLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
FieldCount=0
[Class]
Name=TBLSMsgAfterLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={FBA0E303-E717-49c6-9AC6-4242AECBC07E}
StateName=TBLRSelectSA
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:48
Interval=1.03124984889291E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLSMsgBeforeLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLSMsgAfterLayerVisibility
TimeStamp=05.04.2016 10:48
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=1.54166700667702E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=13
[Class]
Name=TBLRMsgTableNo
TimeStamp=05.04.2016 10:48
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=
tableno=13
[Class]
Name=TBLRMsgNeedClient
TimeStamp=05.04.2016 10:48
Interval=1.10069449874572E-5;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLRMsgClient
TimeStamp=05.04.2016 10:48
Interval=1.03240745374933E-5;30.12.1899
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=1
id=25000089990
[Class]
Name=TBLRMsgNeedPrintInvoice
TimeStamp=05.04.2016 10:49
Interval=1.50462947203778E-5;30.12.1899 0:00:01
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
StateData=
>>>Pos ticket
Printer: A4; Ticket: 15000038425
Invoice                                                                2
--------------------------------------------------------------------------------
From:          01.01.2003  00:00:00                                             
Till:          02.02.2004  11:47:20                                             
--------------------------------------------------------------------------------
Printed by:    Peter                             02.02.2004 11:47               
================================================================================
Leonid on Invoice                                                               
  02.02.2004 11:45                                                              
                    Peter                  222 a                                
     1          7,00           Banana Split             Desserts      7,00      
                                                            Total 7,00          

<<<Pos ticket
<<<<<EOSD
FieldCount=1
CustomLayout=0
[Class]
Name=TBLRMsgNeedClientDepositOnAccount
TimeStamp=05.04.2016 10:49
Interval=3.85069433832541E-5;30.12.1899 0:00:03
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLRMsgNeedMarkInvoice
TimeStamp=05.04.2016 10:49
Interval=2.42245369008742E-5;30.12.1899 0:00:02
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=05.04.2016 10:49
Interval=1.65740784723312E-5;30.12.1899 0:00:01
Exception=
StateGUID={D7791264-CE50-46e3-AF58-EBEA8C86A2AC}
StateName=TBLRMarkInvoice
FieldCount=17
AskEmail=False
AsTips=False
BillQty=1
EFTData=
Email=
ExternalID=
idParts=0
id_payments=**********
DetailId=0
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
DetailSpecified=False
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:49
Interval=1.74652741407044E-5;30.12.1899 0:00:01
Exception=
StateGUID={5C17A421-E651-4e95-9C20-79A06DF31F70}
StateName=TBLRSelectClient
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:49
Interval=1.26041704788804E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLMessageCancel
TimeStamp=05.04.2016 10:49
Interval=2.39814762608148E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=05.04.2016 10:49
Interval=6.63194441585802E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=05.04.2016 10:49
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=05.04.2016 10:49
Interval=2.0590283384081E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.**********;02.02.2004 11:47:20
[Include]
Name=reset_all
