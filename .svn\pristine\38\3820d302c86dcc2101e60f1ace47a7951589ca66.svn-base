unit RestaurantCashDatasetsU;

interface
uses Forms, DBClient, UntillDBU, UntillPOSU, DB, Classes, SysUtils, ClassesU, Variants,
  Graphics, FixedIdU, FalconMsgFrm, NamedParamsU, UntillAdminClientU, Windows,
  KSManagerU, KSDataProFeederU, Generics.Collections, CacheSalesAreaU, Contnrs,
  KSTypesU, RetranslatorU, UrlNotifierU, UntillTimerU, RestaurantCacheU;
const
  URL_DAILYSTOCK_CHANGED = 'daylystockchanged';
  URL_DAILYSTOCK_SF_CHANGED = 'sfdaylystockchanged';
  INIT_MINCOURSE_NUMBER = 10000000;
  ARTILCE_CACHE_REFRESH_INTERVAL = 1000;

const
  cache_art_field_id              = 0;
  cache_art_field_id_departament  = 1;

type
  TReasonData = record
    name: String;
    bookkeepingId: Int64;
  end;

  TArticlePriceData = record
    price       : Currency;
    id_currency : Int64;
    round       : Integer;
    procedure Clear;
    function Empty: boolean;
  end;

  TCashCommonRestaurantData = record
    BonusGroupsExist : boolean;
  end;

  TSupplierData = record
    id           : Int64;
    name         : String;
    min_amount   : Currency;
    delivery_day : Integer;
    order_day    : Integer;
    order_email  : String;
    description  : String;
    customer_nr  : String;
  public
    procedure Clear;
  end;

  TKSPurposeData = record
    name : String;
    purpose_type : Integer;
  end;

  TOrderTypeData = class
  public
    name   : String;
    number : Integer;
    constructor Create(anumber : Integer; aname : string);
  end;

  TCacheOrderTypes = class
  private
    procedure Clear;
    procedure RefreshData;
  public
    list : TObjectDictionary<Int64, TOrderTypeData>;
    function GetOrderTypeName(aid : Int64): String;
    constructor Create;
    destructor Destroy; override;
  end;

  TCacheKSSubst = class
  public
    name       : String;
    id_ks_main : Int64;
    subset     : Integer;
    constructor Create(aname : string; aid_ks_main : Int64; asubset : Integer);
  end;

  TCacheKSSubsts = class
  private
    list : TObjectDictionary<Int64, TCacheKSSubst>;
    procedure Clear;
    procedure RefreshData;
  public
    procedure FillListBySubset(aid_ks : Int64; kslist : TStringList);
    function GetSubstData(aid : Int64): TCacheKSSubst;
    constructor Create;
    destructor Destroy; override;
  end;

  TPriceData = class
    price  : Currency;
    symbol : String;
    symbol_after : boolean;
    constructor Create(aprice  : Currency;
      asymbol : String; asymbol_after :boolean);
  end;

  TArtPriceData = class
  private
    list : TDictionary<Int64, TPriceData>;
  public
    constructor Create;
    destructor Destroy; override;
  end;

  TArtPriceList = class
  private
    list : TDictionary<Int64, TArtPriceData>;
    procedure RefreshData(aid_art : Int64 = 0);
  public
    function GetArtPrice(aid_art, aid_price : Int64): TPriceData;
    constructor Create;
    destructor Destroy; override;
  end;

  TCacheAllergenPics = class
  private
    list : TObjectDictionary<Int64, TMemoryStream>;
    alt_list : TObjectDictionary<Int64, TMemoryStream>;
    procedure ClearList;
    procedure ClearAltList;
    procedure Clear;
  public
    procedure RefreshData;
    function GetImageStream( aid : Int64 ): TMemoryStream;
    function GetAltImageStream( aid : Int64 ): TMemoryStream;
    constructor Create;
    destructor Destroy; override;
  end;

  TKSPURefershParams = record
     dt : TDatetime;
     id_ksc  : Int64;
     max_rec : Integer;
     Status  : TKSWorkflowStatus;
     procedure Reset;
  end;

  TCacheSizeModifier = class
  private
    Fid      : Int64;
    Fid_main : Int64;
    Fnumber  : Integer;
    FName    : string;
  public
    property id      : Int64 read fid;
    property number  : Integer read fnumber;
    property id_main : Int64 read fid_main;
    property Name    : string read fName;
    constructor Create(aid : Int64; aid_main : Int64;
      anumber  : Integer; aName : String);
  end;

  TCacheSizeModifiers = class
  private
    list : TObjectList;
  public
    procedure RefreshData;
    function  GetSMName( aid : Int64; aLangId : String ): String;
    procedure GetByMain( aid_main : Int64; aList : TStringList);
    constructor Create;
    destructor Destroy; override;
  end;

  TCourseData = record
  public
    id         : Int64;
    name       : string;
    number     : Integer;
    separate   : boolean;
    changeable : boolean;
    need_ask_course : boolean;
    color      : Integer;
    auto_fire  : boolean;
    time_frame : Integer;
    ks_countdown_type : Integer;
    print_on_hold : Integer;
    print_next_ticket : boolean;
    start_per_ks      : boolean;
    procedure ClearData;
    procedure FillData(
      aid : Int64
      ; aname       : String
      ; anumber     : Integer
      ; aseparate   : boolean
      ; achangeable : boolean
      ; acolor      : Integer
      ; aauto_fire  : boolean
      ; atime_frame : Integer
      ; aks_countdown_type : Integer
      ; aneed_ask_course : boolean
      ; aprint_on_hold : Integer
      ; aprint_next_ticket : boolean
      ; astart_per_ks  : boolean
      );
  end;

  TPriceDSType  = (pdtArticle,pdtOption,pdtBonus,pdtMainBonus);
  TCashArticleSADataSet = class( TTypedDS)
  private
    procedure DeleteArtByIds(aid_arts: array of Int64);
  protected
    procedure AddFieldDefs; override;
    procedure AssignFieldValues(Qa : TCashArticleSADataSet);
    procedure DeleteArtById( aid_art : Int64 );
  public
    id : TField;
    id_departament    : TField;
    id_sales_area     : TField;
    sequence          : TField;
    limited           : TField;
    id_periods        : TField;
    abs_position      : TField;
    menu_items_count  : TField;
  end;

  TCPTData = class
  private
    FName: String;
    Fid_prices: Int64;
    Fprice: Currency;
    Fid: Int64;
    Fmax_free_qty: Integer;
    FNumber: Integer;
    Fid_options: Int64;
    FArtList   : TStringList;
  public
    property id         : Int64 read Fid write fid;
    property Name       : String read FName write fName;
    property Number     : Integer read FNumber write fNumber;
    property id_options : Int64 read Fid_options write fid_options;
    property id_prices  : Int64 read Fid_prices write fid_prices;
    property price      : Currency read Fprice write fprice;
    property max_free_qty : Integer read Fmax_free_qty write fmax_free_qty;
    property ArtList    : TStringList read FArtList;
    constructor Create;
    destructor Destroy; override;
  end;

  TCacheCPTs = class
  private
    FCacheCPTDatas : TDictionary<Int64, TCPTData>;
    function GetCount :Integer;
  public
    property    Count : integer read GetCount;
    procedure   ClearCacheCptDatas;
    procedure   RefreshData;
    constructor Create;
    destructor  Destroy; override;
    function GetCPTData(AID_CPT: Int64): TCPTData;
  end;

  TCacheClientExcepts = class
  private
    list : TObjectDictionary<Int64, TObjectDictionary<Int64, Int64>>;
    procedure   Clear;
    procedure   RefreshData;
  public
    function GetPriceByDate(aid_client : Int64; adt : TDatetime) : Int64;
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheOPTs = class
  private
    FCacheOPTDatas : TObjectDictionary<Int64, Integer>;
  public
    procedure   RefreshData;
    constructor Create;
    destructor  Destroy; override;
    function GetOPTData(AID_OPT: Int64): Integer;
  end;

  TComboPromoItem = class
  private
    fid_promo_item: Int64;
    foption_number: integer;
    Fprice: Currency;
    fcourse_number: integer;
  public
    property id_promo_item: Int64 read fid_promo_item;
    property option_number: integer read foption_number;
    property price: Currency read fprice;
    property course_number: integer read fcourse_number;
  end;

  TComboPromoItems = class
  private
    Flist       : TDictionary<string, TList<TComboPromoItem>>;
    procedure   ClearData;
  public
    function GetItemByKey(pKey : string) :TList<TComboPromoItem>;
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheWaiter = class
  private
    fallowTransfer  : boolean;
    fdirect_table   : Integer;
    fta_role        : Integer;
    fCLOSE_TABLES_TYPE_ID : Integer;
    fORDER_TABLES_TYPE_ID : Integer;
    fTableAreas  : TStringList;
    procedure AddTableArea(aid_ta : Int64);
  public
    property allowTransfer  : boolean read fallowTransfer write fallowTransfer;
    property direct_table : Integer read fdirect_table;
    property ta_role : Integer read fta_role;
    property CLOSE_TABLES_TYPE_ID : Integer read FCLOSE_TABLES_TYPE_ID;
    property ORDER_TABLES_TYPE_ID : Integer read FORDER_TABLES_TYPE_ID;
    property TableAreas  : TStringList read fTableAreas;
    function TableAreaExists(aid_ta : Int64) : boolean;
    constructor Create(adirect_table, Ata_role, ACLOSE_TABLES_TYPE_ID,
      AORDER_TABLES_TYPE_ID : Integer; aallowTransfer : boolean);
    destructor  Destroy; override;
  end;

  TCacheWaiters = class
  private
    FCacheWaiters : TDictionary<Int64, TCacheWaiter>;
  public
    property    Waiters : TDictionary<Int64, TCacheWaiter> read fCacheWaiters;
    procedure   ClearCacheWaiters;
    procedure   RefreshData;
    constructor Create;
    destructor  Destroy; override;
    function GetCacheWaiterData(AID: Int64): TCacheWaiter;
  end;

  TAllergenData = class
  private
    fnumber  : Integer;
    fname    : String;
    fmsname  : TLabelCaptionList;
    fmsPic   : TMemoryStream;
  public
    property number  : Integer read fnumber;
    property name    : String read fname;
    property msname  : TLabelCaptionList read fmsname;
    property msPic   : TMemoryStream read fmsPic;
    constructor Create(anumber : Integer; aname :String;
      amsname : TLabelCaptionList; amsPic: TMemoryStream);
    destructor  Destroy; override;
  end;

  TCacheAllergens = class
  private
    FList : TObjectDictionary<Int64, TAllergenData>;
    procedure ClearData;
  public
    function    GetCacheAllergenLangName(aid: Int64; alang_id : String): String;
    function    GetAllergenData( aid : Int64) : TAllergenData;
    procedure   RefreshData;
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheArtAllergens = class
  private
    FList : TDictionary<Int64, TStringList>;
    procedure ClearData;
    procedure DeleteByArtIDs( art_ids : array of int64 );
  public
    function    GetAllergenListByArtID( aid_art : Int64 ) : TStringList;
    procedure   RefreshData( art_ids : array of int64 );
    constructor Create;
    destructor  Destroy; override;
  end;

  TCashPromoArtList = class
  private
    list     : TDictionary<Int64, TStringList>;
    procedure ClearData;
    procedure DeleteByID( aid_art : Int64 );
  public
    function    GetPromoListByArtID( aid_art : Int64) : TStringList;
    procedure   RefreshData( aid_art : Int64 );
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheArtTableAreas = class
  private
    FArtTableAreas : TDictionary<string, TStringList>;
    procedure  DeleteByArtID (aid_art : Int64);
    function   GetArtTAkey( aid_art,aid_ta : Int64 ) : string;
  public
    property    ArtTableAreas : TDictionary<string, TStringList> read FArtTableAreas;
    procedure   RefreshData( aid_art : Int64 = 0);
    procedure   ClearArtTableAreas;
    constructor Create;
    destructor  Destroy; override;
    function GetCacheArtTableAreas(aid_art,aid_ta : Int64): TStringList;
  end;

  TCachePAKS = class
  public
    id_ks : Int64;
    purpose : Integer;
    constructor Create( aid_ks : Int64; apurpose : Integer);
  end;

  TCacheArtNotifyList = class
  private
    FList     : TDictionary<Int64, TList<TCachePAKS>>;
    procedure ClearData;
    procedure DeleteByID(aid_art: Int64);
  public
    function    GetArtNotifyByArtID( aid_art : Int64) : TList<TCachePAKS>;
    procedure   RefreshData( aid_art : Int64 );
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheArtTANotifyList = class
  private
    FList     : TDictionary<string, TList<TCachePAKS>>;
    function GetKey(aid_art, aid_ta : Int64) :string;
    procedure ClearData;
    procedure DeleteByID(aid_art: Int64);
  public
    function    GetArtNotifyByArtID( aid_art, aid_ta : Int64) : TList<TCachePAKS>;
    procedure   RefreshData( aid_art : Int64 = 0 );
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheKSWarn = class
  public
    id_periods : Int64;
    time_min   : Integer;
    constructor Create(aid_periods : Int64; atime_min : Integer);
  end;

  TCacheKSWarns = class(TObjectList)
  private
    Fown_min : Integer;
    function GetItems( Index: integer ): TCacheKSWarn;
  public
    property own_min : Integer read fown_min write fown_min;
    property Items[Index: integer]: TCacheKSWarn read GetItems;
    constructor Create(aown_min : Integer); reintroduce;
  end;

  TCacheKSWarnList = class
  private
    FList     : TDictionary<Int64, TCacheKSWarns>;
    procedure Clear;
  public
    function    GetWarnTime(aid_KS: Int64; adt : TDatetime): Integer;
    procedure   RefreshData;
    constructor Create;
    destructor  Destroy; override;
  end;

  // Keeps Cached Courses data
  TCacheCourses = class(TComponent, IUrlListener, IDbEventListener)
  private
    FMinSepNumber : Integer;
    FMaxSepNumber : Integer;
    FCacheCourseDataset : TClientDataset;
    FDbEventsController:IDbEventsController;
    procedure CreateAndInitDataset;
  protected
    function    OnDataChanged(tce: TDbDataChangeEvent):boolean;
    function    OnReset():boolean;
    procedure   OnListenerException(e: Exception);
    procedure   OnUrlEvent(url: String; ue: TUrlEvent);
  public
    procedure RefreshData;
    property CacheCourseDataset : TClientDataset read FCacheCourseDataset;
    property MinSeparateNumber : Integer read FMinSepNumber;
    property MaxSeparateNumber : Integer read FMaxSepNumber;

    function GetCourseIDByName(cName : String): TCourseData;
    function GetCourseLangName(id: Int64; lang_id: String): String;
    function GetCourseCDMin(Course_ID: String): Integer;
    function GetCourseCDType(Course_ID: String): Integer;
    function GetCourseColor(Course_ID: String): Integer;
    function GetCoursePrepMin(Course_ID: String): Integer;
    function NeedPrintDeliver(Course_ID: Int64): boolean;
    function NeedPrintPU(Course_ID: Int64): boolean;
    function GetCourseData(id: Int64): TCourseData;
    function GetCourseDataByNumber(number: Integer): TCourseData;
    function GetCourseName(strCourse_ID: String): String; overload;
    function GetCourseName(Course_ID :Int64) :String; overload;
    function GetCourseNumber(strCourse_ID: String): Integer; overload;
    function GetCourseNumber(Course_ID: Int64): Integer; overload;
    function GetCourseSeparate(Course_ID: String): boolean;
    function GetCourseIDByNumber(CourseNumber: Integer; bSeparate: boolean = false): Int64; overload;
    procedure  GetCourseKSPUPrinter(aCourse_ID: Int64; awf_state : TKSWorkflowStatus;
      var aid_printer : Int64; var aid_ticket : Int64);

    constructor Create(AOwner : TComponent); override;
    destructor  Destroy; override;
  end;

  TCashPromoArtDataSet=class( TTypedDS)
  protected
    procedure AddFieldDefs; override;
    procedure AssignFieldValues(Qa : TCashPromoArtDataSet);
    procedure DeleteArtByID( aid_art : Int64 );
  public
    id_sales_area : TField;
    id_promo : TField;
    promo_name  : TField;
    id_options : TField;
    option_number : TField;
    article_option_number  : TField;
    id_promo_item: TField;
    option_count: TField;
    price: TField;
    article_number : TField;
    course_number : TField;
    procedure CopyDS(fromDS : TCashPromoArtDataSet);
  end;

  TCashPromoDataSet = class( TTypedDS)
  protected
    procedure AddFieldDefs; override;
    procedure AssignFieldValues(Qa : TCashPromoDataSet);
    procedure DeleteArtByID( aid_art : Int64);
  public
    id_sales_area : TField;
    id_promo : TField;
    promo_name  : TField;
    option_count: TField;
  end;

  TArticleBlob = class
  private
    Fname     : string;
    Finternal_name : string;
    FBitmaps  : TMemoryStream;
    FInfo     : TMemoryStream;
  public
    constructor Create;
    destructor  Destroy; override;
  end;

  TCacheOption = record
    name        : string;
    single_prep : integer;
  end;

  TCacheOptionsDataset = class
  private
    list : TDictionary<Int64, TCacheOption>;
  public
    procedure RefreshData(aid: Int64 = 0);
    function GetOptionName(aid: Int64): String;
    function GetOptSinglePrep(aid: Int64): Integer;
    constructor Create;
    destructor Destroy; override;
  end;

  TCachePricesDataset = class
  private
    procedure RefreshData( aid : Int64 = 0);
  public
    list     : TDictionary<Int64, String>;
    function GetAnyId : Int64;
    function GetCashPriceName(aid: Int64): String;
    function GetCashPriceNumber(aid: Int64): Integer;
    constructor Create;
    destructor  Destroy; override;
  end;

  TArticleBlobs = class
  private
    Flist       : TDictionary<Int64, TArticleBlob>;
    procedure   Clear;
    procedure   RemoveByArticle(aid_art : Int64);
    procedure   RefreshData( aid_art : Int64 = 0);
  public
    function    GetBitmapMS(aid_art : Int64) : TMemoryStream;
    constructor Create;
    destructor  Destroy; override;
  end;

  TCashArticleDataSet = class( TTypedDS)
  private
    procedure DeleteByArtIds( aid_arts : Array of Int64 );
  protected
    procedure AddFieldDefs; override;
  public
    id : TField;
    id_departament : TField;
    procedure Post; override;
  end;

  TCashArticleOptionDataSet = class( TClientDataset )
  private
    procedure DeleteByOptID(aid_opt: Int64);
    procedure DeleteByArtID(aid_art : Int64);
  public
    function HasArticlOptions(aid_art, aid_sa: Int64): boolean;
    function GetArticlOptionCompose( aid_art, aid_opt : Int64 ) :Integer;
  end;

  TCashOptionArticleDataSet = class( TClientDataset )
  private
    procedure DeleteByOptId(aid_opt: Int64);
    procedure DeleteByArtId(aid_art : Int64);
  end;

  TCashKSARTDataset = class( TClientDataset )
  private
    procedure DeleteByArtId(aid_art : Int64);
  end;

  TCashBGDataSet    = class( TClientDataset )
  private
    procedure DeleteByArtId(aid_art : Int64);
  end;

  TcdsDepFonts         = class( TClientDataset )
  private
    procedure DeleteByDepID( aid_dep : Int64 );
  end;

  TCashDepartmentDataSet =   class(TClientDataset)
  private
    procedure DeleteByDepID( aid_dep : Int64 );
  end;

  TcdsArtFonts         = class( TClientDataset )
  private
    procedure DeleteByArtIds(aid_arts : array of int64);
  end;

  TCashArticlePOSDataset = class( TClientDataset )
  private
    procedure DeleteByArtId(aid_art : Int64);
  end;

  TCashDepPOSDataset = class( TClientDataset )
  private
    procedure DeleteByDepId(aid_dep : Int64);
  end;

  TItemRefreshData = record
    dt     : TDatetime;
    typeRD : Integer;
  end;

  TCachePOSEntityProvider = class( TComponent, IUrlListener, IDbEventListener )
  private
    bStopTimer : boolean;
    ArtCacheList : TDictionary<Int64, TItemRefreshData>;
    DepCacheList : TDictionary<Int64, TDatetime>;
    ClientCacheList : TDictionary<Int64, TDatetime>;
    FDbEventsController   : IDbEventsController;
    procedure OnUrlEvent(url: String; ue: TUrlEvent);
    function  RefreshArticleCacheByIDs : boolean;
    function  RefreshDepCacheByIDs : boolean;
    function  RefreshClientCacheByIDs: boolean;
    procedure SendUpdate;
    procedure RefreshArtByIDs(aid_arts: array of Int64);
    procedure RefreshSpecArtByIDs(aid_arts: array of Int64);
    procedure RefreshSpecArticles(aid_dep: Int64);
    function  GetArticleIDByDeArtID(aid_dep_art: Int64): Int64;
    procedure SendPosTableChange(id: Int64);
    procedure UpdateClientNamesOnTables(id_clients : Int64);
  protected
    function  OnDataChanged(tce: TDbDataChangeEvent):boolean;
    function  OnReset():boolean;
    procedure OnListenerException(e: Exception);
  public
    constructor Create(AOwner: TComponent); override;
    destructor Destroy; override;
  end;

  TCacheArticleViewer = class
  private
    DefaultCashArticle : TCachedArticle;
  public
    function NeedArtShowInKS(aid_art: Int64): boolean;
    function GetArticleCanSP( aid_art : Int64 ) : Boolean;
    function GetArticlePriceManual(aid_art : Int64) : boolean;
    function GetArticleWeighted(aid_art : Int64) : Boolean;
    function GetArticleAutoSM(aid_art : Int64) : boolean;
    function IgnoreSplitCombo( aid_art : Int64 ) : boolean;
    function IsArticleForRent( aid_art : Int64 ) : Boolean;
    function GetArticleExcludeSP(aid_art : Int64) : boolean;
    function IsOmitTPAPI(aid_art : Int64) : boolean;
    function GetArticleSpt(aid_art: Int64): Integer;
    function GetFGTypeByArtID(aid_art: Int64): Integer;
    function GetArticleCourseNumber(aid: Int64): Integer;
    function ArticleBlockDiscount(aid_art : Int64): boolean;
    function ArticleKSSingle(aid_art: Int64): boolean;
    procedure UpdateArticleDSCache(aid_art: Int64; flag: Integer);
    function GetCachedArticle(aid: Int64): TCachedArticle;
    constructor Create;
    destructor Destroy; override;
  end;

  TKSStageData = class
    ID_STAGE      : Int64;
    PREP_TIME_MIN : integer;
    PREP_TIME_SEC : integer;
    WARNING_MIN   : integer;
    ID_KITCHEN_SCREENS : Int64;
    KS_PURPOSE    : Integer;
    procedure Clear;
  end;

  TTAKSData = class
    id_ta      : Int64;
    id_ks_from : Int64;
    id_ks_to   : Int64;
    id_periods : Int64;
    procedure Clear;
  end;

  TKSData = record
    ks_number     : Integer;
    ks_name       : string;
    ks_short_name : string;
    ks_type       : Integer;
    sort_type     : Integer;
    articles_type : Integer;
    list_count    : Integer;
    active_first  : Integer;
    language      : string;
    id_ks_linked  : Int64;
    sort_items_type : Integer;
    hide_complete : Integer;
    msg_seconds   : Integer;
    device_type   : Integer;
    level         : Integer;
    capacity      : Integer;
    subset        : Integer;
    list_count_ta : Integer;
    average_capacity : Integer;
    decons_pu_items  : Integer;
    allergen_type : Integer;
    keep_warn     : boolean;
    show_non_separate_articles : boolean;
    color         : Integer;
    short_name    : string;
    procedure Clear;
  end;

  TCacheKSDataList = class
  private
    list : TDictionary<Int64, TKSData>;
    procedure Clear;
  public
    property Items : TDictionary<Int64, TKSData> read list;
    function GetDataByID(aidks : Int64) : TKSData;
    function GetKSIDByNumber(aksnumber : Integer) : Int64;
    function GetKSNumberByName(ks_name: String): Integer;
    function GetKSIDByName(ks_name: String): Int64;
    function GetKSNameByNumber(ks_number: Integer): String;
    procedure RefreshData;
    constructor Create;
    destructor Destroy; override;
  end;

  TKSCData = record
     KSC_NUMBER       : Integer;
     KSC_NAME         : String;
     KSC_LIST_COUNT   : Integer;
     KSC_SHOW_OPTIONS : Integer;
     KSC_CHAIRS       : Integer;
     KSC_SPLIT_COURSES: Integer;
     show_information : Integer;
     show_pending     : Integer;
     show_finished    : Integer;
     KSC_TYPE         : Integer;
     procedure Clear;
  end;

  TCacheKSCDataList = class
  private
    list : TDictionary<Int64, TKSCData>;
    procedure Clear;
  public
    function GetDataByID(aidksc : Int64) : TKSCData;
    function GetKSCType(aidksc : Int64) : Integer;
    function GetKSCShowPending(aidksc: Int64): boolean;
    function GetKSCShowFinished(aidksc: Int64): boolean;
    procedure RefreshData;
    constructor Create;
    destructor Destroy; override;
  end;

  TCashReasonDataset = class(TClientDataset)
  private
    Fver : Integer;
  public
    property ver : Integer read Fver write fver;
    constructor Create(AOwner: TComponent); override;
  end;

  TCashKSPickupPOSDataset = class(TClientDataset);

var
  id_screen_group : string;
  CashPromoArtDataSet  : TCashPromoArtDataSet;
  CashPromoDataSet     : TCashPromoDataSet;
  CacheTableLinks      : TList<Integer>;
  CashPromoMain           : TDictionary<Int64, Integer>;
  CacheArticleBarcodeList : TDictionary<Int64, String>;
  CacheTAKS               : TObjectList<TTAKSData>;
  CashPromoArtList        : TCashPromoArtList;
  CacheArtNotifyList      : TCacheArtNotifyList;
  CacheArtTANotifyList    : TCacheArtTANotifyList;
  CashArticleSADataSet : TCashArticleSADataSet;
  CashArticleDataSet : TCashArticleDataSet;
  ArticleBlobs       : TArticleBlobs;
  CashArticlePriceDataSet : TClientDataset;
  CashArticleOptionPriceDataSet : TClientDataset;
  CashArticleBonusPriceDataSet : TClientDataset;
  CashArticleMainBonusPriceDataSet : TClientDataset;
  CashArticlePOSDataset        : TCashArticlePOSDataset;
  CashDepPOSDataset            : TCashDepPOSDataset;
  CacheArticleViewer           : TCacheArticleViewer;
  CacheArticleProvider         : TCachePOSEntityProvider;
  CachePricesDataset           : TCachePricesDataset;

  CashKSPickupPOSDataset       : TCashKSPickupPOSDataset;
  CacheOptionsDataset          : TCacheOptionsDataset;
  CashCatDataSet               : TClientDataset;
  CashFGDataset                : TClientDataset;
  cdsArtFonts                  : TcdsArtFonts;
  cdsDepFonts                  : TcdsDepFonts;
  CashDepartmentDataSet        : TCashDepartmentDataSet;
  CashSpecDataSet              : TClientDataset;
  CashArtPerDataSet            : TClientDataset;
  SuppliersDataSet             : TClientDataset;
  CashPUAArticleDataSet        : TClientDataset;
  CashReasonDataset            : TCashReasonDataset;
  CacheCourses                 : TCacheCourses;
  CacheKSWarnList              : TCacheKSWarnList;
  CashCurrencyDataset          : TClientDataset;
  CashKSDataset                : TCacheKSDataList;
  CashSAExclDataset            : TClientDataset;
  CacheArticlePricePeriodsDataset: TClientDataset;
  CashKSCDataset               : TCacheKSCDataList;
  CashPADataset                : TClientDataset;
  CashKSARTDataset             : TCashKSARTDataset;
  CashBGDataset                : TCashBGDataSet;
  CacheCpts                    : TCacheCpts;
  CacheClientExcepts           : TCacheClientExcepts;
  CacheOpts                    : TCacheOpts;
  KSCacheOverviewList          : TList<Int64>;
  KSDailyStockArtList          : TList<Int64>;
  ComboPromoItems              : TComboPromoItems;
  ArtPriceList                 : TArtPriceList;
  CacheKSSubsts                : TCacheKSSubsts;
  CacheWaiters                 : TCacheWaiters;
  CacheAllergens               : TCacheAllergens;
  CacheArtAllergens            : TCacheArtAllergens;
  CacheArtTableAreas           : TCacheArtTableAreas;
  CacheSalesAreasRange         : TCacheSalesAreas;

  CashOptionArticleDataSet     : TCashOptionArticleDataSet;
  CashArticleOptionDataSet     : TCashArticleOptionDataSet;
  CacheOrderTypes              : TCacheOrderTypes;
  CacheAllergenPics            : TCacheAllergenPics;
  CacheSizeModifiers           : TCacheSizeModifiers;
  saPriceData                  : TDictionary<Int64, int64>;
  ksMultiStages                : TObjectList<TKSStageData>;


// Article cache data
procedure CacheArticleBOData( art_ids : array of int64 );
procedure CacheSizeBOData;
function GetIdPriceBySA(id_sa : Int64 ) : Int64;
procedure CacheKSMultiStages;
procedure CacheTAKSs;

procedure CachePromoArticles( aid_art : Int64 = 0 );
procedure CashArtDatasets(astrScreenGroups : String; aid_arts : Array of Int64);
procedure CacheOptionArticleDatasets(aid_art : Int64 = 0; aid_opt : Int64 = 0);
procedure DoCashKSART( aid_art : Int64 = 0 );
procedure DoCashBGDataset( aid_art : Int64 = 0 );

// Other Clients
procedure CacheClients( aid_client : Int64 = 0 );
// Other Departments
procedure CacheDepartments( astrScreenGroups : String; aid_dep : Int64 = 0 );
// Other data
procedure CashMainDatasetsNoArt;
procedure CashMainDatasets;
function GetOrCreateArticleCache(id_art : Int64) : TCachedArticle;

procedure CashArticlePOSDatasets ( aid_art : Int64 =0 );
procedure CashDepPOSDatasets ( aid_dep : Int64 =0 );
procedure CashCatDataSets;
procedure CashFGDataSets;
procedure CashKSPickupPOSDatasets(id_ksc : Int64;
  max_rec : Integer; Status : TKSWorkflowStatus);
function CashedKSBillValid(aid_bill : Int64 ) : boolean;
procedure CashSupplierDatasets;
function  GetArticleInfo(id : Int64) : TMemoryStream;
// True, if article is not allowed to discount
function GetArticlesKSTime(aid_articles : Int64) : TKSPreparationTime;
function GetFieldMLName(Field : TField; lang_id : String)  : String; overload;
function GetFieldMLName(MS : TMemoryStream; lang_id : String)  : String; overload;
function GetKSArticleName(id, id_sm_item : Int64; ksid : Int64; aLangId : String) : String;
function GetKSArticleLangName(id, id_sm_item : Int64) : String;
function GetArticleLangName(id : Int64; lang_id : String) : String;
function GetArticleLangInternalName(id : Int64; lang_id : String) : String;
function GetOptionArticleKS(id_options, id_articles : Int64) : Boolean;
function GetKSAvCapacity(ksid : Int64) : Integer;
function GetKSCapacity(ksid : Int64) : Integer;
function GetIdOptionsByOptionArt(aid_option_article : Int64) : Int64;
function IsArticleInOption(id_articles, id_options : Int64) : Boolean;
function IsArticleDailyStock(id_articles : Int64) : Boolean;
function GetReasonNameData(id : Int64) : TMemoryStream;
function GetReasonIgnoreStock(id : Int64) : Integer;
function ArticleInBonus(aid : Int64) : Boolean;
function GetReasonNameStr(id : Int64) : String;
function SplitLines(Source: String; np: TNamedParameters): TStringList;
function GetKSLang(ksid : Int64) : String;
procedure FillArticleInfo(id_articles : Int64;
  LineField1, LineField2 : TField; aLangID : String);
procedure FillArticleInfoSingle(id_articles : Int64;
  LineField : TField; aLangID : String);
function GetReasonName(id_reasons : Int64; aLangID : String) : String;
function GetReasonInfo(id_reasons : Int64; aLangID : String) : TReasonData;
procedure FillReasonName(id_reasons : Int64; LineField : TField; aLangID : String);
function GetDepFieldValue(FieldName :String; id : Int64) : String;
function GetCatLangName(id : Int64; lang_id : String) : String;
function GetFGLangName(id : Int64; lang_id : String) : String;
function GetFGTypeByDepID(id_dep : Int64) :Integer;
procedure GetFGBKPByArtID(aid_art : Int64;
  var aID_BOOKKP_TURNOVER : Int64;
  var aID_BOOKKP_VAT : Int64;
  var aID_BOOKKP_VAT_SEC: Int64);
function GetDepLangName(id : Int64; lang_id : String) : String;
function GetBonusName(id_bonus: Int64): String;
function GetSupplierData(id: Int64): TSupplierData;
procedure CacheTableLinksDataset;
function ArticleSingleNotifiedInKS(aid_articles : Int64; aksPurposeList : TKSPurposes) : boolean;
function GetArticlePrepSec( aid_articles : Int64 ) : Integer;
function IsWaiterDeliverer(aid_user: Int64) : boolean;
function GetFirstWFArticleStage(aid_articles : Int64) : Int64;
function ArticleMultiNotifiedInKS(aid_stage : Int64; aksPurposeList : TKSPurposes) : boolean;
function GetPreparationAreaName( aid : Int64 ) : String;
function GetCacheSalesAreaID( ATableNo : Integer ) : Int64;
procedure RestaurantCashDatasetsPosIniFinit(bInit: boolean);
function GetKSCIDByName(aname : String) : Int64;
function GetWithWithoutCaption(id: Integer; name : string; id_sm_item : int64): String;
function GetCacheMaxBGQauntity(aid_bonus_groups, aid_articles : Int64) : Integer;
function GetKSWFStageNumber(aid_stage : Int64) : Integer;
function GetKSWFMaxStageNumber(aid_stage : Int64) : Integer;
function GetArticleKSWFType(aid_articles : Int64) : TKSWFType;
function GetOptionParentNumber(aID_OPTION_ARTICLE : Int64) : Integer;
function DepartmentSearchExcluded(aid_dep : Int64) : boolean;
function GetDepartmentMaxSupplements(aid_dep : Int64) : Integer;
function GetDepartmentMaxCondiments(aid_dep : Int64) : Integer;
function GetDepartmentSupplementID(aid_dep : Int64) : Int64;
function GetDepartmentCondimentID(aid_dep : Int64) : Int64;
function GetArticlePriceData(aid_price, aid_article : Int64) : TArticlePriceData;
function GetDefaultPrice : Int64;
procedure CashArticlePrices( aid_arts : array of Int64 );
function GetSAIDByName( aname : string ) : Int64;
function sortsmlist(item1, item2 :TCacheSizeModifier) : integer;
function GetPOSScreenGroup : String;
function GetAnyArticleFromBonusGroup( aid_bg : Int64 ) : Int64;
function GetKSMultiStagesByID(aid : Int64) : TList<TKSStageData>;
function GetTAKSByID(aid : Int64) : TList<TTAKSData>;

var
 id_default_price : Int64;
 CashCommonRestaurantData : TCashCommonRestaurantData;
 KSPURefreshParams : TKSPURefershParams;

implementation

uses UntillAppU, IBSQL, ThreadObserverU, IdException, BOUntillDBU,
  CommonU, math, BLREqu, BLRArticleU, CurrencyU, DatePeriodsU, OrdermanFrm,
  LangU, UntillDataSetsU, RestaurantPluginU, ExtRestaurantSettingsU,
  DateUtils, CacheTableAreasU,  UntillEquU, PNGImage, KernelDatasetsU,
  KSPUItemsProviderU, BLRTakeAwayU, UntillLogsU, BLRReorderU,AllergenU,
  TPAPIPOSManagerU, SystemDataProviderU, KernelCashDatasetsU, BLRMainU,
  KernelSettingsU;

type
  TButtonDefAttr = record
    Found: boolean;
    Color: integer;
    FontName: string;
    FontColor,
    FontSize,
    FontAttr: integer;
    procedure Clear;
  end;

procedure TButtonDefAttr.Clear;
begin
  Found := false;
  Color := 0;
  FontName := '';
  FontColor := 0;
  FontSize := 0;
  FontAttr := 0;
end;

const MAX_IN_EXPR_LENGTH = 1499;

function GetInExprForIds(fname: String; aids : array of int64) : string;

function GetIdStrFromArray( aids : array of int64 ) : string;
var i : Integer;
begin
  result := '0';
  if length(aids) = 0 then exit;

  for i := 0 to Pred(length(aids)) do begin
    if i=0 then
      result := IntToStr(aids[i])
     else
      result := result + ',' + IntToStr(aids[i]);
  end;
end;

function InExpr(bFirst : Boolean; start:Integer; length : Integer):String;
var
  sublist : array of int64;
  I : Integer;
begin
  result := '';
  if bFirst then
    result := ' or ';
  SetLength(sublist, length);
  for I := 0 to length-1 do
    sublist[i] := aids[start + I];
  result := result + fname + ' in (' + GetIdStrFromArray(sublist) + ')';
end;

var
  i, rest : Integer;
begin
  result := '';
  if length(aids) > MAX_IN_EXPR_LENGTH then result := '(';
  I := 0;
  while I < length(aids) div MAX_IN_EXPR_LENGTH do begin
    result := result + InExpr(i<>0, i * MAX_IN_EXPR_LENGTH, MAX_IN_EXPR_LENGTH);
    Inc(I);
  end;
  rest := length(aids) - i * MAX_IN_EXPR_LENGTH;
  if rest > 0 then begin
    result := result + InExpr(i<>0, length(aids) - rest, rest);
  end;
  if length(aids) > MAX_IN_EXPR_LENGTH then result := result + ')';
end;

function GetSAIDByName( aname : string ) : Int64;
var q : IIBSQL;
begin
  result := 0;
  if aname='' then exit;
  if upos_ = nil then exit;

  q := upos.UntillDB.GetIIbSql;
  q.SetText('select ID from ACCOUNTS where lower(name) = :name');
  q.q.ParamByName('name').asString := LowerCase( aname );
  q.ExecQuery;

  if q.eof then exit;

  result := StrToInt64Def( q.q.fields[0].AsString,0 );

end;

function GetAllergenLangName(aid : Int64; alang_id : String) : String;
var iq        : IIBSQL;
    alrg      : TAllergenManager;
    num       : Integer;
begin
  result := '';
  if aid <= 0 then exit;

  iq := upos.UntillDB.GetPreparedIIbSql('select id, number, name from allergens where id=:id');
  iq.q.ParamByName('id').AsInt64 := aid;
  iq.ExecQuery;
  if iq.eof then exit;
  result := iq.q.FieldByName('name').asString;

  if result = '' then begin
    num := iq.FieldByName('number').asInteger;
    if IsFixAllergen( num ) then begin
      alrg := TAllergenManager.Create;
      try
        result := alrg.GetLangName( TAllergenType( num ), alang_id );
      finally
        alrg.Free;
      end;
    end;
  end;

end;

function GetArticlePriceData(aid_price, aid_article : Int64) : TArticlePriceData;
var id_defprice : Int64;
begin
  result.Clear;
  if (aid_article=0) or (aid_price=0) then exit;

  if CashArticlePriceDataSet.IndexName <> 'mIndex' then
    CashArticlePriceDataSet.IndexName := 'mIndex';
  if CashArticlePriceDataSet.FindKey( [ aid_price, aid_article ] ) then begin
    result.price        := CashArticlePriceDataSet.FieldByName('price').asCurrency;
    result.id_currency  := StrToInt64Def(CashArticlePriceDataSet.FieldByName('id_currency').asString,0);
    result.round        := CashArticlePriceDataSet.FieldByName('round').asInteger;
  end else  begin
    id_defprice := GetDefaultPrice;
    if (aid_price = id_defprice ) then exit;
    result := GetArticlePriceData(id_defprice, aid_article);
  end;
end;

function GetDefaultPrice : Int64;
var q : IIBSQL;
begin
  result := id_default_price;
  if result >= 0 then exit;

  q := upos.UntillDB.GetPreparedIIbSql('select id from prices where default_price<>0');
  q.ExecQuery;
  if q.eof then begin
    id_default_price :=0;
    exit;
  end;

  id_default_price :=StrToInt64Def(q.Fields[0].AsString,0);
  result := id_default_price;
end;

procedure InitCDSFonts(CDSFonts : TClientDataset);
begin
  with CDSFonts do  begin
    with FieldDefs.AddFieldDef do begin
      Name:='ID_DEPARTMENT';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_ARTICLES';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_SCREEN_GROUPS';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_NAME';
      DataType:=ftString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_SIZE';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_ATTR';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_COLOR';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_NAME_ALT';
      DataType:=ftString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_SIZE_ALT';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_ATTR_ALT';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='FONT_COLOR_ALT';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

procedure InitSuppliersDataSet;
begin
  if not Assigned(SuppliersDataSet) then SuppliersDataSet := TClientDataset.Create(UPos);
  with SuppliersDataSet do  begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='MIN_AMOUNT';
      DataType:=ftCurrency;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='DELIVERY_DAY';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ORDER_DAY';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='order_email';
      DataType:=ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='customer_nr';
      DataType:=ftWideString;
      Size := 50;
    end;
    CreateDataset;
  end;
end;

function GetWithWithoutCaption(id: Integer; name : string; id_sm_item : int64): String;
var oldIdx : String;
begin
  Result := '';
  if id>0 then begin
    oldIdx := '';
    if not SameText(CashSpecDataSet.IndexName, 'kNumber') then begin
      CashSpecDataSet.IndexName := 'kNumber';
      oldIdx := CashSpecDataSet.IndexName;
    end;
    if CashSpecDataSet.FindKey([IntToStr(id)]) then
      Result := CashSpecDataSet.FieldByName('internal').asString;
  end;
  if Result <> '' then
    Result := Trim(Result) + ' ' + name
  else
    Result := name;

  if id_sm_item > 0 then
    Result := CacheSizeModifiers.GetSMName(id_sm_item, upos.LanguageId)+ ' ' + result;

  if oldIdx <> '' then
    CashSpecDataSet.IndexName := oldIdx;
end;

procedure InitCashArticlePriceDataSet;
  procedure InitPriceDS(ds : TClientDataSet);
  begin
    with ds do begin
      with FieldDefs.AddFieldDef do begin
        Name:='id';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_articles';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_prices';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_currency';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='round';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='group_vat_type';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='price';
        DataType:=ftCurrency;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='vat_percent';
        DataType:=ftCurrency;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='group_vat_percent';
        DataType:=ftCurrency;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='sec_group_vat_percent';
        DataType:=ftCurrency;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='vat_sign';
        DataType:=ftWideString;
        Size := 10;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='sec_vat_percent';
        DataType:=ftCurrency;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='sec_vat_sign';
        DataType:=ftWideString;
        Size := 10;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='group_vat_sign';
        DataType:=ftWideString;
        Size := 10;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='sec_group_vat_sign';
        DataType:=ftWideString;
        Size := 10;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='use_group_vat';
        DataType:=ftInteger;
      end;
      CreateDataset;
    end;
  end;
begin
  if not Assigned(CashArticlePriceDataSet) then
    CashArticlePriceDataSet := TClientDataset.Create(UPos)
  else
    CashArticlePriceDataSet.FieldDefs.Clear;
  if not Assigned(CashArticleOptionPriceDataSet) then
    CashArticleOptionPriceDataSet := TClientDataset.Create(UPos)
  else
    CashArticleOptionPriceDataSet.FieldDefs.Clear;
  if not Assigned(CashArticleBonusPriceDataSet) then
    CashArticleBonusPriceDataSet := TClientDataset.Create(UPos)
  else
    CashArticleBonusPriceDataSet.FieldDefs.Clear;
  if not Assigned(CashArticleMainBonusPriceDataSet) then
    CashArticleMainBonusPriceDataSet := TClientDataset.Create(UPos)
  else
    CashArticleMainBonusPriceDataSet.FieldDefs.Clear;

  InitPriceDS(CashArticlePriceDataSet);
  InitPriceDS(CashArticleOptionPriceDataSet);
  InitPriceDS(CashArticleBonusPriceDataSet);
  InitPriceDS(CashArticleMainBonusPriceDataSet);
end;

procedure InitCashKSPickupPOSDataset;
begin
  CashKSPickupPOSDataset := TCashKSPickupPOSDataset.Create(nil);
  with CashKSPickupPOSDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='max_time';
      DataType:=ftDatetime;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_bill';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='tableno';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='table_part';
      DataType:=ftString;
      Size := 3;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='table_name';
      DataType:=ftString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_untill_users';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='open_datetime';
      DataType:=ftDatetime;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_waiter';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_courses';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='usedcolor';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='number_of_covers';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='number';
      DataType:=ftString;
      Size := 10;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='sdescription';
      DataType:=ftString;
      Size := 1024;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='takeaway';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ta_bill_id';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ks_sub_name';
      DataType:=ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_ks_sub';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_last_order_user';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='client_name';
      DataType:=ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hc_roomnumber';
      DataType:=ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='dt_status';
      DataType:=ftDatetime;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='day_number';
      DataType:=ftWideString;
      Size := 50;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='last_finish_dt';
      DataType:=ftDatetime;
    end;
    CreateDataset;
  end;
  KSPUItemsProvider := TKSPUItemsProvider.Create(nil);
end;

procedure InitCashFGDataset;
begin
  if assigned(CashFGDataset) then exit;
  CashFGDataset := TClientDataset.Create(nil);
  with CashFGDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size := 100;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='group_type';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_BOOKKP_TURNOVER';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_BOOKKP_VAT';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_BOOKKP_VAT_SEC';
      DataType:=ftLargeInt;
    end;
    CreateDataset;
  end;
end;

procedure InitCashCatDataset;
begin
  if assigned(CashCatDataset) then exit;
  CashCatDataset := TClientDataset.Create(nil);
  with CashCatDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size := 100;
    end;
    CreateDataset;
  end;
end;

procedure InitCashArticlePOSDataset;
begin
  if assigned(CashArticlePOSDataset) then exit;
  CashArticlePOSDataset := TCashArticlePOSDataset.Create(nil);
  with CashArticlePOSDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_articles';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_departments';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_sa';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='disabled';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pos';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='dtupdate';
      DataType:=ftDatetime;
    end;
    CreateDataset;
  end;
end;

procedure InitCashDepPOSDataset;
begin
  if assigned(CashDepPOSDataset) then exit;
  CashDepPOSDataset := TCashDepPOSDataset.Create(nil);
  with CashDepPOSDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_department';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_sa';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pos';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

procedure InitCashDepartmentDataSet;
begin
  if not Assigned(CashDepartmentDataSet) then
    CashDepartmentDataSet := TCashDepartmentDataSet.Create(UPos)
  else
    CashDepartmentDataSet.FieldDefs.Clear;
  with CashDepartmentDataSet do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_food_group';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_bitmap';
      DataType:=ftBlob;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_text';
      DataType:=ftWideString;
      Size:=255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_font_name';
      DataType:=ftWideString;
      Size:=255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_font_size';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_font_attr';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pc_font_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='rm_font_size';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_options_supplement';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_options_condiment';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_sales_area';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='dep_number';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_text';
      DataType:=ftWideString;
      Size:=255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_font_name';
      DataType:=ftWideString;
      Size:=255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_font_size';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_font_attr';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_font_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='fastlane';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='age';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_default_setting';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_default_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_default_font';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='hht_default_alt_font';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='oman_default_setting';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='oman_default_color';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='oman_default_font';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='oman_default_alt_font';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='exclude_art';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='pos_number';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='max_supp_quantity';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='max_cond_quantity';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

function DepartmentSearchExcluded(aid_dep : Int64) : boolean;
begin
  result := false;
  if aid_dep = 0 then exit;
  result := StrToInt64Def(GetDepFieldValue('exclude_art', aid_dep),0) > 0;
end;

function GetDepartmentMaxSupplements(aid_dep : Int64) : Integer;
begin
  result := 0;
  if aid_dep = 0 then exit;
  result := StrToInt64Def(GetDepFieldValue('max_supp_quantity', aid_dep),0);
end;

function GetDepartmentSupplementID(aid_dep : Int64) : Int64;
begin
  result := 0;
  if aid_dep = 0 then exit;
  result := StrToInt64Def(GetDepFieldValue('id_options_supplement', aid_dep),0);
end;

function GetDepartmentCondimentID(aid_dep : Int64) : Int64;
begin
  result := 0;
  if aid_dep = 0 then exit;
  result := StrToInt64Def(GetDepFieldValue('id_options_condiment', aid_dep),0);
end;

function GetDepartmentMaxCondiments(aid_dep : Int64) : Integer;
begin
  result := 0;
  if aid_dep = 0 then exit;
  result := StrToInt64Def(GetDepFieldValue('max_cond_quantity', aid_dep),0);
end;

procedure InitArtPerDataset;
begin
  if not Assigned(CashArtPerDataSet) then CashArtPerDataSet := TClientDataset.Create(UPos);
  with CashArtPerDataSet do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id_periods';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=50;
    end;
    CreateDataset;
  end;
end;

procedure InitCacheArticlePricePeriodsDataset;
begin
  if not Assigned(CacheArticlePricePeriodsDataset) then CacheArticlePricePeriodsDataset := TClientDataset.Create(UPos);
  with CacheArticlePricePeriodsDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id_article';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_price';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_period';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='price';
      DataType:=ftCurrency;
    end;
    CreateDataset;
  end;
end;

procedure InitCurrencyDataset;
begin
  if not Assigned(CashCurrencyDataset) then CashCurrencyDataset := TClientDataset.Create(UPos);
  with CashCurrencyDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftString;
      Size := 255;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='tenor';
      DataType:=ftCurrency;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='picture';
      DataType:=ftBlob;
    end;
    CreateDataset;
  end;
end;

procedure InitKSDataset;
begin
  if not Assigned(CashKSDataset) then CashKSDataset := TCacheKSDataList.Create;
end;

procedure InitKSCDataset;
begin
  if not Assigned(CashKSCDataset) then CashKSCDataset := TCacheKSCDataList.Create;
end;

procedure InitReasonDataset;
begin
  if not Assigned(CashReasonDataset) then
    CashReasonDataset := TCashReasonDataset.Create(UPos);
  with CashReasonDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name_data';
      DataType:=ftBlob;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ignore_stock';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_bookkp';
      DataType:=ftLargeInt;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='r_type';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

procedure InitSAExclDataset;
begin
  CashSAExclDataset := TClientDataset.Create(UPos);
  with CashSAExclDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id_sa';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_periods';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_prices';
      DataType:=ftLargeint;
    end;
    CreateDataset;
  end;
end;

procedure InitKSARTDataset;
begin
  if not Assigned(CashKSARTDataset) then CashKSARTDataset := TCashKSARTDataset.Create(UPos);
  with CashKSARTDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id_articles';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ID_KS';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='KS_PURPOSE';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

procedure InitCashBGDataset;
begin
  if not Assigned(CashBGDataset) then CashBGDataset := TCashBGDataset.Create(UPos);
  with CashBGDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_bonus_groups';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_articles';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='max_quantity';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

procedure InitPADataset;
begin
  if not Assigned(CashPADataset) then CashPADataset := TClientDataset.Create(UPos);
  with CashPADataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=50;
    end;
    CreateDataset;
  end;
end;

procedure InitSpecDataSet;
begin
  if not Assigned(CashSpecDataSet) then CashSpecDataSet := TClientDataset.Create(UPos);
  with CashSpecDataSet do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='key';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='internal';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='number';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='PC_TEXT';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='RM_TEXT';
      DataType:=ftWideString;
      Size:=20;
    end;
    CreateDataset;
  end;
end;

procedure InitCashArticleDataSet;
begin
  if not Assigned(cdsArtFonts)              then cdsArtFonts                := TcdsArtFonts.Create(nil);
  InitcdsFonts(cdsArtFonts);
  if not Assigned(cdsDepFonts)              then cdsDepFonts                := TcdsDepFonts.Create(nil);
  InitcdsFonts(cdsDepFonts);
  if not Assigned(CashArticleDataSet)       then CashArticleDataSet         := TCashArticleDataSet.Create;
  if not Assigned(CashArticleSADataSet)     then CashArticleSADataSet       := TCashArticleSADataSet.Create;
  if not Assigned(CashPromoArtDataSet)      then CashPromoArtDataSet        := TCashPromoArtDataSet.Create;
  if not Assigned(CashPromoDataSet)         then CashPromoDataSet           := TCashPromoDataSet.Create;
  if not Assigned(CashPromoMain)            then CashPromoMain              := TDictionary<Int64, Integer>.Create;
  if not Assigned(CacheTableLinks)          then CacheTableLinks            := TList<Integer>.Create;
  if not Assigned(CashPromoArtList)         then CashPromoArtList           := TCashPromoArtList.Create;
  if not Assigned(CacheArtNotifyList)       then CacheArtNotifyList         := TCacheArtNotifyList.Create;
  if not Assigned(CacheArtTANotifyList)     then CacheArtTANotifyList       := TCacheArtTANotifyList.Create;
  if not Assigned(CacheArticleBarcodeList)  then CacheArticleBarcodeList    := TDictionary<Int64, String>.Create;
end;

procedure InitCashOptionArticleDataSet;
begin
    if not Assigned(CashOptionArticleDataSet) then CashOptionArticleDataSet := TCashOptionArticleDataSet.Create(UPos);
    with CashOptionArticleDataSet do begin
      with FieldDefs.AddFieldDef do begin
        Name:='id';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_articles';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_options';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_sales_area';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='option_number';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='show_in_ks';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='article_number';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_department';
        DataType:=ftLargeint;
      end;
      CreateDataset;
    end;
end;

procedure InitCashPUAArticleDataSet;
begin
    if not Assigned(CashPUAArticleDataSet) then CashPUAArticleDataSet := TClientDataset.Create(UPos);
    with CashPUAArticleDataSet do begin
      with FieldDefs.AddFieldDef do begin
        Name:='id_pua_groups';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_articles';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_sales_area';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='pua_number';
        DataType:=ftInteger;
      end;
      CreateDataset;
    end;
end;

procedure InitCashArticleOptionDataSet;
begin
    if not Assigned(CashArticleOptionDataSet) then CashArticleOptionDataSet := TCashArticleOptionDataSet.Create(UPos);
    with CashArticleOptionDataSet do
    begin
      with FieldDefs.AddFieldDef do begin
        Name:='id';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_options';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='compose';
        DataType:=ftInteger;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_articles';
        DataType:=ftLargeint;
      end;
      with FieldDefs.AddFieldDef do begin
        Name:='id_sales_area';
        DataType:=ftLargeint;
      end;
      CreateDataset;
    end;
end;

procedure InitMainDatasets;
begin
  InitCashCatDataset;
  InitCashDepartmentDataSet;
  InitCashFGDataset;
  InitCashArticleDataSet;
  InitCashArticlePriceDataSet;
  InitCashOptionArticleDataSet;
  InitCashArticleOptionDataSet;
  InitSpecDataSet;
  InitSuppliersDataSet;
  InitCashPUAArticleDataSet;
  InitArtPerDataset;
  InitReasonDataset;
  InitCurrencyDataset;
  InitPADataset;
  InitSAExclDataset;
  InitKSDataset;
  InitKSCDataset;
  InitCashArticlePOSDataset;
  InitCashDepPOSDataset;
  InitCashKSPickupPOSDataset;
  InitKSARTDataset;
  InitCashBGDataset;
  InitCacheArticlePricePeriodsDataset;
end;

function IsArticleDailyStock(id_articles : Int64) : Boolean;
begin
  result := KSDailyStockArtList.IndexOf(id_articles)>=0;
end;

function IsArticleInOption(id_articles, id_options : Int64) : Boolean;
var oldIndex : String;
begin
  result := false;
  if (id_articles=0) or (id_options=0) then exit;

  oldIndex := '';
  if CashOptionArticleDataSet.IndexName<>'oaidIdx' then begin
    oldIndex := CashOptionArticleDataSet.IndexName;
    CashOptionArticleDataSet.IndexName := 'oaidIdx';
  end;
  if CashOptionArticleDataSet.FindKey([IntToStr(id_options), IntToStr(id_articles)]) then
    result := true;
  if oldIndex<>'' then
    CashOptionArticleDataSet.IndexName := oldIndex;
end;

function GetIdOptionsByOptionArt(aid_option_article : Int64) : Int64;
var oldIndex : String;
begin
  result := 0;
  if (aid_option_article <= 0) then exit;

  oldIndex := '';
  if CashOptionArticleDataSet.IndexName<>'idIdx' then begin
    oldIndex := CashOptionArticleDataSet.IndexName;
    CashOptionArticleDataSet.IndexName := 'idIdx';
  end;
  if CashOptionArticleDataSet.FindKey([IntToStr(aid_option_article)]) then
    result := StrToInt64Def(CashOptionArticleDataSet.FieldByName('id_options').AsString,0);
  if oldIndex<>'' then
    CashOptionArticleDataSet.IndexName := oldIndex;
end;

function GetDBArticleName( adb : TUntillDB; aid_art : Int64 ) : string;
var iq : IIBSQL;
begin
  result := '';
  if aid_art=0 then exit;

  iq := upos.UntillDB.GetPreparedIIbSql('select name from ARTICLES where id=:id');
  iq.q.ParamByName('id').AsInt64 := aid_art;
  iq.ExecQuery;

  if iq.eof then exit;
  result := trim(iq.Fields[0].asString);
end;

function GetOptionArticleKS(id_options, id_articles : Int64) : Boolean;
var oldIndex : String;
begin
  result := true;
  if (id_articles=0) or (id_options=0) then exit;

  oldIndex := '';
  if CashOptionArticleDataSet.IndexName<>'oaidIdx' then begin
    oldIndex := CashOptionArticleDataSet.IndexName;
    CashOptionArticleDataSet.IndexName := 'oaidIdx';
  end;
  if CashOptionArticleDataSet.FindKey([IntToStr(id_options), IntToStr(id_articles)]) then
    result := CashOptionArticleDataSet.FieldByName('show_in_ks').AsInteger=1;
  if oldIndex<>'' then
    CashOptionArticleDataSet.IndexName := oldIndex;
end;

function GetKSAvCapacity(ksid : Int64) : Integer;
var ksd : TKSData;
begin
  result := 0;
  if ksid = 0 then exit;
  if not CashKSDataSet.list.TryGetValue(ksid, ksd) then exit;
  result := ksd.average_capacity;
end;

function GetKSCapacity(ksid : Int64) : Integer;
var ksd : TKSData;
begin
  result := 0;
  if ksid = 0 then exit;
  if not CashKSDataSet.list.TryGetValue(ksid, ksd) then exit;
  result := ksd.capacity;
end;

function GetKSLang(ksid : Int64) : String;
var ksd : TKSData;
begin
  result := '';
  if ksid=0 then exit;
  
  if not CashKSDataSet.list.TryGetValue(ksid, ksd) then exit;
  if ksd.language <> '' then
    result := ksd.language;
  if result='' then
    result := upos.LanguageId;
  if result='' then
    result := DEFAULT_LANG_ID;
end;

function GetKSArticleLangName(id, id_sm_item : Int64) : String;
var smname : String;
begin
  result := '';
  if id=0 then exit;

  result := GetArticleLangName(id, DEFAULT_LANG_ID);
  if id_sm_item > 0 then begin
    smname := CacheSizeModifiers.GetSMName(id_sm_item, DEFAULT_LANG_ID);
    if smname<>'' then
      result := smname + ' ' + result;
  end;

end;

function GetKSArticleName(id, id_sm_item : Int64; ksid : Int64; aLangId : String) : String;
var lang_id, smname : String;
    blb       : TArticleBlob;
begin
  result :=  '';
  if id=0 then exit;

  if alangId<>'' then
    lang_id := alangId
  else
    lang_id := GetKSLang(ksid);

  ArticleBlobs.flist.TryGetValue(id, blb);
  if assigned(blb) then
    result := blb.Finternal_name;

  if trim(result)='' then
    result := GetDBArticleName(upos.UntillDB, id);

  if id_sm_item > 0 then begin
    smname := CacheSizeModifiers.GetSMName(id_sm_item, lang_id);
    if smname<>'' then
      result := smname + ' ' + result;
  end;
end;

function SetArticleCashIndex( aidx : string ) : string;
begin
  result :=  '';
  if SameText( aidx, CashArticleDataSet.cds.IndexName ) then exit;

  result := CashArticleDataSet.cds.IndexName;
  CashArticleDataSet.cds.IndexName := aidx;
end;

procedure SetArticleCashIndexBack( aidx : string );
begin
  if aidx = '' then exit;
  CashArticleDataSet.cds.IndexName := aidx;
end;

function FindArticleById(aid : Int64) : boolean;
begin
  result := false;
  if aid=0 then exit;

  result := CashArticleDataSet.cds.FindKey([IntToStr( aid )]);
end;

function GetArticleInfo(id : Int64) : TMemoryStream;
var blb : TArticleBlob;
begin
  result := nil;
  if id=0 then exit;

  if ArticleBlobs.Flist.TryGetValue(id, blb) then
    if assigned(blb) then
      result := blb.FInfo;
end;

function GetReasonNameData(id : Int64) : TMemoryStream;
var oldIndex : String;
    MS       : TMemoryStream;
begin
  result := nil;
  if id<=0 then exit;

  MS := TMemoryStream.Create;
  oldIndex := CashReasonDataSet.IndexName;
  CashReasonDataSet.IndexName := 'rIndex';
  if CashReasonDataSet.FindKey([IntToStr(id)]) then begin
    TBlobField(CashReasonDataSet.FieldByName('name_data')).SaveToStream(MS);
    MS.Seek(0,0);
  end;
  CashReasonDataSet.IndexName := oldIndex;
  result := MS;
end;

function ArticleInBonus(aid : Int64) : Boolean;
var
  qSel, dep: IIBSQL;
begin
  result := false;
  if aid<=0 then exit;

  qSel := upos.UntillDB.GetPreparedIIbSql('select count(*) '
    + ' from BONUS_GROUPS_ARTICLES bns '
    + ' join BONUS_GROUPS bng on bng.id=bns.ID_BONUS_GROUPS '
    + ' and bng.is_active=1 '
    + ' where id_articles=:id and bns.is_active=1' );
  qSel.q.Params[0].AsInt64 := aid;
  qSel.ExecQuery;
  result := qSel.fields[0].asInteger > 0;

  if not result then begin
    // find in linked departments
    dep := upos.UntillDB.GetPreparedIIbSql(
      'select first 1 1' +
      '  from bonus_groups_articles bga' +
      '  join department d on d.id = bga.id_department and d.is_active = 1' +
      '  join articles a on a.id_departament = d.id and a.is_active = 1' +
      ' where bga.is_active = 1' +
      '   and a.id = :id_articles'
    );
    dep.q.ParamByName('id_articles').asInt64 := aid;
    dep.ExecQuery;
    Result := not dep.eof;
  end;
end;

function GetReasonIgnoreStock(id : Int64) : Integer;
var oldIndex : String;
begin
  result :=  0;
  if id=0 then exit;

  oldIndex := CashReasonDataSet.IndexName;
  CashReasonDataSet.IndexName := 'rIndex';
  if CashReasonDataSet.FindKey([IntToStr(id)]) then begin
    result := CashReasonDataSet.FieldByName('IGNORE_STOCK').asInteger;
  end;
  CashReasonDataSet.IndexName := oldIndex;
end;

function GetReasonNameStr(id : Int64) : String;
var oldIndex : String;
begin
  result :=  '';
  if id=0 then exit;

  oldIndex := CashReasonDataSet.IndexName;
  CashReasonDataSet.IndexName := 'rIndex';
  if CashReasonDataSet.FindKey([IntToStr(id)]) then begin
    result := CashReasonDataSet.FieldByName('name').asString;
  end;
  CashReasonDataSet.IndexName := oldIndex;
end;

function SplitLines(Source: String; np: TNamedParameters): TStringList;
var
  width: Integer;
begin
  Result := TStringList.Create;

  if np.Params[TTicketWidthDatasetParam.Key] <> nil then
  begin
    width := Integer(np.Params[TTicketWidthDatasetParam.Key]);
    if (Length(Source) > width) then
    begin
      Result.Text := StrSplitToLines(Source, width);
      while Result.Count > 2 do
      begin
        Result[1] := Result[1] + Result[2];
        Result.Delete(2);
      end;
    end
    else
      Result.Text := Source;
  end
  else
    Result.Text := Source;
end;

procedure FillArticleInfo(id_articles : Int64;
  LineField1, LineField2 : TField; aLangID : String);
var a_info : String;
    lines: TStringList;
begin
  if id_articles=0 then exit;

  a_info := CacheArticleViewer.GetCachedArticle( id_articles ).a_info;
  if trim(a_info) = '' then exit;
  if assigned(LineField2) then begin
    lines := SplitLines(a_info, upos.NamedParams);
    try
      if (lines.Count > 0) then
      begin
        LineField1.asString := lines[0];
        if (lines.Count > 1) then
          LineField2.asString := lines[1];
      end;
    finally
      FreeAndNil(lines);
    end;
  end else begin
    LineField1.asString := a_info;
  end;
end;

procedure FillArticleInfoSingle(id_articles : Int64;
  LineField : TField; aLangID : String);
var a_info : String;
    lines: TStringList;
begin
  if id_articles=0 then exit;
  a_info := CacheArticleViewer.GetCachedArticle( id_articles ).a_info;
  if trim(a_info) = '' then exit;
  if assigned(LineField) then begin
    lines := SplitLines(a_info, upos.NamedParams);
    try
      if (lines.Count > 0) then
      begin
        LineField.asString := lines[0];
        if (lines.Count > 1) then
          LineField.asString := LineField.asString + CHR(13)+CHR(10) + lines[1];
      end;
    finally
      FreeAndNil(lines);
    end;
  end else begin
    LineField.asString := a_info;
  end;
end;

function GetReasonName(id_reasons : Int64; aLangID : String) : String;
var r_name : String;
begin
  result := '';
  if id_reasons = 0 then exit;

   r_name := GetReasonNameStr ( id_reasons );
end;

function GetReasonInfo(id_reasons : Int64; aLangID : String) : TReasonData;
var oldIndex : String;
    MS       : TMemoryStream;
    r_name : String;
    InfoList : TLabelCaptionList;
begin
  result.name := '';
  result.bookkeepingId := 0;

  if id_reasons<=0 then exit;

  InfoList  := nil;
  MS        := nil;
  try
    MS := TMemoryStream.Create;
    oldIndex := CashReasonDataSet.IndexName;
    CashReasonDataSet.IndexName := 'rIndex';
    if CashReasonDataSet.FindKey([IntToStr(id_reasons)]) then begin
      TBlobField(CashReasonDataSet.FieldByName('name_data')).SaveToStream(MS);
      MS.Seek(0,0);
      InfoList := TLabelCaptionList.Create;
      try
        if MS.Size>0 then begin
          InfoList.LoadFromStream(MS);
          r_name := InfoList.Value[aLangID];
          if trim(r_name) = '' then
            r_name := InfoList.Value[DEFAULT_LANG_ID];
        end;
        if InfoList.TextList.count=0 then      // Has old text data
          r_name := GetReasonNameStr ( id_reasons );
      except
      end;
      result.name := r_name;
      result.bookkeepingId := CashReasonDataSet.FieldByName('id_bookkp').asLargeInt;
    end;
    CashReasonDataSet.IndexName := oldIndex;
  finally
    FreeAndNil(InfoList);
    FreeAndNil(MS);
  end;
end;

procedure FillReasonName(id_reasons : Int64; LineField : TField; aLangID : String);
begin
  LineField.asString := GetReasonName(id_reasons, aLangID);
end;

function GetSupplierData(id: Int64): TSupplierData;
begin
  Result.Clear;
  if id<=0 then exit;
  SuppliersDataset.IndexName := 'supIdx';
  if SuppliersDataset.FindKey([id]) then begin
    result.id   := id;
    result.name := SuppliersDataset.fieldByname('name').asString;
    result.min_amount := SuppliersDataset.fieldByname('MIN_AMOUNT').asCurrency;
    result.delivery_day := SuppliersDataset.fieldByname('DELIVERY_DAY').asInteger;
    result.customer_nr:= SuppliersDataset.fieldByname('customer_nr').asString;
    result.order_day := SuppliersDataset.fieldByname('ORDER_DAY').asInteger;
    result.order_email:= SuppliersDataset.fieldByname('order_email').asString;
  end;
end;

function GetBonusName(id_bonus: Int64): String;
var iq : IIBSQL;
begin
  Result := '';
  if id_bonus<=0 then exit;

  iq:= upos.Untilldb.GetPreparedIIbSql('Select name from BONUS_GROUPS where id=:id');
  iq.q.Params[0].asInt64 :=id_bonus;
  iq.ExecQuery;
  if not iq.eof then
    Result := iq.q.fields[0].asString;
end;

function GetArticleLangName(id : Int64; lang_id : String) : String;
var blb       : TArticleBlob;
begin
  result :=  '';
  if id=0 then exit;

  if not ArticleBlobs.flist.TryGetValue(id, blb) then exit;
  if assigned(blb) then
    result  := blb.fname;
end;

function GetArticleLangInternalName(id : Int64; lang_id : String) : String;
var blb     : TArticleBlob;
begin
  result :=  '';
  if id=0 then exit;

  if not ArticleBlobs.flist.TryGetValue(id, blb) then exit;
  if assigned(blb) then
    result  := blb.finternal_name;
end;

function GetFieldMLName(MS : TMemoryStream; lang_id : String) : String; overload;
var NameList : TLabelCaptionList;
    mlname  : String;
begin
  result := '';
  if not assigned(MS) then exit;
  if MS.size=0 then exit;

  NameList := nil;
  try
    MS.Seek(0,0);
    NameList := TLabelCaptionList.Create;
    try
      ms.Seek(0,0);
      NameList.LoadFromStream(ms);
      mlname := NameList.Value[lang_id];
      if trim(mlname) <> '' then
        result := mlname;
    except
    end;
  finally
    FreeAndNil(NameList);
  end;
end;

function GetFieldMLName(Field : TField; lang_id : String) : String;
var ms : TmemoryStream;
    NameList : TLabelCaptionList;
begin
  result := '';
  ms       := nil;
  NameList := nil;
  try
    ms := TMemoryStream.Create;
    TBlobField(Field).SaveToStream(ms);
    if ms.Size>0 then begin
      NameList := TLabelCaptionList.Create;
      try
        ms.Seek(0,0);
        NameList.LoadFromStream(ms);
        result := trim(NameList.Value[lang_id]);
      except
      end;
    end;
  finally
    FreeAndNil(ms);
    FreeAndNil(NameList);
  end;
end;

function GetCatLangName(id : Int64; lang_id : String) :String;
begin
  Result := '';
  if id=0 then exit;

  CashCatDataset.IndexName := 'catIdx';
  if CashCatDataset.FindKey([id]) then
    result := CashCatDataset.FieldByName('name').AsString;
end;

function GetFGLangName(id : Int64; lang_id : String) :String;
begin
  Result := '';
  if id=0 then exit;

  CashFGDataset.IndexName := 'fgIdx';
  if CashFGDataset.FindKey([id]) then
    result := CashFGDataset.FieldByName('name').AsString;
end;

procedure GetFGBKPByArtID(aid_art : Int64;
  var aID_BOOKKP_TURNOVER : Int64;
  var aID_BOOKKP_VAT : Int64;
  var aID_BOOKKP_VAT_SEC : Int64);
var id_g : Int64;
begin
  aID_BOOKKP_TURNOVER := 0;
  aID_BOOKKP_VAT      := 0;
  aID_BOOKKP_VAT_SEC  := 0;
  if aid_art <= 0 then exit;

  id_g := CacheArticleViewer.GetCachedArticle(aid_art).id_food_group;
  if id_g <= 0 then exit;

  CashFGDataset.IndexName := 'fgIdx';
  if CashFGDataset.FindKey([id_g]) then begin
    aID_BOOKKP_TURNOVER := StrToInt64Def(CashFGDataset.FieldByName('ID_BOOKKP_TURNOVER').AsString,0);
    aID_BOOKKP_VAT      := StrToInt64Def(CashFGDataset.FieldByName('ID_BOOKKP_VAT').AsString,0);
    aID_BOOKKP_VAT_SEC  := StrToInt64Def(CashFGDataset.FieldByName('ID_BOOKKP_VAT_SEC').AsString,0);
  end;
end;

function GetFGTypeByDepID(id_dep : Int64) :Integer;
var id_g : Int64;
begin
  Result := 0;
  if id_dep <= 0 then exit;

  id_g := StrToInt64Def(GetDepFieldValue('id_food_group', id_dep),0);
  if id_g <= 0 then exit;

  CashFGDataset.IndexName := 'fgIdx';
  if CashFGDataset.FindKey([id_g]) then
    result := CashFGDataset.FieldByName('group_type').AsInteger;
end;

function GetDepFieldValue(FieldName :String; id : Int64) : String;
var q : TClientDataset;
  oldIndex : String;
begin
  result := '0';
  if id=0 then exit;

  q := CashDepartmentDataSet;
  oldIndex := q.IndexName;
  q.IndexName := 'didIndex3';
  if q.FindKey([IntToStr(id)]) then
    result := q.FieldByName(FieldName).asString;
  q.IndexName := oldIndex;
end;

function GetDepLangName(id : Int64; lang_id : String) : String;
var q : TClientDataset;
    oldIndex : String;
begin
  result := '';
  if id=0 then exit;

  q := CashDepartmentDataSet;
  oldIndex := q.IndexName;
  q.IndexName := 'didIndex3';
  if q.FindKey([IntToStr(id)]) then
    result := CashDepartmentDataSet.FieldByName('name').asString;
end;

function CalcPrice(id_currency : Int64; price : Currency; round : Integer) : Currency;
var iq : IIBSQL;
begin
  Result := price;
  if id_currency=0 then exit;

  iq:= upos.Untilldb.GetPreparedIIbSql('Select rate from currency where id=:id');
  iq.q.Params[0].asInt64 :=id_currency;
  iq.ExecQuery;
  if not iq.eof then
    Result := SimpleRoundTo(iq.fields[0].AsDouble * price, -round);
end;

procedure CashPriceDatasets(dstype : TPriceDSType; aid_arts : array of int64);
var sqltxt : String;
    id_prices : Int64;
    price : Currency;
    id_currency : Int64;
    use_group_vat,round : Integer;
    ds : TClientDataSet;
    q : IIBSQL;
    i : Integer;
    strIds : string;
begin
  ds := nil;
  if dstype=pdtArticle then
    ds := CashArticlePriceDataSet
  else if dstype=pdtOption then begin
    ds := CashArticleOptionPriceDataSet;
  end else if dstype=pdtBonus then
    ds := CashArticleBonusPriceDataSet
  else if dstype=pdtMainBonus then
    ds := CashArticleMainBonusPriceDataSet;

  if not assigned(ds) then exit;

  if length( aid_arts ) = 0 then
    ds.EmptyDataSet
  else begin
    ds.IndexName := 'idIndex';
    for I := 0 to Pred( length( aid_arts )) do begin
      while ds.FindKey([aid_arts[i]]) do
        ds.Delete;
    end;
  end;

  strIds := '';
  if length(aid_arts) > 0 then
    strIds := strIds + ' and ' + GetInExprForIds('articles.id', aid_arts);

  for id_prices in CachePricesDataset.list.keys do begin
    if id_prices>0 then begin
      if dstype=pdtArticle then begin
        sqltxt := 'select article_prices.id, article_prices.id_articles, coalesce(article_prices.price,0) price, article_prices.id_currency, round, ' +
                      ' use_group_vat, food_group.group_vat, food_group.group_vat_sign, article_prices.vat, article_prices.vat_sign, ' +
                      ' article_prices.sec_vat, article_prices.sec_vat_sign, food_group.sec_group_vat, food_group.sec_group_vat_sign ' +
                      ' from articles ' +
                      ' join department on department.id = articles.id_departament ' +
                      ' join food_group on food_group.id = department.id_food_group ' +
                      ' left join article_prices on articles.id = article_prices.id_articles and article_prices.is_active=1 and id_prices=' + IntToStr(id_prices) +
                      ' left join currency on article_prices.id_currency = currency.id and currency.is_active=1 '+
                      ' where articles.is_active=1 ';
      end else if dstype=pdtOption then begin
        sqltxt := 'select option_article_prices.id, option_article_prices.id_option_article id_articles, coalesce(option_article_prices.price,0) price,  ' +
                      ' option_article_prices.id_currency, round, ' +
                      ' use_group_vat, food_group.group_vat, food_group.group_vat_sign, option_article_prices.vat, option_article_prices.vat_sign, ' +
                      ' option_article_prices.sec_vat, option_article_prices.sec_vat_sign, food_group.sec_group_vat, food_group.sec_group_vat_sign ' +
                      ' from option_article ' +
                      ' join articles on articles.id = option_article.id_articles and articles.is_active=1' +
                      ' join options on options.id = option_article.id_options and options.is_active=1 ' +
                      ' join department on department.id = articles.id_departament ' +
                      ' join food_group on food_group.id = department.id_food_group ' +
                      ' left join option_article_prices on option_article_prices.id_option_article = option_article.id and option_article_prices.is_active=1 and id_prices=' + IntToStr(id_prices) +
                      ' left join currency on option_article_prices.id_currency = currency.id and currency.is_active=1 ' +
                      ' where option_article.is_active=1 ';
      end else if dstype=pdtBonus then begin
        sqltxt := 'select BONUS_GROUPS_ARTICLES_PRICES.id, BONUS_GROUPS_ARTICLES.id id_articles, coalesce(BONUS_GROUPS_ARTICLES_PRICES.price,0) price,' +
                      ' BONUS_GROUPS_ARTICLES_PRICES.id_currency, round, ' +
                      ' use_group_vat, food_group.group_vat, food_group.group_vat_sign, BONUS_GROUPS_ARTICLES_PRICES.vat, BONUS_GROUPS_ARTICLES_PRICES.vat_sign, ' +
                      ' BONUS_GROUPS_ARTICLES_PRICES.sec_vat, BONUS_GROUPS_ARTICLES_PRICES.sec_vat_sign, food_group.sec_group_vat, food_group.sec_group_vat_sign ' +
                      ' from BONUS_GROUPS_ARTICLES ' +
                      ' join articles on articles.id = BONUS_GROUPS_ARTICLES.id_articles and articles.is_active=1' +
                      ' join BONUS_GROUPS on BONUS_GROUPS.id = BONUS_GROUPS_ARTICLES.ID_BONUS_GROUPS and BONUS_GROUPS.is_active=1 ' +
                      ' join department on department.id = articles.id_departament ' +
                      ' join food_group on food_group.id = department.id_food_group ' +
                      ' join BONUS_GROUPS_ARTICLES_PRICES on BONUS_GROUPS_ARTICLES_PRICES.ID_BONUS_GROUPS_ARTICLES = BONUS_GROUPS_ARTICLES.id and BONUS_GROUPS_ARTICLES_PRICES.is_active=1 and id_prices=' + IntToStr(id_prices) +
                      ' left join currency on BONUS_GROUPS_ARTICLES_PRICES.id_currency = currency.id and currency.is_active=1 ' +
                      ' where BONUS_GROUPS_ARTICLES.is_active=1';
      end else if dstype=pdtMainBonus then begin
        sqltxt := 'select ARTICLE_BONUS_PRICES.id, ARTICLE_BONUS_GROUPS.id id_articles, coalesce(ARTICLE_BONUS_PRICES.price,0) price,' +
                      ' ARTICLE_BONUS_PRICES.id_currency, round, ' +
                      ' use_group_vat, food_group.group_vat, food_group.group_vat_sign, ARTICLE_BONUS_PRICES.vat, ARTICLE_BONUS_PRICES.vat_sign, ' +
                      ' ARTICLE_BONUS_PRICES.sec_vat, ARTICLE_BONUS_PRICES.sec_vat_sign, food_group.sec_group_vat, food_group.sec_group_vat_sign ' +
                      ' from ARTICLE_BONUS_GROUPS ' +
                      ' join articles on articles.id = ARTICLE_BONUS_GROUPS.id_articles and articles.is_active=1' +
                      ' join BONUS_GROUPS on BONUS_GROUPS.id = ARTICLE_BONUS_GROUPS.ID_BONUS_GROUPS and BONUS_GROUPS.is_active=1 ' +
                      ' join department on department.id = articles.id_departament ' +
                      ' join food_group on food_group.id = department.id_food_group ' +
                      ' join ARTICLE_BONUS_PRICES on ARTICLE_BONUS_PRICES.ID_ARTICLE_BONUS_GROUPS = ARTICLE_BONUS_GROUPS.id and ARTICLE_BONUS_PRICES.is_active=1 and id_prices=' + IntToStr(id_prices) +
                      ' left outer join currency on ARTICLE_BONUS_PRICES.id_currency = currency.id and currency.is_active=1 ' +
                      ' where ARTICLE_BONUS_GROUPS.is_active=1';
      end;
      sqltxt := sqltxt + strIds;
      q := upos.UntillDB.GetPreparedIIbSql(sqltxt);
      q.ExecQuery;
      while not q.eof do begin
         with ds do begin
           Append;
           FieldByName('id').asString :=  q.FieldByName('id').asString;
           FieldByName('id_articles').asString :=  q.FieldByName('id_articles').asString;
           FieldByName('id_prices').asString :=  IntToStr(id_prices);
           id_currency := StrToInt64def(q.FieldByName('id_currency').asString,0);
           price := q.FieldByName('price').asCurrency;
           if id_currency>0 then begin
             round := q.FieldByName('round').asInteger;
             if (upos.MainCurrency.Id <> id_currency) then begin
               price := CalcPrice(id_currency, price, round);
             end;
             FieldByName('round').asInteger  :=  round;
             FieldByName('id_currency').asString :=  IntToStr(id_currency);
             FieldByName('price').asCurrency := price;
           end;

           use_group_vat := q.FieldByName('use_group_vat').asInteger;
           if use_group_vat=1 then begin
             FieldByName('group_vat_percent').asCurrency :=  q.FieldByName('group_vat').asCurrency;
             FieldByName('sec_group_vat_percent').asCurrency :=  q.FieldByName('sec_group_vat').asCurrency;
             FieldByName('group_vat_sign').asString :=  q.FieldByName('group_vat_sign').asString;
             FieldByName('sec_group_vat_sign').asString :=  q.FieldByName('sec_group_vat_sign').asString;
           end else begin
             FieldByName('group_vat_percent').asCurrency :=  0;
             FieldByName('sec_group_vat_percent').asCurrency :=  0;
             FieldByName('group_vat_sign').asString :=  '';
             FieldByName('sec_group_vat_sign').asString :=  '';
           end;
           FieldByName('use_group_vat').asInteger     :=  use_group_vat;
           FieldByName('vat_percent').asCurrency      :=  q.FieldByName('vat').asCurrency;
           FieldByName('vat_sign').asString           :=  q.FieldByName('vat_sign').asString;
           FieldByName('sec_vat_percent').asCurrency  :=  q.FieldByName('sec_vat').asCurrency;
           FieldByName('sec_vat_sign').asString       :=  q.FieldByName('sec_vat_sign').asString;
           Post;
         end;
         q.Next;
      end;
    end;
    MainThreadAlive;
    Application.ProcessMessages;
  end;

  with ds do begin
    IndexDefs.Clear;
    AddIndex('idIndex', 'id_articles', [], '');
    AddIndex('mIndex', 'id_prices;id_articles', [], '');
  end;
end;

procedure AssignArtFont(item : TCachedArticle);
var
  bFontFound: boolean;
  DefaultSCRG: Int64;
  bNeedSpecialGroup: boolean;
  DefAttr: TButtonDefAttr;
begin
  bFontFound := false;
  DefaultSCRG := ID_SCREEN_GROUPS_PC;
  bNeedSpecialGroup := false;
  DefAttr.Clear;
  if RunMode in [armFalcon, armOrderman] then begin
    DefAttr.Found := cdsArtFonts.FindKey([item.Id, DefaultSCRG]);
    if DefAttr.Found then begin
      DefAttr.Color := cdsArtFonts.FieldByName('color').AsInteger;;
      DefAttr.FontName := cdsArtFonts.FieldByName('font_name').AsString;
      DefAttr.FontColor := cdsArtFonts.FieldByName('font_color').AsInteger;
      DefAttr.FontSize := cdsArtFonts.FieldByName('font_size').AsInteger;
      DefAttr.FontAttr := cdsArtFonts.FieldByName('font_attr').AsInteger;
    end;
  end;
  if (RunMode in [armPos, armNormal]) then
    if cdsArtFonts.FindKey([item.Id, id_screen_group]) then begin
      if not (cdsArtFonts.FieldByName('color').IsNull
        and cdsArtFonts.FieldByName('font_color').IsNull
        and cdsArtFonts.FieldByName('font_name').IsNull
        and cdsArtFonts.FieldByName('font_size').IsNull)
      then begin
        bFontFound := true;
        bNeedSpecialGroup := true;
      end;
    end;
  if RunMode = armFalcon then begin
    if (item.hht_default_color = 0)
      or (item.hht_default_font = 0)
      or (item.hht_default_alt_font = 0)
    then begin
      DefaultSCRG := ID_SCREEN_GROUPS_FALCON;
      bNeedSpecialGroup := true;
    end;
  end else if RunMode = armOrderman then begin
    if (item.oman_default_color = 0)
      or (item.oman_default_font = 0)
      or (item.oman_default_alt_font = 0)
    then begin
      DefaultSCRG := ID_SCREEN_GROUPS_ORDERMAN;
      bNeedSpecialGroup := true;
    end;
  end;
  if bNeedSpecialGroup then begin
    if cdsArtFonts.FindKey([item.Id, id_screen_group]) then begin
      if not (cdsArtFonts.FieldByName('color').IsNull
        and cdsArtFonts.FieldByName('font_color').IsNull
        and cdsArtFonts.FieldByName('font_name').IsNull
        and cdsArtFonts.FieldByName('font_size').IsNull)
      then
        bFontFound := true;
    end;
  end;
  if RunMode = armFalcon then begin
    if not bFontFound then begin
      if cdsArtFonts.FindKey([item.Id, DefaultSCRG]) then
        if not (cdsArtFonts.FieldByName('color').IsNull
          and cdsArtFonts.FieldByName('font_color').IsNull
          and cdsArtFonts.FieldByName('font_name').IsNull
          and cdsArtFonts.FieldByName('font_size').IsNull)
        then
          bFontFound := true;
    end;
    if not bFontFound then begin
      if cdsArtFonts.FindKey([item.Id, ID_SCREEN_GROUPS_PC]) then
        if not (cdsArtFonts.FieldByName('color').IsNull
          and cdsArtFonts.FieldByName('font_color').IsNull
          and cdsArtFonts.FieldByName('font_name').IsNull
          and cdsArtFonts.FieldByName('font_size').IsNull)
        then
          bFontFound := true;
    end;
    if bFontFound then begin
      if item.hht_default_color = 0 then begin
        item.hht_color := cdsArtFonts.FieldByName('color').asInteger;
        if item.hht_color = clBtnFace then item.hht_color := clWhite;
      end else begin
        item.hht_color := DefAttr.Color;
      end;
      item.pc_color := item.hht_color;
      if item.hht_default_font = 0 then begin
        item.hht_font_name  := cdsArtFonts.FieldByName('font_name').asString;
        item.hht_font_size  := cdsArtFonts.FieldByName('font_size').asInteger;
        item.hht_font_attr  := cdsArtFonts.FieldByName('font_attr').asInteger;
        item.hht_font_color := cdsArtFonts.FieldByName('font_color').asInteger;
      end else begin
        item.hht_font_name  := DefAttr.FontName;
        item.hht_font_size  := DefAttr.FontSize;
        item.hht_font_attr  := DefAttr.FontAttr;
        item.hht_font_color := DefAttr.FontColor;
      end;
      item.pc_font_name  := item.hht_font_name;
      item.pc_font_size  := item.hht_font_size;
      item.pc_font_attr  := item.hht_font_attr;
      item.pc_font_color := item.hht_font_color;
      if item.hht_default_alt_font = 0 then begin
        item.hht_font_name_alt  := cdsArtFonts.FieldByName('font_name_alt').asString;
        item.hht_font_size_alt  := cdsArtFonts.FieldByName('font_size_alt').asInteger;
        item.hht_font_attr_alt  := cdsArtFonts.FieldByName('font_attr_alt').asInteger;
        item.hht_font_color_alt := cdsArtFonts.FieldByName('font_color_alt').asInteger;
      end else begin
        item.hht_font_name_alt  := DefAttr.FontName;
        item.hht_font_size_alt  := DefAttr.FontSize;
        item.hht_font_attr_alt  := DefAttr.FontAttr;
        item.hht_font_color_alt := DefAttr.FontColor;
      end;
      item.pc_font_name_alt  := item.hht_font_name_alt;
      item.pc_font_size_alt  := item.hht_font_size_alt;
      item.pc_font_attr_alt  := item.hht_font_attr;
      item.pc_font_color_alt := item.hht_font_color;
    end else begin
      item.hht_color          := cdsArtFonts.FieldByName('color').asInteger;
      item.hht_font_name      := cdsArtFonts.FieldByName('font_name').asString;
      item.hht_font_size      := cdsArtFonts.FieldByName('font_size').asInteger;
      item.hht_font_attr      := cdsArtFonts.FieldByName('font_attr').asInteger;
      item.hht_font_color     := cdsArtFonts.FieldByName('font_color').asInteger;
      item.hht_font_name_alt  := cdsArtFonts.FieldByName('font_name_alt').asString;
      item.hht_font_size_alt  := cdsArtFonts.FieldByName('font_size_alt').asInteger;
      item.hht_font_attr_alt  := cdsArtFonts.FieldByName('font_attr_alt').asInteger;
      item.hht_font_color_alt := cdsArtFonts.FieldByName('font_color_alt').asInteger;
    end;
  end else if RunMode = armOrderman then begin
    if not bFontFound then begin
      if cdsArtFonts.FindKey([item.id , DefaultSCRG]) then
        if not (cdsArtFonts.FieldByName('color').IsNull
          and cdsArtFonts.FieldByName('font_color').IsNull
          and cdsArtFonts.FieldByName('font_name').IsNull
          and cdsArtFonts.FieldByName('font_size').IsNull)
        then
          bFontFound := true;
    end;
    if not bFontFound then begin
      if cdsArtFonts.FindKey([item.id , ID_SCREEN_GROUPS_PC]) then
       if not (cdsArtFonts.FieldByName('color').IsNull
         and cdsArtFonts.FieldByName('font_color').IsNull
         and cdsArtFonts.FieldByName('font_name').IsNull
         and cdsArtFonts.FieldByName('font_size').IsNull)
       then
         bFontFound := true;
    end;
    if bFontFound then begin
      if item.oman_default_color = 0 then begin
        item.hht_color :=  cdsArtFonts.FieldByName('color').asInteger;
        if item.hht_color = clBtnFace then item.hht_color := clWhite;
      end else begin
        item.hht_color :=  DefAttr.Color;
      end;
      item.pc_color := item.hht_color;
      if item.oman_default_font = 0 then begin
        item.hht_font_name  := cdsArtFonts.FieldByName('font_name').asString;
        item.hht_font_size  := cdsArtFonts.FieldByName('font_size').asInteger;
        item.hht_font_attr  := cdsArtFonts.FieldByName('font_attr').asInteger;
        item.hht_font_color := cdsArtFonts.FieldByName('font_color').asInteger;
      end else begin
        item.hht_font_name  := DefAttr.FontName;
        item.hht_font_size  := DefAttr.FontSize;
        item.hht_font_attr  := DefAttr.FontAttr;
        item.hht_font_color := DefAttr.FontColor;
      end;
      item.pc_font_name  := item.hht_font_name;
      item.pc_font_size  := item.hht_font_size;
      item.pc_font_attr  := item.hht_font_attr;
      item.pc_font_color := item.hht_font_color;
      if item.oman_default_alt_font = 0 then begin
        item.hht_font_name_alt  := cdsArtFonts.FieldByName('font_name_alt').asString;
        item.hht_font_size_alt  := cdsArtFonts.FieldByName('font_size_alt').asInteger;
        item.hht_font_attr_alt  := cdsArtFonts.FieldByName('font_attr_alt').asInteger;
        item.hht_font_color_alt := cdsArtFonts.FieldByName('font_color_alt').asInteger;
      end else begin
        item.hht_font_name_alt  := DefAttr.FontName;
        item.hht_font_size_alt  := DefAttr.FontSize;
        item.hht_font_attr_alt  := DefAttr.FontAttr;
        item.hht_font_color_alt := DefAttr.FontColor;
      end;
      item.pc_font_name_alt  := item.hht_font_name;
      item.pc_font_size_alt  := item.hht_font_size;
      item.pc_font_attr_alt  := item.hht_font_attr;
      item.pc_font_color_alt := item.hht_font_color;
    end else begin
      item.hht_color          :=  cdsArtFonts.FieldByName('color').asInteger;
      item.hht_font_name      := cdsArtFonts.FieldByName('font_name').asString;
      item.hht_font_size      := cdsArtFonts.FieldByName('font_size').asInteger;
      item.hht_font_attr      := cdsArtFonts.FieldByName('font_attr').asInteger;
      item.hht_font_color     := cdsArtFonts.FieldByName('font_color').asInteger;
      item.hht_font_name_alt  := cdsArtFonts.FieldByName('font_name_alt').asString;
      item.hht_font_size_alt  := cdsArtFonts.FieldByName('font_size_alt').asInteger;
      item.hht_font_attr_alt  := cdsArtFonts.FieldByName('font_attr_alt').asInteger;
      item.hht_font_color_alt := cdsArtFonts.FieldByName('font_color_alt').asInteger;
    end;
  end else begin
    if not bFontFound then begin
      if cdsArtFonts.FindKey([item.id , ID_SCREEN_GROUPS_PC]) then
       if not (cdsArtFonts.FieldByName('color').IsNull
         and cdsArtFonts.FieldByName('font_color').IsNull
         and cdsArtFonts.FieldByName('font_name').IsNull
         and cdsArtFonts.FieldByName('font_size').IsNull)
       then
         bFontFound := true;
    end;
    if bFontFound then begin
      item.pc_color           := cdsArtFonts.FieldByName('color').asInteger;
      item.hht_color          := item.pc_color;
      item.pc_font_name       := cdsArtFonts.FieldByName('font_name').asString;
      item.pc_font_size       := cdsArtFonts.FieldByName('font_size').asInteger;
      item.pc_font_attr       := cdsArtFonts.FieldByName('font_attr').asInteger;
      item.pc_font_color      := cdsArtFonts.FieldByName('font_color').asInteger;
      item.hht_font_name      := item.pc_font_name;
      item.hht_font_size      := item.pc_font_size;
      item.hht_font_attr      := item.pc_font_attr;
      item.hht_font_color     := item.pc_font_color;
      item.pc_font_name_alt   := cdsArtFonts.FieldByName('font_name_alt').asString;
      item.pc_font_size_alt   := cdsArtFonts.FieldByName('font_size_alt').asInteger;
      item.pc_font_attr_alt   := cdsArtFonts.FieldByName('font_attr_alt').asInteger;
      item.pc_font_color_alt  := cdsArtFonts.FieldByName('font_color_alt').asInteger;
      item.hht_font_name_alt  := item.pc_font_name_alt;
      item.hht_font_size_alt  := item.pc_font_size_alt;
      item.hht_font_attr_alt  := item.pc_font_attr_alt;
      item.hht_font_color_alt := item.pc_font_color_alt;
    end;
  end;
end;

procedure AssignDepFont(qq : IIBSQL);
var
  bFontFound: boolean;
  DefaultSCRG: Int64;
  bNeedSpecialGroup: boolean;
  id: String;
  DefAttr: TButtonDefAttr;
begin
  bFontFound := false;
  DefaultSCRG := ID_SCREEN_GROUPS_PC;
  bNeedSpecialGroup := false;
  DefAttr.Clear;
  id := qq.FieldByName('id').asString;
  if RunMode in [armFalcon, armOrderman] then begin
    DefAttr.Found := cdsDepFonts.FindKey([id, DefaultSCRG]);
    if DefAttr.Found then begin
      DefAttr.Color := cdsDepFonts.FieldByName('color').AsInteger;
      DefAttr.FontColor := cdsDepFonts.FieldByName('font_color').AsInteger;
      DefAttr.FontName := cdsDepFonts.FieldByName('font_name').AsString;
      DefAttr.FontSize := cdsDepFonts.FieldByName('font_size').AsInteger;
      DefAttr.FontAttr := cdsDepFonts.FieldByName('font_attr').AsInteger;
    end;
  end;
  if  (RunMode in [armPos, armNormal]) then
    if cdsDepFonts.FindKey([CashDepartmentDataSet.fieldByName('id').asString, id_screen_group]) then begin
      if not (cdsDepFonts.FieldByName('color').IsNull
        and cdsDepFonts.FieldByName('font_color').IsNull
        and cdsDepFonts.FieldByName('font_name').IsNull
        and cdsDepFonts.FieldByName('font_size').IsNull )
      then begin
        bFontFound := true;
        bNeedSpecialGroup := true;
      end;
    end;
  if RunMode = armFalcon then begin
    if (qq.FieldByName('hht_default_color').AsInteger = 0)
      or (qq.FieldByName('hht_default_font').AsInteger = 0)
    then begin
      DefaultSCRG := ID_SCREEN_GROUPS_FALCON;
      bNeedSpecialGroup := true;
    end;
  end else if RunMode = armOrderman then begin
    if (qq.FieldByName('oman_default_color').AsInteger = 0)
      or (qq.FieldByName('oman_default_font').AsInteger = 0)
    then begin
      DefaultSCRG := ID_SCREEN_GROUPS_ORDERMAN;
      bNeedSpecialGroup := true;
    end;
  end;

  if bNeedSpecialGroup then begin
    if cdsDepFonts.FindKey([id, id_screen_group]) then begin
      if not cdsDepFonts.FieldByName('color').IsNull then
        bFontFound := true;
    end;
  end;
  if RunMode = armFalcon then begin
    if not bFontFound then begin
      if cdsDepFonts.FindKey([id, DefaultSCRG]) then
        if not cdsDepFonts.FieldByName('color').IsNull then
          bFontFound := true;
    end;
    if not bFontFound then begin
      if cdsDepFonts.FindKey([id, ID_SCREEN_GROUPS_PC]) then
        if not cdsDepFonts.FieldByName('color').IsNull then
          bFontFound := true;
    end;
    if bFontFound then begin
      if qq.FieldByName('hht_default_color').asInteger = 0 then begin
        CashDepartmentDataSet.FieldByName('hht_color').asString := cdsDepFonts.FieldByName('color').asString;
        if CashDepartmentDataSet.FieldByName('hht_color').asInteger = clBtnFace then
          CashDepartmentDataSet.FieldByName('hht_color').asInteger := clWhite;
      end else if DefAttr.Found then begin
        CashDepartmentDataSet.FieldByName('hht_color').AsInteger := DefAttr.color;
      end else begin
        CashDepartmentDataSet.FieldByName('hht_color').asString := qq.FieldByName('hht_color').asString;
      end;
      if qq.FieldByName('hht_default_font').asInteger = 0 then begin
        CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  cdsDepFonts.FieldByName('font_name').asString;
        CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  cdsDepFonts.FieldByName('font_size').asString;
        CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  cdsDepFonts.FieldByName('font_attr').asString;
        CashDepartmentDataSet.FieldByName('hht_font_color').asString := cdsDepFonts.FieldByName('font_color').asString;
      end else if DefAttr.Found then begin
        CashDepartmentDataSet.FieldByName('hht_font_name').AsString :=   DefAttr.FontName;
        CashDepartmentDataSet.FieldByName('hht_font_size').AsInteger :=  DefAttr.FontSize;
        CashDepartmentDataSet.FieldByName('hht_font_attr').AsInteger :=  DefAttr.FontAttr;
        CashDepartmentDataSet.FieldByName('hht_font_color').AsInteger := DefAttr.FontColor;
      end else begin
        CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  qq.FieldByName('pc_font_name').asString;
        CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  qq.FieldByName('pc_font_size').asString;
        CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  qq.FieldByName('pc_font_attr').asString;
        CashDepartmentDataSet.FieldByName('hht_font_color').asString := qq.FieldByName('hht_font_color').asString;
      end;
      CashDepartmentDataSet.FieldByName('pc_color').asString :=  CashDepartmentDataSet.FieldByName('hht_color').asString;
      CashDepartmentDataSet.FieldByName('pc_font_name').asString :=  CashDepartmentDataSet.FieldByName('hht_font_name').asString;
      CashDepartmentDataSet.FieldByName('pc_font_size').asString :=  CashDepartmentDataSet.FieldByName('hht_font_size').asString;
      CashDepartmentDataSet.FieldByName('pc_font_attr').asString :=  CashDepartmentDataSet.FieldByName('hht_font_attr').asString;
      CashDepartmentDataSet.FieldByName('pc_font_color').asString := CashDepartmentDataSet.FieldByName('hht_font_color').asString;
    end else begin
      CashDepartmentDataSet.FieldByName('hht_color').asString :=  qq.FieldByName('pc_color').asString;
      CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  qq.FieldByName('pc_font_name').asString;
      CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  qq.FieldByName('pc_font_size').asString;
      CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  qq.FieldByName('pc_font_attr').asString;
      CashDepartmentDataSet.FieldByName('hht_font_color').asString := qq.FieldByName('hht_font_color').asString;
    end;
  end else if RunMode = armOrderman then begin
    if not bFontFound then begin
      if cdsDepFonts.FindKey([CashDepartmentDataSet.FieldByName('id').asString, DefaultSCRG]) then
        if not cdsDepFonts.FieldByName('color').IsNull then
          bFontFound := true;
    end;
    if not bFontFound then begin
      if cdsDepFonts.FindKey([CashDepartmentDataSet.FieldByName('id').asString, ID_SCREEN_GROUPS_PC]) then
        if not cdsDepFonts.FieldByName('color').IsNull then
          bFontFound := true;
    end;
    if bFontFound then begin
      if qq.FieldByName('oman_default_color').asInteger = 0 then begin
        CashDepartmentDataSet.FieldByName('hht_color').asString := cdsDepFonts.FieldByName('color').asString;
        if CashDepartmentDataSet.FieldByName('hht_color').asInteger = clBtnFace then
          CashDepartmentDataSet.FieldByName('hht_color').asInteger := clWhite;
      end else if DefAttr.Found then begin
        CashDepartmentDataSet.FieldByName('hht_color').AsInteger := DefAttr.color;
      end else begin
        CashDepartmentDataSet.FieldByName('hht_color').asString := qq.FieldByName('hht_color').asString;
      end;
      if qq.FieldByName('oman_default_font').asInteger = 0 then begin
        CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  cdsDepFonts.FieldByName('font_name').asString;
        CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  cdsDepFonts.FieldByName('font_size').asString;
        CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  cdsDepFonts.FieldByName('font_attr').asString;
        CashDepartmentDataSet.FieldByName('hht_font_color').asString := cdsDepFonts.FieldByName('font_color').asString;
      end else if DefAttr.Found then begin
        CashDepartmentDataSet.FieldByName('hht_font_name').asString :=   DefAttr.FontName;
        CashDepartmentDataSet.FieldByName('hht_font_size').AsInteger :=  DefAttr.FontSize;
        CashDepartmentDataSet.FieldByName('hht_font_attr').AsInteger :=  DefAttr.FontAttr;
        CashDepartmentDataSet.FieldByName('hht_font_color').AsInteger := DefAttr.FontColor;
      end else begin
        CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  qq.FieldByName('pc_font_name').asString;
        CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  qq.FieldByName('pc_font_size').asString;
        CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  qq.FieldByName('pc_font_attr').asString;
        CashDepartmentDataSet.FieldByName('hht_font_color').asString := qq.FieldByName('hht_font_color').asString;
      end;
      CashDepartmentDataSet.FieldByName('pc_color').asString :=      CashDepartmentDataSet.FieldByName('hht_color').asString;
      CashDepartmentDataSet.FieldByName('pc_font_name').asString :=  CashDepartmentDataSet.FieldByName('hht_font_name').asString;
      CashDepartmentDataSet.FieldByName('pc_font_size').asString :=  CashDepartmentDataSet.FieldByName('hht_font_size').asString;
      CashDepartmentDataSet.FieldByName('pc_font_attr').asString :=  CashDepartmentDataSet.FieldByName('hht_font_attr').asString;
      CashDepartmentDataSet.FieldByName('pc_font_color').asString := CashDepartmentDataSet.FieldByName('hht_font_color').asString;
    end else begin
      CashDepartmentDataSet.FieldByName('hht_color').asString :=      qq.FieldByName('pc_color').asString;
      CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  qq.FieldByName('pc_font_name').asString;
      CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  qq.FieldByName('pc_font_size').asString;
      CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  qq.FieldByName('pc_font_attr').asString;
      CashDepartmentDataSet.FieldByName('hht_font_color').asString := qq.FieldByName('hht_font_color').asString;
    end;
  end else begin
    if not bFontFound then begin
      if cdsDepFonts.FindKey([CashDepartmentDataSet.FieldByName('id').asString, ID_SCREEN_GROUPS_PC]) then
        if not cdsDepFonts.FieldByName('color').IsNull then
          bFontFound := true;
    end;
    if bFontFound then begin
      CashDepartmentDataSet.FieldByName('pc_color').asString :=  cdsDepFonts.FieldByName('color').asString;
      CashDepartmentDataSet.FieldByName('pc_font_name').asString :=  cdsDepFonts.FieldByName('font_name').asString;
      CashDepartmentDataSet.FieldByName('pc_font_size').asString :=  cdsDepFonts.FieldByName('font_size').asString;
      CashDepartmentDataSet.FieldByName('pc_font_attr').asString :=  cdsDepFonts.FieldByName('font_attr').asString;
      CashDepartmentDataSet.FieldByName('pc_font_color').asString := cdsDepFonts.FieldByName('font_color').asString;
      CashDepartmentDataSet.FieldByName('hht_color').asString :=  CashDepartmentDataSet.FieldByName('pc_color').asString;
      CashDepartmentDataSet.FieldByName('hht_font_name').asString :=  CashDepartmentDataSet.FieldByName('pc_font_name').asString;
      CashDepartmentDataSet.FieldByName('hht_font_size').asString :=  CashDepartmentDataSet.FieldByName('pc_font_size').asString;
      CashDepartmentDataSet.FieldByName('hht_font_attr').asString :=  CashDepartmentDataSet.FieldByName('pc_font_attr').asString;
      CashDepartmentDataSet.FieldByName('hht_font_color').asString := CashDepartmentDataSet.FieldByName('pc_font_color').asString;
    end;
  end;
end;

procedure InitArtPositions;
var q : IIBSQL;
    wt : IWTran;
    prev : TUntillDbLogging;
begin
  prev := upos.UntillDB.GetULogging;
  upos.UntillDB.SetULogging(udblClearSales);
  try
    wt := upos.UntillDB.GetwTran;
    q := wt.GetPreparedIIbSQL('execute procedure fix_pos_article_positions');
    q.ExecQuery;
    wt.Commit;
  finally
    upos.UntillDB.SetULogging(prev);
  end;
end;

procedure ConvertImageToSmall(ams : TMemoryStream;
  aW, aH : Integer; var aOutMS : TMemoryStream);
var
  ver, w,h : Integer;
  ar, DrawAr   : TPolyArray;
  picType  : Integer;
  BackPng  : TPNGImage;
  BackBmp  : Graphics.TBitmap;
  tempBMP  : Graphics.TBitmap;
  msPos    : Integer;
  bLoaded  : boolean;
  DrawR    : TRect;
  maxW, maxH : Integer;
begin
  assert(assigned(aMS));
  assert(assigned(aOutMS));
  aOutms.Clear;
  if ams.Size=0 then exit;


  ams.Seek(0,0);
  ams.Read(Ver, sizeof(Ver));
  ams.Read(W, sizeof(W));
  ams.Read(H, sizeof(H));
  ar := GetImgRect(aW, aH, W, H);
  picType := 2;
  BackPng := nil;
  BackBmp := nil;
  tempBMP := nil;
  maxW    := Round(aw * 1.2);
  maxH    := Round(ah * 1.2);
  try
    BackPng := TPngImage.Create;
    BackBmp := Graphics.TBitmap.Create;
    tempBMP := Graphics.TBitmap.Create;
    tempBMP.Width  := ar[2].X - ar[0].X;
    tempBMP.Height := ar[2].Y - ar[0].Y;
    DrawR  := Rect(0, 0, tempBMP.Width, tempBMP.Height);
    DrawaR[0] := Point(0,0);
    DrawaR[1] := Point(tempBMP.Width,0);
    DrawaR[2] := Point(tempBMP.Width,tempBMP.height);
    DrawaR[3] := Point(0,tempBMP.height);
    bLoaded := false;
    if Ver>1 then begin
      ams.Read(picType, sizeof(picType));
      if ams.Position < ams.Size then begin
        if picType = 1 then begin
          BackPng.LoadFromStream(ams);
          if (BackPng.Width > maxW) or (BackPng.Height > maxH) then begin
            BackPng.Draw(tempBMP.Canvas, DrawR );
            bLoaded := true;
          end;
        end else if PicType=0 then begin
          BackBmp.LoadFromStream(ams);
          if (BackBmp.Width > maxW) or (BackBmp.Height > maxH) then begin
            FastDrawBitmap(tempBMP.Canvas, BackBmp, Drawar);
            bLoaded := true;
          end;
        end;
      end;
    end else begin
      msPos := ams.Position;
      if msPos < ams.Size then begin
        try
          BackPng.LoadFromStream(ams);
          if (BackPng.Width > maxW) or (BackPng.Height > maxH) then begin
            BackPng.Draw(tempBMP.Canvas, DrawR );
            bLoaded := true;
            PicType := 1;
          end;
        except
        end;
        if not bLoaded then begin
          try
            ams.Position := msPos;
            BackBmp.LoadFromStream(ams);
            if (BackBmp.Width > maxW) or (BackBmp.Height > maxH) then begin
              FastDrawBitmap(tempBMP.Canvas, BackBmp, Drawar);
              bLoaded := true;
              PicType := 0;
            end;
          except
          end;
        end;
      end;
    end;

    if not bLoaded or tempBmp.empty then begin
      ams.Seek(0,0);
      aOutms.CopyFrom(ams, ams.size);
    end else begin
      ver := CURRENT_DS_PICTURE_VERSION;
      aOutms.Write(ver,sizeof(ver));
      w := tempBmp.width;
      h := tempBmp.Height;
      aOutms.Write(w,sizeof(w));
      aOutms.Write(h,sizeof(h));
      PicType := 0;
      aOutms.Write(PicType,sizeof(PicType));
      tempBmp.SaveToStream(aOutms);
    end;

  finally
    FreeAndNil( BackPng );
    FreeAndNil( BackBmp );
    FreeAndNil( tempBMP );
  end;
end;

procedure CacheOptionArticleDatasets(aid_art : Int64 = 0; aid_opt : Int64 = 0);
var qq : IIBSQL;
    strSQL : String;
begin
  if (aid_art > 0) then
    CashOptionArticleDataSet.DeleteByArtID(aid_art)
  else if (aid_opt > 0) then
    CashOptionArticleDataSet.DeleteByOptID(aid_opt)
  else
    CashOptionArticleDataSet.EmptyDataSet;

  strSQL := 'select distinct option_article.*, articles.id_departament,'
    + ' option_sales_area.id_sales_area, articles.ARTICLE_NUMBER '
    + ' from option_article '
    + ' join options on options.id=option_article.id_options and options.is_active=1 '
    + ' join articles on articles.id=option_article.id_articles and articles.is_active=1 '
    + ' left outer join option_sales_area on option_sales_area.id_options = option_article.id_options '
    + ' and option_sales_area.is_active=1'
    + ' where option_article.is_active=1 ';
  if aid_art>0 then
    strSQL := strSQL + ' and option_article.id_articles = :id_art';
  if aid_opt>0 then
    strSQL := strSQL + ' and option_article.id_options = :id_opt';
  qq := upos.UntillDB.GetPreparedIIBSQL( strSQL );
  if aid_art>0 then
    qq.q.ParamByName('id_art').AsInt64 := aid_art;
  if aid_opt>0 then
    qq.q.ParamByName('id_opt').AsInt64 := aid_opt;
  qq.ExecQuery;
  while not qq.eof do begin
    CashOptionArticleDataSet.Append;
    CashOptionArticleDataSet.FieldByName('id').asString   :=  qq.FieldByName('id').asString;
    CashOptionArticleDataSet.FieldByName('id_articles').asString   :=  qq.FieldByName('id_articles').asString;
    CashOptionArticleDataSet.FieldByName('id_options').asString    :=  qq.FieldByName('id_options').asString;
    CashOptionArticleDataSet.FieldByName('id_sales_area').asString :=  qq.FieldByName('id_sales_area').asString;
    CashOptionArticleDataSet.FieldByName('option_number').asString :=  qq.FieldByName('option_number').asString;
    CashOptionArticleDataSet.FieldByName('show_in_ks').asInteger   :=  qq.FieldByName('show_in_ks').asInteger;
    CashOptionArticleDataSet.FieldByName('article_number').asInteger :=  qq.FieldByName('ARTICLE_NUMBER').asInteger;
    CashOptionArticleDataSet.FieldByName('id_department').AsString :=  qq.FieldByName('id_departament').AsString;
    CashOptionArticleDataSet.Post;
    qq.Next;
  end;
  with CashOptionArticleDataSet do begin
    IndexDefs.Clear;
    AddIndex('idIdx', 'id', [], '');
    AddIndex('idartIdx', 'id_articles', [], '');
    AddIndex('idoptIdx', 'id_options', [], '');
    AddIndex('oaidIdx' , 'id_options; id_articles', [], '');
    AddIndex('oidIndex', 'id_options;id_sales_area;option_number', [], '');
  end;
  MainThreadAlive;
         Application.ProcessMessages;

  if (aid_art > 0) then
    CashArticleOptionDataSet.DeleteByArtID(aid_art)
  else if (aid_opt > 0) then
    CashArticleOptionDataSet.DeleteByOptID(aid_opt)
  else
    CashArticleOptionDataSet.EmptyDataSet;
  strSQL := 'select distinct article_options.*, OPTION_SALES_AREA.id_sales_area ' +
    ' from article_options ' +
    ' join articles on articles.id=article_options.id_articles and articles.is_active=1 ' +
    ' join OPTION_SALES_AREA on OPTION_SALES_AREA.ID_OPTIONS = ARTICLE_OPTIONS.ID_OPTIONS and OPTION_SALES_AREA.IS_ACTIVE = 1'+
    ' where article_options.is_active=1 ';
  if UntillAppU.IsTPAPI then
    strSQL := strSQL + ' and coalesce(articles.omittpapi,0)=0';
  if aid_art>0 then
    strSQL := strSQL + ' and article_options.id_articles = :id_art';
  if aid_opt>0 then
    strSQL := strSQL + ' and article_options.id_options = :id_opt';
  strSQL := strSQL + ' order by option_number';
  qq := upos.UntillDB.GetPreparedIIBSQL(strSQL);
  if aid_art>0 then
    qq.q.ParamByName('id_art').AsInt64 := aid_art;
  if aid_opt>0 then
    qq.q.ParamByName('id_opt').AsInt64 := aid_opt;
  qq.ExecQuery;
  while not qq.eof do begin
    CashArticleOptionDataSet.Append;
    CashArticleOptionDataSet.FieldByName('id').asString   :=  qq.FieldByName('id').asString;
    CashArticleOptionDataSet.FieldByName('id_articles').asString   :=  qq.FieldByName('id_articles').asString;
    CashArticleOptionDataSet.FieldByName('id_options').asString    :=  qq.FieldByName('id_options').asString;
    CashArticleOptionDataSet.FieldByName('compose').asInteger      :=  qq.FieldByName('compose').asInteger;
    CashArticleOptionDataSet.FieldByName('id_sales_area').asString :=  qq.FieldByName('id_sales_area').asString;
    CashArticleOptionDataSet.Post;
    qq.Next;
  end;
  with CashArticleOptionDataSet do begin
    IndexDefs.Clear;
    AddIndex('idIdx', 'id', [], '');
    AddIndex('idartIdx', 'id_articles', [], '');
    AddIndex('idoptIdx', 'id_options', [], '');
    AddIndex('idArtSAIdx', 'id_articles; id_sales_area', [], '');
    AddIndex('ArtOptIdx', 'id_articles; id_options', [], '');
  end;
  MainThreadAlive;
         Application.ProcessMessages;
end;

procedure CacheClients( aid_client: Int64 );
var qSel : IIBSQL;
    qstr : string;
    cachecl : TCachedClient;
begin
  qstr := BORestaurantCache.Clients.GetSQLText();
  qstr := qstr + ' where is_active=1 ';
  if aid_client > 0 then
    qstr := qstr +  ' and id = ' + IntToStr(aid_client);
  qSel := upos.UntillDB.GetPreparedIIBSQL( qstr );
  qSel.ExecQuery;
  if qSel.eof then exit;

  while not qSel.eof do begin
    cachecl := BORestaurantCache.Clients.Get(aid_client, nil);
    if not assigned(cachecl) then begin
      cachecl          := TCachedClient.Create;
      with cachecl do begin
        Id               := StrToInt64Def(qSel.FieldbyName('id').asString,0);
        IsActive         := qSel.FieldbyName('is_active').asInteger = 1;
        Name             := qSel.FieldbyName('name').asString;
      end;
      BORestaurantCache.Clients.AddNew(cachecl.Id, cachecl);
    end;
    with cachecl do begin
      Name             := qSel.FieldbyName('name').asString;
      id_prices_price  := StrToInt64Def(qSel.FieldbyName('id_prices_price').asString,0);
      id_accounts      := StrToInt64Def(qSel.FieldbyName('id_accounts').asString,0);
      common_accounts  := qSel.FieldbyName('common_accounts').asInteger;
      email            := qSel.FieldbyName('email').asString;
      invoice_name     := qSel.FieldbyName('invoice_name').asString;
      phone            := qSel.FieldbyName('phone').asString;
      enable_sp        := qSel.FieldbyName('enable_sp').asInteger;
    end;
    qSel.Next;
  end;
end;

procedure CacheDepartments( astrScreenGroups : String; aid_dep : Int64 = 0 );
var qf, qq : IIBSQL;
    sqlstr : string;
    wt : IWTran;
    strAdd : string;
    ms     : TMemoryStream;
begin
  if aid_dep > 0 then begin
    strAdd := ' and department.id=' + IntToStr( aid_dep );
    cdsDepFonts.DeleteByDepID( aid_dep );
    CashDepartmentDataSet.DeleteByDepID( aid_dep );
  end else begin
    // Fix duplicating depatment pos records
    wt := upos.UntillDB.GetwTran;
    sqlstr  := 'delete from dep_position_ex d1 where exists(select * from dep_position_ex d2 '
      + ' where d1.id_sales_area=d2.id_sales_area '
      + '       and d1.id_department=d2.id_department '
      + '       and d1.id< d2.id)';
    qq := wt.GetPreparedIIBSQL( sqlstr );
    qq.ExecQuery;
    wt.commit;
    cdsDepFonts.EmptyDataSet;
    CashDepartmentDataSet.EmptyDataSet;
  end;

  ms        := nil;
  try
    ms      := TMemoryStream.Create;
    sqlstr  := 'select distinct department.*, department_available.id_sales_area, age_groups.age,'
        + 'coalesce(dep_position_ex.pos, department.dep_number) pos_num,';
    if PosRestaurantSettings.Settings.ExtraSettings.DepPosition=apBO then
      sqlstr := sqlstr + ' department.dep_number dep_num'
    else
      sqlstr := sqlstr + ' coalesce(dep_position_ex.pos, department.dep_number) dep_num';
    sqlstr := sqlstr + ' from department '
      + ' left outer join department_available on department_available.id_department = department.id and department_available.is_active=1'
      + ' left outer join age_groups on age_groups.id = department.id_age_groups and age_groups.is_active=1'
      + ' left outer join dep_position_ex on dep_position_ex.id_department = department.id and dep_position_ex.id_sales_area=department_available.ID_SALES_AREA'
      + ' where department.is_active=1 ';
    sqlstr := sqlstr + strAdd;
    qq := upos.UntillDB.GetPreparedIIBSQL(sqlstr);
    qq.ExecQuery;

    sqlstr := 'select dep_button_setting.* from dep_button_setting  '
      + ' join department on department.id=dep_button_setting.id_department '
      + ' where department.is_active=1 and id_screen_groups in (' + astrScreenGroups + ')';
    sqlstr := sqlstr + strAdd;
    sqlstr := sqlstr + ' order by id_department, id_screen_groups ';
    qf := upos.UntillDB.GetPreparedIIBSQL( sqlstr );
    qf.ExecQuery;
    while not qf.eof do begin
      cdsDepFonts.Append;
      cdsDepFonts.FieldByName('ID_DEPARTMENT').asString := qf.FieldByName('ID_DEPARTMENT').asString;
      cdsDepFonts.FieldByName('ID_SCREEN_GROUPS').asString := qf.FieldByName('ID_SCREEN_GROUPS').asString;
      if not qf.FieldByName('COLOR').IsNull then
        cdsDepFonts.FieldByName('COLOR').asInteger := qf.FieldByName('COLOR').asInteger;
      if not qf.FieldByName('FONT_NAME').IsNull then
        cdsDepFonts.FieldByName('FONT_NAME').asString := qf.FieldByName('FONT_NAME').asString;
      if not qf.FieldByName('FONT_SIZE').IsNull then
        cdsDepFonts.FieldByName('FONT_SIZE').asInteger := qf.FieldByName('FONT_SIZE').asInteger;
      if not qf.FieldByName('FONT_ATTR').IsNull then
        cdsDepFonts.FieldByName('FONT_ATTR').asInteger := qf.FieldByName('FONT_ATTR').asInteger;
      if not qf.FieldByName('FONT_COLOR').IsNull then
        cdsDepFonts.FieldByName('FONT_COLOR').asInteger := qf.FieldByName('FONT_COLOR').asInteger;
      if not qf.FieldByName('FONT_NAME_ALT').IsNull then
        cdsDepFonts.FieldByName('FONT_NAME_ALT').asString := qf.FieldByName('FONT_NAME_ALT').asString;
      if not qf.FieldByName('FONT_SIZE_ALT').IsNull then
        cdsDepFonts.FieldByName('FONT_SIZE_ALT').asInteger := qf.FieldByName('FONT_SIZE_ALT').asInteger;
      if not qf.FieldByName('FONT_ATTR_ALT').IsNull then
        cdsDepFonts.FieldByName('FONT_ATTR_ALT').asInteger := qf.FieldByName('FONT_ATTR_ALT').asInteger;
      if not qf.FieldByName('FONT_COLOR_ALT').IsNull then
        cdsDepFonts.FieldByName('FONT_COLOR_ALT').asInteger := qf.FieldByName('FONT_COLOR_ALT').asInteger;
      cdsDepFonts.Post;
      qf.Next;
    end;
    with cdsDepFonts do begin
      IndexDefs.Clear;
      AddIndex('cfdepIndex', 'id_department', [], '');
      AddIndex('cfIndex', 'id_department;id_screen_groups', [], '');
      IndexName :='cfIndex';
    end;

    while not qq.eof do begin
       CashDepartmentDataSet.Append;
       CashDepartmentDataSet.FieldByName('id').asString        :=  qq.FieldByName('id').asString;
       CashDepartmentDataSet.FieldByName('name').asString      :=  qq.FieldByName('name').asString;
       CashDepartmentDataSet.FieldByName('id_food_group').asString :=  qq.FieldByName('id_food_group').asString;
       CashDepartmentDataSet.FieldByName('pc_text').asString   :=  qq.FieldByName('pc_text').asString;
       CashDepartmentDataSet.FieldByName('fastlane').asInteger :=  qq.FieldByName('fastlane').asInteger;
       CashDepartmentDataSet.FieldByName('age').asInteger      :=  qq.FieldByName('age').asInteger;
       CashDepartmentDataSet.FieldByName('hht_default_setting').asInteger := qq.FieldByName('hht_default_setting').asInteger;
       CashDepartmentDataSet.FieldByName('hht_default_color').asInteger := qq.FieldByName('hht_default_color').asInteger;
       CashDepartmentDataSet.FieldByName('hht_default_font').asInteger := qq.FieldByName('hht_default_font').asInteger;
       CashDepartmentDataSet.FieldByName('hht_default_alt_font').asInteger := qq.FieldByName('hht_default_alt_font').asInteger;
       CashDepartmentDataSet.FieldByName('oman_default_setting').asInteger := qq.FieldByName('oman_default_setting').asInteger;
       CashDepartmentDataSet.FieldByName('oman_default_color').asInteger := qq.FieldByName('oman_default_color').asInteger;
       CashDepartmentDataSet.FieldByName('oman_default_font').asInteger := qq.FieldByName('oman_default_font').asInteger;
       CashDepartmentDataSet.FieldByName('oman_default_alt_font').asInteger := qq.FieldByName('oman_default_alt_font').asInteger;
       CashDepartmentDataSet.FieldByName('hht_text').asString := qq.FieldByName('pc_text').asString;
       if RunMode=armOrderman then begin
         if not qq.FieldByName('oman_text').IsNull then
           CashDepartmentDataSet.FieldByName('hht_text').asString := qq.FieldByName('oman_text').asString
       end else begin
         if not qq.FieldByName('rm_text').IsNull then
           CashDepartmentDataSet.FieldByName('hht_text').asString :=  qq.FieldByName('rm_text').asString;
       end;

       AssignDepFont(qq);

       CashDepartmentDataSet.FieldByName('id_options_supplement').asString :=  qq.FieldByName('id_options_supplement').asString;
       CashDepartmentDataSet.FieldByName('id_options_condiment').asString  :=  qq.FieldByName('id_options_condiment').asString;
       CashDepartmentDataSet.FieldByName('id_sales_area').asString         :=  qq.FieldByName('id_sales_area').asString;
       CashDepartmentDataSet.FieldByName('dep_number').asInteger           :=  qq.FieldByName('dep_num').asInteger;
       CashDepartmentDataSet.FieldByName('pos_number').asInteger           :=  qq.FieldByName('pos_num').asInteger;
       CashDepartmentDataSet.FieldByName('exclude_art').asInteger          :=  qq.FieldByName('exclude_art').asInteger;
       CashDepartmentDataSet.FieldByName('max_supp_quantity').asInteger    :=  qq.FieldByName('max_supp_quantity').asInteger;
       CashDepartmentDataSet.FieldByName('max_cond_quantity').asInteger    :=  qq.FieldByName('max_cond_quantity').asInteger;

       if not qq.FieldByName('pc_bitmap').IsNull then begin
         MS.Clear;
         qq.FieldByName('pc_bitmap').SaveToStream(MS);
         if MS.Size > 12 then begin
           MS.Seek(0,0);
           TBlobField(CashDepartmentDataSet.FieldByName('pc_bitmap')).LoadFromStream(MS);
         end;
       end;

       CashDepartmentDataSet.Post;
       qq.Next;
    end;

    with CashDepartmentDataSet do begin
      IndexDefs.Clear;
      AddIndex('didIndex', 'id_sales_area;dep_number', [], '');
      AddIndex('didIndexPOS', 'id_sales_area;pos_number', [], '');
      AddIndex('didIndex2', 'dep_number;id', [], '');
      AddIndex('didIndex3', 'id', [], '');
      AddIndex('didIndex4', 'id_sales_area;id;exclude_art', [], '');
    end;
  finally
    FreeAndNil( ms );
    MainThreadAlive;
    Application.ProcessMessages;
  end;
end;

procedure CacheArtFonts(astrScreenGroups : String; art_ids : array of int64);
var qstr : String;
    qf   : IIBSQL;
    ic   : Int64;
    strIds : string;
begin
  cdsArtFonts.DeleteByArtIDs( art_ids );

  strIds := '';
  if length(art_ids) > 0 then
    strIds := strIds + ' and ' + GetInExprForIds('ID_ARTICLES', art_ids);

  qstr :=
    'select bs.*' +
    '  from article_button_setting bs' +
    '  join articles a on a.id = bs.id_articles' +
    ' where a.is_active = 1' +
    '   and bs.id_screen_groups in (' + astrScreenGroups + ')';
  qstr := qstr  + strIds;
  qstr := qstr  + ' order by bs.id_articles, bs.id_screen_groups ';
  qf := upos.UntillDB.GetPreparedIIbSql(qstr);
  qf.ExecQuery;
  ic := 0;
  while not qf.eof do begin
    cdsArtFonts.Append;
    cdsArtFonts.FieldByName('id_articles').asString       := qf.FieldByName('id_articles').asString;
    cdsArtFonts.FieldByName('id_screen_groups').asString  := qf.FieldByName('id_screen_groups').asString;
    if not qf.FieldByName('color').IsNull then
      cdsArtFonts.FieldByName('color').asInteger          := qf.FieldByName('color').asInteger;
    if not qf.FieldByName('font_name').IsNull then
      cdsArtFonts.FieldByName('font_name').asString       := qf.FieldByName('font_name').asString;
    if not qf.FieldByName('font_size').IsNull then
      cdsArtFonts.FieldByName('font_size').asInteger      := qf.FieldByName('font_size').asInteger;
    if not qf.FieldByName('font_attr').IsNull then
      cdsArtFonts.FieldByName('font_attr').asInteger      := qf.FieldByName('font_attr').asInteger;
    if not qf.FieldByName('font_color').IsNull then
      cdsArtFonts.FieldByName('font_color').asInteger     := qf.FieldByName('font_color').asInteger;
    if not qf.FieldByName('font_name_alt').IsNull then
      cdsArtFonts.FieldByName('font_name_alt' ).asString  := qf.FieldByName('font_name_alt').asString;
    if not qf.FieldByName('font_size_alt').IsNull then
      cdsArtFonts.FieldByName('font_size_alt').asInteger  := qf.FieldByName('font_size_alt').asInteger;
    if not qf.FieldByName('font_attr_alt').IsNull then
      cdsArtFonts.FieldByName('font_attr_alt').asInteger  := qf.FieldByName('font_attr_alt').asInteger;
    if not qf.FieldByName('font_color_alt').IsNull then
      cdsArtFonts.FieldByName('font_color_alt').asInteger := qf.FieldByName('font_color_alt').asInteger;
    cdsArtFonts.Post;
    qf.Next;
    Inc(ic);
    if ((ic div 200) * 200) = ic then begin
       MainThreadAlive;
         Application.ProcessMessages;
    end;
  end;
  with cdsArtFonts do begin
    IndexDefs.Clear;
    AddIndex('cfartIndex', 'id_articles', [], '');
    AddIndex('cfaIndex', 'id_articles;id_screen_groups', [], '');
    IndexName :='cfaIndex';
  end;
end;

procedure CacheArticlesSAData ( art_ids : array of int64 );
var qstr    : string;
    qq      : IIBSQL;
    qOptCnt : string;
    ic      : Int64;
    strIds  : string;
begin
  CashArticleSADataSet.DeleteArtByIds( art_ids );

  strIds := '';
  if length(art_ids) > 0 then
    strIds := strIds + ' and '+ GetInExprForIds('a.id', art_ids);;

  ic := 0;
  qOptCnt := ',(case when a.menu=1 then (select count(article_options.id_options) '
    + ' from article_options '
    + ' join option_sales_area on option_sales_area.id_options=article_options.id_options and option_sales_area.is_active=1 '
    + ' join options on options.id=article_options.id_options and options.is_active=1 '
    + ' where id_articles = a.id and option_sales_area.id_sales_area = article_available.ID_SALES_AREA ' ;
  if UntillAppU.IsTPAPI then
    qOptCnt := qOptCnt + ' and coalesce(a.omittpapi,0)=0';
 qOptCnt := qOptCnt + '   and article_options.is_active=1) else 0 end) menu_items_count ';

  qstr        := 'select a.id, ';
  if PosRestaurantSettings.Settings.ExtraSettings.ArtPosition=apBO then begin
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + 'a.rm_sequence sequence, '
    else
      qstr := qstr + 'a.sequence sequence, ';
    qstr := qstr + 'a.id_departament, article_available.id_sales_area, article_available.limited, article_available.id_periods, ';
    qstr := qstr + ' 0 abs_position';
    qstr := qstr + qOptCnt;
    qstr := qstr +  ' from articles a ';
    qstr := qstr +  ' left outer join article_available on article_available.id_articles = a.id and article_available.is_active=1';
    qstr := qstr +  ' where a.is_active=1';
    qstr := qstr + strIds;
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + ' order by a.rm_sequence'
    else
      qstr := qstr + ' order by a.sequence';
  end else begin
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + 'a.rm_sequence sequence, '
    else
      qstr := qstr + 'coalesce(ap.pos + 50000, a.sequence) sequence, ';
    qstr := qstr +  ' a.id_departament, article_available.id_sales_area, article_available.limited, '
      +  ' article_available.id_periods, ap.pos abs_position '
      + qOptCnt
      +  ' from articles a '
      +  ' join article_available on article_available.id_articles = a.id and article_available.is_active=1'
      +  ' left outer join ARTICLE_POSITION_EX ap on ap.id_articles = a.id and '
      + ' ap.id_sales_area=article_available.id_sales_area and ap.id_department=a.id_departament';
    qstr := qstr +  ' where a.is_active=1';
    qstr := qstr + strIds;
    qstr := qstr + ' order by a.sequence';
  end;
  qq := upos.UntillDB.GetPreparedIIbSql(qstr);
  qq.ExecQuery;
  while not qq.eof do begin
     CashArticleSADataSet.Append;
     CashArticleSADataSet.ds.FieldByName('id').asString                 := qq.FieldByName('id').asString;
     CashArticleSADataSet.ds.FieldByName('id_departament').asString     := qq.FieldByName('id_departament').asString;
     CashArticleSADataSet.ds.FieldByName('id_sales_area').asString      := qq.FieldByName('id_sales_area').asString;
     CashArticleSADataSet.ds.FieldByName('sequence').asInteger          := qq.FieldByName('sequence').asInteger;
     CashArticleSADataSet.ds.FieldByName('limited').asInteger           := qq.FieldByName('limited').asInteger;
     CashArticleSADataSet.ds.FieldByName('id_periods').asString         := qq.FieldByName('id_periods').asString;
     CashArticleSADataSet.ds.FieldByName('abs_position').asInteger      := qq.FieldByName('abs_position').asInteger;
     CashArticleSADataSet.ds.FieldByName('menu_items_count').asInteger  := qq.FieldByName('menu_items_count').asInteger;
     CashArticleSADataSet.Post;
     qq.next;
     Inc(ic);
     if ((ic div 1000) * 1000) = ic then begin
       MainThreadAlive;
       Application.ProcessMessages;
     end;

  end;
  MainThreadAlive;
  Application.ProcessMessages;

  qstr := 'select a.id, ';
  if PosRestaurantSettings.Settings.ExtraSettings.ArtPosition=apBO then begin
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + 'department_articles.rmsequence+10000 sequence, '
    else
      qstr := qstr + 'department_articles.sequence+10000 sequence, ';
    qstr := qstr + 'department_articles.id_department id_departament,'+
     'article_available.id_sales_area, article_available.limited, article_available.id_periods, '+
     'a.can_savepoints, a.rent_price_type, a.decrease_savepoints, a.savepoints, 0 abs_position ';
    qstr := qstr + qOptCnt;
    qstr := qstr +  ' from articles a ';
    qstr := qstr +  ' left outer join article_available on article_available.id_articles = a.id and article_available.is_active=1';
    qstr := qstr +  ' join department_articles on department_articles.id_articles = a.id and department_articles.is_active=1';
    qstr := qstr +  ' where a.is_active=1 ';
    qstr := qstr + strIds;
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + ' order by department_articles.rmsequence'
    else
      qstr := qstr + ' order by department_articles.sequence';
  end else begin
    if (RunMode = armOrderman) or (RunMode = armFalcon) then
      qstr := qstr + 'department_articles.rmsequence+10000 sequence, '
    else
      qstr := qstr + 'coalesce(ap.pos + 50000, department_articles.sequence+10000) sequence, ';
    qstr := qstr + 'department_articles.id_department id_departament,'+
     'article_available.id_sales_area, article_available.limited, article_available.id_periods, ap.pos abs_position, '+
     'a.can_savepoints, a.rent_price_type, a.decrease_savepoints, a.savepoints';
    qstr := qstr + qOptCnt;
    qstr := qstr +  ' from articles a ';
    qstr := qstr +  ' join article_available on article_available.id_articles = a.id and article_available.is_active=1';
    qstr := qstr +  ' join department_articles on department_articles.id_articles = a.id and department_articles.is_active=1';
    qstr := qstr +  ' left outer join ARTICLE_POSITION_EX ap on ap.id_articles = a.id and '
      + ' ap.id_sales_area=article_available.id_sales_area and AP.ID_DEPARTMENT = DEPARTMENT_ARTICLES.ID_DEPARTMENT';
    qstr := qstr +  ' where a.is_active=1 ';
    qstr := qstr + strIds;
  end;
  qq := upos.UntillDB.GetPreparedIIbSql(qstr);
  qq.ExecQuery;
  while not qq.eof do begin
     CashArticleSADataSet.Append;
     CashArticleSADataSet.ds.FieldByName('id').asString             := qq.FieldByName('id').asString;
     CashArticleSADataSet.ds.FieldByName('id_departament').asString := qq.FieldByName('id_departament').asString;
     CashArticleSADataSet.ds.FieldByName('id_sales_area').asString  := qq.FieldByName('id_sales_area').asString;
     CashArticleSADataSet.ds.FieldByName('sequence').asInteger      := qq.FieldByName('sequence').asInteger;
     CashArticleSADataSet.ds.FieldByName('limited').asInteger       := qq.FieldByName('limited').asInteger;
     CashArticleSADataSet.ds.FieldByName('id_periods').asString     := qq.FieldByName('id_periods').asString;
     CashArticleSADataSet.ds.FieldByName('abs_position').asInteger  := qq.FieldByName('abs_position').asInteger;
     CashArticleSADataSet.ds.FieldByName('menu_items_count').asInteger  := qq.FieldByName('menu_items_count').asInteger;
     CashArticleSADataSet.Post;
     qq.next;
  end;
  MainThreadAlive;
  Application.ProcessMessages;

  with CashArticleSADataSet.cds do begin
    IndexDefs.Clear;
    AddIndex('aidArtIndex', 'id', [], '');
    AddIndex('aidIndex', 'id;id_sales_area', [], '');
    AddIndex('aiddepIndex', 'id_departament;id_sales_area;sequence', [], '');
    AddIndex('asaIndex', 'id_sales_area', [], '');
    AddIndex('aDepSaArtIndex', 'id_departament;id_sales_area;id', [], '');
    AddIndex('adepIndex', 'id_departament;sequence', [], '');
  end;
end;

procedure CacheArticleBarcodes(aid_art : Int64 = 0);
var strSQL  : string;
    qq      : IIBSQL;
    id      : Int64;
    strBC   : string;
begin
//  profenter('CacheArticleBarcodes');
  if aid_art = 0 then
    CacheArticleBarcodeList.Clear;
  strSQL := 'select id_articles, barcode from article_barcodes where is_active=1';
  if aid_art>0 then
    strSQL := strSQL + ' and id_articles=:id_art';
  strSQL := strSQL + ' order by id_articles ';
  qq := upos.UntillDB.GetPreparedIIBSQL( strSQL );
  if aid_art>0 then
    qq.q.Params[0].AsInt64 := aid_art;
  qq.ExecQuery;
  while not qq.eof do begin
    id := StrToInt64Def(qq.q.Fields[0].asString,0);
    CacheArticleBarcodeList.TryGetValue(id, strBC);
    if trim(strBC) = '' then
      strBC := qq.q.Fields[1].asString
    else
      strBC := strBC + ';' + qq.q.Fields[1].asString;
    CacheArticleBarcodeList.AddOrSetValue(id, strBC);
    qq.next;
  end;
end;

procedure CacheArtBlobs(aid_art : Int64 = 0);
begin
  if not assigned(ArticleBlobs) then
    ArticleBlobs := TArticleBlobs.Create;
  ArticleBlobs.RefreshData(aid_art);
end;

procedure CashArtDatasets( astrScreenGroups : String; aid_arts : Array of Int64 );
var qstr, join_sel : String;
    qq, qqDep : IIBSQL;
    MS    : TMemoryStream;
    ic    : Int64;
    strIds : string;
    i      : Integer;
    cachart : TCachedArticle;
    id_art : Int64;

    procedure UpdateCacheArticle(item :TCachedArticle; qSel : IIBSQL);
    begin
      with item do begin
        ExternalId             := qq.FieldbyName('external_id').asString;
        Number                 := qq.FieldbyName('article_number').asInteger;
        DepartmentId           := StrToInt64Def(qq.FieldbyName('id_own_department').asString,0);
        HqId                   := qq.FieldbyName('hq_id').asString;
        oman_default_setting   := qq.FieldbyName('oman_default_setting').asInteger;
        oman_default_color     := qq.FieldbyName('oman_default_color').asInteger;
        oman_default_font      := qq.FieldbyName('oman_default_font').asInteger;
        oman_default_alt_font  := qq.FieldbyName('oman_default_alt_font').asInteger;
        time_active            := qq.FieldbyName('TIME_ACTIVE').asInteger;
        id_group               := StrToInt64Def(qq.FieldbyName('id_group').asString,0);
        savepoints             := qq.FieldbyName('savepoints').asInteger;
        id_courses             := StrToInt64Def(qq.FieldbyName('id_courses').asString,0);
        id_ksc                 := StrToInt64Def(qq.FieldbyName('id_ksc').asString,0);
        purchase_price         := qq.FieldbyName('purchase_price').asCurrency;
        omittpapi              := qq.FieldbyName('omittpapi').asInteger;
        age                    := qq.FieldbyName('age').asInteger;
        single_free_option     := qq.FieldbyName('single_free_option').asInteger;
        article_type           := qq.FieldbyName('article_type').asInteger;
        daily_numeric_1        := qq.FieldbyName('daily_numeric_1').asCurrency;
        daily_numeric_2        := qq.FieldbyName('daily_numeric_2').asCurrency;
        daily_stock            := qq.FieldbyName('daily_stock').asInteger;
        must_combined          := qq.FieldbyName('must_combined').asInteger;
        warning_level          := qq.FieldbyName('warning_level').asInteger;
        department             := qq.FieldbyName('department').asString;
        hht_default_setting    := qq.FieldbyName('hht_default_setting').asInteger;
        hht_default_color      := qq.FieldbyName('hht_default_color').asInteger;
        hht_default_font       := qq.FieldbyName('hht_default_font').asInteger;
        hht_default_alt_font   := qq.FieldbyName('hht_default_alt_font').asInteger;
        ayce_mins              := qq.FieldbyName('ayce_mins').asInteger;
        SHOW_IN_KITCHEN_SCREEN := qq.FieldbyName('SHOW_IN_KITCHEN_SCREEN').asInteger;
        fastlane               := qq.FieldbyName('fastlane').asInteger;
        rm_text                := qq.FieldbyName('rm_text').asString;
        surface_point          := qq.FieldbyName('surface_point').asInteger;
        empty_article          := qq.FieldbyName('empty_article').asInteger;
        rent_price_type        := qq.FieldbyName('rent_price_type').asInteger;
        can_savepoints         := qq.FieldbyName('can_savepoints').asInteger;
        auto_resetcourse       := qq.FieldbyName('auto_resetcourse').asInteger;
        department_number      := qq.FieldbyName('department_number').asInteger;
        pos_article_type       := qq.FieldbyName('pos_article_type').asInteger;
        decrease_savepoints    := qq.FieldbyName('decrease_savepoints').asInteger;
        isrental               := qq.FieldbyName('isrental').asInteger;
        pc_text                := qq.FieldbyName('pc_text').asString;
        internal_name          := qq.FieldbyName('internal_name').asString;
        ks_single_item         := qq.FieldbyName('ks_single_item').asInteger;
        barcode                := qq.FieldbyName('barcode').asString;
        article_manual         := qq.FieldbyName('article_manual').asInteger;
        consolidate_alias_name := qq.FieldbyName('consolidate_alias_name').asString;
        free_after_pay         := qq.FieldbyName('free_after_pay').asInteger;
        a_info                 := qq.FieldbyName('a_info').asString;
        group_name             := qq.FieldbyName('group_name').asString;
        article_hash           := qq.FieldbyName('article_hash').asInteger;
        ignore_split_combo     := qq.FieldbyName('ignore_split_combo').asInteger;
        ALLOW_ORDER_ITEMS      := qq.FieldbyName('ALLOW_ORDER_ITEMS').asInteger;
        block_transfer         := qq.FieldbyName('block_transfer').asInteger;
        popup_info             := qq.FieldbyName('popup_info').asString;
        id_food_group          := StrToInt64Def(qq.FieldbyName('id_food_group').asString,0);
        allergen               := qq.FieldbyName('allergen').asInteger;
        prep_min               := qq.FieldbyName('prep_min').asInteger;
        category               := qq.FieldbyName('category').asString;
        id_free_option         := StrToInt64Def(qq.FieldbyName('id_free_option').asString,0);
        ID_SIZE_MODIFIER       := StrToInt64Def(qq.FieldbyName('ID_SIZE_MODIFIER').asString,0);
        prep_sec               := qq.FieldbyName('prep_sec').asInteger;
        id_category            := StrToInt64Def(qq.FieldbyName('id_category').asString,0);
        auto_sm                := qq.FieldbyName('auto_sm').asInteger;
        WEIGHT_REQUIRED        := qq.FieldbyName('WEIGHT_REQUIRED').asInteger;
        auto_onhold            := qq.FieldbyName('auto_onhold').asInteger;
        DELAY_SEPARATE_MINS    := qq.FieldbyName('DELAY_SEPARATE_MINS').asInteger;
        KS_WF_TYPE             := qq.FieldbyName('KS_WF_TYPE').asInteger;
        block_discount         := qq.FieldbyName('block_discount').asInteger;
        ask_course             := qq.FieldbyName('ask_course').asInteger;
        promo                  := qq.FieldbyName('promo').asInteger;
        ignore_sp              := qq.FieldbyName('ignore_sp').asInteger;
        default_options        := qq.FieldbyName('default_options').asInteger;
        warn_min               := qq.FieldbyName('warn_min').asInteger;
      end;
    end;

begin
  MS := nil;
  try
    MS := TMemoryStream.Create;
    CashArticleDataSet.DeleteByArtIds( aid_arts );

    strIds := '';
    if length(aid_arts) > 0 then
      strIds := strIds + ' and ' + GetInExprForIds('a.id', aid_arts);;

    CacheArtFonts( astrScreenGroups, aid_arts );

    qstr := BORestaurantCache.Articles.GetSQLText();
    qstr := qstr +  ' where 1 = 1 ';
    join_sel := '';
    if length( aid_arts ) > 0 then
      join_sel := join_sel +  strIds;

    qstr := qstr + join_sel;
    qq := upos.UntillDB.GetPreparedIIbSql(qstr);
    qq.ExecQuery;

    if length(aid_arts)=0 then
      BORestaurantCache.Articles.Reload;

    ic := 0;
    while not qq.eof do begin
      id_art := StrToInt64Def(qq.FieldbyName('id').asString,0);

      CashArticleDataSet.Append;
      CashArticleDataSet.ds.Fields[cache_art_field_id].asString             := IntToStr(id_art);
      CashArticleDataSet.ds.Fields[cache_art_field_id_departament].asString := qq.FieldbyName('id_departament').asString;
      CashArticleDataSet.Post;

      cachart := BORestaurantCache.Articles.Get(id_art, nil);
      if not assigned(cachart) then begin
        cachart          := TCachedArticle.Create;
        with cachart do begin
          Id               := StrToInt64Def(qq.FieldbyName('id').asString,0);
          IsActive         := qq.FieldbyName('is_active').asInteger = 1;
          Name             := qq.FieldbyName('name').asString;
        end;
        BORestaurantCache.Articles.AddNew(cachart.Id, cachart);
      end;
      if cachart.daily_stock = 1 then
        KSDailyStockArtList.Add( id_art );
      UpdateCacheArticle(cachart, qq);
      AssignArtFont(cachart);

      qq.Next;
      Inc(ic);
      if ((ic div 1000) * 1000) = ic then begin
        MainThreadAlive;
        Application.ProcessMessages;
      end;
    end;

    qstr := 'select a.id, department_articles.id_department id_departament from articles a'
     + ' join department_articles on department_articles.id_articles = a.id and department_articles.is_active=1';
    qstr := qstr +  ' where 1 = 1 ';
    qstr := qstr + join_sel;
    qqDep := upos.UntillDB.GetPreparedIIbSql(qstr);
    qqDep.ExecQuery;
    while not qqDep.eof do begin
      CashArticleDataSet.Append;
      CashArticleDataSet.ds.Fields[cache_art_field_id].asString             := qqDep.FieldbyName('id').asString;
      CashArticleDataSet.ds.Fields[cache_art_field_id_departament].asString := qqDep.FieldbyName('id_departament').asString;
      CashArticleDataSet.Post;
      qqDep.Next;
    end;

    MainThreadAlive;
    Application.ProcessMessages;
    with CashArticleDataSet.cds do begin
      IndexDefs.Clear;
      AddIndex('midIndex', 'id', [], '');
      AddIndex('middIndex', 'id; id_departament', [], '');
    end;
  finally
    FreeAndNil(MS);
  end;
  if length(aid_arts)=0 then begin
    CacheArtBlobs;
    CacheArticleBarcodes;
  end else begin
    for i := 0 to Pred(length(aid_arts)) do begin
      CacheArtBlobs( aid_arts[i] );
      CacheArticleBarcodes( aid_arts[i] );
    end;
  end;
  CashArticlePrices( aid_arts );
end;

function GetIdPriceBySA(id_sa : Int64 ) : Int64;
var id_price : Int64;
begin
  result := 0;
  if saPriceData.TryGetValue(id_sa, id_price) then
    result := id_price;
end;

procedure CacheTAKSs;
var q : IIBSQL;
    d : TTAKSData;
begin
  q := upos.UntillDB.GetPreparedIIbSql('select ID_TABLE_AREA, ID_KS_FROM, ID_KS_TO, ID_PERIODS '
    + ' from TABLE_AREA_KS '
    + ' where IS_ACTIVE = 1 and ID_KS_FROM <> ID_KS_TO '
    + ' order by ID_TABLE_AREA');
  q.ExecQuery;
  if q.eof then exit;

  while not q.Eof do begin
    d := TTAKSData.Create;
    with d do begin
      ID_TA      := StrToInt64Def(q.Fields[0].asString,0);
      ID_KS_FROM := StrToInt64Def(q.Fields[1].asString,0);
      ID_KS_TO   := StrToInt64Def(q.Fields[2].asString,0);
      ID_PERIODS := StrToInt64Def(q.Fields[3].asString,0);
    end;
    CacheTAKS.Add(d);
    q.next;
  end;
end;

function GetTAKSByID(aid : Int64) : TList<TTAKSData>;
var d : TTAKSData;
    bfound : boolean;
begin
  result := TList<TTAKSData>.Create;
  if aid<=0 then exit;

  bfound := false;
  for d in CacheTAKS do begin
    if d.ID_TA = aid then begin
      bfound := true;
      result.Add( d );
    end else begin
      if bfound then
        exit;
    end;
  end;
end;

procedure CacheKSMultiStages;
var q : IIBSQL;
    d : TKSStageData;
begin
  q := upos.UntillDB.GetPreparedIIbSql('select a.ID, A.PREP_TIME_MIN, A.PREP_TIME_SEC, '
    + ' A.WARNING_MIN, C.ID_KITCHEN_SCREENS, C.KS_PURPOSE'
    + ' from ARTICLES_KS_WF_ITEM A'
    + ' join KS_WF_TEMPLATE_ITEM B on B.ID = A.ID_KS_WF_TEMPLATE_ITEM '
    + '   and B.IS_ACTIVE = 1'
    + ' join KS_WF_STAGE_TEMPLATE_ITEM C on C.ID_STAGE_TEMPLATE = B.ID_STAGE_TEMPLATE '
    + '   and C.IS_ACTIVE = 1 '
    + ' where A.IS_ACTIVE = 1 '
    + ' order by a.ID');
  q.ExecQuery;
  if q.eof then exit;

  while not q.Eof do begin
    d := TKSStageData.Create;
    with d do begin
      ID_STAGE      := StrToInt64Def(q.Fields[0].asString,0);
      PREP_TIME_MIN := q.Fields[1].asInteger;
      PREP_TIME_SEC := q.Fields[2].asInteger;
      WARNING_MIN   := q.Fields[3].asInteger;
      ID_KITCHEN_SCREENS := StrToInt64Def(q.Fields[4].asString,0);
      KS_PURPOSE    := q.Fields[5].asInteger;
    end;
    KSMultiStages.Add(d);
    q.next;
  end;
end;

function GetKSMultiStagesByID(aid : Int64) : TList<TKSStageData>;
var d : TKSStageData;
    bfound : boolean;
begin
  result := TList<TKSStageData>.Create;
  if aid<=0 then exit;

  bfound := false;
  for d in KSMultiStages do begin
    if d.ID_STAGE = aid then begin
      bfound := true;
      result.Add( d );
    end else begin
      if bfound then
        exit;
    end;
  end;
end;

procedure CachePromoArticles( aid_art : Int64 = 0 );
var qq, qSel : IIBSQL;
    strSQL : String;
    idx     : Integer;
    bPromoExisted, bPromoFound : boolean;
    ComboPromoItem : TComboPromoItem;
    pkey : string;
    pList : TList<TComboPromoItem>;
    id_cp : Int64;
    ic : Integer;
begin

  bPromoExisted := false;
  if aid_art=0 then
    CashPromoMain.Clear
  else begin
    bPromoExisted := CashPromoMain.TryGetValue(aid_art, idx);
    if bPromoExisted then
      CashPromoMain.Remove(aid_art);
  end;

  strSQL := 'select id, ALLOW_ORDER_ITEMS from articles where is_active=1 and promo=1';
  if aid_art > 0 then
    strSQL := strSQL + ' and articles.id=' + IntToStr(aid_art);
  qSel := upos.UntillDB.GetPreparedIIbSql( strSQL );
  qSel.ExecQuery;
  bPromoFound := false;
  while not qSel.eof do begin
    bPromoFound := true;
    CashPromoMain.AddOrSetValue(StrToInt64Def(qSel.q.fields[0].asString,0), qSel.q.fields[1].asInteger);
    qsel.Next;
  end;

  if (aid_art>0) and (not bPromoFound and not bPromoExisted) then exit;

  if aid_art=0 then
    CashPromoDataSet.cds.EmptyDataSet
  else
    CashPromoDataSet.DeleteArtByID( aid_art );

  strSQL :='select distinct os.id_sales_area, articles.id id_promo, '
      + ' (select count(distinct article_options.id_options) '
      + '     from article_options '
      + '     join options on article_options.id_options = options.id and options.is_active = 1'
      + '     join option_sales_area osa on article_options.id_options = osa.id_options '
      + '       and osa.id_sales_area=os.id_sales_area and osa.is_active=1'
      + '  where article_options.id_articles=articles.id and article_options.is_active=1';
  if UntillAppU.IsTPAPI then
      strSQL := strSQL + ' and coalesce(articles.omittpapi,0)=0';
  strSQL := strSQL + ') option_count '
      + ' from articles '
      + ' join article_options on articles.id=article_options.id_articles and article_options.is_active=1'
      + ' join options on article_options.id_options = options.id and options.is_active = 1'
      + ' join option_sales_area os on options.id = os.id_options and os.is_active=1'
      + ' join sales_area on sales_area.id = os.id_sales_area and sales_area.is_active=1'
      + ' where articles.promo=1 and articles.is_active=1';
  if aid_art > 0 then
    strSQL := strSQL + ' and articles.id=' + IntToStr(aid_art);
  strSQL := strSQL + ' order by articles.id';
  qq := upos.UntillDB.GetPreparedIIBSQL( strSQL );
  qq.ExecQuery;
  while not qq.eof do begin
    CashPromoDataSet.Append;
    CashPromoDataSet.ds.FieldByName('id_sales_area').asString   := qq.FieldByName('id_sales_area').asString;
    CashPromoDataSet.ds.FieldByName('id_promo').asString        := qq.FieldByName('id_promo').asString;
    CashPromoDataSet.ds.FieldByName('option_count').asInteger   := qq.FieldByName('option_count').asInteger;
    CashPromoDataSet.Post;
    qq.Next;
  end;
  MainThreadAlive;

  CashPromoArtList.RefreshData( aid_art );
  CacheArtNotifyList.RefreshData( aid_art );
  CacheArtTANotifyList.RefreshData( aid_art );

  if aid_art=0 then begin
    CashPromoArtDataSet.cds.EmptyDataSet;
    ComboPromoItems.ClearData;
  end else begin
    CashPromoArtDataSet.DeleteArtByID( aid_art );
  end;

  strSQL :='select os.id_sales_area, articles.id id_promo, articles.name promo_name, '
    + ' article_options.id_options, article_options.option_number, '
    + ' option_article.option_number article_option_number, option_article.id_articles id_promo_item, '
    + ' (select count(distinct article_options.id_options) '
    + '     from article_options '
    + '     join option_sales_area osa on article_options.id_options = osa.id_options and osa.id_sales_area=os.id_sales_area and osa.is_active=1'
    + '     where article_options.id_articles=articles.id and article_options.is_active=1';
  if UntillAppU.IsTPAPI then
    strSQL := strSQL + ' and coalesce(articles.omittpapi,0)=0';
  strSQL := strSQL + ' ) option_count, '
    + ' option_article_prices.price, articles.article_number, COURSES.COURSENUMBER '
    + ' from articles '
    + ' join article_options on articles.id=article_options.id_articles and article_options.is_active=1'
    + ' join option_article on option_article.id_options =article_options.id_options and option_article.is_active=1'
    + ' join ARTICLES art on OPTION_ARTICLE.id_articles = art.ID '
    + ' join COURSES on art.ID_COURSES = COURSES.id'
    + ' join options on article_options.id_options = options.id and options.is_active=1'
    + ' join option_sales_area os on options.id = os.id_options and os.is_active=1'
    + ' join sales_area on sales_area.id = os.id_sales_area and sales_area.is_active=1'
    + ' join option_article_prices on option_article_prices.id_option_article =option_article.id and option_article_prices.id_prices=sales_area.id_prices and option_article_prices.is_active=1'
    + ' where articles.promo=1 and articles.is_active=1'
    + '   and coalesce(articles.omittpapi,0)=0';
  if aid_art > 0 then
    strSQL := strSQL + ' and articles.id=' + IntToStr(aid_art);
  strSQL := strSQL + ' order by os.id_sales_area, articles.article_number desc, article_options.option_number  desc, option_article.option_number desc';
  qq := upos.UntillDB.GetPreparedIIBSQL( strSQL );
  qq.ExecQuery;
  with CashPromoDataSet.cds do begin
    IndexDefs.Clear;
    AddIndex('idIndex',  'id_promo', [], '');
    AddIndex('psIndex',  'id_sales_area', [], '');
    AddIndex('pspIndex',  'id_sales_area; id_promo', [], '');
  end;

  ic := 0;
  while not qq.eof do begin
    id_cp := StrToInt64Def(qq.FieldByName('id_promo_item').asString,0);
    CashPromoArtDataSet.Append;
    CashPromoArtDataSet.ds.FieldByName('id_sales_area').asString := qq.FieldByName('id_sales_area').asString;
    CashPromoArtDataSet.ds.FieldByName('id_promo').asString := qq.FieldByName('id_promo').asString;
    CashPromoArtDataSet.ds.FieldByName('promo_name').asString := qq.FieldByName('promo_name').asString;
    CashPromoArtDataSet.ds.FieldByName('id_options').asString := qq.FieldByName('id_options').asString;
    CashPromoArtDataSet.ds.FieldByName('option_number').asString := qq.FieldByName('option_number').asString;
    CashPromoArtDataSet.ds.FieldByName('article_option_number').asString := qq.FieldByName('article_option_number').asString;
    CashPromoArtDataSet.ds.FieldByName('id_promo_item').asString := qq.FieldByName('id_promo_item').asString;
    CashPromoArtDataSet.ds.FieldByName('option_count').asInteger := qq.FieldByName('option_count').asInteger;
    CashPromoArtDataSet.ds.FieldByName('price').asCurrency := qq.FieldByName('price').asCurrency;
    CashPromoArtDataSet.ds.FieldByName('article_number').asString := qq.FieldByName('article_number').asString;
    CashPromoArtDataSet.ds.FieldByName('course_number').asInteger := qq.FieldByName('COURSENUMBER').asInteger;

    if aid_art > 0 then begin
      CashPromoArtList.RefreshData(id_cp);
      CacheArtNotifyList.RefreshData( id_cp );
      CacheArtTANotifyList.RefreshData( id_cp );
    end;

    CashPromoArtDataSet.Post;
    qq.Next;
    inc(ic);
    if ((ic div 1000) * 1000) = ic then
      MainThreadAlive;
  end;

  with CashPromoArtDataSet.cds do begin
    IndexDefs.Clear;
    AddIndex('idIndex', 'id_promo', [ixCaseInsensitive]);
    AddIndex('pArtIndex', 'id_promo_item', [ixCaseInsensitive]);
    AddIndex('psaIndex', 'id_sales_area; article_number; option_number; article_option_number', [ixCaseInsensitive]);
    AddIndex('pIndex',    'id_sales_area; id_promo; option_number; article_option_number', [], '');
    AddIndex('pspIndex',  'id_sales_area; id_promo', [], '');
    AddIndex('psonIndex', 'id_sales_area; id_promo; option_number; id_promo_item', [], '');
  end;
  if aid_art=0 then begin
    CashPromoArtDataSet.First;
    while not CashPromoArtDataSet.eof do begin
      ComboPromoItem := TComboPromoItem.Create;
      ComboPromoItem.fid_promo_item := StrToInt64Def(CashPromoArtDataSet.cds.FieldByName('id_promo_item').asString,0);
      ComboPromoItem.foption_number := CashPromoArtDataSet.cds.FieldByName('option_number').asInteger;
      ComboPromoItem.Fprice := CashPromoArtDataSet.cds.FieldByName('price').asCurrency;
      ComboPromoItem.fcourse_number:= CashPromoArtDataSet.cds.FieldByName('course_number').asInteger;
      pkey := CashPromoArtDataSet.cds.FieldByName('id_sales_area').asString + ';'
        + CashPromoArtDataSet.cds.FieldByName('id_promo').asString + ';'
        + CashPromoArtDataSet.cds.FieldByName('id_promo_item').asString;
      if not ComboPromoItems.FList.TryGetValue(pkey, pList) then begin
        pList := TList<TComboPromoItem>.Create;
        ComboPromoItems.FList.Add(pkey, pList);
      end;
      if assigned(pList) then
        pList.Add(ComboPromoItem);
      CashPromoArtDataSet.Next;
    end;
  end;
end;

procedure CacheArtPeriods;
var qq : IIBSQL;
begin
  qq := upos.UntillDB.GetPreparedIIBSQL('select distinct articles_periods.id_periods, periods.name '
    + ' from articles_periods '
    + ' join periods on periods.id=articles_periods.id_periods and periods.is_active=1 '
    + ' where articles_periods.is_active=1');
  qq.ExecQuery;
  CashArtPerDataSet.EmptyDataSet;
  while not qq.eof do  begin
     CashArtPerDataSet.Append;
     CashArtPerDataSet.FieldByName('id_periods').asString :=  qq.FieldByName('id_periods').asString;
     CashArtPerDataSet.FieldByName('name').asString :=  qq.FieldByName('name').asString;
     CashArtPerDataSet.Post;
     qq.Next;
  end;
  with CashArtPerDataSet do begin
    IndexDefs.Clear;
    AddIndex('apIndex', 'id_periods', [ixCaseInsensitive], '');
  end;
  MainThreadAlive;
end;

procedure CacheSalesAreaExc;
var qq : IIBSQL;
begin
  qq := upos.UntillDB.GetPreparedIIBSQL('select sales_area_exceptions.* '
    + ' from sales_area_exceptions '
    + ' join sales_area on sales_area.id = sales_area_exceptions.id_sales_area and sales_area.is_active=1 '
    + ' join prices on (prices.id = sales_area_exceptions.id_prices and prices.is_active=1) '
    + ' join periods on (periods.id = sales_area_exceptions.id_periods and periods.is_active=1) '
    + ' where sales_area_exceptions.is_active=1');
  qq.ExecQuery;
  CashSAExclDataSet.EmptyDataSet;
  while not qq.eof do  begin
     CashSAExclDataSet.Append;
     CashSAExclDataSet.FieldByName('id_sa').asString        :=  qq.FieldByName('ID_SALES_AREA').asString;
     CashSAExclDataSet.FieldByName('ID_PRICES').asString    :=  qq.FieldByName('ID_PRICES').asString;
     CashSAExclDataSet.FieldByName('ID_PERIODS').asString   :=  qq.FieldByName('ID_PERIODS').asString;
     CashSAExclDataSet.Post;
     qq.Next;
  end;
  with CashSAExclDataSet do begin
    IndexDefs.Clear;
    AddIndex('saeIndex', 'id_sa', [], '');
  end;
  MainThreadAlive;
end;

procedure DoCashBGDataset( aid_art : Int64 = 0 );
var qq     : IIBSQL;
    strSQL : string;
begin
  if aid_art=0 then
    CashBGDataSet.EmptyDataSet
  else
    CashBGDataSet.DeleteByArtId( aid_art );

  strSQL := 'select * from ARTICLE_BONUS_GROUPS '
    + ' join BONUS_GROUPS on BONUS_GROUPS.id=ARTICLE_BONUS_GROUPS.id_bonus_groups and BONUS_GROUPS.is_active=1 '
    + ' where ARTICLE_BONUS_GROUPS.is_active=1';
  if aid_art > 0 then
    strSQL := strSQL + ' and ARTICLE_BONUS_GROUPS.ID_ARTICLES=:id_art';
  qq:= upos.UntillDB.GetPreparedIIbSql( strSQL );
  if aid_art > 0 then
    qq.q.ParamByName('id_art').asInt64 := aid_art;
  qq.ExecQuery;
  while not qq.eof do  begin
     CashBGDataSet.Append;
     CashBGDataSet.FieldByName('id').asString              :=  qq.FieldByName('id').asString;
     CashBGDataSet.FieldByName('ID_BONUS_GROUPS').asString :=  qq.FieldByName('ID_BONUS_GROUPS').asString;
     CashBGDataSet.FieldByName('ID_ARTICLES').asString     :=  qq.FieldByName('ID_ARTICLES').asString;
     CashBGDataSet.FieldByName('MAX_QUANTITY').asInteger   :=  qq.FieldByName('MAX_QUANTITY').asInteger;
     CashBGDataSet.Post;
     qq.Next;
  end;
  with CashBGDataSet do begin
    IndexDefs.Clear;
    AddIndex('bgIDArtIndex', 'id_articles', [], '');
    AddIndex('bgIDIndex', 'id_bonus_groups;id_articles', [], '');
  end;
  MainThreadAlive;
end;

function GetCacheMaxBGQauntity(aid_bonus_groups, aid_articles : Int64) : Integer;
begin
  result := 0;
  if (aid_articles=0) or (aid_bonus_groups=0) then exit;

  CashBGDataSet.IndexName := 'bgIDIndex';
  if CashBGDataSet.FindKey([aid_bonus_groups, aid_articles]) then
    result := CashBGDataSet.FieldByName('MAX_QUANTITY').asInteger;
end;

function GetOptionParentNumber(aID_OPTION_ARTICLE : Int64) : Integer;
var oldIndex : String;
begin
  result   := 0;
  if (aID_OPTION_ARTICLE = 0) then exit;

  oldIndex := '';
  if CashOptionArticleDataSet.IndexName<>'idIdx' then begin
    oldIndex := CashOptionArticleDataSet.IndexName;
    CashOptionArticleDataSet.IndexName := 'idIdx';
  end;
  if CashOptionArticleDataSet.FindKey([aID_OPTION_ARTICLE]) then
    result := CashOptionArticleDataSet.FieldByName('ARTICLE_NUMBER').AsInteger;
  if oldIndex<>'' then
    CashOptionArticleDataSet.IndexName := oldIndex;
end;

function GetArticleKSWFType(aid_articles : Int64) : TKSWFType;
begin
  result := TKSWFType(CacheArticleViewer.GetCachedArticle(aid_articles).KS_WF_TYPE);
end;

function GetKSWFMaxStageNumber(aid_stage : Int64) : Integer;
var qSelStage   : IIBSQL;
    qSelNum     : IIBSQL;
    id_template : Int64;
begin
  result := 0;
  if aid_stage <= 0 then exit;

  qSelStage   := upos.UntillDB.GetPreparedIIbSql('select ID_KS_WF_TEMPLATE from KS_WF_TEMPLATE_ITEM '
    + ' join ARTICLES_KS_WF_ITEM on ARTICLES_KS_WF_ITEM.id_KS_WF_TEMPLATE_ITEM = KS_WF_TEMPLATE_ITEM.id and ARTICLES_KS_WF_ITEM.is_active=1 '
    + ' where ARTICLES_KS_WF_ITEM.id=:id and KS_WF_TEMPLATE_ITEM.is_active=1');
  qSelStage.q.ParamByName('id').asInt64 := aid_stage;
  qSelStage.ExecQuery;
  if qSelStage.eof then exit;

  id_template := StrToInt64Def(qSelStage.q.FieldByName('ID_KS_WF_TEMPLATE').AsString,0);
  if id_template <= 0 then exit;

  qSelNum   := upos.UntillDB.GetPreparedIIbSql('select Max(item_number) from KS_WF_TEMPLATE_ITEM '
    + ' where ID_KS_WF_TEMPLATE=:id and KS_WF_TEMPLATE_ITEM.is_active=1');
  qSelNum.q.ParamByName('id').asInt64 := id_template;
  qSelNum.ExecQuery;
  if qSelNum.eof then exit;
  result := qSelNum.fields[0].asInteger;

end;

function GetKSWFStageNumber(aid_stage : Int64) : Integer;
var qSel : IIBSQL;
begin
  result := 0;
  if aid_stage <= 0 then exit;
  qSel   := upos.UntillDB.GetPreparedIIbSql('select ITEM_NUMBER from KS_WF_TEMPLATE_ITEM '
    + ' join ARTICLES_KS_WF_ITEM on ARTICLES_KS_WF_ITEM.id_KS_WF_TEMPLATE_ITEM = KS_WF_TEMPLATE_ITEM.id and ARTICLES_KS_WF_ITEM.is_active=1 '
    + ' where ARTICLES_KS_WF_ITEM.id=:id and KS_WF_TEMPLATE_ITEM.is_active=1');
  qSel.q.ParamByName('id').asInt64 := aid_stage;
  qSel.ExecQuery;
  if qSel.eof then exit;
  result := qSel.q.FieldByName('ITEM_NUMBER').AsInteger;
end;

procedure CacheDiscountReasons;
var qq : IIBSQL;
    ms : TmemoryStream;
begin
  ms := TmemoryStream.Create;
  try
    qq := upos.UntillDB.GetPreparedIIBSQL('select id, name, name_data, IGNORE_STOCK, id_bookkp, r_type '
      + ' from discount_reasons '
      + ' where is_active=1');
    qq.ExecQuery;
    CashReasonDataSet.EmptyDataSet;
    Inc(CashReasonDataSet.fver);
    while not qq.eof do  begin
       CashReasonDataSet.Append;
       CashReasonDataSet.FieldByName('id').asString            :=  qq.FieldByName('id').asString;
       CashReasonDataSet.FieldByName('name').asString          :=  qq.FieldByName('name').asString;
       CashReasonDataSet.FieldByName('id_bookkp').AsLargeInt   :=  qq.FieldByName('id_bookkp').asInt64;
       CashReasonDataSet.FieldByName('IGNORE_STOCK').asInteger :=  qq.FieldByName('IGNORE_STOCK').asInteger;
       CashReasonDataSet.FieldByName('r_type').asInteger       :=  qq.FieldByName('r_type').asInteger;

       if not qq.FieldByName('name_data').IsNull then begin
         ms.Clear;
         qq.FieldByName('name_data').SaveToStream(ms);
         ms.Seek(0,0);
         TBlobField(CashReasonDataSet.FieldByName('name_data')).LoadFromStream(ms);
       end;

       CashReasonDataSet.Post;
       qq.Next;
    end;
    with CashReasonDataSet do begin
      IndexDefs.Clear;
      AddIndex('rIndex', 'id', [], '');
    end;
  finally
    FreeAndNil(ms);
  end;
end;

procedure CachePuaGroups;
var qq : IIBSQL;
begin
  CashPUAArticleDataSet.EmptyDataSet;
  qq := upos.UntillDB.GetPreparedIIBSQL('select distinct pua_groups_articles.*, '
    + ' pua_groups_sales_area.id_sales_area '
    + ' from pua_groups_articles '
    + ' join pua_groups_sales_area on pua_groups_sales_area.id_pua_groups = pua_groups_articles.id_pua_groups '
    + '   and pua_groups_sales_area.is_active = 1'
    + ' join articles on articles.id = pua_groups_articles.ID_ARTICLES and articles.is_active=1'
    + ' where pua_groups_articles.is_active=1 '
    + ' order by pua_groups_articles.id_pua_groups, pua_groups_sales_area.id_sales_area');
  qq.ExecQuery;
  with CashPUAArticleDataSet do
    while not qq.eof do begin
       Append;
       FieldByName('id_pua_groups').asString :=  qq.FieldByName('id_pua_groups').asString;
       FieldByName('id_articles').asString   :=  qq.FieldByName('id_articles').asString;
       FieldByName('id_sales_area').asString :=  qq.FieldByName('id_sales_area').asString;
       FieldByName('pua_number').asInteger   :=  qq.FieldByName('pua_number').asInteger;
       Post;
       qq.Next;
  end;
  with CashPUAArticleDataSet do begin
    Indexdefs.Clear;
    AddIndex('pgidIndex', 'id_pua_groups;id_sales_area;pua_number', [], '');
  end;
  MainThreadAlive;
end;

procedure CacheSpecWords;
var qq : IIBSQL;
begin
  CashSpecDataSet.EmptyDataSet;
  qq := upos.UntillDB.GetPreparedIIBSQL('select distinct * from special_words where is_active=1');
  qq.ExecQuery;
  while not qq.eof do  begin
     CashSpecDataSet.Append;
     CashSpecDataSet.FieldByName('id').asString       :=  qq.FieldByName('id').asString;
     CashSpecDataSet.FieldByName('key').asString      :=  qq.FieldByName('keyname').asString;
     CashSpecDataSet.FieldByName('name').asString     :=  qq.FieldByName('name').asString;
     CashSpecDataSet.FieldByName('internal').asString :=  qq.FieldByName('internal').asString;
     CashSpecDataSet.FieldByName('number').asInteger  :=  qq.FieldByName('number').asInteger;
     CashSpecDataSet.FieldByName('PC_TEXT').asString  :=  qq.FieldByName('PC_TEXT').asString;
     CashSpecDataSet.FieldByName('RM_TEXT').asString  :=  qq.FieldByName('RM_TEXT').asString;
     CashSpecDataSet.Post;
     qq.Next;
  end;
  with CashSpecDataSet do begin
    IndexDefs.Clear;
    AddIndex('kIndex', 'key', [ixCaseInsensitive], '');
  end;
  with CashSpecDataSet do begin
    IndexDefs.Clear;
    AddIndex('kNumber', 'number', [ixCaseInsensitive], '');
  end;
  with CashSpecDataSet do begin
    IndexDefs.Clear;
    AddIndex('kwID', 'id', [], '');
  end;
  MainThreadAlive;
end;

function GetArticlesKSTime( aid_articles : Int64 ) : TKSPreparationTime;
var item : TCachedArticle;
begin
  result.Clear;
  if aid_articles <= 0 then exit;

  item := CacheArticleViewer.GetCachedArticle( aid_articles );
  if item.Id > 0 then begin
    with result do begin
      prep_min := item.prep_min;
      prep_sec := item.prep_sec;
      warn_min := item.warn_min;
    end;
  end;
end;


function ArticleSingleNotifiedInKS(aid_articles : Int64; aksPurposeList : TKSPurposes) : boolean;
var id_ks   : Int64;
    purpose : Integer;
    prepTime : TKSPreparationTime;
begin
  result := false;
  if (aid_articles = 0) then exit;
  if not assigned(aksPurposeList) then exit;

  aksPurposeList.Clear;
  CashKSARTDataset.IndexName := 'ksartIdIndex';
  if CashKSARTDataset.FindKey([aid_articles]) then begin
    result := true;
    while not CashKSARTDataset.Eof
      and ( aid_articles = StrToInt64Def(CashKSARTDataset.FieldByName('id_articles').asString,0) ) do begin
      id_ks    := StrToInt64Def(CashKSARTDataset.FieldByName('id_ks').asString,0);
      purpose  := CashKSARTDataset.FieldByName('KS_PURPOSE').asInteger;
      prepTime := GetArticlesKSTime(aid_articles);

      aksPurposeList.Add(TKSPurpose.Create(id_ks, purpose, prepTime ));
      CashKSARTDataset.Next;

    end;
  end;
end;

function GetFirstWFArticleStage(aid_articles : Int64) : Int64;
var art_kswfType : TKSWFType;
    qSel         : IIBSQL;
begin

  result := 0;
  if aid_articles = 0 then exit;
  art_kswfType := RestaurantCashDatasetsU.GetArticleKSWFType(aid_articles);
  if art_kswfType = kswfSingle then exit;

  qSel := upos.UntillDB.GetPreparedIIbSql('select first 1 ARTICLES_KS_WF_ITEM.id '
    + ' from ARTICLES_KS_WF_ITEM '
    + ' join KS_WF_TEMPLATE_ITEM on KS_WF_TEMPLATE_ITEM.id=ARTICLES_KS_WF_ITEM.ID_KS_WF_TEMPLATE_ITEM and KS_WF_TEMPLATE_ITEM.is_active=1 '
    + ' where ARTICLES_KS_WF_ITEM.id_articles=:id_articles and ARTICLES_KS_WF_ITEM.is_active=1'
    + ' order by KS_WF_TEMPLATE_ITEM.ITEM_NUMBER'
  );
  qSel.q.ParamByName('id_articles').AsInt64 := aid_articles;
  qSel.ExecQuery;
  if qSel.eof then exit; // no stages found
  result := StrToInt64Def(qSel.q.Fields[0].AsString,0);

end;
function GetArticlePrepSec( aid_articles : Int64 ) : Integer;
var qSel               : IIBSQL;
    id_stage           : Int64;
    art_kswfType       : TKSWFType;
begin

  art_kswfType := GetArticleKSWFType(aid_articles);

  if art_kswfType = kswfSingle then
    result := CacheArticleViewer.GetCachedArticle(aid_articles).prep_min * 60
       + CacheArticleViewer.GetCachedArticle(aid_articles).prep_sec
  else begin
    id_stage := GetFirstWFArticleStage( aid_articles );
    qSel := upos.UntillDB.GetPreparedIIbSql('select first 1 PREP_TIME_MIN, PREP_TIME_SEC'
      + ' from ARTICLES_KS_WF_ITEM where is_active=1 and id=:id_stage'
    );
    qSel.q.ParamByName('id_stage').AsInt64 := id_stage;
    qSel.ExecQuery;
    result := qSel.q.FieldByName('PREP_TIME_MIN').asInteger * 60
      + qSel.q.FieldByName('PREP_TIME_SEC').asInteger;
  end;
end;

function ArticleMultiNotifiedInKS(aid_stage : Int64; aksPurposeList : TKSPurposes) : boolean;
var id_ks   : Int64;
    purpose : Integer;
    prep_time : TKSPreparationTime;
    listks : TList<TKSStageData>;
    d : TKSStageData;
begin
  result := false;
  if aid_stage <= 0  then exit;

  if not assigned(aksPurposeList) then exit;

  aksPurposeList.Clear;
  listks := nil;
  try
    listks := GetKSMultiStagesByID(aid_stage);
    result := listks.Count > 0;
    for d in listks do begin
      id_ks    := d.ID_KITCHEN_SCREENS;
      purpose  := d.KS_PURPOSE;
      with prep_time do begin
        prep_min := d.PREP_TIME_MIN;
        prep_sec := d.PREP_TIME_SEC;
        warn_min := d.WARNING_MIN;
      end;
      aksPurposeList.Add(TKSPurpose.Create(id_ks, purpose, prep_time ));
    end;
  finally
    FreeAndNil( listks );
  end;
end;

procedure DoCashKSART( aid_art : Int64 = 0 );
var q      : IIBSQL;
    sqlStr : String;
begin
  if aid_art = 0 then
    CashKSARTDataset.EmptyDataSet
  else
    CashKSARTDataset.DeleteByArtId( aid_art );

  sqlStr := 'select id_articles, ID_KS, KS_PURPOSE '
    + ' from ARTICLE_KS_NOTIFY '
    + ' where is_active=1 ';
  if aid_art > 0 then
    sqlStr := sqlStr + ' and id_articles=:id_articles';
  q := upos.UntillDB.GetPreparedIIbSql( sqlStr );
  if aid_art > 0 then
    q.q.ParamByName('id_articles').AsInt64 := aid_art;
  q.ExecQuery;

  while not q.eof do begin
    CashKSARTDataset.Append;
    CashKSARTDataset.Fields[0].AsString := q.q.Fields[0].asString;
    CashKSARTDataset.Fields[1].AsString := q.q.Fields[1].asString;
    CashKSARTDataset.Fields[2].AsInteger := q.q.Fields[2].AsInteger;
    CashKSARTDataset.Post;
    q.next;
  end;
  CashKSARTDataset.IndexDefs.Clear;
  with CashKSARTDataset do begin
    IndexDefs.Clear;
    AddIndex('ksartIdIndex', 'id_articles', [], '');
  end;
  MainThreadAlive;
end;

procedure CacheArticlePricePeriodsDatasets( aid_arts : array of Int64);
var iq : IIBSQL;
    ic : Int64;
    sqlStr : string;
    i : Integer;
    strIds : string;
begin
  if length(aid_arts) = 0 then
    CacheArticlePricePeriodsDataset.EmptyDataSet
  else begin
    CacheArticlePricePeriodsDataset.IndexName := 'idArtIndex';
    for i := 0 to Pred(length(aid_arts)) do begin
      while CacheArticlePricePeriodsDataset.FindKey([aid_arts[i]]) do
        CacheArticlePricePeriodsDataset.Delete;
    end;
  end;

  strIds := '';
  if length(aid_arts) > 0 then
    strIds := strIds + ' and ' + GetInExprForIds('articles.id', aid_arts );

  sqlStr := 'select id_articles, id_prices, id_periods, ARTICLE_PRICE_PERIODS.price '
    + ' from ARTICLE_PRICE_PERIODS '
    + ' join ARTICLE_PRICES on ARTICLE_PRICE_PERIODS.ID_ARTICLE_PRICES=ARTICLE_PRICEs.id and ARTICLE_PRICES.is_active=1'
    + ' join ARTICLES on ARTICLE_PRICES.ID_ARTICLES=ARTICLES.id and ARTICLES.is_active=1'
    + ' join periods on ARTICLE_PRICE_PERIODS.ID_periods=periods.id and periods.is_active=1'
    + ' where ARTICLE_PRICE_PERIODS.is_active=1';
  sqlStr := sqlStr + strIds;
  iq := upos.UntillDB.GetPreparedIIBSQL( sqlStr );
  iq.ExecQuery;
  ic := 0;
  while not iq.eof do begin
    CacheArticlePricePeriodsDataset.Append;
    CacheArticlePricePeriodsDataset.FieldByName('id_article').asString :=  iq.FieldByName('id_articles').asString;
    CacheArticlePricePeriodsDataset.FieldByName('id_price').asString   :=  iq.FieldByName('id_prices').asString;
    CacheArticlePricePeriodsDataset.FieldByName('id_period').asString  :=  iq.FieldByName('id_periods').asString;
    CacheArticlePricePeriodsDataset.FieldByName('price').asCurrency  :=  iq.FieldByName('price').asCurrency;
    CacheArticlePricePeriodsDataset.Post;
    iq.Next;
    Inc(ic);
    if ((ic div 200) * 200) = ic then
      MainThreadAlive;
  end;
  with CacheArticlePricePeriodsDataset do begin
    IndexDefs.Clear;
    AddIndex('idArtIndex', 'id_article', [], '');
    AddIndex('cappIndex', 'id_article; id_price', [], '');
  end;
end;

procedure CacheCurrencyNotes;
var qq : IIBSQL;
    ms : TMemoryStream;
begin
  ms := TMemoryStream.Create;
  try
    qq := upos.UntillDB.GetPreparedIIBSQL('select  CURRENCY_NOTES_COINS.ID_CURRENCY, notes_coins.price, '
    + ' CURRENCY_NOTES_COINS.picture, currency.name '
    + ' from currency_notes_coins '
    + ' join notes_coins on notes_coins.id=currency_notes_coins.id_notes_coins'
    + ' join currency on currency.id=currency_notes_coins.id_currency '
    + ' where currency_notes_coins.is_active=1 and notes_coins.is_active=1');
    qq.ExecQuery;
    CashCurrencyDataSet.EmptyDataSet;
    while not qq.eof do  begin
       CashCurrencyDataSet.Append;
       CashCurrencyDataSet.FieldByName('id').asString      :=  qq.FieldByName('ID_CURRENCY').asString;
       CashCurrencyDataSet.FieldByName('name').asString    :=  qq.FieldByName('name').asString;
       CashCurrencyDataSet.FieldByName('tenor').asCurrency :=  qq.FieldByName('price').asCurrency;

       if not qq.FieldByName('picture').IsNull then begin
         ms.Clear;
         qq.FieldByName('picture').SaveToStream(ms);
         ms.Seek(0,0);
         TBlobField(CashCurrencyDataSet.FieldByName('picture')).LoadFromStream(ms);
       end;

       CashCurrencyDataSet.Post;
       qq.Next;
    end;
    with CashCurrencyDataSet do begin
      IndexDefs.Clear;
      AddIndex('idIndex', 'id', [], '');
      AddIndex('cidIndex', 'id;tenor', [], '');
    end;
  finally
    FreeAndNil(ms);
  end;
  MainThreadAlive;
end;

procedure CachePreparationAreas;
var qq : IIBSQL;
begin
  qq := upos.UntillDB.GetPreparedIIBSQL('select id, name '
     + ' from PREPARATION_AREA '
     + ' where is_active=1');
  qq.ExecQuery;
  CashPADataSet.EmptyDataSet;
  while not qq.eof do  begin
     CashPADataSet.Append;
     CashPADataSet.FieldByName('id').asString      :=  qq.FieldByName('id').asString;
     CashPADataSet.FieldByName('name').asString    :=  qq.FieldByName('name').asString;
     CashPADataSet.Post;
     qq.Next;
  end;
  with CashPADataSet do begin
    IndexDefs.Clear;
    AddIndex('paIndex', 'id', [], '');
  end;
  MainThreadAlive;
end;

function GetPOSScreenGroup : String;
var iq : IIBSQL;
begin
  if id_screen_group='' then begin
    if RunMode = armOrderman then begin
      iq := upos.UntillDB.GetPreparedIIbSql('select first 1 id_screen_groups ' +
      ' from ORDERMANS where SERIAL= :SERIAL and is_active=1');
      iq.q.Params[0].asInteger := OrdermanForm.SerialNumber;
    end else if RunMode = armFalcon then begin
      iq := upos.UntillDB.GetPreparedIIbSql('select id_screen_groups ' +
      ' from falcon_terminals where upper(ip)= upper(:cip) and is_active=1');
      iq.q.Params[0].asString := Trim(FalconMsgForm.FalconComm.Address);
    end else begin
      iq := upos.UntillDB.GetPreparedIIbSql('select id_screen_groups from computers where upper(name)= upper(:cname) and comp_active=1');
      iq.q.Params[0].asString := Trim(UPos.POSComputerName);
    end;
    iq.ExecQuery;
    if not iq.eof then
      id_screen_group := iq.q.Fields[0].AsString;
  end;

  result := id_screen_group;
  if RunMode=armFalcon then begin
    result := result + ',' + IntToStr(ID_SCREEN_GROUPS_FALCON);
    if (StrToInt64Def(id_screen_group,0) <> ID_SCREEN_GROUPS_PC) then
      result := result + ',' + IntToStr(ID_SCREEN_GROUPS_PC);
  end else if RunMode=armOrderman then begin
    result := result + ',' + IntToStr(ID_SCREEN_GROUPS_ORDERMAN);
    if (StrToInt64Def(id_screen_group,0) <> ID_SCREEN_GROUPS_PC) then
      result := result + ',' + IntToStr(ID_SCREEN_GROUPS_PC);
  end else begin
    if (StrToInt64Def(id_screen_group,0) <> ID_SCREEN_GROUPS_PC) then
      result := result + ',' + IntToStr(ID_SCREEN_GROUPS_PC);
  end;
end;

procedure CashMainDatasetsNoArt;
var strScreenGroups : String;
    arr : Array of Int64;
begin

  {$ifndef UNTILL_RELEASE}
  if GetUntillIniSetting('debug', 'CashMainDatasetsFailureEmulation', '0')='1' then
    raise Exception.Create('Server connection failure emulation');
  {$endif}

  id_screen_group := '';
  if not (UntillApp.GetPosForm=nil) then
    id_screen_group := UntillApp.GetPosForm.PosScreenGroup;

  strScreenGroups := GetPOSScreenGroup;

  setlength(arr, 0);
  CacheDepartments ( strScreenGroups );
  CacheArticlesSAData( arr );
  CacheOptionArticleDatasets;
  CachePuaGroups;
  CacheSpecWords;
  CacheDiscountReasons;

  CashKSDataset.RefreshData;
  CashKSCDataset.RefreshData;
  CacheCurrencyNotes;
  CachePreparationAreas;
  CacheSalesAreaExc;
  CacheCpts.RefreshData;
  CacheOpts.RefreshData;

  CacheArtPeriods;
end;

procedure CacheTableLinksDataset;
var q : IIBSQL;
    tableno : Integer;
    d1, d2  : TDatetime;
begin
//    profenter('CacheTableLinksDataset');
  CacheTableLinks.Clear;
  q := upos.UntillDB.GetPreparedIIbSql('select distinct table_no '
    + ' from tables'
    + ' join reservations '
    + '   on reservations.reservation_id=tables.reservation_id '
    + '   and reservations.res_start between :d1 and :d2'
    + ' union all '
    + ' select distinct tables.table_no '
    + ' from tables '
    + ' where tables.reservation_id is null'
    );
  GetFromTillDateTime(PosKernelSettings.Settings.FromTime, PosKernelSettings.Settings.ToTime, SysDateTimeToLocal(upos.GetPosNow), d1, d2);
  q.q.ParamByName('d1').AsDateTime := d1;
  q.q.ParamByName('d2').AsDateTime := d2;
  q.ExecQuery;

  while not q.eof do begin
    tableno := q.Fields[0].asInteger;
    if CacheTableLinks.IndexOf( tableno ) < 0 then
      CacheTableLinks.Add( tableno );
    q.next;
  end;
end;

function GetOrCreateArticleCache(id_art : Int64) : TCachedArticle;
var arr : Array of int64;
begin
  result := nil;
  if id_art = 0 then exit;
  try
    result := BORestaurantCache.Articles.GetOrFail( id_art );
  except
  end;
  if assigned(result) then exit;

  setlength(arr, 1);
  arr[0] := id_art;
  CashArtDatasets( GetPOSScreenGroup, id_art);
  result := BORestaurantCache.Articles.GetOrFail( id_art );
end;

procedure CashMainDatasets;
var strScreenGroups : String;
    arr : Array of int64;
begin

  {$ifndef UNTILL_RELEASE}
  if GetUntillIniSetting('debug', 'CashMainDatasetsFailureEmulation', '0')='1' then
    raise Exception.Create('Server connection failure emulation');
  {$endif}

  id_screen_group := '';
  if not (UntillApp.GetPosForm=nil) then
    id_screen_group := UntillApp.GetPosForm.PosScreenGroup;

  strScreenGroups := GetPOSScreenGroup;

  setlength(arr, 0);
  CacheClients;
  CacheDepartments ( strScreenGroups );
  CashArtDatasets ( strScreenGroups, arr );
  CacheArticlesSAData( arr );
  CacheOptionArticleDataSets;

  CachePuaGroups;
  CacheSpecWords;
  CacheDiscountReasons;

  CashKSDataset.RefreshData;
  CashKSCDataset.RefreshData;
  CacheCurrencyNotes;
  CachePreparationAreas;

  DoCashKSART;
  DoCashBGDataset;
  CacheSalesAreaExc;

  CacheArtPeriods;
  CachePromoArticles;
  CashDepPOSDatasets;
  CashArticlePOSDatasets;
  CacheTableLinksDataset;
end;

procedure CacheCommonData;
var q : IIBSQL;
begin
  q := upos.UntillDB.GetPreparedIIbSql('select count(*) from BONUS_GROUPS where is_active=1');
  q.ExecQuery;
  CashCommonRestaurantData.BonusGroupsExist := (q.fields[0].asInteger>0);
end;

procedure CashArticlePrices( aid_arts : array of Int64 );
begin
//    profenter('CashArticlePrices');
  CashPriceDatasets(pdtArticle, aid_arts);
  CashPriceDatasets(pdtOption, aid_arts);
  CashPriceDatasets(pdtBonus, aid_arts);
  CashPriceDatasets(pdtMainBonus, aid_arts);
  CacheArticlePricePeriodsDatasets( aid_arts );
end;

procedure RestaurantCashDatasetsPosIniFinit(bInit: boolean);
var arr : Array of Int64;
begin
  if(bInit) then begin
    {$ifndef REPORTGEN}
    InitMainDatasets;

    id_default_price := -1;
    setlength(arr,0);
    CacheTableAreas := TCacheTableAreas.Create(nil);
    CacheTableAreas.RefreshData;

    CacheCourses      := TCacheCourses.Create(nil);
    CacheCourses.RefreshData;
    CacheKSWarnList  := TCacheKSWarnList.Create;
    CacheOrderTypes   := TCacheOrderTypes.Create;
    CacheAllergenPics := TCacheAllergenPics.Create;
    CacheSizeModifiers:= TCacheSizeModifiers.Create;
    saPriceData           := TDictionary<Int64, int64>.Create;
    ksMultiStages         := TObjectList<TKSStageData>.Create;
    CacheTAKS             := TObjectList<TTAKSData>.Create;
    CacheCpts             := TCacheCPTs.Create;
    CacheClientExcepts    := TCacheClientExcepts.Create;
    CacheOpts             := TCacheOPTs.Create;
    KSCacheOverviewList   := TList<Int64>.Create;
    KSDailyStockArtList   := TList<Int64>.Create;
    ComboPromoItems       := TComboPromoItems.Create;
    ArtPriceList          := TArtPriceList.Create;
    CacheKSSubsts         := TCacheKSSubsts.Create;
    CacheWaiters          := TCacheWaiters.Create;
    CacheAllergens        := TCacheAllergens.Create;
    CacheArtAllergens     := TCacheArtAllergens.Create;
    CacheArtTableAreas    := TCacheArtTableAreas.Create;
    CacheSalesAreasRange  := TCacheSalesAreas.Create;
    CacheArticleViewer    := TCacheArticleViewer.Create;
    CacheOptionsDataset   := TCacheOptionsDataset.Create;
    CachePricesDataset    := TCachePricesDataset.Create;

    CachePricesDataset.RefreshData;
    ArtPriceList.RefreshData;
    CacheKSSubsts.RefreshData;
    CacheAllergenPics.RefreshData;
    CacheOrderTypes.RefreshData;
    CacheSizeModifiers.RefreshData;
    CacheSalesAreasRange.RefreshData;
    CacheCpts.RefreshData;
    CacheClientExcepts.RefreshData;
    CacheOpts.RefreshData;
    CacheWaiters.RefreshData;
    CacheArtAllergens.RefreshData(arr);
    CacheArtTableAreas.RefreshData;
    CacheAllergens.RefreshData;
    CacheKSWarnList.RefreshData;
    CacheOptionsDataset.RefreshData;
    CacheKSMultiStages;
    CacheTAKSs;

//    https://dev.untill.com/projects/#!468335 InitArtPositions;
    CacheCommonData;
    CashMainDatasets;

    CashCatDataSets;
    CashFGDatasets;
    CashSupplierDatasets;

    CacheArticleProvider  := TCachePOSEntityProvider.Create( nil );

    {$endif}
  end
  else
  begin
    {$ifndef REPORTGEN}
    FreeAndNil(ArticleBlobs);
    FreeAndNil(CacheTableAreas);
    FreeAndNil(CacheCourses);
    CacheOrderTypes.Clear;
    FreeAndNil(CacheOrderTypes);
    CacheAllergenPics.Clear;
    FreeAndNil(CacheAllergenPics);
    FreeAndNil(CacheSizeModifiers);
    FreeAndNil(saPriceData);
    FreeAndNil(ksMultiStages);
    FreeAndNil(CacheTAKS);
    FreeAndNil(CacheCPTs);

    CacheClientExcepts.Clear;
    FreeAndNil(CacheClientExcepts);
    FreeAndNil(CacheOPTs);
    FreeAndNil(CacheKSWarnList);
    FreeAndNil(KSCacheOverviewList);
    FreeAndNil(KSDailyStockArtList);
    FreeAndNil(CacheWaiters);
    FreeAndNil(CacheAllergens);
    FreeAndNil(CacheArtAllergens);
    FreeAndNil(CacheArtTableAreas);
    FreeAndNil(CacheSalesAreasRange);
    FreeAndNil(CashArticleSADataSet);

    FreeAndNil(cdsArtFonts);
    FreeAndNil(cdsDepFonts);
    FreeAndNil(CashDepartmentDataSet);
    FreeAndNil(CashArticleDataSet);
    FreeAndNil(SuppliersDataSet);
    FreeAndNil(CashPromoArtDataSet);
    FreeAndNil(CashPromoMain);
    FreeAndNil(CacheTableLinks);
    FreeAndNil(CashPromoArtList);
    FreeAndNil(CacheArtNotifyList);
    FreeAndNil(CacheArtTANotifyList);
    FreeAndNil(CacheArticleBarcodeList);
    FreeAndNil(CashPromoDataSet);
    FreeAndNil(CashArticlePriceDataSet);
    FreeAndNil(CashArticleOptionPriceDataSet);
    FreeAndNil(CashArticleBonusPriceDataSet);
    FreeAndNil(CashArticleMainBonusPriceDataSet);
    FreeAndNil(CashOptionArticleDataSet);
    FreeAndNil(CashArticleOptionDataSet);
    FreeAndNil(CashSpecDataSet);
    FreeAndNil(CachePricesDataSet);
    FreeAndNil(CashPUAArticleDataSet);
    FreeAndNil(CacheOptionsDataset);
    FreeAndNil(CashArtPerDataset);
    FreeAndNil(CashReasonDataSet);
    FreeAndNil(CashCurrencyDataSet);
    FreeAndNil(CashPADataSet);
    FreeAndNil(CashKSDataSet);
    FreeAndNil(CashKSCDataSet);
    FreeAndNil(KSPUItemsProvider);
    FreeAndNil(CashKSPickupPOSDataset);
    FreeAndNil(CashArticlePOSDataset);
    FreeAndNil(CashDepPOSDataset);
    FreeAndNil(CashSAExclDataSet);
    FreeAndNil(CashKSARTDataSet);
    FreeAndNil(CashBGDataset);
    FreeAndNil(CashFGDataset);
    FreeAndNil(CashCatDataset);
    FreeAndNil(CacheArticlePricePeriodsDataset);
    FreeAndNil(ComboPromoItems);
    FreeAndNil(ArtPriceList);
    CacheKSSubsts.Clear;
    FreeAndNil(CacheKSSubsts);
    FreeAndNil(CacheArticleViewer);
    FreeAndNil(CacheArticleProvider);
    {$endif}
  end;
end;

function GetKSCIDByName(aname : String) : Int64;
var kscd : TKSCData;
    id   : Int64;
begin
  Result := 0;
  kscd.Clear;

  for id in CashKSCDataset.list.Keys do begin
    if CashKSCDataset.list.TryGetValue(id, kscd) then begin
      if SameText(aname, kscd.KSC_NAME) then begin
        result := id;
        exit;
      end;
    end;
  end;
end;

// Returns Sales area interfaced object for given Table, Date, PCname
function GetCacheSalesAreaID( ATableNo : Integer ) : Int64;
var  iRange : Integer;
    curArea  : TCacheSalesArea;
    curRange : TTableRange;
    bInTableRange : boolean;
    i   : Integer;
begin
  for I := 0 to 1 do begin
    // If no table areas found - empty reference must be returned
    Result := 0;
    // Check if CacheSalesAreasRange exists already
    if not assigned(CacheSalesAreasRange) then exit;
    if CacheSalesAreasRange.Count = 0 then exit;
    // Go to through all cached table areas

    for curArea in CacheSalesAreasRange.AreaList.Values do begin
      if assigned(curArea) then begin
        if curArea.TableRanges.count > 0 then begin
          // Go to Table ranges of current Table area to see if given Table belongs to Table area
          for iRange := 0 to Pred( curArea.TableRanges.Count ) do begin
            curRange := curArea.TableRanges.items[ iRange ];
            bInTableRange := (ATableNo >= curRange.FromTable) and (ATableNo <= curRange.ToTable);
            if bInTableRange then begin
              Result := curArea.id;
              exit;
            end;
          end;
        end;
      end;
    end;
    if result = 0 then begin
      if i = 0 then
        CacheSalesAreasRange.RefreshData
      else
        Plugin.RaisePosException('Sales area for table number %d not defined',[ATableNo],'BlrMain.GetSalesAreaID');
    end;
  end;
end;

function GetPreparationAreaName( aid : Int64 ) : String;
begin
  Result := '';
  if aid <= 0 then exit;

  CashPADataSet.IndexName := 'paIndex';
  if CashPADataSet.FindKey([ aid ]) then
    Result := CashPADataSet.FieldByName('name').asString;
end;

function CashedKSBillValid(aid_bill : Int64 ) : boolean;
var oldIdx : string;
begin
  result := false;
  if (aid_bill = 0) then exit;

  oldIdx := CashKSPickupPOSDataset.IndexName;
  CashKSPickupPOSDataset.IndexName :='kspu_idx';
  result := CashKSPickupPOSDataset.FindKey([abs(aid_bill)]);
  if CashKSPickupPOSDataset.IndexName <> oldIdx then
    CashKSPickupPOSDataset.IndexName := oldIdx;
end;

procedure CashKSPickupPOSDatasets(id_ksc : Int64; max_rec : Integer; Status : TKSWorkflowStatus);

type TPUPOSData = record
  maxdt   : TDatetime;
  mindt   : TDatetime;
  minst   : Integer;
  id_bill : Int64;
end;

var sqlstr : String;
    q : IIBSQL;
    bSplitCourses : boolean;
    ksc_type      : TKSCType;
    bills         : TList<TPUPOSData>;
    puData        : TPUPOSData;
    kscd          : TKSCData;
    ksc_show_pending  : boolean;
begin
//    profenter('CashKSPickupPOSDatasets');

  if PosRestaurantSettings.Settings.ExtraSettings.KSType = 0 then exit;

  if not Plugin.IsTesterModeEnabled then begin
    if (KSPURefreshParams.id_ksc = id_ksc)
    and (KSPURefreshParams.max_rec = max_rec)
    and (KSPURefreshParams.Status = Status) then
    if MilliSecondsBetween(Now, KSPURefreshParams.dt) < ARTILCE_CACHE_REFRESH_INTERVAL * 2 then
      exit;
  end;

  try
    sqlstr := ' select ';
    bSplitCourses := true;

    kscd := CashKSCDataset.GetDataByID(id_ksc);
    if id_ksc > 0 then
      bSplitCourses := kscd.KSC_SPLIT_COURSES=1;

    ksc_type := kscRegular;
    if (id_ksc > 0) and (Status in [kswsNotStart, kswsStart, kswsReady, kswsSent, kswsPending]) then
      ksc_type := TKSCType(kscd.KSC_TYPE);

    if max_rec>0 then
      sqlstr := sqlstr + ' first ' + IntToStr(max_rec);
    if ksc_type = kscRegular then
      sqlstr := sqlstr +  ' k.id_bill, max(k.dt_status) max_time, Min(k.DT_STATUS) min_time, '
    else
      sqlstr := sqlstr +  ' k.id_bill, max(k.dt_status) max_time, Min(k.DT_STATUS) min_time, ';
    sqlstr := sqlstr  + ' min(k.wf_status) min_status, '
     + '  min( '
     + '  case '
     + '    when K.WF_STATUS = 5 then 1 '
     + '    when K.WF_STATUS = 2 then 1 '
     + '    else K.WF_STATUS '
     + '  end) MIN_STATUS_SYNT '
     + ' from ks_workflow k ';
//    if ksc_type = kscExtended then
//       sqlstr := sqlstr +  ' join ks_wf_steps on ks_wf_steps.id_ks_workflow=k.id';
    sqlstr := sqlstr +  ' where (select sum(quantity) from KS_WORKFLOW_items kwi where k.id=kwi.id_ks_workflow) > 0'
     + ' and (dt_status between DATEADD(-' + IntToStr(PUDATA_HOUR_DEEP) + ' hour to :dt) and :dt)';
    ksc_show_pending  := false;
    if ksc_type = kscRegular then
      sqlstr := sqlstr + ' and k.wf_status=:stSent '
    else begin
      if (id_ksc > 0) then
        ksc_show_pending := CashKSCDataset.GetKSCShowPending(id_ksc);
      if ksc_show_pending then
        sqlstr := sqlstr + ' and ((k.wf_status=:stPending) or (k.wf_status between :stStart and :stSent))'
      else
        sqlstr := sqlstr + ' and (k.wf_status between :stStart and :stSent)';
    end;
    if id_ksc>0 then
      sqlstr := sqlstr + GetKSCContiton;
    sqlstr := sqlstr + ' group by k.id_bill';
    if Status in [kswsFinished, kswsPreFinished] then
      sqlstr := sqlstr +  ' order by max_time desc, k.id_bill'
    else
      sqlstr := sqlstr +  ' order by min_status_synt desc, min_time, k.id_bill';
    q := upos.UntillDB.GetPreparedIIbSql(sqlstr);
    q.q.ParamByName('stSent').asInteger := Ord(Status);
    if ksc_type = kscExtended then
      q.q.ParamByName('stStart').asInteger := Ord(kswsStart);
    if id_ksc>0 then
      q.q.ParamByName('id_ksc').asInt64   := id_ksc;
    if ksc_show_pending then
      q.q.ParamByName('stPending').asInt64   := Ord( kswsPending );
    q.q.ParamByName('dt').asDatetime := IncHour(upos.GetPOSNow,1);
    q.ExecQuery;

    CashKSPickupPOSDataset.EmptyDataSet;
    CashKSPickupPOSDataset.IndexName := '';
    if q.eof then exit;

    bills := TList<TPUPOSData>.Create;
    try
      while not q.eof do begin
        puData.maxdt := q.FieldByName('max_time').AsDatetime;
        puData.mindt := q.FieldByName('min_time').AsDatetime;
        puData.minst := q.FieldByName('min_status').AsInteger;
        puData.id_bill := StrToInt64Def(q.FieldByName('id_bill').AsString,0);
        bills.Add( puData );
        q.next;
      end;

      if bills.Count = 0 then exit;
      for puData in bills do begin
        sqlstr :=
          'select distinct bill.id id_bill, bill.tableno,'
          + ' bill.table_part, bill.table_name, bill.id_untill_users, ks_workflow_ks.id_ks_sub,'
          + ' bill.open_datetime, bill.id_untill_users id_waiter, bill.number_of_covers, 1 order_idx,'
          + ' bill.name, bill.sdescription, bill.number, bill.suffix, bill.take_away,'
          + ' ta_bills.id ta_bill_id, ks_sub.ks_sub_name, clients.name client_name, bill.hc_roomnumber,'
          + ' (bill.day_number || coalesce(bill.day_suffix, '''')) day_number,'
          + ' (select first 1 kk.dt_status'
          + '    from ks_workflow kk'
          + '   where kk.id_bill = bill.id'
          + '     and kk.id_courses = bill.id_courses'
          + '   order by kk.dt_status desc'
          + ' ) dt_status,'
          + ' k.last_finish_dt,'
          + ' (select first 1 id_untill_users'
          + '    from orders'
          + '   where id_bill = bill.id'
          + '   order by ord_datetime desc'
          + ' ) id_last_order_user';
        if bSplitCourses then
          sqlstr := sqlstr + ', c.usedcolor, k.id_courses'
        else
          sqlstr := sqlstr + ', -1 id_courses, -1 usedcolor, bill.open_datetime dt_status';
        sqlstr := sqlstr
          + ' from ks_workflow k '
          + ' join bill on bill.id = k.id_bill'
          + ' left outer join courses c on c.id = k.id_courses '
          + ' left outer join ks_workflow_ks on ks_workflow_ks.id_ks_workflow = k.id '
          + ' left outer join clients on clients.id = bill.id_clients '
          + ' left outer join kitchen_screens ks_sub on ks_sub.id = ks_workflow_ks.id_ks_sub '
          + ' left outer join ta_bills on bill.id = ta_bills.id_bill'
          + ' where k.id_bill = :id_bill';
        if ksc_type = kscRegular then
          sqlstr := sqlstr + ' and k.wf_status = :stSent '
        else begin
          if ksc_show_pending then
            sqlstr := sqlstr + ' and ((k.wf_status = :stPending) or (k.wf_status between :stStart and :stSent))'
          else
            sqlstr := sqlstr + ' and (k.wf_status between :stStart and :stSent)';
        end;

        q := upos.UntillDB.GetPreparedIIbSql(sqlstr);
        q.q.ParamByName('id_bill').asInt64  := puData.id_bill;
        q.q.ParamByName('stSent').asInteger := Ord(Status);
        if ksc_type = kscExtended then begin
          if Status in [kswsFinished, kswsPreFinished] then
            q.q.ParamByName('stStart').asInteger := Ord(kswsFinished)
          else
            q.q.ParamByName('stStart').asInteger := Ord(kswsStart);
          if ksc_show_pending then
            q.q.ParamByName('stPending').asInt64   := Ord( kswsPending );
        end;
        q.ExecQuery;

        while not q.eof do begin
          CashKSPickupPOSDataset.Append;
          CashKSPickupPOSDataset.FieldByName('max_time').asDatetime         := q.q.fieldByName('dt_status').asDatetime;
          CashKSPickupPOSDataset.FieldByName('id_bill').asString            := q.q.fieldByName('id_bill').asString;
          CashKSPickupPOSDataset.FieldByName('tableno').asInteger           := q.q.fieldByName('tableno').asInteger;
          CashKSPickupPOSDataset.FieldByName('table_part').asString         := q.q.fieldByName('table_part').asString;
          CashKSPickupPOSDataset.FieldByName('table_name').asString         := q.q.fieldByName('table_name').asString;
          CashKSPickupPOSDataset.FieldByName('id_untill_users').asString    := q.q.fieldByName('id_untill_users').asString;
          CashKSPickupPOSDataset.FieldByName('open_datetime').asDatetime    := q.q.fieldByName('open_datetime').asDatetime;
          CashKSPickupPOSDataset.FieldByName('id_waiter').asString          := q.q.fieldByName('id_waiter').asString;
          CashKSPickupPOSDataset.FieldByName('id_courses').asString         := q.q.fieldByName('id_courses').asString;
          CashKSPickupPOSDataset.FieldByName('client_name').asString        := q.q.fieldByName('client_name').asString;
          CashKSPickupPOSDataset.FieldByName('usedcolor').asInteger         := q.q.fieldByName('usedcolor').asInteger;
          CashKSPickupPOSDataset.FieldByName('number_of_covers').asInteger  := q.q.fieldByName('number_of_covers').asInteger;
          CashKSPickupPOSDataset.FieldByName('name').asString               := q.q.fieldByName('name').asString;
          CashKSPickupPOSDataset.FieldByName('sdescription').asString       := q.q.fieldByName('sdescription').asString;
          CashKSPickupPOSDataset.FieldByName('number').asString             := q.q.fieldByName('number').asString + q.q.fieldByName('suffix').asString;
          CashKSPickupPOSDataset.FieldByName('day_number').asString         := q.q.fieldByName('day_number').asString;
          CashKSPickupPOSDataset.FieldByName('takeaway').asInteger          := q.q.fieldByName('take_away').asInteger;
          CashKSPickupPOSDataset.FieldByName('ta_bill_id').asString         := q.q.fieldByName('ta_bill_id').asString;
          CashKSPickupPOSDataset.FieldByName('ks_sub_name').asString        := q.q.fieldByName('ks_sub_name').asString;
          CashKSPickupPOSDataset.FieldByName('id_ks_sub').asString          := q.q.fieldByName('id_ks_sub').asString;
          CashKSPickupPOSDataset.FieldByName('id_last_order_user').asString := q.q.fieldByName('id_last_order_user').asString;
          CashKSPickupPOSDataset.FieldByName('hc_roomnumber').asString      := q.q.fieldByName('hc_roomnumber').asString;
          CashKSPickupPOSDataset.FieldByName('dt_status').asDatetime        := q.q.fieldByName('dt_status').asDatetime;
          if q.q.fieldByName('last_finish_dt').IsNull then
            CashKSPickupPOSDataset.FieldByName('last_finish_dt').asDatetime := q.q.fieldByName('dt_status').asDatetime
          else
            CashKSPickupPOSDataset.FieldByName('last_finish_dt').asDatetime := q.q.fieldByName('last_finish_dt').asDatetime;
          CashKSPickupPOSDataset.Post;
          q.next;
        end;
      end;
    finally
      FreeAndNil( bills );
    end;

    if assigned(KSPUItemsProvider) then begin
      KSPUItemsProvider.Refresh( ksdPro.IDKSC );
    end;

    with CashKSPickupPOSDataset do begin
      IndexDefs.Clear;
      AddIndex('kspu_idx', 'id_bill', [], '');
      if Status in [kswsFinished, kswsPreFinished] then begin
        AddIndex('kspu_idxdt1', 'last_finish_dt', [ixDescending], 'last_finish_dt');
        CashKSPickupPOSDataset.IndexName := 'kspu_idxdt1';
      end;
    end;
  finally
    KSPURefreshParams.id_ksc  := id_ksc;
    KSPURefreshParams.max_rec := max_rec;
    KSPURefreshParams.Status  := Status;
    KSPURefreshParams.dt      := Now;
  end;
end;

procedure CashSupplierDatasets;
var q : IIBSQL;
begin
  SuppliersDataset.EmptyDataSet;
  q := upos.UntillDB.GetPreparedIIbSql('select * from SUPPLIERS where is_active=1');
  q.ExecQuery;
  while not q.Eof do begin
    SuppliersDataset.Append;
    SuppliersDataset.FieldByName('id').asString   := q.FieldByName('id').asString;
    SuppliersDataset.FieldByName('name').asString := q.FieldByName('name').asString;
    SuppliersDataset.FieldByName('MIN_AMOUNT').asCurrency := q.FieldByName('MIN_AMOUNT').asCurrency;
    SuppliersDataset.FieldByName('DELIVERY_DAY').asInteger := q.FieldByName('DELIVERY_DAY').asInteger;
    SuppliersDataset.FieldByName('customer_nr').asString := q.FieldByName('customer_nr').asString;
    SuppliersDataset.FieldByName('ORDER_DAY').asInteger  := q.FieldByName('ORDER_DAY').asInteger;
    SuppliersDataset.FieldByName('order_email').asString := q.FieldByName('order_email').asString;
    SuppliersDataset.Post;
    q.next;
  end;
  with SuppliersDataset do begin
    IndexDefs.Clear;
    AddIndex('supIdx', 'id', [], '');
  end;
end;

procedure CashCatDataSets;
var q : IIBSQL;
begin
  CashCatDataSet.EmptyDataSet;
  q := upos.UntillDB.GetPreparedIIbSql('select id, name from category where is_active=1');
  q.ExecQuery;
  while not q.Eof do begin
    CashCatDataSet.Append;
    CashCatDataSet.FieldByName('id').asString   := q.FieldByName('id').asString;
    CashCatDataSet.FieldByName('name').asString := q.FieldByName('name').asString;
    CashCatDataSet.Post;
    q.next;
  end;
  with CashCatDataSet do begin
    IndexDefs.Clear;
    AddIndex('catIdx', 'id', [], '');
  end;
end;

procedure CashFGDataSets;
var q : IIBSQL;
begin
  CashFGDataSet.EmptyDataSet;
  q := upos.UntillDB.GetPreparedIIbSql('select id, name, '
    + ' GROUP_TYPE, ID_BOOKKP_TURNOVER, ID_BOOKKP_VAT, ID_BOOKKP_VAT_SEC '
    + ' from food_group '
    + ' where is_active=1');
  q.ExecQuery;
  while not q.Eof do begin
    CashFGDataSet.Append;
    CashFGDataSet.FieldByName('id').asString   := q.FieldByName('id').asString;
    CashFGDataSet.FieldByName('name').asString := q.FieldByName('name').asString;
    CashFGDataSet.FieldByName('GROUP_TYPE').asString := q.FieldByName('GROUP_TYPE').asString;
    CashFGDataSet.FieldByName('ID_BOOKKP_TURNOVER').asString := q.FieldByName('ID_BOOKKP_TURNOVER').asString;
    CashFGDataSet.FieldByName('ID_BOOKKP_VAT').asString := q.FieldByName('ID_BOOKKP_VAT').asString;
    CashFGDataSet.FieldByName('ID_BOOKKP_VAT_SEC').asString := q.FieldByName('ID_BOOKKP_VAT_SEC').asString;
    CashFGDataSet.Post;
    q.next;
  end;
  with CashFGDataSet do begin
    IndexDefs.Clear;
    AddIndex('fgIdx', 'id', [], '');
  end;
end;

procedure CashArticlePOSDatasets( aid_art : Int64 );
var str : String;
    q : IIBSQL;
begin
  if not assigned(CashArticlePOSDataset) then exit;

  if aid_art=0 then
    CashArticlePOSDataset.EmptyDataSet
  else
    CashArticlePOSDataset.DeleteByArtID( aid_art );
  str := 'select ap.id apid, ap.disabled, ap.pos, articles.id, ap.id_department, ap.id_sales_area'
    + ' from articles'
    + ' left outer join ARTICLE_POSITION_EX ap on ap.id_articles = articles.id  '
    + ' where articles.is_active=1 ';
  if aid_art > 0 then
    str := str + ' and articles.id=:id_art';
  q := upos.UntillDB.GetPreparedIIbSql(str);
  if aid_art > 0 then
    q.q.ParamByName('id_art').AsInt64 := aid_art;
  q.ExecQuery;
  while not q.Eof do begin
    CashArticlePOSDataset.Append;
    CashArticlePOSDataset.FieldByName('id').asString             := q.FieldByName('apid').asString;
    CashArticlePOSDataset.FieldByName('id_articles').asString    := q.FieldByName('id').asString;
    CashArticlePOSDataset.FieldByName('id_departments').asString := q.FieldByName('id_department').asString;
    CashArticlePOSDataset.FieldByName('id_sa').asString          := q.FieldByName('id_sales_area').asString;
    CashArticlePOSDataset.FieldByName('disabled').asInteger      := q.FieldByName('disabled').asInteger;
    CashArticlePOSDataset.FieldByName('pos').asInteger           := q.FieldByName('pos').asInteger;
    CashArticlePOSDataset.FieldByName('dtupdate').asDatetime     := Now;
    CashArticlePOSDataset.Post;
    if aid_art > 0 then
      if q.FieldByName('disabled').asInteger = 1 then begin
        BLRMainU.DisableArtMem(aid_art,
        StrToInt64Def(q.FieldByName('id_department').asString,0),
        StrToInt64Def(q.FieldByName('id_sales_area').asString,0),
        false);
   end;
    q.next;
  end;
  with CashArticlePOSDataset do begin
    IndexDefs.Clear;
    AddIndex('artPosIdx', 'id_articles;id_departments;id_sa', [], '');
    AddIndex('posIdx', 'id', [], '');
    AddIndex('artidx', 'id_articles', [], '');
  end;
  MainThreadAlive;
end;

procedure CashDepPOSDatasets( aid_dep : Int64 );
var str : String;
    q : IIBSQL;
begin
  if aid_dep=0 then
    CashDepPOSDataset.EmptyDataSet
  else
    CashDepPOSDataset.DeleteByDepID( aid_dep );
  str := 'select ap.id apid, ap.pos, department.id, ap.id_sales_area'
    + ' from department'
    + ' left outer join DEP_POSITION_EX ap on ap.id_department = department.id  '
    + ' where department.is_active=1 ';
  if aid_dep > 0 then
    str := str + ' and department.id=:id_dep';
  q := upos.UntillDB.GetPreparedIIbSql(str);
  if aid_dep > 0 then
    q.q.ParamByName('id_dep').AsInt64 := aid_dep;
  q.ExecQuery;
  while not q.Eof do begin
    CashDepPOSDataset.Append;
    CashDepPOSDataset.FieldByName('id').asString    := q.FieldByName('apid').asString;
    CashDepPOSDataset.FieldByName('id_department').asString    := q.FieldByName('id').asString;
    CashDepPOSDataset.FieldByName('id_sa').asString          := q.FieldByName('id_sales_area').asString;
    CashDepPOSDataset.FieldByName('pos').asInteger           := q.FieldByName('pos').asInteger;
    CashDepPOSDataset.Post;
    q.next;
  end;

  with CashDepPOSDataset do begin
    IndexDefs.Clear;
    AddIndex('depPosIdx', 'id_department;id_sa', [], '');
    AddIndex('posIdx', 'id', [], '');
    AddIndex('depidx', 'id_department', [], '');
  end;
  MainThreadAlive;
end;

{ TCashArticleDataSet }
procedure TCashArticleDataSet.AddFieldDefs;
begin
  inherited;

{0}  Self.AddFieldDef('id', id, ftLargeint, 0);
{1}  Self.AddFieldDef('id_departament', id_departament, ftLargeint, 0);

end;

procedure TCashArticleDataSet.DeleteByArtIds(aid_arts: array of Int64);
var i : Integer;
begin
  if length(aid_arts) = 0 then begin
     CashArticleDataSet.cds.EmptyDataSet;
     KSDailyStockArtList.Clear;
     exit;
  end;

  cds.IndexName := 'midIndex';
  for i := 0 to Pred(length(aid_arts)) do begin
    while cds.FindKey([aid_arts[i]]) do
      cds.Delete;
    KSDailyStockArtList.Remove( aid_arts[i] );
  end;
end;

procedure TCashArticleDataSet.Post;
var
  ue: TPMDataChangedEvent;
begin
  inherited;
  ue := TPMDataChangedEvent.Create(0, '', uetUpdate);
  try
    upos.un.SendEvent(URL_DAILYSTOCK_CHANGED, ue);
  finally
    FreeAndNil(ue);
  end;
end;

{ TCashArticleSADataSet }

procedure TCashArticleSADataSet.AddFieldDefs;
begin
  inherited;
  Self.AddFieldDef('id', id, ftLargeint, 0);
  Self.AddFieldDef('id_departament', id_departament, ftLargeint, 0);
  Self.AddFieldDef('id_sales_area', id_sales_area, ftLargeint, 0);
  Self.AddFieldDef('sequence', sequence, ftInteger, 0);
  Self.AddFieldDef('limited', limited, ftInteger, 0);
  Self.AddFieldDef('id_periods', id_periods, ftLargeint, 0);
  Self.AddFieldDef('abs_position', abs_position, ftInteger, 0);
  Self.AddFieldDef('menu_items_count', menu_items_count, ftInteger, 0);
end;

procedure TCashArticleSADataSet.AssignFieldValues(Qa: TCashArticleSADataSet);
begin
  id.asString                 :=  Qa.id.asString;
  id_departament.asString     :=  Qa.id_departament.asString;
  id_sales_area.asString      :=  Qa.id_sales_area.asString;
  sequence.asString           :=  Qa.sequence.asString;
  limited.asString            :=  Qa.limited.asString;
  id_periods.asString         :=  Qa.id_periods.asString;
  abs_position.asInteger      :=  Qa.abs_position.asInteger;
  menu_items_count.AsInteger  :=  Qa.menu_items_count.asInteger;
end;

procedure TCashArticleSADataSet.DeleteArtByIds(aid_arts: Array of Int64);
var i : Integer;
begin
  if length(aid_arts)=0 then begin
    CashArticleSADataSet.cds.EmptyDataSet;
    exit;
  end;

  cds.IndexName := 'aidArtIndex';
  for i := 0 to Pred(length(aid_arts)) do
  while cds.FindKey([IntToStr(aid_arts[i])]) do
    cds.Delete;
end;

procedure TCashArticleSADataSet.DeleteArtById(aid_art: Int64);
begin
  if aid_art <= 0 then exit;

  cds.IndexName := 'aidArtIndex';
  while cds.FindKey([aid_art]) do
    cds.Delete;
end;

{ TCashPromoArtDataSet }

procedure TCashPromoArtDataSet.AddFieldDefs;
begin
  inherited;
  Self.AddFieldDef('id_sales_area', id_sales_area, ftLargeint);
  Self.AddFieldDef('id_promo', id_promo, ftLargeint);
  Self.AddFieldDef('promo_name', promo_name, ftWideString, 255);
  Self.AddFieldDef('id_options', id_options, ftLargeint);
  Self.AddFieldDef('option_number', option_number, ftInteger);
  Self.AddFieldDef('article_option_number', article_option_number, ftInteger);
  Self.AddFieldDef('id_promo_item', id_promo_item, ftLargeint);
  Self.AddFieldDef('option_count', option_count, ftInteger);
  Self.AddFieldDef('price', price, ftCurrency);
  Self.AddFieldDef('article_number', article_number, ftInteger);
  Self.AddFieldDef('course_number',course_number, ftInteger);
end;

procedure TCashPromoArtDataSet.AssignFieldValues(Qa: TCashPromoArtDataSet);
begin
  id_sales_area.asString           := Qa.id_sales_area.asString;
  id_promo.asString                := Qa.id_promo.asString;
  promo_name.asString              := Qa.promo_name.asString;
  id_options.asString              := Qa.id_options.asString;
  option_number.asString           := Qa.option_number.asString;
  article_option_number.asString   := Qa.article_option_number.asString;
  id_promo_item.asString           := Qa.id_promo_item.asString;
  option_count.asInteger           := Qa.option_count.asInteger;
  price.asCurrency                 := Qa.price.asCurrency;
  article_number.asString          := Qa.article_number.asString;
  course_number.asInteger          := Qa.course_number.asInteger;
end;

procedure TCashPromoArtDataSet.CopyDS(fromDS: TCashPromoArtDataSet);
begin
  self.cds.EmptyDataset;
  fromDS.ds.first;
  while not fromDS.ds.eof do begin
     Append;
     AssignFieldValues(fromDS);
     Post;
     fromDS.ds.Next;
  end;
end;

procedure TCashPromoArtDataSet.DeleteArtByID(aid_art: Int64);
begin
  if aid_art <= 0 then exit;

  cds.IndexName := 'idIndex';
  while cds.FindKey([aid_art]) do
    cds.Delete;
end;

{ TCacheCourse }

constructor TCacheCourses.Create(AOwner : TComponent);
begin
  inherited Create(AOwner);
  CreateAndInitDataset;
  FDbEventsController:=TDbEventsController.Init(upos.UntillDB, Self, Self.ClassName)
    .AddWatchingTables(['courses']);
  upos.un.RegisterListener(URL_POS_GENERAL, self);
end;

destructor TCacheCourses.Destroy;
begin
  upos.un.UnRegisterListener(URL_POS_GENERAL, self);
  FreeAndNil(FCacheCourseDataset);
  inherited;
end;

procedure TCacheOrderTypes.RefreshData;
var qSel : IIBSQL;
begin
  qSel := upos.UntillDB.GetPreparedIIBSQL('select  id, number, name from order_type where is_active=1 ');
  qSel.ExecQuery;

  Clear;
  while not qSel.eof do begin
    list.Add(
      StrToInt64Def(qSel.q.FieldByName('id').asString,0)
      , TOrderTypeData.Create(qSel.q.FieldByName('number').asInteger
      , qSel.q.FieldByName('name').asString));
    qSel.next;
  end;
end;

procedure TCacheCourses.RefreshData;
var qSel : IIBSQL;
    minSep, maxSep : Integer;
begin

  qSel := upos.UntillDB.GetPreparedIIBSQL('select id, name, course_prep_min, ks_countdown_type, ks_countdown_min, '
    + ' COURSENUMBER, USEDCOLOR, separate, changeable, print_deliver, auto_fire, course_prep_min time_frame, '
    + ' id_ks_printer, id_ks_ticket, id_pu_printer, id_pu_ticket, ask_to_change, print_on_hold, print_next_ticket, '
    + ' start_per_ks'
    + ' from courses '
    + ' where is_active=1');
  qSel.ExecQuery;

  FCacheCourseDataset.EmptyDataSet;
  minSep := INIT_MINCOURSE_NUMBER;
  maxSep := 0;
  while not qSel.eof do  begin
     FCacheCourseDataset.Append;
     FCacheCourseDataset.FieldByName('id').asString                 :=  qSel.FieldByName('id').asString;
     FCacheCourseDataset.FieldByName('name').asString               :=  qSel.FieldByName('name').asString;
     FCacheCourseDataset.FieldByName('id_ks_printer').asString      :=  qSel.FieldByName('id_ks_printer').asString;
     FCacheCourseDataset.FieldByName('id_ks_ticket').asString       :=  qSel.FieldByName('id_ks_ticket').asString;
     FCacheCourseDataset.FieldByName('id_pu_printer').asString      :=  qSel.FieldByName('id_pu_printer').asString;
     FCacheCourseDataset.FieldByName('id_pu_ticket').asString       :=  qSel.FieldByName('id_pu_ticket').asString;
     FCacheCourseDataset.FieldByName('course_prep_min').asInteger   :=  qSel.FieldByName('course_prep_min').asInteger;
     FCacheCourseDataset.FieldByName('ks_countdown_type').asInteger :=  qSel.FieldByName('ks_countdown_type').asInteger;
     FCacheCourseDataset.FieldByName('ks_countdown_min').asInteger  :=  qSel.FieldByName('ks_countdown_min').asInteger;
     FCacheCourseDataset.FieldByName('COURSENUMBER').asInteger      :=  qSel.FieldByName('COURSENUMBER').asInteger;
     FCacheCourseDataset.FieldByName('USEDCOLOR').asInteger         :=  qSel.FieldByName('USEDCOLOR').asInteger;
     FCacheCourseDataset.FieldByName('separate').asInteger          :=  qSel.FieldByName('separate').asInteger;
     FCacheCourseDataset.FieldByName('changeable').asInteger        :=  qSel.FieldByName('changeable').asInteger;
     FCacheCourseDataset.FieldByName('print_deliver').asInteger     :=  qSel.FieldByName('print_deliver').asInteger;
     FCacheCourseDataset.FieldByName('auto_fire').asInteger         :=  qSel.FieldByName('auto_fire').asInteger;
     FCacheCourseDataset.FieldByName('print_on_hold').asInteger         :=  qSel.FieldByName('print_on_hold').asInteger;
     FCacheCourseDataset.FieldByName('time_frame').asInteger        :=  qSel.FieldByName('time_frame').asInteger;
     FCacheCourseDataset.FieldByName('print_next_ticket').asInteger :=  qSel.FieldByName('print_next_ticket').asInteger;
     FCacheCourseDataset.FieldByName('ask_to_change').asInteger     :=  qSel.FieldByName('ask_to_change').asInteger;
     FCacheCourseDataset.FieldByName('start_per_ks').asInteger      :=  qSel.FieldByName('start_per_ks').asInteger;
     FCacheCourseDataset.Post;

     // Define Min and Max Separate course number
     if qSel.FieldByName('separate').asInteger = 1 then begin
       if qSel.FieldByName('COURSENUMBER').asInteger < minSep then
         minSep := qSel.FieldByName('COURSENUMBER').asInteger;
       if qSel.FieldByName('COURSENUMBER').asInteger > maxSep then
         maxSep := qSel.FieldByName('COURSENUMBER').asInteger;
     end;

     qSel.Next;
  end;
  if minSep = INIT_MINCOURSE_NUMBER then minSep := 0;
  FMinSepNumber := minSep;
  FMaxSepNumber := maxSep;
  with FCacheCourseDataset do begin
    IndexDefs.Clear;
    AddIndex('crIndex', 'id', [], '');
    AddIndex('crnIndex', 'COURSENUMBER', [], '');
    AddIndex('crnmIndex', 'name', [], '');
  end;
end;

function TCacheCourses.GetCoursePrepMin(Course_ID :String) :Integer;
begin

  Result := 0;
  if (StrToInt64Def(Course_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then
    result := FCacheCourseDataset.fieldByname('course_prep_min').asInteger;
end;

function TCacheCourses.GetCourseName(Course_ID: Int64): String;
begin
  Result := GetCourseName(IntToStr(Course_ID));
end;

function TCacheCourses.GetCourseNumber(strCourse_ID :String) :Integer;
begin
  Result := 0;
  if (StrToInt64Def(strCourse_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([strCourse_ID]) then
    result := FCacheCourseDataset.fields[5].asInteger; // coursenumber
end;

function TCacheCourses.GetCourseName(strCourse_ID :String) :String;
begin
  Result := '';
  if StrToInt64Def(strCourse_ID,0) = 0 then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([strCourse_ID]) then
    result := FCacheCourseDataset.fields[1].asString;
end;

function TCacheCourses.GetCourseDataByNumber(number : Integer) : TCourseData;
begin
  result.ClearData;

  FCacheCourseDataset.IndexName := 'crnIndex';
  if FCacheCourseDataset.FindKey([number]) then
    result.FillData(
        StrToInt64Def(FCacheCourseDataset.fieldByname('id').asString,0)
      , FCacheCourseDataset.fieldByname('name').asString
      , FCacheCourseDataset.fieldByname('COURSENUMBER').asInteger
      , (FCacheCourseDataset.fieldByname('separate').asInteger = 1)
      , (FCacheCourseDataset.fieldByname('CHANGEABLE').asInteger = 1)
      , FCacheCourseDataset.fieldByname('USEDCOLOR').asInteger
      , (FCacheCourseDataset.fieldByname('auto_fire').asInteger=1)
      , FCacheCourseDataset.fieldByname('time_frame').asInteger
      , FCacheCourseDataset.fieldByname('ks_countdown_type').asInteger
      , (FCacheCourseDataset.fieldByname('ask_to_change').asInteger = 1)
      , (FCacheCourseDataset.fieldByname('print_on_hold').asInteger)
      , (FCacheCourseDataset.fieldByname('print_next_ticket').asInteger = 1)
      , (FCacheCourseDataset.fieldByname('start_per_ks').asInteger = 1)
    )
end;

function TCacheCourses.GetCourseData(id : Int64) : TCourseData;
begin
//    profenter('TCacheCourses.GetCourseData');
  result.ClearData;
  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([id]) then begin
    result.FillData(
        StrToInt64Def(FCacheCourseDataset.fieldByname('id').asString,0)
      , FCacheCourseDataset.fieldByname('name').asString
      , FCacheCourseDataset.fieldByname('COURSENUMBER').asInteger
      , (FCacheCourseDataset.fieldByname('separate').asInteger = 1)
      , (FCacheCourseDataset.fieldByname('CHANGEABLE').asInteger = 1)
      , FCacheCourseDataset.fieldByname('USEDCOLOR').asInteger
      , (FCacheCourseDataset.fieldByname('auto_fire').asInteger=1)
      , FCacheCourseDataset.fieldByname('time_frame').asInteger
      , FCacheCourseDataset.fieldByname('ks_countdown_type').asInteger
      , (FCacheCourseDataset.fieldByname('ask_to_change').asInteger=1)
      , (FCacheCourseDataset.fieldByname('print_on_hold').asInteger)
      , (FCacheCourseDataset.fieldByname('print_next_ticket').asInteger=1)
      , (FCacheCourseDataset.fieldByname('start_per_ks').asInteger=1)
    )
  end;
end;

function TCacheCourses.GetCourseNumber(Course_ID :Int64) :Integer;
begin
  result := GetCourseNumber( IntToStr(Course_ID) );
end;

function TCacheCourses.GetCourseIDByName(cName: String): TCourseData;
begin
  result.ClearData;
  if trim(cName)='' then exit;

  FCacheCourseDataset.IndexName := 'crnmIndex';
  if FCacheCourseDataset.FindKey([cName]) then
    result.FillData(
        StrToInt64Def(FCacheCourseDataset.fieldByname('id').asString,0)
      , FCacheCourseDataset.fieldByname('name').asString
      , FCacheCourseDataset.fieldByname('COURSENUMBER').asInteger
      , (FCacheCourseDataset.fieldByname('separate').asInteger = 1)
      , (FCacheCourseDataset.fieldByname('CHANGEABLE').asInteger = 1)
      , FCacheCourseDataset.fieldByname('USEDCOLOR').asInteger
      , (FCacheCourseDataset.fieldByname('auto_fire').asInteger=1)
      , FCacheCourseDataset.fieldByname('time_frame').asInteger
      , FCacheCourseDataset.fieldByname('ks_countdown_type').asInteger
      , (FCacheCourseDataset.fieldByname('ask_to_change').asInteger =1)
      , (FCacheCourseDataset.fieldByname('print_on_hold').asInteger)
      , (FCacheCourseDataset.fieldByname('print_next_ticket').asInteger =1)
      , (FCacheCourseDataset.fieldByname('start_per_ks').asInteger =1)
    )
end;

function TCacheCourses.GetCourseIDByNumber(CourseNumber :Integer; bSeparate: boolean = false) :Int64;
var cd : TCourseData;
begin
  result := 0;
  cd := GetCourseDataByNumber(CourseNumber);
  if cd.id>0 then begin
    result := cd.id;
    if bSeparate and not cd.separate then
      result := 0;
  end;
end;

procedure TCacheCourses.GetCourseKSPUPrinter(aCourse_ID: Int64;
  awf_state: TKSWorkflowStatus; var aid_printer : Int64; var aid_ticket : Int64);
var    id_temp : Int64;
begin
  aid_printer := 0;
  aid_ticket := 0;
  if (aCourse_ID = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([aCourse_ID]) then begin
    aid_printer := StrToInt64Def(FCacheCourseDataset.fieldByname('id_ks_printer').asString,0);
    if awf_state in [kswsFinished, kswsPreFinished] then begin // we are on PU screen now
      id_temp := StrToInt64Def(FCacheCourseDataset.fieldByName('id_pu_printer').AsString,0);
      if id_temp > 0 then
        aid_printer := id_temp;
    end;
    aid_ticket := StrToInt64Def(FCacheCourseDataset.fieldByname('id_ks_ticket').asString,0);
    if awf_state in [kswsFinished, kswsPreFinished] then begin // we are on PU screen now
      id_temp := StrToInt64Def(FCacheCourseDataset.fieldByName('id_pu_ticket').AsString,0);
      if id_temp > 0 then
        aid_ticket := id_temp;
    end;
  end;
end;

function TCacheCourses.GetCourseSeparate(Course_ID :String) :boolean;
begin
  Result := false;
  if (StrToInt64Def(Course_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then
    result := (FCacheCourseDataset.fieldByname('separate').asInteger = 1);
end;

function TCacheCourses.NeedPrintDeliver(Course_ID :Int64) :boolean;
var prFlag : Integer;
begin
  Result := false;
  if (Course_ID = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then begin
    prFlag := FCacheCourseDataset.fieldByname('print_deliver').asInteger;
    result := GetBits(prFlag, 0);
  end;
end;

function TCacheCourses.NeedPrintPU(Course_ID :Int64) :boolean;
var prFlag : Integer;
begin
  Result := false;
  if (Course_ID = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then begin
    prFlag := FCacheCourseDataset.fieldByname('print_deliver').asInteger;
    result := GetBits(prFlag, 1);
  end;
end;

function TCacheCourses.OnDataChanged(tce: TDbDataChangeEvent): boolean;
begin
  RefreshData;
  result := true;
end;

procedure TCacheCourses.OnListenerException(e: Exception);
begin

end;

function TCacheCourses.OnReset: boolean;
begin
  RefreshData;
  result := true;
end;

procedure TCacheCourses.OnUrlEvent(url: String; ue: TUrlEvent);
begin
  if ue is TPosStartedEvent then
    FDbEventsController.Subscribe;
end;

function TCacheCourses.GetCourseCDType(Course_ID :String) :Integer;
begin
  Result := 0;
  if (StrToInt64Def(Course_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then
    result := FCacheCourseDataset.fieldByname('ks_countdown_type').asInteger;
end;

function TCacheCourses.GetCourseCDMin(Course_ID :String) :Integer;
begin
  Result := 0;
  if (StrToInt64Def(Course_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then
    result := FCacheCourseDataset.fieldByname('ks_countdown_min').asInteger;
end;

function TCacheCourses.GetCourseColor(Course_ID :String) :Integer;
begin
  Result := 0;
  if (StrToInt64Def(Course_ID,0) = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([Course_ID]) then
    result := FCacheCourseDataset.fieldByname('USEDCOLOR').asInteger;
end;

function TCacheCourses.GetCourseLangName(id : Int64; lang_id : String) :String;
begin
  Result := '';
  if (id = 0) then exit;

  FCacheCourseDataset.IndexName := 'crIndex';
  if FCacheCourseDataset.FindKey([id]) then
    result := FCacheCourseDataset.FieldByName('name').AsString;
end;

procedure TCacheCourses.CreateAndInitDataset;
begin
  if not Assigned(FCacheCourseDataset) then FCacheCourseDataset := TClientDataset.Create(UPos);
  with FCacheCourseDataset do begin
    with FieldDefs.AddFieldDef do begin
      Name:='id';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='name';
      DataType:=ftWideString;
      Size:=20;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='course_prep_min';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ks_countdown_type';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ks_countdown_min';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='COURSENUMBER';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='USEDCOLOR';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='separate';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='print_deliver';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='auto_fire';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='time_frame';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='changeable';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_ks_printer';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_ks_ticket';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_pu_printer';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='id_pu_ticket';
      DataType:=ftLargeint;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='ask_to_change';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='print_on_hold';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='print_next_ticket';
      DataType:=ftInteger;
    end;
    with FieldDefs.AddFieldDef do begin
      Name:='start_per_ks';
      DataType:=ftInteger;
    end;
    CreateDataset;
  end;
end;

{ TCourseData }

procedure TCourseData.ClearData;
begin
  id         := 0;
  name       := '';
  number     := 0;
  separate   := false;
  changeable := false;
  color      := 0;
  auto_fire  := false;
  time_frame := 0;
  print_next_ticket := false;
  start_per_ks := false;
  ks_countdown_type := Ord(kscdNextCourse);
  print_on_hold := 0;
end;

procedure TCourseData.FillData(
  aid           : Int64
  ; aname       : String
  ; anumber     : Integer
  ; aseparate   : boolean
  ; achangeable : boolean
  ; acolor      : Integer
  ; aauto_fire  : boolean
  ; atime_frame : Integer
  ; aks_countdown_type : Integer
  ; aneed_ask_course : boolean
  ; aprint_on_hold : Integer
  ; aprint_next_ticket : boolean
  ; astart_per_ks : boolean
  );
begin
  id          := aid;
  name        := aname;
  number      := anumber;
  separate    := aseparate;
  changeable  := achangeable;
  color       := acolor;
  auto_fire   := aauto_fire;
  time_frame  := atime_frame;
  ks_countdown_type := aks_countdown_type;
  need_ask_course := aneed_ask_course;
  print_on_hold := aprint_on_hold;
  print_next_ticket := aprint_next_ticket;
  start_per_ks := astart_per_ks;
end;

{ TSupplierData }

procedure TSupplierData.Clear;
begin
  id           := 0;
  name         := '';
  min_amount   := 0;
  delivery_day := 0;
  order_day    := 0;
  order_email  := '';
  customer_nr  := '';
end;

{ TCashOptionArticleDataSet }

procedure TCashOptionArticleDataSet.DeleteByArtID(aid_art : Int64);
begin
  if aid_art <= 0 then exit;
  IndexName := 'idartIdx';
  while FindKey([aid_art]) do
    Delete;
end;

procedure TCashOptionArticleDataSet.DeleteByOptID(aid_opt : Int64);
begin
  if aid_opt <= 0 then exit;
  IndexName := 'idoptIdx';
  while FindKey([aid_opt]) do
    Delete;
end;

{ TCashArticleOptionDataSet }

procedure TCashArticleOptionDataSet.DeleteByArtID(aid_art: Int64);
begin
  if aid_art <= 0 then exit;
  IndexName := 'idartIdx';
  while FindKey([aid_art]) do
    Delete;
end;

procedure TCashArticleOptionDataSet.DeleteByOptID(aid_opt: Int64);
begin
  if aid_opt <= 0 then exit;
  IndexName := 'idoptIdx';
  while FindKey([aid_opt]) do
    Delete;
end;

function TCashArticleOptionDataSet.GetArticlOptionCompose(aid_art,
  aid_opt: Int64): Integer;
var oldIndex : string;
begin
  result := 0;
  if (aid_art = 0) or (aid_opt = 0) then exit;

  oldIndex    := IndexName;
  try
    IndexName   := 'ArtOptIdx';
    if FindKey([aid_opt, aid_opt]) then
      result := FieldByName('compose').asInteger;
  finally
    IndexName := oldIndex;
  end;
end;

function TCashArticleOptionDataSet.HasArticlOptions(aid_art, aid_sa: Int64): boolean;
var oldIndex : string;
begin
  profenter('TCashArticleOptionDataSet.HasArticlOptions');
  result := false;
  if (aid_art = 0) or (aid_sa = 0) then exit;

  oldIndex    := IndexName;
  try
    IndexName   := 'idArtSAIdx';
    if FindKey([aid_art, aid_sa]) then
      result := true
  finally
    IndexName := oldIndex;
  end;
end;


procedure CacheArticleBOData( art_ids : array of int64 );
var strScreenGroups : String;
    i : Integer;
begin
  id_screen_group := '';
  if not (UntillApp.GetPosForm=nil) then
    id_screen_group := UntillApp.GetPosForm.PosScreenGroup;

  strScreenGroups := GetPOSScreenGroup;

  if length(art_ids)=0 then exit;

  CacheArticlesSAData( art_ids );
  CacheArtAllergens.RefreshData( art_ids );
  for i := 0 to Pred(length(art_ids)) do begin
    CachePromoArticles( art_ids[i] );
    CacheOptionArticleDatasets(art_ids[i]);
    DoCashKSART( art_ids[i] );
    DoCashBGDataset( art_ids[i] );
    ArtPriceList.RefreshData( art_ids[i] );
    CashArticlePOSDatasets( art_ids[i] );
    CacheArtTableAreas.RefreshData( art_ids[i] );
    CacheArtTANotifyList.RefreshData( art_ids[i] );
    CacheArtNotifyList.RefreshData( art_ids[i] );
    CashArtDatasets( strScreenGroups, art_ids[i]);
  end;
  CacheOpts.RefreshData;
end;

procedure CacheSizeBOData;
begin
  CacheSizeModifiers.RefreshData;
end;

{ TCashKSARTDataset }

procedure TCashKSARTDataset.DeleteByArtId(aid_art: Int64);
begin
  if aid_art <= 0 then exit;
  IndexName := 'ksartIdIndex';
  while FindKey([aid_art]) do
    Delete;
end;

{ TCashBGDataSet }

procedure TCashBGDataSet.DeleteByArtId(aid_art: Int64);
begin
  if aid_art <= 0 then exit;
  IndexName := 'bgIDArtIndex';
  while FindKey([aid_art]) do
    Delete;
end;

{ TcdsFonts }

procedure TcdsArtFonts.DeleteByArtIds(aid_arts: array of int64);
var i : Integer;
begin
  with cdsArtFonts do begin
    if length(aid_arts)=0 then begin
      EmptyDataSet;
      exit;
    end;

    IndexName := 'cfartIndex';
    for i := 0 to Pred(length(aid_arts)) do
    while FindKey([IntToStr(aid_arts[i])]) do
      Delete;
  end;
end;


{ CashArticlePOSDataset }

procedure TCashArticlePOSDataset.DeleteByArtId(aid_art: Int64);
begin
  if aid_art <= 0 then exit;
  IndexName := 'posIdx';
  while FindKey([aid_art]) do
    Delete;
end;

{ TCacheCPTs }

procedure TCacheCPTs.ClearCacheCptDatas;
var key    : Int64;
    cpData : TCPTData;
begin
  for key in FCacheCPTDatas.keys do begin
    cpData := FCacheCPTDatas.Items[key];
    FreeAndNil( cpData );
  end;
  FCacheCPTDatas.Clear;
end;

constructor TCacheCPTs.Create;
begin
  inherited;
  FCacheCPTDatas    := TDictionary<Int64, TCPTData>.Create;
end;

destructor TCacheCPTs.Destroy;
begin
  ClearCacheCPTDatas;
  FreeAndNil(FCacheCPTDatas);
  inherited;
end;

function TCacheCPTs.GetCount: Integer;
begin
  result := FCacheCPTDatas.Count;
end;

function TCacheCPTs.GetCPTData(AID_CPT: Int64): TCPTData;
begin
  FCacheCPTDatas.TryGetValue(AID_CPT, result);
end;

procedure TCacheCPTs.RefreshData;
var qSel, qSelArt : IIBSQL;
    d    : TCPTData;
    id   : Int64;
    id_opt : Int64;
begin

  ClearCacheCptDatas;
  qSel := upos.UntillDB.GetPreparedIIBSQL('select cpt.id, opt.opt_number, opt.name, '
    + ' cpt.ID_OPTIONS, cpt.ID_PRICES, cpt.PRICE, cpt.MAX_FREE_QTY'
    + ' from options opt, coupon_templates cpt '
    + ' where cpt.id_options = opt.id and opt.is_active=1');
  qSel.ExecQuery;

  while not qSel.eof do  begin
    id     := StrToInt64Def(qSel.FieldByName('id').asString,0);
    id_opt := StrToInt64Def(qSel.FieldByName('ID_OPTIONS').asString,0);

    qSelArt := upos.UntillDB.GetPreparedIIBSQL('select id_articles '
      + ' from option_article '
      + ' where id_options=:id_options and option_article.is_active=1 ');
    qSelArt.q.ParamByName('id_options').AsInt64 := id_opt;
    qSelArt.ExecQuery;
    if not qSelArt.eof then begin
      d := TCPTData.Create;
      d.id     :=  id;
      d.Number :=  qSel.FieldByName('opt_number').asInteger;
      d.name   :=  qSel.FieldByName('name').asString;
      d.id_options :=  id_opt;
      d.ID_PRICES  :=  StrToInt64Def(qSel.FieldByName('ID_PRICES').asString,0);
      d.PRICE      :=  qSel.FieldByName('PRICE').asCurrency;
      d.max_free_qty := qSel.FieldByName('max_free_qty').asInteger;
      FCacheCptDatas.AddOrSetValue(id, d);
      while not qSelArt.eof do begin
        d.ArtList.Add(qSelArt.FieldByName('id_articles').asString);
        qSelArt.next;
      end;
    end;
    qSel.Next;
  end;
end;
{ TCPTData }

constructor TCPTData.Create;
begin
  FArtList := TStringList.Create;
end;

destructor TCPTData.Destroy;
begin
  FreeAndNil(FArtList);
  inherited;
end;

{ TArticlePriceData }

procedure TArticlePriceData.Clear;
begin
  price       := 0;
  id_currency := 0;
  round       := 0;
end;

function TArticlePriceData.Empty: boolean;
begin
  result :=  id_currency = 0;
end;

{ TCacheWaiters }

procedure TCacheWaiters.ClearCacheWaiters;
var v : TCacheWaiter;
begin
  for v in FCacheWaiters.values do
    if assigned(v) then
      v.free;
  FCacheWaiters.Clear;
end;

constructor TCacheWaiters.Create;
begin
  inherited;
  FCacheWaiters := TDictionary<Int64, TCacheWaiter>.Create;
end;

destructor TCacheWaiters.Destroy;
begin
  ClearCacheWaiters;
  FreeAndNil(FCacheWaiters);
  inherited;
end;

function TCacheWaiters.GetCacheWaiterData(AID: Int64): TCacheWaiter;
begin
  FCacheWaiters.TryGetValue(AID, result);
end;

procedure TCacheWaiters.RefreshData;
var iq, qq : IIBSQL;
    cw : TCacheWaiter;
    id : Int64;
begin
  ClearCacheWaiters;
  iq := upos.UntillDB.GetPreparedIIbSql('select untill_users.id, '
    + ' direct_sales_table, ta_role, CLOSE_TABLES_TYPE_ID, ORDER_TABLES_TYPE_ID,'
    + ' allow_transfer'
    + ' from waiters '
    + ' join untill_users on untill_users.id=waiters.ID_UNTILL_USERS'
    + ' where untill_users.is_active=1');
  iq.ExecQuery;

  while not iq.Eof do begin
    cw := TCacheWaiter.Create(
        iq.q.FieldByName('direct_sales_table').AsInteger,
        iq.q.FieldByName('ta_role').AsInteger,
        iq.q.FieldByName('CLOSE_TABLES_TYPE_ID').AsInteger,
        iq.q.FieldByName('ORDER_TABLES_TYPE_ID').AsInteger,
        iq.q.FieldByName('allow_transfer').AsInteger=1);

    id := StrToInt64Def(iq.q.FieldByName('id').AsString,0);
    qq := upos.UntillDB.GetPreparedIIBSQL('select a.id_table_area '
      + ' from WAITER_CLOSETABLE_AREA a, waiters b '
      + ' where a.id_waiters = b.id'
      + ' and b.ID_UNTILL_USERS =:idUser and a.is_active=1');
    qq.q.Params[0].asInt64 := id;
    qq.ExecQuery;
    while not qq.eof do begin
      cw.AddTableArea( qq.q.fields[0].asInt64 );
      qq.next;
    end;
    FCacheWaiters.Add( id, cw);
    iq.Next;
  end;
end;

{ TCacheWaiter }

procedure TCacheWaiter.AddTableArea(aid_ta: Int64);
begin
  if fTableAreas.IndexOf(IntToStr(aid_ta))>0 then exit;
  fTableAreas.Add(IntToStr(aid_ta));
end;

constructor TCacheWaiter.Create(adirect_table, Ata_role,
  ACLOSE_TABLES_TYPE_ID, AORDER_TABLES_TYPE_ID: Integer;
  aallowTransfer : boolean);
begin
  fdirect_table   := adirect_table;
  fta_role        := ata_role;
  FCLOSE_TABLES_TYPE_ID := ACLOSE_TABLES_TYPE_ID;
  FORDER_TABLES_TYPE_ID := AORDER_TABLES_TYPE_ID;
  FTableAreas     := TStringList.Create;
  fallowTransfer  := aallowTransfer;
end;

destructor TCacheWaiter.Destroy;
begin
  FreeAndNil(FTableAreas);
  inherited;
end;

function TCacheWaiter.TableAreaExists(aid_ta: Int64): boolean;
begin
  result := false;
  if aid_ta<=0 then exit;

  result := (fTableAreas.count=0) or (fTableAreas.IndexOf(IntToStr(aid_ta))>0);
end;

function IsWaiterDeliverer(aid_user: Int64) : boolean;
var cwd : TCacheWaiter;
begin
  result := false;
  if aid_user=0 then exit;

  cwd := CacheWaiters.GetCacheWaiterData(aid_user);
  if assigned( cwd ) then
    result := cwd.ta_role = Ord(taurDeliverer);
end;

{ TCacheArtTableAreas }

procedure TCacheArtTableAreas.ClearArtTableAreas;
var key    : string;
    taData : TStringList;
begin
  for key in FArtTableAreas.keys do begin
    if ArtTableAreas.TryGetValue(key, taData) then
      if assigned(taData) then
        taData.Free;
  end;
  FArtTableAreas.Clear;
end;

constructor TCacheArtTableAreas.Create;
begin
  FArtTableAreas := TDictionary<string, TStringList>.Create;
end;

procedure TCacheArtTableAreas.DeleteByArtID(aid_art: Int64);
var key : string;
    iq  : IIBSQL;
    lst : TStringList;
begin
  if aid_art=0 then exit;

  iq := upos.UntillDB.GetPreparedIIbSql('select id from table_area where is_active=1');
  iq.ExecQuery;
  while not iq.eof do begin
    key := GetArtTAkey(aid_art, StrToInt64Def(iq.Fields[0].asString,0));
    if key <> '' then begin
      if ArtTableAreas.TryGetValue(key, lst) then begin
        lst.Free;
        FArtTableAreas.Remove(key);
      end;
    end;
    iq.next;
  end;
end;

destructor TCacheArtTableAreas.Destroy;
begin
  ClearArtTableAreas;
  FreeAndNil(FArtTableAreas);
  inherited;
end;

function TCacheArtTableAreas.GetArtTAkey(aid_art, aid_ta: Int64): string;
begin
  result := '';
  if (aid_ta=0) or (aid_art=0) then exit;

  result := IntToStr(aid_art)+'/'+IntToStr(aid_ta);
end;

function TCacheArtTableAreas.GetCacheArtTableAreas(aid_art,aid_ta : Int64): TStringList;
var ArtTAKey: string;
begin
  result   := nil;
  if (aid_art=0) or (aid_ta=0) then exit;

  ArtTAKey := GetArtTAkey(aid_art,aid_ta);
  if ArtTAKey='' then exit;

  FArtTableAreas.TryGetValue(ArtTAKey, result);
end;

procedure TCacheArtTableAreas.RefreshData( aid_art : Int64 );
var q : IIBSQL;
    str       : string;
    ArtTAKey  : string;
    newList   : TStringList;
    id_ksStr  : String;
    ic        : Int64;
begin
  if aid_art=0 then
    ClearArtTableAreas
  else
    DeleteByArtID(aid_art);

  str := 'select distinct an.id_articles, tp.ID_TABLE_AREA, takn.id_ks'
    + ' from TA_KS_NOTIFY takn'
    + ' join table_printer tp on takn.ID_TABLE_PRINTER = tp.id and tp.is_active=1'
    + ' join ARTICLE_NOTIFY an on an.id_preparation_area=tp.ID_PREPARATION_AREA '
    + '    and an.is_active=1 and an.purpose = 0 '
    + ' join ARTICLES on an.id_ARTICLES=ARTICLES.ID and ARTICLES.is_active=1 '
    + '    and ARTICLES.SHOW_IN_KITCHEN_SCREEN=1'
    + ' where takn.is_active=1';
  if aid_art > 0 then
    str := str + ' and an.id_articles=:id_art';
  str := str + ' Order by an.id_articles, tp.ID_TABLE_AREA';
  q := upos.UntillDB.GetPreparedIIbSql(str);
  if aid_art > 0 then
    q.q.ParamByName('id_art').AsInt64 := aid_art;
  q.ExecQuery;
  ic := 0;
  while not q.eof do begin
    ArtTAKey := GetArtTAkey(StrToInt64Def(q.q.fieldByName('id_articles').AsString,0),
      StrToInt64Def(q.q.fieldByName('ID_TABLE_AREA').AsString,0));
    if ArtTAKey<>'' then begin
      id_ksStr  := q.q.fieldByName('id_ks').AsString;
      if StrToInt64Def(id_ksStr,0)>0 then begin
        newList := nil;
        if FArtTableAreas.TryGetValue(ArtTAKey, newList) then begin
          if newList.IndexOf(id_ksStr) < 0 then
            newList.Add(id_ksStr);
        end else begin
          newList := TStringList.Create;
          newList.Add(id_ksStr);
          FArtTableAreas.AddOrSetValue(ArtTAkey, newList);
        end;
      end;
    end;
    q.Next;
    Inc(ic);
    if ((ic div 200) * 200) = ic then
      MainThreadAlive;
  end;
end;

{ TCashPromoDataSet }

procedure TCashPromoDataSet.AddFieldDefs;
begin
  inherited;
  Self.AddFieldDef('id_sales_area', id_sales_area, ftLargeint);
  Self.AddFieldDef('id_promo', id_promo, ftLargeint);
  Self.AddFieldDef('promo_name', promo_name, ftWideString, 255);
  Self.AddFieldDef('option_count', option_count, ftInteger);
end;

procedure TCashPromoDataSet.AssignFieldValues(Qa: TCashPromoDataSet);
begin
  id_sales_area.asString           := Qa.id_sales_area.asString;
  id_promo.asString                := Qa.id_promo.asString;
  promo_name.asString              := Qa.promo_name.asString;
  option_count.asInteger           := Qa.option_count.asInteger;
end;

procedure TCashPromoDataSet.DeleteArtByID(aid_art: Int64);
begin
  if aid_art <= 0 then exit;

  cds.IndexName := 'idIndex';
  while cds.FindKey([aid_art]) do
    cds.Delete;
end;

{ TCacheOrderTypes }

procedure TCacheOrderTypes.Clear;
var item : TOrderTypeData;
begin
  for item in list.Values do
    if assigned(item) then
      item.Free;
  list.Clear;
end;

constructor TCacheOrderTypes.Create;
begin
  list := TObjectDictionary<Int64, TOrderTypeData>.Create;
end;

destructor TCacheOrderTypes.Destroy;
begin
  Clear;
  FreeAndNil(list);
  inherited;
end;

function TCacheOrderTypes.GetOrderTypeName(aid: Int64): String;
var ordData : TOrderTypeData;
begin
  result := '';
  if aid <= 0 then exit;

  if list.TryGetValue(aid, ordData)
    and assigned(ordData) then
      result := ordData.name;

end;

{ TOrderTypeData }

constructor TOrderTypeData.Create(anumber: Integer; aname: string);
begin
  number := anumber;
  name   := aname;
end;

{ TCacheArtAllergens }
procedure TCacheArtAllergens.ClearData;
var wData : TStringList;
begin
  for wData in FList.values do
    if assigned(wData) then
      wData.Free;
  FList.Clear;
end;

constructor TCacheArtAllergens.Create;
begin
  FList := TDictionary<Int64, TStringList>.Create;
end;

procedure TCacheArtAllergens.DeleteByArtIDs(art_ids: array of int64);
var wData : TStringList;
    i : Integer;
begin
  if length(art_ids) = 0 then
    ClearData
  else begin
    for i := 0 to Pred( length(art_ids) ) do begin
      if FList.TryGetValue( art_ids[i], wData ) then
        FreeAndNil( wData );
      FList.Remove( art_ids[i] );
    end;
  end;
end;

destructor TCacheArtAllergens.Destroy;
begin
  ClearData;
  FreeAndNil( FList );
  inherited;
end;

function TCacheArtAllergens.GetAllergenListByArtID(aid_art: Int64): TStringList;
begin
  result := nil;
  if not assigned(FList) then exit;
  FList.TryGetValue(aid_art, result);
end;

procedure TCacheArtAllergens.RefreshData( art_ids : array of int64 );
var qSel     : IIBSQL;
    strl     : TStringList;
    prev_art : Int64;
    id_art   : Int64;
    id_al    : String;
    sqlStr   : string;
    strIds   : string;
begin
  DeleteByArtIDs( art_ids );

  strIds := '';
  if length(art_ids) > 0 then
    strIds := strIds + ' and ' + GetInExprForIds('ID_ARTICLES', art_ids );

  sqlStr := 'select ID_ARTICLES, ID_ALLERGENS '
    + ' from ARTICLES_ALLERGENS where IS_ACTIVE = 1 ';
  sqlStr := sqlStr  + strIds;
  sqlStr := sqlStr  + ' order by ID_ARTICLES';
  qSel := upos.UntillDB.GetPreparedIIbSql(sqlStr);
  qSel.ExecQuery;

  if qSel.eof then exit;

  prev_art := 0;
  strl     := nil;
  while not qSel.eof  do begin
    id_art := StrToInt64Def(qSel.FieldByName('ID_ARTICLES').asString,0);
    if prev_art <> id_art then begin
      strl := TStringList.Create;
      FList.Add( id_art, strl );
      prev_art := id_art;
    end;
    if assigned(strl) then begin
      id_al := qSel.FieldByName('ID_ALLERGENS').asString;
      if strl.IndexOf( id_al ) < 0 then
        strl.Add( id_al  );
    end;
    qSel.next;
  end;
end;

{ TCacheAllergenPics }

procedure TCacheAllergenPics.Clear;
begin
  ClearList;
  ClearAltList;
end;

procedure TCacheAllergenPics.ClearAltList;
var item : TMemoryStream;
begin
  for item in alt_list.Values do
    if assigned(item) then
      item.Free;
  alt_list.Clear;
end;

procedure TCacheAllergenPics.ClearList;
var item : TMemoryStream;
begin
  for item in list.Values do
    if assigned(item) then
      item.Free;
  list.Clear;
end;

constructor TCacheAllergenPics.Create;
begin
  list := TObjectDictionary<Int64, TMemoryStream>.Create;
  alt_list := TObjectDictionary<Int64, TMemoryStream>.Create;
end;

destructor TCacheAllergenPics.Destroy;
begin
  Clear;
  FreeAndNil(list);
  FreeAndNil(alt_list);
  inherited;
end;

function TCacheAllergenPics.GetAltImageStream(aid: Int64): TMemoryStream;
begin
  result := nil;
  if aid <= 0 then exit;

  alt_list.TryGetValue(aid, result);
end;

function TCacheAllergenPics.GetImageStream(aid: Int64): TMemoryStream;
begin
  result := nil;
  if aid <= 0 then exit;

  list.TryGetValue(aid, result);
end;

procedure TCacheAllergenPics.RefreshData;
var allergens : TAllergenManager;
    iq        : IIBSQL;
    path      : string;
    al_type   : TAllergenType;
    ms        : TMemoryStream;
    id        : Int64;
    oldms, newms     : TMemoryStream;
begin
  Clear;
  allergens := TAllergenManager.Create;
  try
    iq := upos.UntillDB.GetPreparedIIbSql('select * from ALLERGENS where is_active=1');
    iq.ExecQuery;
    if iq.eof then exit;

    while not iq.Eof do begin
      id      := StrToInt64Def(iq.q.fieldByName('id').asString,0);
      al_type := TAllergenType(iq.q.fieldByName('number').asInteger);
      ms   := TMemoryStream.Create;
      try

        if not iq.q.fieldByName('picture').IsNull then
          iq.q.fieldByName('picture').SaveToStream(ms)
        else if IsFixAllergen( Ord( al_type ) ) then begin
          path := allergens.GetImage( al_type );
          try
            ms.LoadFromFile(plugin.GetImageFileName( 'Allergen/' + path ));
          except
          end;
        end;
        if ms.Size > 10 then begin
          newms := TMemoryStream.Create;
          ms.Seek(0,0);
          newms.CopyFrom(ms, ms.size);
          if list.TryGetValue( id, oldms ) then begin
            if assigned(oldms) then
              FreeAndNil(oldms);
            list.Remove(id);
          end;
          list.AddOrSetValue( id, newms );
        end;
        ms.Clear;
        if not iq.q.fieldByName('alt_picture').IsNull then
          iq.q.fieldByName('alt_picture').SaveToStream(ms)
        else if IsFixAllergen( Ord( al_type ) ) then begin
          path := allergens.GetAltImage( al_type );
          try
            ms.LoadFromFile(plugin.GetImageFileName( 'Allergen/' + path ));
          except
          end;
        end;
        if ms.Size > 10 then begin
          newms := TMemoryStream.Create;
          ms.Seek(0,0);
          newms.CopyFrom(ms, ms.size);
          if alt_list.TryGetValue( id, oldms ) then begin
            if assigned(oldms) then
              FreeAndNil(oldms);
            alt_list.Remove(id);
          end;
          alt_list.AddOrSetValue( id, newms );
        end;
      finally
        FreeAndNil( ms );
      end;
      iq.Next;
    end;
  finally
    FreeAndNil( allergens );
  end;
end;

{ TAllergenData }

constructor TAllergenData.Create(anumber: Integer; aname: String;
  amsname : TLabelCaptionList; amsPic: TMemoryStream);
begin
  fnumber := anumber;
  fname   := aname;
  fmsname := amsname;
  fmsPic := amsPic;
end;

destructor TAllergenData.Destroy;
begin
  FreeAndNil( fmsname );
  FreeAndNil( fmsPic );
  inherited;
end;

{ TCacheAllergens }

procedure TCacheAllergens.ClearData;
var wData : TAllergenData;
begin
  for wData in FList.values do begin
    if assigned(wData) then
      wData.Free;
  end;
  FList.Clear;
end;

constructor TCacheAllergens.Create;
begin
  FList := TObjectDictionary<Int64, TAllergenData>.Create;
end;

destructor TCacheAllergens.Destroy;
begin
  ClearData;
  FreeAndNil( FList );
  inherited;
end;

function TCacheAllergens.GetCacheAllergenLangName(aid: Int64; alang_id : String) : String;
var allData : TAllergenData;
    num     : Integer;
    alrg    : TAllergenManager;
begin
  if not FList.TryGetValue(aid, allData) then
    result := GetAllergenLangName(aid, alang_id)
  else begin
    result := allData.msname.Value[alang_id];
    if trim(result)='' then begin
      num := allData.number;
      if IsFixAllergen( num ) then begin
        alrg := TAllergenManager.Create;
        try
          result := alrg.GetLangName( TAllergenType( num ), alang_id );
        finally
          alrg.Free;
        end;
      end;
      if trim(result) = '' then
        result := allData.name;
    end;
  end;
end;

function TCacheAllergens.GetAllergenData(aid: Int64): TAllergenData;
begin
  FList.TryGetValue(aid, result);
end;

procedure TCacheAllergens.RefreshData;
var qSel     : IIBSQL;
    allData  : TAllergenData;
    id       : String;
    aname    : String;
    anumber  : Integer;
    amsname  : TLabelCaptionList;
    amsPic   : TMemoryStream;
begin
  ClearData;
  qSel := upos.UntillDB.GetPreparedIIbSql('select * '
    + ' from ALLERGENS '
    + ' where IS_ACTIVE = 1 order by number');
  qSel.ExecQuery;

  if qSel.eof then exit;

  while not qSel.eof  do begin
    id := qSel.FieldByName('ID').asString;
    aname   := qSel.FieldByName('NAME').asString;
    anumber := qSel.FieldByName('NUMBER').asInteger;
    amsname := TLabelCaptionList.Create;
    amsPic := TMemoryStream.Create;
    if not qSel.FieldByName('picture').IsNull then
      qSel.FieldByName('picture').SaveToStream(amsPic);
    allData := TAllergenData.Create(anumber, aname, amsname, amsPic);
    FList.Add( StrToInt64Def(id,0), allData );
    qSel.next;
  end;
end;

{ TCacheSizeModifier }

constructor TCacheSizeModifier.Create(aid : Int64; aid_main : Int64;
  anumber  : Integer; aName : String);
begin
  fid      := aid;
  fid_main := aid_main;
  Fnumber  := anumber;
  Fname    := aName;
end;

{ TCacheSizeModifiers }

constructor TCacheSizeModifiers.Create;
begin
  list := TObjectList.Create;
end;

destructor TCacheSizeModifiers.Destroy;
begin
  FreeAndNil(list);
  inherited;
end;

function sortsmlist(item1, item2 :TCacheSizeModifier) : integer;
begin
  if item1.number < item2.number then
    result := -1
  else if item1.number > item2.number then
    result := 1
  else
    result := 0;
end;

procedure TCacheSizeModifiers.GetByMain(aid_main: Int64; aList: TStringList);
var i     : Integer;
begin
  assert(assigned(aList));
  aList.Clear;
  if aid_main=0 then exit;

  list.Sort(@sortsmlist);
  for i := 0 to pred(list.Count) do begin
    if aid_main=TCacheSizeModifier(list.items[i]).id_main then
      aList.Add(IntToStr(TCacheSizeModifier(list.items[i]).id));
  end;
end;

function TCacheSizeModifiers.GetSMName(aid: Int64; aLangId: String): String;
var data     : TCacheSizeModifier;
    i        : Integer;
begin
  result :=  '';
  if aid=0 then exit;

  for i := 0 to Pred(list.count) do begin
    data := TCacheSizeModifier(list.items[i]);
    if assigned(data) then
    if data.id = aid then begin
      result := data.Name;
      exit;
    end;
  end;

end;

procedure TCacheSizeModifiers.RefreshData;
var qSel : IIBSQL;
begin
  qSel := upos.UntillDB.GetPreparedIIBSQL('select SIZE_MODIFIER_ITEM.ID ID_item, '
     + ' SIZE_MODIFIER.ID, SIZE_MODIFIER_ITEM.SMI_NAME, '
     +  'SIZE_MODIFIER_ITEM.smi_number'
     + ' from SIZE_MODIFIER_ITEM'
     + ' join SIZE_MODIFIER on SIZE_MODIFIER.ID = SIZE_MODIFIER_ITEM.ID_SIZE_MODIFIER'
     + ' where SIZE_MODIFIER_ITEM.is_active=1 '
     + ' order by SIZE_MODIFIER_ITEM.smi_number' );
  qSel.ExecQuery;

  list.Clear;
  while not qSel.eof do begin
    list.Add(
      TCacheSizeModifier.Create(
        StrToInt64Def(qSel.q.FieldByName('id_item').asString,0),
        StrToInt64Def(qSel.q.FieldByName('ID').asString,0),
        qSel.q.FieldByName('smi_number').asInteger,
        qSel.q.FieldByName('SMI_NAME').asString ));
    qSel.next;
  end;
end;

{ TCashPromoArtList }

procedure TCashPromoArtList.ClearData;
var wData : TStringList;
begin
  for wData in list.values do begin
    if assigned(wData) then
      wData.Free;
  end;
  list.Clear;
end;

constructor TCashPromoArtList.Create;
begin
  inherited;
  list := TDictionary<Int64, TStringList>.Create;
end;

procedure TCashPromoArtList.DeleteByID(aid_art: Int64);
var lst : TstringList;
begin
  if list.TryGetValue(aid_art, lst) then begin
    if assigned(lst) then
      lst.Free;
    list.Remove(aid_art);
  end;
end;

destructor TCashPromoArtList.Destroy;
begin
  ClearData;
  FreeAndNil( list );
  inherited;
end;

function TCashPromoArtList.GetPromoListByArtID(aid_art: Int64): TStringList;
begin
  result := nil;
  if not assigned(list) then exit;
  list.TryGetValue(aid_art, result);
end;

procedure TCashPromoArtList.RefreshData( aid_art : Int64 );
var qSelArt : IIBSQL;
    strl     : TStringList;
    prev_id_art : Int64;
    id_art   : Int64;
    id_promo : string;
    strSQL : String;
begin
  if aid_art > 0 then
    DeleteByID(aid_art)
  else
    ClearData;
  strSQL := 'select option_article.id_articles id_art, article_options.id_articles id_promo'
      + ' from option_article'
      + ' join article_options on article_options.id_options=option_article.id_options and article_options.is_active=1'
      + ' join articles on article_options.id_articles=articles.id and articles.is_active=1 and articles.promo=1'
      + ' where option_article.is_active=1';
  if aid_art > 0 then
    strSQL := strSQL + ' and option_article.id_articles = :aid_art';
  strSQL := strSQL + ' order by option_article.id_articles';
  qSelArt := upos.UntillDB.GetPreparedIIbSql( strSQL );
  if aid_art > 0 then
    qSelArt.q.Params[0].AsInt64 := aid_art;
  qSelArt.ExecQuery;

  prev_id_art := 0;
  strl     := nil;
  while not qSelArt.eof do begin
    id_art := StrToInt64Def(qSelArt.Fields[0].asString,0);
    if prev_id_art <> id_art then begin
      strl := TStringList.Create;
      list.Add( id_art, strl );
      prev_id_art := id_art;
    end;

    if assigned(strl) then begin
      id_promo := qSelArt.Fields[1].asString;
      if strl.IndexOf( id_promo ) < 0 then
        strl.Add( id_promo  );
    end;

    qSelArt.next;
  end;
end;

{ TComboPromoItems }

procedure TComboPromoItems.ClearData;
var key   : string;
    wData : TList<TComboPromoItem>;
    i : Integer;
begin
  for key in FList.keys do begin
    wData := FList.Items[key];
    for i := 0 to Pred(wData.count) do begin
      TComboPromoItem(wData[i]).Free;
    end;
    FreeAndNil( wData );
  end;
  FList.Clear;
end;

constructor TComboPromoItems.Create;
begin
  Flist := TDictionary<string, TList<TComboPromoItem>>.Create;
end;

destructor TComboPromoItems.Destroy;
begin
  ClearData;
  FreeAndNil(FList);
  inherited;
end;

function TComboPromoItems.GetItemByKey(pKey: string): TList<TComboPromoItem>;
begin
  FList.TryGetValue(pKey, result);
end;

{ TArtPriceList }
constructor TArtPriceList.Create;
begin
  inherited;
  list := TDictionary<Int64, TArtPriceData>.Create;
end;

destructor TArtPriceList.Destroy;
var item : TArtPriceData;
begin
  for item in list.values do begin
    if assigned(item) then
       item.Free;
  end;
  FreeAndNil(list);
  inherited;
end;

function TArtPriceList.GetArtPrice(aid_art, aid_price: Int64): TPriceData;
var item  : TArtPriceData;
    pdata : TPriceData;
begin
  result := nil;
  if aid_art = 0 then exit;
  if aid_price = 0 then exit;

  if list.TryGetValue( aid_art, item ) then
    if assigned(item) then
      if item.list.TryGetValue(aid_price, pdata) then
          result := pdata;
end;

procedure TArtPriceList.RefreshData(aid_art : Int64 = 0);
var q : IIBSQL;
    item    : TArtPriceData;
    pdata   : TPriceData;
    id_art, id_price : Int64;
    strSQL  : String;
    ic      : Int64;
begin

  saPriceData.Clear;
  strSQL := 'select id, ID_PRICES from SALES_AREA';
  q := upos.UntillDB.GetPreparedIIbSql( strSQL );
  q.ExecQuery;
  while not q.Eof do begin
    saPriceData.AddOrSetValue(StrToInt64Def(q.fields[0].AsString,0), StrToInt64Def(q.fields[1].AsString,0));
    q.next;
  end;

  strSQL := 'select ID_ARTICLES, ID_PRICES, PRICE, SYMBOL, SYM_ALIGNMENT'
    + ' from ARTICLE_PRICES '
    + ' join CURRENCY on CURRENCY.id=ARTICLE_PRICES.ID_CURRENCY'
    + ' join articles on articles.id=ARTICLE_PRICES.ID_ARTICLES and articles.is_active=1'
    + ' where ARTICLE_PRICES.is_active = 1';
  if aid_art > 0 then
    strSQL := strSQL + ' and ID_ARTICLES=:ID_ARTICLES';
  strSQL := strSQL + ' order by ID_ARTICLES';
  q := upos.UntillDB.GetPreparedIIbSql( strSQL );
  if aid_art > 0 then
    q.q.ParamByName('ID_ARTICLES').AsInt64 := aid_art;
  q.ExecQuery;
  ic := 0;
  while not q.Eof do begin
    id_art    := StrToInt64Def(q.FieldByName('ID_ARTICLES').asString,0);
    id_price  := StrToInt64Def(q.FieldByName('ID_PRICES').asString,0);
    item  := nil;
    if not list.TryGetValue(id_art, item) then begin
      item  := TArtPriceData.Create;
      list.Add( id_art, item );
    end;
    if assigned(item) then begin
      if item.list.TryGetValue( id_price, pdata) then
        if assigned(pdata) then
          pdata.Free;
      pdata := TPriceData.Create(
        q.FieldByName('PRICE').asCurrency,
        q.FieldByName('SYMBOL').asString,
        q.FieldByName('SYM_ALIGNMENT').asInteger=1);
      item.list.AddOrSetValue(id_price, pdata);
    end;
    q.Next;
    Inc(ic);
    if ((ic div 200) * 200) = ic then
       MainThreadAlive;
  end;
end;

{ TPriceData }

constructor TPriceData.Create(aprice: Currency;
  asymbol: String; asymbol_after :boolean);
begin
  price         := aprice;
  symbol        := asymbol;
  symbol_after  := asymbol_after;
end;

{ TArtPriceData }

constructor TArtPriceData.Create;
begin
  list := TDictionary<Int64, TPriceData>.Create;
end;

destructor TArtPriceData.Destroy;
var pdata : TPriceData;
begin
  for pdata in list.values do begin
    if assigned(pdata) then
      pdata.Free;
  end;
  FreeAndNil(list);
  inherited;
end;

{ TArticleBlobs }

procedure TArticleBlobs.Clear;
var blb : TArticleBlob;
begin
  for blb in Flist.values do
    if assigned(blb) then
       blb.Free;
  Flist.Clear;
end;

constructor TArticleBlobs.Create;
begin
  Flist  := TDictionary<Int64, TArticleBlob>.Create;
end;

destructor TArticleBlobs.Destroy;
begin
  Clear;
  Flist.Free;
  inherited;
end;

function TArticleBlobs.GetBitmapMS(aid_art: Int64): TMemoryStream;
var blb : TArticleBlob;
begin
  result := nil;
  if aid_art=0 then exit;

  if ArticleBlobs.FList.TryGetValue(aid_art, blb) then
    result := blb.FBitmaps;
end;

procedure TArticleBlobs.RefreshData( aid_art: Int64 = 0 );
var qq     : IIBSQL;
    sqlStr : string;
    ms     : TMemoryStream;
    blb    : TArticleBlob;
begin
  if aid_art=0 then begin
    Clear;
  end else begin
    RemoveByArticle(aid_art);
  end;

  sqlStr := ' select a.id, case when (octet_length(a.info_data)>20) then a.info_data else null end a_info_data, '
     + ' a.pc_bitmap, a.name, internal_name'
     + ' from articles a  '
     + ' where a.is_active = 1';
   if aid_art > 0 then
      sqlStr := sqlStr + ' and a.id=:id';
   qq := upos.UntillDB.GetPreparedIIbSql( sqlStr );
   if aid_art > 0 then
     qq.q.ParamByName('id').AsInt64 := aid_art;
   qq.ExecQuery;

   if qq.eof then exit;

   ms := TMemoryStream.Create;
   try
     while not qq.eof do begin
       aid_art := StrToInt64Def(qq.FieldByName('id').asString,0);
       blb := nil;
       if not ArticleBlobs.Flist.TryGetValue( aid_art, blb) then begin
         blb     := TArticleBlob.Create;
         blb.fname := qq.FieldByName('name').asString;
         blb.Finternal_name := qq.FieldByName('internal_name').asString;
         if not qq.FieldByName('pc_bitmap').IsNull then begin
           MS.Clear;
           qq.FieldByName('pc_bitmap').SaveToStream(MS);
           if MS.Size > 20 then begin
             MS.Seek(0,0);
             blb.FBitmaps := TMemoryStream.Create;
             blb.FBitmaps.CopyFrom(ms, ms.size);
           end;
         end;
         if not qq.FieldByName('a_info_data').IsNull then begin
           MS.Clear;
           qq.FieldByName('a_info_data').SaveToStream(MS);
           MS.Seek(0,0);
           blb.FInfo := TMemoryStream.Create;
           blb.FInfo.CopyFrom(ms, ms.size);
         end;
         ArticleBlobs.Flist.Add(aid_art, blb);
       end;
       qq.Next;
     end;
   finally
     FreeAndNil(ms);
   end;
end;

procedure TArticleBlobs.RemoveByArticle(aid_art: Int64);
var blb : TArticleBlob;
begin
  if aid_art=0 then exit;

  if flist.TryGetValue(aid_art, blb) then begin
    if assigned(blb) then
      blb.Free;
    flist.Remove(aid_art);
  end;
end;

{ TArticleBlob }

constructor TArticleBlob.Create;
begin
  Fname     :='';
  FBitmaps  := nil;
  FInfo     := nil;
end;

destructor TArticleBlob.Destroy;
begin
  FreeAndNil(FBitmaps);
  FreeAndNil(FInfo);
  inherited;
end;

{ TCacheKSSubst }

constructor TCacheKSSubst.Create(aname: string; aid_ks_main: Int64; asubset : Integer);
begin
  name        := aname;
  id_ks_main  := aid_ks_main;
  subset      := asubset;
end;

{ TCacheKSSubsts }

procedure TCacheKSSubsts.Clear;
var item : TCacheKSSubst;
begin
  for item in list.Values do
    if assigned(item) then
      item.Free;
  list.Clear;
end;

constructor TCacheKSSubsts.Create;
begin
  list := TObjectDictionary<Int64, TCacheKSSubst>.Create;
end;

destructor TCacheKSSubsts.Destroy;
begin
  Clear;
  FreeAndNil(list);
  inherited;
end;

procedure TCacheKSSubsts.FillListBySubset(aid_ks: Int64; kslist: TStringList);
var subData : TCacheKSSubst;
    key : Int64;
    s   : TCacheKSSubst;
begin
  assert(assigned(kslist));
  kslist.Clear;
  if aid_ks = 0 then exit;

  subData := GetSubstData(aid_ks);
  if not assigned(subData) then exit;

  for key in list.keys do begin
    if list.TryGetValue(key, s) then
      if assigned(s) then
        if (s.subset = subData.subset) and (s.id_ks_main = subData.id_ks_main) then begin
          kslist.Add(IntToStr(key))
        end;
  end;

end;

function TCacheKSSubsts.GetSubstData(aid: Int64): TCacheKSSubst;
var s   : TCacheKSSubst;
begin
  result := nil;
  if list.TryGetValue(aid, s) then
    if assigned(s) then
      result := s;
end;

procedure TCacheKSSubsts.RefreshData;
var qSel : IIBSQL;
begin
  qSel := upos.UntillDB.GetPreparedIIBSQL('select KITCHEN_SCREENS_SUBSTATIONS.id_ks_sub, ks_sub.ks_name, id_ks_main, ks_sub.subset '
    + ' from KITCHEN_SCREENS_SUBSTATIONS '
    + ' join KITCHEN_SCREENS ks_main on ks_main.id=KITCHEN_SCREENS_SUBSTATIONS.id_ks_main and ks_main.is_active=1'
    + ' join KITCHEN_SCREENS ks_sub on ks_sub.id=KITCHEN_SCREENS_SUBSTATIONS.id_ks_sub and ks_sub.is_active=1'
    + ' where KITCHEN_SCREENS_SUBSTATIONS.is_active=1 ');
  qSel.ExecQuery;

  Clear;
  while not qSel.eof do begin
    list.Add(
      StrToInt64Def(qSel.q.FieldByName('id_ks_sub').asString,0)
      , TCacheKSSubst.Create(qSel.q.FieldByName('ks_name').asString
      , StrToInt64Def(qSel.q.FieldByName('id_ks_main').asString,0)
      , qSel.q.FieldByName('subset').asInteger
      ));
    qSel.next;
  end;
end;

{ TCashDepPOSDataset }

procedure TCashDepPOSDataset.DeleteByDepId(aid_dep: Int64);
begin
  if aid_dep <= 0 then exit;
  IndexName := 'posIdx';
  while FindKey([aid_dep]) do
    Delete;
end;

{ TCachePAKS }

constructor TCachePAKS.Create(aid_ks: Int64; apurpose : Integer);
begin
  id_ks := aid_ks;
  purpose := apurpose;
end;

{ TCacheArtNotifyList }

procedure TCacheArtNotifyList.DeleteByID(aid_art: Int64);
var data  : TList<TCachePAKS>;
    i     : Integer;
begin
  if aid_art<=0 then exit;

  if FList.TryGetValue(aid_art, data) then begin
    if assigned(data) then begin
      for i := 0 to Pred(data.Count) do begin
        data.Items[i].Free;
      end;
      data.Free;
    end;
    FList.Remove(aid_art);
  end;
end;

procedure TCacheArtNotifyList.ClearData;
var key   : Int64;
    data  : TList<TCachePAKS>;
    i     : Integer;
begin
  for key in FList.keys do begin
    if FList.TryGetValue(key, data) then
      if assigned(data) then begin
        for i := 0 to Pred(data.Count) do begin
          data.Items[i].Free;
        end;
        data.Free;
      end;
  end;
  FList.Clear;
end;

constructor TCacheArtNotifyList.Create;
begin
  FList := TDictionary<Int64, TList<TCachePAKS>>.Create;
end;

destructor TCacheArtNotifyList.Destroy;
begin
  ClearData;
  FreeAndNil(FList);
  inherited;
end;

function TCacheArtNotifyList.GetArtNotifyByArtID(aid_art: Int64): TList<TCachePAKS>;
var data  : TList<TCachePAKS>;
begin
  result := nil;
  if aid_art<=0 then exit;

  if not FList.TryGetValue(aid_art, data) then exit;
  result := data;
end;

procedure TCacheArtNotifyList.RefreshData( aid_art: Int64 );
var strSql  : string;
    q       : IIBSQL;
    item    : TCachePAKS;
    data    : TList<TCachePAKS>;
begin
  if aid_art > 0 then
    DeleteByID(aid_art)
  else
    ClearData;

  strSql := ' select distinct an.ID_articles, pakn.ID_KS, an.PURPOSE  '
    + ' from ARTICLE_NOTIFY an  '
    + ' join PA_KS_NOTIFY pakn on pakn.id_preparation_area=an.id_preparation_area and pakn.is_active=1'
    + ' join articles on articles.id = an.id_articles and SHOW_IN_KITCHEN_SCREEN=1 and articles.is_active=1'
    + ' where an.is_active=1 ';
  if aid_art > 0 then
    strSql := strSql + ' and an.ID_articles=:ID_articles';
  q := upos.Untilldb.GetPreparedIIbSql( strSql );
  if aid_art > 0 then
    q.q.ParamByName('ID_articles').AsInt64   :=  aid_art;
  q.ExecQuery;
  while not q.Eof do begin
    aid_art := StrToInt64Def(q.q.FieldByName('ID_articles').AsString,0);
    item := TCachePAKS.Create(
      StrToInt64Def(q.q.FieldByName('ID_KS').AsString,0),
      q.q.FieldByName('PURPOSE').AsInteger);
    if not flist.TryGetValue( aid_art, data ) then begin
      data := TList<TCachePAKS>.Create;
      flist.Add( aid_art, data );
    end;
    data.Add(item);
    q.Next;
  end;

end;

{ TCacheArtTANotifyList }

procedure TCacheArtTANotifyList.ClearData;
var key   : string;
    data  : TList<TCachePAKS>;
    i     : Integer;
begin
  for key in FList.keys do begin
    if FList.TryGetValue(key, data) then
      if assigned(data) then begin
        for i := 0 to Pred(data.Count) do begin
          data.Items[i].Free;
        end;
        data.Free;
      end;
  end;
  FList.Clear;
end;

constructor TCacheArtTANotifyList.Create;
begin
  FList := TDictionary<string, TList<TCachePAKS>>.Create;
end;

procedure TCacheArtTANotifyList.DeleteByID(aid_art: Int64);
var key   : string;
    data  : TList<TCachePAKS>;
    i     : Integer;
begin
  for key in FList.keys do begin
    if FList.TryGetValue(key, data) then
    if pos(IntToStr(aid_art), key)>0 then begin
      if assigned(data) then begin
        for i := 0 to Pred(data.Count) do begin
          data.Items[i].Free;
        end;
        data.Free;
      end;
      FList.Remove(key);
      exit;
    end;
  end;
end;

destructor TCacheArtTANotifyList.Destroy;
begin
  ClearData;
  FreeAndNil(FList);
  inherited;
end;

function TCacheArtTANotifyList.GetArtNotifyByArtID(
  aid_art, aid_ta: Int64): TList<TCachePAKS>;
var data  : TList<TCachePAKS>;
    key : string;
begin
  result := nil;
  if aid_art<=0 then exit;
  if aid_ta<=0 then exit;

  key := GetKey(aid_art, aid_ta);
  if not FList.TryGetValue(key, data) then exit;
  result := data;
end;

function TCacheArtTANotifyList.GetKey(aid_art, aid_ta: Int64): string;
begin
  result := IntToStr(aid_art) + ';' + IntToStr(aid_ta);
end;

procedure TCacheArtTANotifyList.RefreshData(aid_art: Int64 = 0);
var strSql  : string;
    q       : IIBSQL;
    item    : TCachePAKS;
    data    : TList<TCachePAKS>;
    id_ta   : Int64;
    key     : string;
begin
  if aid_art > 0 then
    DeleteByID(aid_art)
  else
    ClearData;

  strSql := 'select distinct an.id_articles, tp.ID_TABLE_AREA, takn.ID_KS, an.purpose'
    + ' from TA_KS_NOTIFY takn'
    + ' join table_printer tp on takn.ID_TABLE_PRINTER= tp.id and tp.is_active=1'
    + ' join ARTICLE_NOTIFY an on an.id_preparation_area=tp.ID_PREPARATION_AREA and an.is_active=1'
    + ' where takn.is_active=1';
  if aid_art > 0 then
    strSql := strSql + ' and an.id_articles=:id_articles';
  q := upos.Untilldb.GetPreparedIIbSql( strSql );
  if aid_art > 0 then
    q.q.ParamByName('ID_articles').AsInt64   :=  aid_art;
  q.ExecQuery;
  while not q.Eof do begin
    aid_art := StrToInt64Def(q.q.FieldByName('ID_articles').AsString,0);
    id_ta   := StrToInt64Def(q.q.FieldByName('ID_TABLE_AREA').AsString,0);
    key     := getKey(aid_art, id_ta);
    item := TCachePAKS.Create(
      StrToInt64Def(q.q.FieldByName('ID_KS').AsString,0),
      q.q.FieldByName('PURPOSE').AsInteger);
    if not flist.TryGetValue( key, data ) then begin
      data := TList<TCachePAKS>.Create;
      flist.Add( key, data );
    end;
    data.Add(item);
    q.Next;
  end;
end;

{ TCacheKSWarn }

constructor TCacheKSWarn.Create(aid_periods: Int64; atime_min: Integer);
begin
  id_periods := aid_periods;
  time_min   := atime_min;
end;

{ TCacheKSWarns }

constructor TCacheKSWarns.Create(aown_min: Integer);
begin
  inherited Create;
  fown_min := aown_min;
end;

function TCacheKSWarns.GetItems(Index: integer): TCacheKSWarn;
begin
  result := TCacheKSWarn(inherited items[index]);
end;

{ TCacheKSWarnList }

procedure TCacheKSWarnList.Clear;
var v  : TCacheKSWarns;
begin
  for v in FList.values do begin
    if assigned(v) then
      v.Free;
  end;
  FList.Clear;
end;

constructor TCacheKSWarnList.Create;
begin
  inherited;
  FList := TDictionary<Int64, TCacheKSWarns>.Create;
end;

destructor TCacheKSWarnList.Destroy;
begin
  Clear;
  FreeAndNil(FList);
  inherited;
end;

function TCacheKSWarnList.GetWarnTime(aid_KS: Int64; adt : TDatetime): Integer;
var data : TCacheKSWarns;
    item : TCacheKSWarn;
    i    : Integer;
begin
  result := 0;
  if aid_KS <=0 then exit;

  if not flist.TryGetValue(aid_KS, data) then exit;
  if not assigned(data) then exit;
  result := data.own_min;

  for i := 0 to Pred(data.count) do begin
    item := data.getItems(i);
    if IsBODateInPeriod(upos.UntillDB, SysDateTimeToLocal(adt), item.id_periods) then begin
      result := item.time_min;
      exit;
    end;
  end;

end;

procedure TCacheKSWarnList.RefreshData;
var strSql  : string;
    id_ks   : Int64;
    q       : IIBSQL;
    item    : TCacheKSWarn;
    data    : TCacheKSWarns;
begin
  Clear;
  strSql := 'select distinct ks.id, ks.alter_minutes own_min, ksa.id_periods, ksa.alter_minutes'
    + ' from kitchen_screens ks'
    + ' left outer join kitchen_screen_alert_periods ksa on ksa.id_kitchen_screens= ks.id and ksa.is_active = 1'
    + ' where ks.alter_minutes > 0 and ks.is_active = 1';
  q := upos.Untilldb.GetPreparedIIbSql( strSql );
  q.ExecQuery;
  while not q.Eof do begin
    id_ks := StrToInt64Def(q.q.FieldByName('ID').AsString,0);
    item := TCacheKSWarn.Create(
      StrToInt64Def(q.q.FieldByName('id_periods').AsString,0),
      q.q.FieldByName('alter_minutes').AsInteger);
    if not flist.TryGetValue( id_ks, data ) then begin
      data := TCacheKSWarns.Create(q.q.FieldByName('own_min').AsInteger);
      flist.Add( id_ks, data );
    end;
    data.Add(item);
    q.Next;
  end;
end;

{ TCacheArticleViewer }
function TCacheArticleViewer.GetCachedArticle(aid: Int64): TCachedArticle;
begin
  result := DefaultCashArticle;
  if aid <= 1000000 then exit;
  if aid > 10000000000000 then exit;
  try
    result := BORestaurantCache.Articles.Get( aid, DefaultCashArticle );
  except
  end;
end;

procedure TCacheArticleViewer.UpdateArticleDSCache(aid_art : Int64; flag : Integer);
var item : TCachedArticle;
begin
  if aid_art = 0 then exit;

  item := GetCachedArticle( aid_art );
  if assigned(item) then
    item.daily_stock := flag;
end;

function TCacheArticleViewer.GetArticleCourseNumber(aid: Int64): Integer;
begin
  result := CacheCourses.GetCourseNumber( GetCachedArticle( aid ).id_courses );
end;

function TCacheArticleViewer.ArticleKSSingle(aid_art: Int64): boolean;
begin
  result := GetCachedArticle( aid_art ).ks_single_item <> 0;
end;

constructor TCacheArticleViewer.Create;
begin
  DefaultCashArticle := TCachedArticle.Create;
end;

destructor TCacheArticleViewer.destroy;
begin
  FreeAndNil(DefaultCashArticle);
  inherited;
end;

function TCacheArticleViewer.ArticleBlockDiscount(aid_art : Int64): boolean;
begin
  result := GetCachedArticle( aid_art ).block_discount <> 0;
end;

function TCacheArticleViewer.NeedArtShowInKS(aid_art : Int64): boolean;
begin
  result := GetCachedArticle( aid_art ).SHOW_IN_KITCHEN_SCREEN = 1;
end;

function TCacheArticleViewer.GetArticlePriceManual(aid_art : Int64) : boolean;
begin
  result := GetCachedArticle( aid_art ).article_manual>0;
end;

function TCacheArticleViewer.GetArticleWeighted(aid_art : Int64) : Boolean;
begin
  result := GetCachedArticle( aid_art ).WEIGHT_REQUIRED > 0;
end;

function TCacheArticleViewer.GetArticleAutoSM(aid_art : Int64) : boolean;
begin
  result := GetCachedArticle( aid_art ).auto_sm = 1;
end;

function TCacheArticleViewer.GetArticleSpt(aid_art : Int64) : Integer;
begin
  if GetCachedArticle( aid_art ).SURFACE_POINT = 0 then
    result := 1
  else
    result := 0;
end;

function TCacheArticleViewer.IsOmitTPAPI(aid_art : Int64) : boolean;
begin
  result := GetCachedArticle( aid_art ).omittpapi=1;
end;

function TCacheArticleViewer.GetArticleExcludeSP(aid_art : Int64) : boolean;
begin
  result := GetCachedArticle( aid_art ).ignore_sp > 0;
end;

function TCacheArticleViewer.IsArticleForRent( aid_art : Int64 ) : Boolean;
begin
  result := GetCachedArticle( aid_art ).isrental> 0;
end;

function TCacheArticleViewer.IgnoreSplitCombo( aid_art : Int64 ) : boolean;
begin
  result := GetCachedArticle( aid_art ).ignore_split_combo = 1;
end;

function TCacheArticleViewer.GetArticleCanSP( aid_art : Int64 ) : Boolean;
var item : TCachedArticle;
begin
  result := false;
  item := GetCachedArticle( aid_art );
  if assigned(item) then
    result := item.can_savepoints = 1;
end;

function TCacheArticleViewer.GetFGTypeByArtID(aid_art : Int64) :Integer;
var item : TCachedArticle;
    id_g : Int64;
begin
  Result := 0;
  item := GetCachedArticle( aid_art );

  if not assigned(item) then exit;

  id_g := item.id_food_group;
  if id_g <= 0 then exit;

  CashFGDataset.IndexName := 'fgIdx';
  if CashFGDataset.FindKey([id_g]) then
    result := CashFGDataset.FieldByName('group_type').AsInteger;
end;

function GetAnyArticleFromBonusGroup( aid_bg : Int64 ) : Int64;
var
  qSel, dep: IIBSQL;
begin
  result := 0;
  if aid_bg = 0 then exit;

  qSel := upos.UntillDB.GetPreparedIIbSql('select ID_ARTICLES '
    + ' from BONUS_GROUPS_ARTICLES '
    + ' where ID_BONUS_GROUPS=:ID_BONUS_GROUPS and is_active=1 ');
  qSel.q.Params[0].AsInt64 := aid_bg;
  qSel.ExecQuery;
  if not qSel.Eof then begin
    result := StrToInt64Def(qSel.Fields[0].AsString, 0);
  end else begin
    // find in linked departments
    dep := upos.UntillDB.GetPreparedIIbSql(
      'select a.id' +
      '  from bonus_groups_articles bga' +
      '  join department d on d.id = bga.id_department and d.is_active = 1' +
      '  join articles a on a.id_departament = d.id and a.is_active = 1' +
      ' where bga.is_active = 1' +
      '   and bga.id_bonus_groups = :id_bonus_groups'
    );
    dep.q.ParamByName('id_bonus_groups').asInt64 := aid_bg;
    dep.ExecQuery;
    if not dep.Eof then
      result := StrToInt64Def(dep.Fields[0].AsString, 0);
  end;
end;

{ TCachePOSEntityProvider }

constructor TCachePOSEntityProvider.Create(AOwner: TComponent);
begin
  inherited;
  ArtCacheList := TDictionary<Int64, TItemRefreshData>.Create;
  DepCacheList := TDictionary<Int64, TDatetime>.Create;
  ClientCacheList := TDictionary<Int64, TDatetime>.Create;
  if not assigned(upos_) then exit;
  FDbEventsController := TDbEventsController.Init(upos.UntillDB, Self, Self.ClassName)
    .AddWatchingTables(['articles', 'SALES_AREA', 'options', 'pua_groups_articles',
      'pua_groups_sales_area', 'department', 'PERIODS', 'clients', 'discount_reasons',
      'untill_users', 'RESTAURANT_VARS']);
  FDbEventsController.Subscribe;
  bStopTimer   := false;
  upos.un.RegisterListener(URL_POS_GENERAL, self);
  upos.un.RegisterListener(URL_POS_GENERAL_CLEAR, self);
end;

destructor TCachePOSEntityProvider.Destroy;
begin
  bStopTimer := true;
  if assigned(upos_) then begin
    upos.un.UnRegisterListener(URL_POS_GENERAL, self);
    upos.un.UnRegisterListener(URL_POS_GENERAL_CLEAR, self);
  end;
  FreeAndNil( DepCacheList );
  FreeAndNil( ArtCacheList );
  FreeAndNil( ClientCacheList );
  inherited;
end;

function TCachePOSEntityProvider.GetArticleIDByDeArtID(
  aid_dep_art: Int64): Int64;
var q       : IIBSQL;
begin
  result := 0;
  if aid_dep_art = 0 then exit;

  q := upos.UntillDB.GetPreparedIIbSql('select id_articles from DEPARTMENT_ARTICLES where id=:id');
  q.q.Params[0].AsInt64 := aid_dep_art;
  q.ExecQuery;
  if q.eof then exit;

  result := StrToInt64Def(q.Fields[0].asString,0);
end;

procedure TCachePOSEntityProvider.SendPosTableChange(id : Int64);
var ue  : TPosTableChanged;
begin
  ue := TPosTableChanged.Create(id, 'bill', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_TABLE_CHANGED, ue);
  finally
    FreeAndNil(ue);
  end;
end;

function TCachePOSEntityProvider.OnDataChanged(tce: TDbDataChangeEvent): boolean;
var trd : TItemRefreshData;
    dt  : TDatetime;
    arr : array of Int64;
begin
  result := false;
  if not assigned(upos_) then exit;

  if SameText(tce.Table, 'RESTAURANT_VARS') then begin
    PosRestaurantSettings.Settings.RefreshSettings;
    SendPosTableChange(-2);
  end else if SameText(tce.Table, 'articles') then begin
    if (tce.RowID = 0) then exit;
    if not ArtCacheList.TryGetValue( tce.RowID, trd ) then begin
      trd.dt := IncMilliSecond( now, ARTILCE_CACHE_REFRESH_INTERVAL );
      trd.typeRD := 0;
      ArtCacheList.Add( tce.RowID, trd);
    end;
  end else if SameText(tce.Table, 'DEPARTMENT_ARTICLES') then begin
    trd.dt := IncMilliSecond( now, ARTILCE_CACHE_REFRESH_INTERVAL );
    trd.typeRD := 1;
    ArtCacheList.AddOrSetValue( GetArticleIDByDeArtID(tce.RowID), trd);
  end else if SameText(tce.Table, 'SALES_AREA') then
    CacheSalesAreasRange.RefreshData
  else if SameText(tce.Table, 'untill_users') then
    CashUsersDatasets(upos.Untilldb)
  else if SameText(tce.Table, 'discount_reasons') then
    CacheDiscountReasons
  else if SameText(tce.Table, 'pua_groups_articles') or SameText(tce.Table, 'pua_groups_sales_area') then
    CachePuaGroups
  else if SameText(tce.Table, 'options') then begin
    CacheOptionsDataset.RefreshData(tce.RowID);
    CacheOptionArticleDatasets(0, tce.RowID);
  end else if SameText(tce.Table, 'department') then begin
    if not DepCacheList.TryGetValue( tce.RowID, dt ) then
      DepCacheList.Add( tce.RowID, IncMilliSecond( now, ARTILCE_CACHE_REFRESH_INTERVAL * 3 ));
  end else if SameText(tce.Table, 'periods') then begin
    CashPeriodDatasets(upos.UntillDB);
    setlength(arr,0);
    CacheArticlePricePeriodsDatasets(arr);
  end else if SameText(tce.Table, 'clients') then begin
    if not ClientCacheList.TryGetValue( tce.RowID, dt ) then
      ClientCacheList.Add( tce.RowID, IncMilliSecond( now, ARTILCE_CACHE_REFRESH_INTERVAL * 3 ));
  end;

  result := true;
end;

procedure TCachePOSEntityProvider.RefreshSpecArtByIDs(aid_arts: array of Int64);
var strScreenGroups : String;
begin
  id_screen_group := '';
  if not (UntillApp.GetPosForm=nil) then
    id_screen_group := UntillApp.GetPosForm.PosScreenGroup;
  strScreenGroups := GetPOSScreenGroup;
  if length(aid_arts)=0 then exit;
  CacheArticlesSAData( aid_arts );
end;

procedure TCachePOSEntityProvider.RefreshSpecArticles( aid_dep : Int64 );
var q   : IIBSQL;
    trd : TItemRefreshData;
    id_art : Int64;
begin
  q := upos.UntillDB.GetPreparedIIbSql('select id_articles from DEPARTMENT_ARTICLES where ID_DEPARTMENT=:id_dep');
  q.q.params[0].asInt64 := aid_dep;
  q.ExecQuery;
  while not q.eof do begin
    id_art := StrToInt64Def(q.q.Fields[0].AsString,0);
    if not ArtCacheList.TryGetValue( id_art, trd )  then begin
      trd.dt := IncMilliSecond( now, ARTILCE_CACHE_REFRESH_INTERVAL );
      trd.typeRD := 1;
      ArtCacheList.Add(id_art , trd);
    end;
    q.Next;
  end;

end;

procedure TCachePOSEntityProvider.OnListenerException(e: Exception);
begin

end;

function TCachePOSEntityProvider.OnReset: boolean;
begin
  result:=true;
end;

procedure TCachePOSEntityProvider.OnUrlEvent(url: String; ue: TUrlEvent);
begin
  if bStopTimer then exit;
  if (ue is TPMClearChangeFlags) then begin
    if RefreshDepCacheByIDs then begin
      SendUpdateDep;
      SendUpdate;
    end;
    if RefreshArticleCacheByIDs then
      SendUpdate;
    if RefreshClientCacheByIDs then
      SendUpdate;
  end;
end;

procedure TCachePOSEntityProvider.SendUpdate;
var  e : TPMRefreshDataset;
begin
  e := TPMRefreshDataset.Create(0, '', uetUpdate);
  try
    UPos.Un.SendEvent(URL_POS_GENERAL, e);
  finally
    FreeAndNil(e);
  end;
end;

procedure TCachePOSEntityProvider.RefreshArtByIDs( aid_arts: Array of Int64 );
begin
  CacheArticleBOData( aid_arts );
end;

function TCachePOSEntityProvider.RefreshArticleCacheByIDs : boolean;
var id_art : Int64;
    trd    : TItemRefreshData;
    arr1    : Array of Int64;
    arr2    : Array of Int64;
    i      : Integer;
begin
  result := false;
  if not assigned(ArtCacheList) then exit;
  if ArtCacheList.Count=0 then exit;

  setlength(arr1, 0);
  setlength(arr2, 0);
  for id_art in ArtCacheList.Keys do begin
    if ArtCacheList.TryGetValue(id_art, trd) then begin
      if trd.typeRD = 0 then begin
        if trd.dt <= now then begin
          setlength(arr1,length(arr1) + 1);
          arr1[length(arr1)-1] := id_art;
        end;
      end else if trd.typeRD = 1 then begin
        if trd.dt <= now then begin
          setlength(arr2,length(arr2) + 1);
          arr2[length(arr2)-1] := id_art;
        end;
      end;
    end;
  end;
  if (length(arr1) = 0) and (length(arr2) = 0) then exit;

  if length(arr1)>0 then begin
    for i := 0 to length(arr1)-1 do
      ArtCacheList.Remove(arr1[i]);
    RefreshArtByIDs( arr1 );
  end;

  if length(arr2)>0 then begin
    for i := 0 to length(arr2)-1 do
      ArtCacheList.Remove(arr2[i]);
    RefreshSpecArtByIDs( arr2 );
  end;

  result := true;
end;

function TCachePOSEntityProvider.RefreshDepCacheByIDs: boolean;
var id_dep : Int64;
    dt     : TDatetime;
    arr    : Array of Int64;
    i       : Integer;
begin
  result := false;
  if not assigned(DepCacheList) then exit;
  if DepCacheList.Count=0 then

  setlength(arr, 0);
  for id_dep in DepCacheList.Keys do begin
    if DepCacheList.TryGetValue(id_dep, dt) then begin
      if dt <= now then begin
        setlength(arr,length(arr) + 1);
        arr[length(arr)-1] := id_dep;
      end;
    end;
  end;
  if length(arr) = 0 then exit;

  for i := 0 to length(arr) - 1 do begin
    DepCacheList.Remove(arr[i]);
    CacheDepartments( GetPOSScreenGroup, arr[i] );
    RefreshSpecArticles( arr[i] );
  end;
  result := true;

end;

procedure TCachePOSEntityProvider.UpdateClientNamesOnTables(id_clients : Int64);
var id_bill : Int64;
    q : IIBSQL;
begin
  if id_clients<=0 then exit;

  q := upos.UntillDB.GetPreparedIIbSql('select bill.id '
    + ' from bill'
    + ' where close_datetime is null and id_clients = :id_clients');
  q.q.Params[0].AsInt64 := id_clients;
  q.ExecQuery;
  if q.Eof then exit;

  while not q.eof do begin
    id_bill := StrToInt64Def(q.Fields[0].AsString,0);
    if id_bill<=0 then exit;
    SendPosTableChange(id_bill);
    q.Next;
  end;
end;

function TCachePOSEntityProvider.RefreshClientCacheByIDs: boolean;
var id_client : Int64;
    dt        : TDatetime;
    arr       : Array of Int64;
    i         : Integer;
begin
  result := false;
  if not assigned(ClientCacheList) then exit;
  if ClientCacheList.Count=0 then

  setlength(arr, 0);
  for id_Client in ClientCacheList.Keys do begin
    if ClientCacheList.TryGetValue(id_Client, dt) then begin
      if dt <= now then begin
        setlength(arr,length(arr) + 1);
        arr[length(arr)-1] := id_Client;
      end;
    end;
  end;
  if length(arr) = 0 then exit;

  for i := 0 to length(arr) - 1 do begin
    ClientCacheList.Remove(arr[i]);
    CacheClients( arr[i] );
    UpdateClientNamesOnTables(arr[i]);
  end;
  result := true;
end;
{ TCacheOPTs }

constructor TCacheOPTs.Create;
begin
  FCacheOPTDatas := TObjectDictionary<Int64, Integer>.Create;
end;

destructor TCacheOPTs.Destroy;
begin
  FreeAndNil( FCacheOPTDatas );
  inherited;
end;

function TCacheOPTs.GetOPTData(AID_OPT: Int64): Integer;
begin
  result := 0;
  if AID_OPT = 0 then exit;

  FCacheOPTDatas.TryGetValue(AID_OPT, result);
end;

procedure TCacheOPTs.RefreshData;
var q : IIBSQL;
begin
  FCacheOPTDatas.Clear;
  q := upos.UntillDB.GetPreparedIIbSql('select id, option_number'
    + ' from article_options '
    + ' where article_options.is_active = 1');
  q.ExecQuery;
  while not q.eof do begin
    FCacheOPTDatas.Add( StrToInt64Def(q.q.Fields[0].AsString,0), q.q.Fields[1].AsInteger );
    q.next;
  end;;
end;

{ TCacheOptionsDataset }

constructor TCacheOptionsDataset.Create;
begin
  list := TDictionary<Int64, TCacheOption>.Create;
end;

destructor TCacheOptionsDataset.Destroy;
begin
  FreeAndNil( list );
  inherited;
end;

function TCacheOptionsDataset.GetOptionName(aid: Int64): String;
var rec : TCacheOption;
begin
  Result := '';
  if aid=0 then exit;
  if list.TryGetValue(aid, rec) then
    result := rec.name;
end;

function TCacheOptionsDataset.GetOptSinglePrep(aid :Int64) :Integer;
var rec : TCacheOption;
begin
  Result := 0;
  if aid=0 then exit;
  if list.TryGetValue(aid, rec) then
    result := rec.single_prep;
end;


procedure TCacheOptionsDataset.RefreshData( aid : Int64 =0);
var q       : IIBSQL;
    sqlstr  : string;
    rec     : TCacheOption;
begin
  if aid=0 then
    list.clear
  else
    list.Remove(aid);
  sqlstr := 'select id, single_prep, name from options where is_active=1';
  if aid > 0 then
    sqlstr := sqlstr + ' and id = ' + IntToStr(aid);
  q := upos.UntillDB.GetPreparedIIbSql( sqlstr );
  q.ExecQuery;
  while not q.Eof do begin
    aid := StrToInt64Def(q.q.fields[0].AsString,0);
    rec.single_prep := q.q.fields[1].AsInteger;
    rec.name        := q.q.fields[2].AsString;
    list.AddOrSetValue(aid, rec);
    q.next;
  end;
end;

{ TCachePricesDataset }

constructor TCachePricesDataset.Create;
begin
  list := TDictionary<Int64, String>.Create
end;

destructor TCachePricesDataset.Destroy;
begin
  FreeAndNil(list);
  inherited;
end;

function TCachePricesDataset.GetAnyId: Int64;
var id : Int64;
begin
  result := 0;
  if list.count = 0 then exit;

  for id in list.keys do begin
    result := id;
    exit;
  end;

end;

function TCachePricesDataset.GetCashPriceName(aid :Int64) :String;
var str, num : string;
begin
  Result := '';
  if aid = 0 then exit;
  list.TryGetValue(aid, str);
  StrSplit(str, ';', result, num);
end;

function TCachePricesDataset.GetCashPriceNumber(aid :Int64) :Integer;
var str, num, resnum : string;
begin
  Result := 0;
  if aid = 0 then exit;
  list.TryGetValue(aid, str);
  StrSplit(str, ';', num, resnum);
  result := StrToIntDef(resnum,0);
end;

procedure TCachePricesDataset.RefreshData(aid: Int64);
var q       : IIBSQL;
    sqlstr  : string;
begin
  if aid=0 then
    list.clear
  else
    list.Remove(aid);
  sqlstr := 'select id, name, number from prices where is_active=1';
  if aid > 0 then
    sqlstr := sqlstr + ' and id = ' + IntToStr(aid);
  q := upos.UntillDB.GetPreparedIIbSql( sqlstr );
  q.ExecQuery;
  while not q.Eof do begin
    aid := StrToInt64Def(q.q.fields[0].AsString,0);
    list.AddOrSetValue(aid, q.q.fields[1].AsString+';'+q.q.fields[2].AsString);
    q.next;
  end;
end;

{ TcdsDepFonts }

procedure TcdsDepFonts.DeleteByDepID(aid_dep: Int64);
begin
  if aid_dep <= 0 then exit;
  IndexName := 'cfdepIndex';
  while FindKey([aid_dep]) do
    Delete;
end;

{ TCashDepartmentDataSet }

procedure TCashDepartmentDataSet.DeleteByDepID(aid_dep: Int64);
begin
  if aid_dep <= 0 then exit;
  IndexName := 'didIndex3';
  while FindKey([aid_dep]) do
    Delete;
end;

{ TKSPURefershParams }

procedure TKSPURefershParams.Reset;
begin
  id_ksc  := 0 ;
  max_rec := 0;
  Status  := kswsNotStart;
  dt := 0;
end;

{ TKSStageData }

procedure TKSStageData.Clear;
begin
  ID_STAGE      := 0;
  PREP_TIME_MIN := 0;
  PREP_TIME_SEC := 0;
  WARNING_MIN   := 0;
  ID_KITCHEN_SCREENS := 0;
  KS_PURPOSE    := 0;
end;

{ TTAKSData }

procedure TTAKSData.Clear;
begin
  id_ta      := 0;
  id_ks_from := 0;
  id_ks_to   := 0;
  id_periods := 0;
end;

{ TCacheKSDataList }

procedure TCacheKSDataList.Clear;
begin
  list.Clear
end;

constructor TCacheKSDataList.Create;
begin
  list := TDictionary<Int64, TKSData>.Create;
end;

destructor TCacheKSDataList.Destroy;
begin
  FreeAndNil(list);
  inherited;
end;

function TCacheKSDataList.GetDataByID(aidks : Int64): TKSData;
var ksd : TKSData;
begin
  result.Clear;
  if not list.TryGetValue(aidks, ksd) then exit;
  result := ksd;
end;

function TCacheKSDataList.GetKSIDByName(ks_name: String): Int64;
var ksd : TKSData;
    id  : Int64;
begin
  Result := 0;
  for id in CashKSDataset.list.keys do begin
    if CashKSDataset.list.TryGetValue(id, ksd) then begin
      if SameText(ks_name, ksd.ks_name) then begin
        result := id;
        exit;
      end;
    end;
  end;
end;

function TCacheKSDataList.GetKSIDByNumber(aksnumber: Integer): Int64;
var id : Int64;
    ksd : TKSData;
begin
  result := 0;
  if aksnumber<=0 then exit;

  for id in list.keys do begin
    list.TryGetValue(id, ksd);
    if ksd.ks_number=aksnumber then begin
      result := id;
      exit;
    end;
  end;
end;

function TCacheKSDataList.GetKSNameByNumber(ks_number: Integer): String;
var ksd : TKSData;
    id  : Int64;
begin
  Result := '';
  for id in CashKSDataset.list.keys do begin
    if CashKSDataset.list.TryGetValue(id, ksd) then begin
      if ks_number=ksd.ks_number then begin
        result := ksd.ks_name;
        exit;
      end;
    end;
  end;
end;

function TCacheKSDataList.GetKSNumberByName(ks_name: String): Integer;
var ksd : TKSData;
    id  : Int64;
begin
  Result := 0;
  for id in CashKSDataset.list.keys do begin
    if CashKSDataset.list.TryGetValue(id, ksd) then begin
      if SameText(ks_name, ksd.ks_name) then begin
        result := ksd.ks_number;
        exit;
      end;
    end;
  end;
end;

procedure TCacheKSDataList.RefreshData;
var iq, q : IIBSQL;
    id_ks : Int64;
    avCapacity : Integer;
    ksd : TKSData;
begin
  iq := upos.UntillDB.GetPreparedIIbSql('select kitchen_screens.*, '
    + ' (select first 1 id_ks_to from kitchen_screens_link where kitchen_screens_link.id_ks_from=kitchen_screens.id) id_ks_linked '
    + ' from kitchen_screens where is_active=1');
  iq.ExecQuery;
  CashKSDataSet.Clear;
  KSCacheOverviewList.Clear;
  while not iq.eof do  begin
     id_ks := StrToInt64Def(iq.FieldByName('id').asString,0);
     q := upos.UntillDB.GetPreparedIIbSql('select sum(kitchen_screens.capacity), count(kitchen_screens.capacity) '
      + ' from kitchen_screens_substations '
      + ' join kitchen_screens on kitchen_screens.id=kitchen_screens_substations.id_ks_sub and kitchen_screens.is_active = 1 '
      + ' where coalesce(kitchen_screens_substations.status,0)=0 and kitchen_screens_substations.is_active=1'
      + ' and kitchen_screens_substations.id_ks_main =:id_ks_main'
      );
     q.q.ParamByName('id_ks_main').AsInt64 := id_ks;
     q.ExecQuery;

     avCapacity := 0;
     if not q.eof then
       if q.fields[1].asInteger >0 then
         avCapacity := q.fields[0].asInteger div q.fields[1].asInteger;

     ksd.ks_number         := iq.FieldByName('KS_NUMBER').asInteger;
     ksd.ks_name           := iq.FieldByName('KS_NAME').asString;
     ksd.ks_short_name     := iq.FieldByName('KS_SUB_NAME').asString;
     ksd.ks_type           := iq.FieldByName('KS_TYPE').asInteger;
     ksd.sort_type         := iq.FieldByName('SORT_TYPE').asInteger;
     ksd.articles_type     := iq.FieldByName('ARTICLES_TYPE').asInteger;
     ksd.list_count        := iq.FieldByName('LIST_COUNT').asInteger;
     ksd.list_count_ta     := iq.FieldByName('LIST_COUNT_TA').asInteger;
     ksd.active_first      := iq.FieldByName('active_first').asInteger;
     ksd.language          := iq.FieldByName('language').asString;
     ksd.id_ks_linked      := StrToInt64Def(iq.FieldByName('id_ks_linked').asString,0);
     ksd.sort_items_type   := iq.FieldByName('SORT_ITEMS_TYPE').asInteger;
     ksd.hide_complete     := iq.FieldByName('hide_complete').asInteger;
     ksd.msg_seconds       := iq.FieldByName('msg_seconds').asInteger;
     ksd.device_type       := iq.FieldByName('device_type').asInteger;
     ksd.capacity          := iq.FieldByName('capacity').asInteger;
     ksd.level             := iq.FieldByName('level').asInteger;
     ksd.subset            := iq.FieldByName('subset').asInteger;
     ksd.decons_pu_items   := iq.FieldByName('decons_pu_items').asInteger;
     ksd.allergen_type     := iq.FieldByName('allergen_type').asInteger;
     ksd.keep_warn         := iq.FieldByName('keep_warn').asInteger=1;
     ksd.show_non_separate_articles := iq.FieldByName('show_non_separate_articles').asInteger=1;
     ksd.color             := iq.FieldByName('color').asInteger;
     ksd.short_name            := iq.FieldByName('short_name').asString;
     ksd.average_capacity  := avCapacity;
     if (iq.FieldByName('level').asInteger = 0)
       and (iq.FieldByName('device_type').asInteger = Ord(ksdtMainSubstation)) then
       if (KSCacheOverviewList.IndexOf(id_ks)<0) then
         KSCacheOverviewList.Add( id_ks );

     CashKSDataset.list.AddOrSetValue( id_ks, ksd);
     iq.Next;
  end;
end;

{ TCacheKSCDataList }

procedure TCacheKSCDataList.Clear;
begin
  list.Clear
end;

constructor TCacheKSCDataList.Create;
begin
  list := TDictionary<Int64, TKSCData>.Create;
end;

destructor TCacheKSCDataList.Destroy;
begin
  FreeAndNil(list);
  inherited;
end;

function TCacheKSCDataList.GetDataByID(aidksc: Int64): TKSCData;
var kscd : TKSCData;
begin
  result.Clear;
  if aidksc=0 then exit;

  if not list.TryGetValue(aidksc, kscd) then exit;
  result := kscd;
end;

function TCacheKSCDataList.GetKSCShowPending(aidksc : Int64): boolean;
var kscd : TKSCData;
begin
  result := false;
  if aidksc=0 then exit;

  kscd := CashKSCDataset.GetDataByID(aidksc);
  result := kscd.show_pending=1;
end;

function TCacheKSCDataList.GetKSCShowFinished(aidksc : Int64): boolean;
var kscd : TKSCData;
begin
  result := false;
  if aidksc=0 then exit;
  kscd := CashKSCDataset.GetDataByID(aidksc);
  result := kscd.show_finished = 1;
end;

function TCacheKSCDataList.GetKSCType(aidksc : Int64): Integer;
var kscd : TKSCData;
begin
  result := 0;
  if aidksc=0 then exit;
  kscd := CashKSCDataset.GetDataByID(aidksc);
  result := kscd.KSC_TYPE;
end;

procedure TCacheKSCDataList.RefreshData;
var iq     : IIBSQL;
    id_ksc : Int64;
    ksdc   : TKSCData;
begin
  iq := upos.UntillDB.GetPreparedIIBSQL('select * from KITCHEN_SCREEN_COUNTERS where is_active=1');
  iq.ExecQuery;
  CashKSCDataSet.Clear;
  while not iq.eof do  begin
    id_ksc := StrToInt64Def(iq.FieldByName('id').asString,0);
    ksdc.KSC_NUMBER        := iq.FieldByName('KSC_NUMBER').asInteger;
    ksdc.KSC_NAME          := iq.FieldByName('KSC_NAME').asString;
    ksdc.KSC_LIST_COUNT    := iq.FieldByName('KSC_LIST_COUNT').asInteger;
    ksdc.KSC_SHOW_OPTIONS  := iq.FieldByName('show_options').asInteger;
    ksdc.KSC_CHAIRS        := iq.FieldByName('consolidate_chairs').asInteger;
    ksdc.KSC_SPLIT_COURSES := iq.FieldByName('SPLIT_COURSES').asInteger;
    ksdc.show_information  := iq.FieldByName('show_information').asInteger;
    ksdc.show_pending      := iq.FieldByName('show_pending').asInteger;
    ksdc.show_finished     := iq.FieldByName('show_finished').asInteger;
    ksdc.KSC_TYPE          := iq.FieldByName('pu_type').asInteger;
    CashKSCDataSet.list.AddOrSetValue(id_ksc, ksdc);
    iq.Next;
  end;
end;

{ TKSData }

procedure TKSData.Clear;
begin
  ks_number     := 0;
  ks_name       := '';
  ks_short_name := '';
  ks_type       := 0;
  sort_type     := 0;
  articles_type := 0;
  list_count    := 0;
  active_first  := 0;
  language      := '';
  id_ks_linked  := 0;
  sort_items_type := 0;
  hide_complete := 0;
  msg_seconds   := 0;
  device_type   := 0;
  level         := 0;
  capacity      := 0;
  subset        := 0;
  list_count_ta := 0;
  average_capacity := 0;
  decons_pu_items  := 0;
  short_name    := '';
  allergen_type    := 0;
  keep_warn     := false;
  show_non_separate_articles := false;
  color         := 0;
end;

{ TKSCData }

procedure TKSCData.Clear;
begin
  KSC_NUMBER       := 0;
  KSC_NAME         := '';
  KSC_LIST_COUNT   := 0;
  KSC_SHOW_OPTIONS := 0;
  KSC_CHAIRS       := 0;
  KSC_SPLIT_COURSES:= 0;
  show_information := 0;
  KSC_TYPE         := 0;
  show_pending     := 0;
  show_finished    := 0;
end;

{ TCacheClientExcepts }

procedure TCacheClientExcepts.Clear;
var item : TObjectDictionary<Int64, Int64>;
begin
  for item in list.Values do
    if assigned(item) then
      item.Free;
  list.Clear;
end;

constructor TCacheClientExcepts.Create;
begin
  list := TObjectDictionary<Int64, TObjectDictionary<Int64, Int64>>.Create;
end;

destructor TCacheClientExcepts.Destroy;
begin
  Clear;
  FreeAndNil( list );
  inherited;
end;

function TCacheClientExcepts.GetPriceByDate(aid_client: Int64;
  adt: TDatetime): Int64;
var item : TObjectDictionary<Int64, Int64>;
    id_periods, id_prices : Int64;
begin
  result := 0;
  if aid_client = 0 then exit;

  if not list.TryGetValue(aid_client, item) then exit;
  for id_periods in item.keys do begin
    if IsDateInPeriod(adt, id_periods) then begin
      if item.TryGetValue(id_periods, id_prices) then begin
        result := id_prices;
        exit;
      end;
    end;
  end;
end;

procedure TCacheClientExcepts.RefreshData;
var q : IIBSQL;
    ID_CLIENTS, id_prices, id_periods : Int64;
    periodItem : TObjectDictionary<Int64, Int64>;
begin
  Clear;
  q := upos.UntillDB.GetPreparedIIbSql('select distinct ID_CLIENTS, id_prices, id_periods '
    + ' from client_exceptions  '
    + ' join prices on prices.id=client_exceptions.id_prices and prices.is_active=1 '
    + ' join periods on periods.id=client_exceptions.id_periods and periods.is_active=1 '
    + ' where client_exceptions.is_active=1 order by ID_CLIENTS' );
  q.ExecQuery;

  while not q.eof do begin
    ID_CLIENTS := StrToInt64Def(q.q.Fields[0].asString,0);
    if not list.TryGetValue( ID_CLIENTS, periodItem ) then begin
      periodItem := TObjectDictionary<Int64, Int64>.Create;
      list.Add(ID_CLIENTS, periodItem);
    end;
    if assigned(periodItem) then begin
      id_periods := StrToInt64Def(q.q.Fields[2].asString,0);
      id_prices  := StrToInt64Def(q.q.Fields[1].asString,0);
      periodItem.Add( id_periods,  id_prices );
    end;

    q.next;
  end;
end;

{ TCashReasonDataset }

constructor TCashReasonDataset.Create(AOwner: TComponent);
begin
  inherited;
  Fver := 0;
end;

end.
