ALTER TABLE AKS_ITEM_ENTRIES DROP CONSTRAINT FK_AKS_ITEM_ENTRIES_2;
ALTER TABLE AKS_ITEM_ENTRIES DROP CONSTRAINT FK_AKS_ITEM_ENTRIES_4;
ALTER TABLE AKS_ITEM_ENTRIES DROP CONSTRAINT FK_AKS_ITEM_ENTRIES_7;
ALTER TABLE AKS_ITEM_ENTRIES DROP CONSTRAINT FK_AKS_ITEM_ENTRIES_6;
CREATE INDEX AKS_ITEM_ENTRIES_IDX4 ON AKS_ITEM_ENTRIES (ID_BILL_CURRENT);
update aks_item_entries set quantity = 0 where quantity is null;
commit;
