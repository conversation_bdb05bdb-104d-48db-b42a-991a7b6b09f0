create table skins (
    id u_id,
    name varchar(50),
    comments varchar(250),
    content blob,
    caption varchar(50),
    screen_type integer,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint skins_pk primary key (id)
);
commit;
grant all on skins to untilluser;
commit;
execute procedure register_sync_table_ex('skins', 'b', 1);
commit;
execute procedure register_bo_table('skins', '', '');
commit;
