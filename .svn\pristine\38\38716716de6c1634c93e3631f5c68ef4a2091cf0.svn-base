inherited FunctionalButtonsDataSetParamsFrame: TFunctionalButtonsDataSetParamsFrame
  Width = 290
  Height = 205
  ExplicitWidth = 290
  ExplicitHeight = 205
  object UntillPanel1: TUntillPanel
    Left = 0
    Top = 0
    Width = 290
    Height = 205
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    BorderColor = clBlack
    object lblBtnGroup: TTntLabel
      Left = 16
      Top = 16
      Width = 72
      Height = 13
      Caption = 'Buttons group:'
    end
    object lblIdx: TTntLabel
      Left = 16
      Top = 72
      Width = 62
      Height = 13
      Caption = 'Group index:'
    end
    object edtSelButtonsGroup: TUntillSelectBox
      Left = 16
      Top = 34
      Width = 257
      Height = 21
      ReadOnly = True
      RightButton.DisabledImageIndex = 1
      RightButton.HotImageIndex = 2
      RightButton.ImageIndex = 0
      RightButton.Visible = True
      TabOrder = 0
      OnButtonClick = edtSelButtonsGroupButtonClick
      SelStart = 0
      SelLength = 0
    end
    object edtIdx: TUntillSpinEdit
      Left = 194
      Top = 65
      Width = 79
      Height = 21
      ParentBackground = False
      TabStop = True
      TabOrder = 1
      Alignment = taLeftJustify
      Value = 0
      MaxValue = 100
      MinValue = 0
      MaxLength = 1
    end
    object rbKeep: TTntRadioButton
      Left = 16
      Top = 132
      Width = 257
      Height = 21
      Caption = 'Keep button selection on start'
      TabOrder = 3
    end
    object rbSome: TTntRadioButton
      Left = 16
      Top = 102
      Width = 257
      Height = 21
      Caption = 'Do not keep button selection on start'
      Checked = True
      TabOrder = 2
      TabStop = True
    end
    object rbNever: TTntRadioButton
      Left = 16
      Top = 163
      Width = 257
      Height = 21
      Caption = 'All article allergens'
      TabOrder = 4
    end
  end
end
