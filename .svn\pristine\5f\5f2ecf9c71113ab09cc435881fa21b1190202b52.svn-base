unit KSMessageU;

interface
uses U<PERSON><PERSON>ot<PERSON>U, Classes, UntillDBU, ClassesU, RetranslatorU,
     BLAlgU, SysUtils, UntillPOSU;

const URL_KS_ACTIVATE_MESSAGES : String = 'URL_KS_ACTIVATE_MESSAGES';
type
  // Event, which sends to POS.UN listener, when kitchen screen messages are activated
  TActivateKSMessagesEvent = class(TUrlEvent);

  // Object is watching for new Kicthen screen messages of any type
  //  ...and refreshes
  TKSMessageListener  = class(TComponent, IUrlListener, IDbEventListener)
  private
    FDbEventsController      : IDbEventsController;
    procedure ActivateCheckKSMessages;
    procedure   OnUrlEvent(url: String; ue: TUrlEvent);
    procedure Initialize;
  protected
    function    OnDataChanged(tce: TDbDataChangeEvent):boolean;
    function    OnReset():boolean;
    procedure   OnListenerException(e: Exception);
  public
    constructor Create(AOwner: TComponent); override;
    destructor  Destroy; override;
  end;

var KSMessageListener: TKSMessageListener;

procedure KSMessagePosIniFinit(bInit: boolean);

implementation

{ TKSMessageListener }

procedure TKSMessageListener.Initialize;
begin
  FDbEventsController.Subscribe;
end;

constructor TKSMessageListener.Create(AOwner: TComponent);
begin
  inherited;
  if not assigned(upos_) then exit;

  FDbEventsController:=TDbEventsController.Init(upos.UntillDB, Self, Self.ClassName)
    .AddWatchingTables(['ks_messages']);
  upos.un.RegisterListener(URL_POS_GENERAL, self);
end;
// Stop checking KS messages in Kicthen screens
destructor TKSMessageListener.Destroy;
begin
  upos.un.UnRegisterListener(URL_POS_GENERAL, self);
  inherited;
end;

procedure  TKSMessageListener.ActivateCheckKSMessages;
var ue : TActivateKSMessagesEvent;
begin
  // Send message to upos.un - global pos listener list
  ue:=TActivateKSMessagesEvent.Create(0, '', uetUpdate);
  try
    upos.un.SendEvent(URL_KS_ACTIVATE_MESSAGES, ue);
  finally
    FreeAndNil(ue);
  end;
end;

function TKSMessageListener.OnDataChanged(tce: TDbDataChangeEvent): boolean;
begin
  ActivateCheckKSMessages;
  result := true;
end;

procedure TKSMessageListener.OnListenerException(e: Exception);
begin

end;

function TKSMessageListener.OnReset: boolean;
begin
  ActivateCheckKSMessages;
  result := true;
end;

procedure TKSMessageListener.OnUrlEvent(url: String; ue: TUrlEvent);
begin
  if ue is TScreensLoadedEvent then
    Initialize;
end;

// Activates checking of KS Messages in Kicthen screen
procedure KSMessagePosIniFinit(bInit: boolean);
begin
  if bInit then begin
    // During Untill POS initialization - create Global Listener KSMessageListener
    KSMessageListener := TKSMessageListener.Create(nil);
  end else begin
    // During Untill POS de-initialization - destroy Global Listener KSMessageListener
    FreeAndNil(KSMessageListener);
  end;
end;

end.
