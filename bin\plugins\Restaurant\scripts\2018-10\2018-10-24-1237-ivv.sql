create table card_bill (
    id u_id,
    id_smartcards bigint,
    id_bill   bigint,
    constraint card_bill_pk primary key (id),
    constraint card_bill_fk1 foreign key (id_smartcards) references smartcards (id),
    constraint card_bill_fk2 foreign key (id_bill) references bill (id)
);
commit;
grant all on card_bill to untilluser;
commit;
execute procedure register_sync_table_ex('card_bill', 'p', 1);
commit;

