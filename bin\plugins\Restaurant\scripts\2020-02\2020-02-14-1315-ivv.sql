insert into ARTICLE_SIZE_MODIFIER (id_articles, id_size_modifier_item, is_active)
select distinct id_articles, article_size_modifier_price.id_size_modifier_item, 1 from article_size_modifier_price
join article_prices on article_prices.id=article_size_modifier_price.id_article_prices
join articles on articles.id=article_prices.id_articles
where not exists (select * from article_size_modifier where article_size_modifier.id_articles=article_prices.id_articles);

commit;

