set term !! ;
create or alter procedure fix_clientcard_name
as 
  declare variable server_no integer;
  declare variable idcn bigint;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no is null) then exit;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;
        
    delete from client_cards where ID_CLIENT_CARDS_NAME is null;

    insert into CLIENT_CARD_NAMES(CARD_NAME, is_active)
    values('Default client cards',1);

  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_clientcard_name to untilluser
!!
execute procedure fix_clientcard_name
!!
commit
!!
set term ; !!


set term !! ;
create or alter procedure fix_clientcard_tables
as 
  declare variable server_no integer;
  declare variable idcn bigint;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no is null) then exit;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;

    select first 1 id from CLIENT_CARD_NAMES where CARD_NAME = 'Default client cards'  and is_active=1 into :idcn;
    if (:idcn > 0) then begin
      insert into client_cards (id_clients, item_number, card_number, ID_CLIENT_CARDS_NAME, is_active)
      select id, 1, card, :idcn, 1 from clients
      where (not (card is null)) and (char_length(card) > 0);
    end

  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_clientcard_tables to untilluser
!!
execute procedure fix_clientcard_tables
!!
commit
!!
set term ; !!


