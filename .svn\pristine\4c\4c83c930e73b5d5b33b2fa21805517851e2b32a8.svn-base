unit HotelConceptsU;

interface
uses SysUtils, IdTCPClient, UntillLogsU, UntillDBU, DevicesU, ProgressU, BLRBillU,
HotelConceptsIntfU, HotelConceptsImplU, Generics.Collections;

type

  TSaveDataProcedure = procedure(source: string; data: string) of object;

  EHCException = class(Exception);

  THotelGuestHC = class(THotelGuest)
  private
    FStatus: THCGuestStatus;
    procedure SetStatus(const Value: THCGuestStatus);
  public
    property Status: THCGuestStatus read FStatus write SetStatus;
  end;

  TDetailedPosting = record
    PLU           : Integer;
    Article       : String;
    Amount        : Integer;
    Vat           : Integer;
    Quantity      : Integer;
    PaymentMethod : Byte;
    VATCode       : Byte;
    SalesAreaNum  : Integer;
  end;

  TConsolidatedPosting = record
    Amount        : Integer;
    Vat           : Integer;
    PaymentMethod : Byte;
    SalesAreaNum  : Integer;
  end;

  PHCPacket = ^THCPacket;
  THCPacket = record
    Command         : String[2];
    TerminalNumber  : String[4];
    RoomNumber      : String[5];
    FolioSequence   : String[2];
    FolioNumber     : String[10];
    GuestName       : String[30];
    PLU             : String[8];
    Article         : UTF8String;
    Amount          : String[10];
    VAT1            : String[10];
    VAT2            : String[10];
    VAT3            : String[10];
    VAT4            : String[10];
    Response        : String[2];
    Ticket          : String[10];
    MealPeriod      : String[2];
    SequenceNumber  : String[1];
    ArticleQuantity : String[4];
    RevenueCenter   : String[8];
    PaymentMethod   : String[2];
    VATCode         : String[2];
    AmountDiscount  : String[10];
    AmountTip       : String[10];
    AmountTax       : String[10];
    AmountService   : String[10];
  end;

  TCommPacket = array[1..215] of byte;

  THotelConceptsLink = class
  private
    FTCPClient  : TIdTCPClient;
    FLeadingStr : String;
    FTerminal   : String;
    FSaveDataProc: TSaveDataProcedure;
    FKeepAlive  : Boolean;
    FHost       : String;
    FPort       : Integer;

    function    IsEmulate: Boolean;

    function    PacketToString(Packet: PHCPacket): AnsiString;
    procedure   StringToPacket(data: AnsiString; Packet: PHCPacket);
    procedure   SendReceive(Packet: PHCPacket);
    procedure   SetTerminal(const Value: String);
    procedure   FixFolioNumber(Packet: PHCPacket);

    function    ByteToStr(b: byte): string;
    function    ArrToStr(b: TCommPacket): string;
    procedure   SetKeepAlive(const Value: Boolean);

  protected

{    function    EmulateGuest(RoomNumber: Word; FolioSequence: Word=0; FolioNumber: String=''): THotelGuestHC; overload;
    procedure   EmulateCharge(Client: THotelGuestHC;  Posting: TDetailedPosting); overload;
    procedure   EmulateCharge(Client: THotelGuestHC;  Posting: TConsolidatedPosting); overload;}
    procedure   EmulateRequest(Pack: TCommPacket);
    procedure   EmulateResponse(Packet: PHCPacket);
    function    GetVatCode(VatPercent: Currency): String;
    procedure   CheckClient;

  public
    constructor Create(ATerminal: String; Host: String; Port: Integer; LeadingStr: String; SaveDataProc: TSaveDataProcedure=nil);
    function    GetGuest(RoomNumber: String; FolioSequence: Word): THotelGuestHC;
    function    GetGuestByFolio(FolioNumber: String): THotelGuestHC;
    procedure   Charge(Client: THotelGuestHC;  Posting: TDetailedPosting; VatPercent: Double); overload;
    procedure   Charge(Client: THotelGuestHC;  Posting: TConsolidatedPosting); overload;
    procedure   ChargeEODTurnoverItem(Posting: TEODTurnoverItem);
    procedure   ChargeEODPaymentItem(Posting: TEODPaymentItem);
    destructor  Destroy; override;
    property    Terminal: String read FTerminal write SetTerminal;
    property    KeepAlive: Boolean read FKeepAlive write SetKeepAlive;
  end;

  THCLink = class(THotelwareLink)
  private
    cp: TConsolidatedPosting;
    dp: TDetailedPosting;
    hc:THotelConceptsLink;
    guest: THotelGuestHC;
    procedure CheckClient();
    procedure ChargeConsolidated(Context: THotelBillPaymentContext);
    procedure ChargeDetailedItem(Context: THotelBillPaymentContext; Article: String; Qty, Amount, Vat: Integer; PaymentNumber: Byte; SalesAreaNum: Integer; PLU: Integer; VatPercent: Currency);
    procedure ChargeMenuItems(Context: THotelBillPaymentContext; id_menu: Int64; parent_qty: Integer; SalesAreaNum: Integer);
    procedure ChargeDetailed(Context: THotelBillPaymentContext);
  protected
    procedure SaveDBInfo(Cred: THotelCredentials; Context: THotelBillPaymentContext); override;
  public
    procedure Charge(Context: THotelBillPaymentContext); override;
    function  GetGuestInfo(Request: THotelwareGuestRequest;
                out NotValidMsg: WideString): TObjectList<THotelGuest>; override;
    procedure EndOfDay(conf: THCDeviceConfiguration; Turnover: TList<TEODTurnoverItem>;
    		Payments: TList<TEODPaymentItem>; Extras: TList<TEODExtraItem>; sp: IShowProgress); override;

  end;

function CreateHotelwareDevice(db: TUntillDB; conf: THCDeviceConfiguration): THotelwareLink;
function IsHotelConceptsInstalled(db: TUntillDB): boolean;
function GetCurrentPCHotelDevice(db: TUntillDB; PaymentId: Int64=0; Params: String=''): THCDeviceConfiguration;
function CorrectFolioNumber(db: TUntillDB; FolioNumber: String; out OutDeviceId: Int64): boolean;
function GetHCClient(db: TUntillDB; RoomNumber: String; FolioSequence: Integer; FolioNumber: String): THotelGuestHC;
function HCStatusToInt(Status: THCGuestStatus): Integer;
function IntToHCStatus(Status: Integer): THCGuestStatus;
function HCStatusToWideString(Status: THCGuestStatus): WideString;
procedure HCEndOfDay(db: TUntillDB; FromSys, TillSys: TDateTime; SaveDataProc: TSaveDataProcedure; sp: IShowProgress);

implementation
uses StrUtils, IniFiles, Forms, UntillPOSU, RestaurantPluginU, Classes, PosAlgU, UntillExceptionU,
  UntillDateTimeU, DateUtils, IBSQL, RegClassesU, PaymentsU, BLRMainU,
  BLREqu, RestaurantCashDatasetsU, DB, IBQuery, IndyHelpersU, AnsiStrings, MemTableU, UTF8U,
  iTessoU, ClassesU, HcEndOfDayU, ThreadObserverU, EpiMicrosU, ProtelU,
  DriverConstU, HotelJavaU, UBLProxyU, CommonU;

const
  STX = 2;
  ETX = 3;
  ENQ = 5;
  ACK = 6;
  NAK = 21;

  READ_TIMEOT = 20000;
  CONNECT_TIMEOT = 20000;

  MAX_SEND_TRIES = 10;

function HCStatusToInt(Status: THCGuestStatus): Integer;
begin
  result:=20;
  case Status of
    hcgsConsolidated: result:=10;
    hcgsDetailed: result:=11;
    hcgsNotInHouse: result:=20;
    hcgsNoCredit: result:=21;
    hcgsOverlimit: result:=22;
  end;
end;

function HCStatusToWideString(Status: THCGuestStatus): WideString;
begin
  result:='';
  case Status of
    hcgsConsolidated: result:=Plugin.Translate('HotelConceptsU','Ok, consolidated mode');
    hcgsDetailed: result:=Plugin.Translate('HotelConceptsU','Ok, detailed mode');
    hcgsNotInHouse: result:=Plugin.Translate('HotelConceptsU','Not in house');
    hcgsNoCredit: result:=Plugin.Translate('HotelConceptsU','No credit');
    hcgsOverlimit: result:=Plugin.Translate('HotelConceptsU','Overlimit');
  end;
end;

function IntToHCStatus(Status: Integer): THCGuestStatus;
begin
  result:=hcgsNotInHouse;
  case Status of
    10: result:=hcgsConsolidated;
    11: result:=hcgsDetailed;
    20: result:= hcgsNotInHouse;
    21: result:=hcgsNoCredit;
    22: result:=hcgsOverlimit;
  end;
end;

function GetCurrentPCHotelDevice(db: TUntillDB; PaymentId: Int64=0; Params: String=''): THCDeviceConfiguration;
var
  devices: TDeviceConfigurationMap;
  DeviceId: Int64;
  ip: IUntillPayment;
  conf: THCDeviceConfiguration;
begin
  result := nil;

  if PaymentId > 0 then
    ip := GetPayment(db, PaymentId)
  else
    ip := nil;

  devices := TDeviceConfiguration.CreateListFromDb(upos.UntillDB, THCDeviceConfiguration.GetDeviceKind);
  try
    for DeviceId in devices.Keys do begin
      conf := THCDeviceConfiguration(devices.Items[DeviceId]);

      if (assigned(ip)
      		and (conf.DriverKind = DRIVER_KIND_EMBEDDED)
      		and (conf.Model = HOTEL_ITESSO_V1)
          and (StartsStr('HotelCode=', ip.eftParams))
          and (not SameText(conf.HotelCode, RightStr(ip.eftParams, Length(ip.eftParams)-Length('HotelCode='))))) then begin
      	continue;
      end;

      if ((Length(Params) > 0)
      		and (conf.DriverKind = DRIVER_KIND_EMBEDDED)
      		and (conf.Model = HOTEL_ITESSO_V1)
          and (StartsStr('HotelCode=', Params))
          and (not SameText(conf.HotelCode, RightStr(Params, Length(Params)-Length('HotelCode='))))) then begin
      	continue;
      end;

      if not assigned(ip) then begin // Payment not defined in this operation
        result := THCDeviceConfiguration(conf.MakeCopy(TLoadDeviceContextImpl.Create(db), True));
        exit;
      end;

      if (ip.psp_model<0) and (ip.driver_kind=DRIVER_KIND_EMBEDDED) then begin // Auto device
        result := THCDeviceConfiguration(conf.MakeCopy(TLoadDeviceContextImpl.Create(db), True));
        exit;
      end;

      if (ip.driver_kind=DRIVER_KIND_EMBEDDED) and (conf.DriverKind = DRIVER_KIND_EMBEDDED) and (conf.Model=ip.psp_model) then begin
        result := THCDeviceConfiguration(conf.MakeCopy(TLoadDeviceContextImpl.Create(db), True));
        exit;
      end;

      if (ip.driver_kind = DRIVER_KIND_JAVA) and (conf.DriverKind = DRIVER_KIND_JAVA) and (conf.DriverId=ip.driver_id) then begin
        result := THCDeviceConfiguration(conf.MakeCopy(TLoadDeviceContextImpl.Create(db), True));
        exit;
      end;

    end;

    Plugin.RaisePOSException('Hotel Interface not found', 'GetCurrentPCHotelConceptsDeviceId');;

  finally
    devices.Free;
    if assigned(result) then
    	HCLog.Log(logDebug, 'HotelConceptsU', 'Hotel Interface HotelCode: ' + result.HotelCode);
  end;
end;


function IsHotelConceptsInstalled(db: TUntillDB): boolean;
var conf: THCDeviceConfiguration;
begin
  conf := nil;
  result := true;
  try
    try
      conf := GetCurrentPCHotelDevice(db);
    except
      result:=false;
    end;
  finally
    FreeAndNil(conf);
  end;
end;

function CorrectFolioNumber(db: TUntillDB; FolioNumber: String; out OutDeviceId: Int64): boolean;
var
  devices: TDeviceConfigurationMap;
  DeviceId: Int64;
  conf: THCDeviceConfiguration;
  HasLeadingString: Boolean;
begin
	result := false;
  OutDeviceId := 0;
  devices := TDeviceConfiguration.CreateListFromDb(upos.UntillDB, THCDeviceConfiguration.GetDeviceKind);
  try
    for DeviceId in devices.Keys do begin
      conf := THCDeviceConfiguration(devices.Items[DeviceId]);

      HasLeadingString := (conf.DriverKind = DRIVER_KIND_EMBEDDED) and (conf.Model in [HOTEL_ITESSO_V1, HOTEL_HOTELCONCEPTS_1_6]);

      if HasLeadingString then begin
        if Length(conf.LeadingString) > 0 then
          result := LeftStr(FolioNumber, Length(conf.LeadingString)) = conf.LeadingString
        else
          result := true;
      end;

			if result then begin
  			OutDeviceId := Deviceid;
      	break;
      end;
    end;

  finally
    devices.Free;
  end;
end;

function GetHCClient(db: TUntillDB; RoomNumber: String; FolioSequence: Integer; FolioNumber: String): THotelGuestHC;
var
  hc: THotelConceptsLink;
  conf: THCDeviceConfiguration;
  byRoom: Boolean;
begin
  conf := GetCurrentPCHotelDevice(db);
  try
    hc:=THotelConceptsLink.Create(upos.POSComputerName, conf.Host, conf.Port, conf.LeadingString, posalg.WriteStateData);
    try
      if conf.HCAlphanumericRoomNumbers then begin
        byRoom := Trim(RoomNumber)<>'';
        assert((Trim(FolioNumber)<>'') or byRoom);
      end else begin
        RoomNumber := IntToStr(StrToIntDef(RoomNumber, 0));
        byRoom := StrToIntDef(RoomNumber, 0)>0;
        assert((Trim(FolioNumber)<>'') or byRoom);
      end;
      if byRoom then
        result:=hc.GetGuest(RoomNumber, FolioSequence)
      else
        result:=hc.GetGuestByFolio(FolioNumber);

      if (result.Status in [hcgsConsolidated, hcgsDetailed]) and (not conf.HCAlphanumericRoomNumbers) then
        result.RoomNumber:= IntToStr(StrToIntDef(RESULT.RoomNumber, 0))
      else
        result.RoomNumber := Trim(result.RoomNumber);
    finally
      FreeAndNil(hc);
    end;
  finally
    FreeAndNil(conf);
  end;
end;

{ THotelConceptsLink }

procedure THotelConceptsLink.Charge(Client: THotelGuestHC; Posting: TDetailedPosting; VatPercent: Double);
var packet: THCPacket;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='22';
  packet.RoomNumber:=ShortString(IntToStr(StrToIntDef(Client.RoomNumber, 0)));
  packet.FolioSequence:=ShortString(IntToStr(Client.FolioSequence));
  packet.FolioNumber:=ShortString(Client.FolioNumber);
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(IntToStr(Posting.SalesAreaNum));
  packet.PLU:=ShortString(IntToStr(Posting.PLU));
  packet.Article:=UTF8Encode(Posting.Article);
  packet.Amount:=ShortString(IntToStr(Posting.Amount));
  packet.VAT1:=ShortString(IntToStr(Posting.Vat));
  packet.ArticleQuantity:=ShortString(IntToStr(Posting.Quantity));
  packet.PaymentMethod:=ShortString(IntToStr(Posting.PaymentMethod));
  packet.VATCode:=ShortString(GetVatCode(VatPercent));

  SendReceive(@packet);

  case StrToIntDef(String(packet.Response), 0) of
    10: Client.FStatus:=hcgsConsolidated;
    11: Client.FStatus:=hcgsDetailed;
    20: Client.FStatus:=hcgsNotInHouse;
    21: Client.FStatus:=hcgsNoCredit;
    22: Client.FStatus:=hcgsOverlimit;
    else raise EHCException.Create('Unknown response code to THotelConceptsLink.Charge(Detailed) request: '+String(packet.Response));
  end;
end;

function THotelConceptsLink.ArrToStr(b: TCommPacket): string;
var i:integer;
begin
    result:='';
  for i:=1 to 215 do begin
    result:=result+ByteToStr(b[i]);
  end;
end;

function THotelConceptsLink.ByteToStr(b: byte): string;
begin
  case b of
    STX : result:='<STX>';
    ETX : result:='<ETX>';
    ENQ : result:='<ENQ>';
    ACK : result:='<ACK>';
    NAK : result:='<NAK>';
    else begin
      if (b>=ord(' ')) and (b<127) then
        result:=char(ansichar(chr(b)))
      else
        result:='#'+IntToStr(b);
    end;
  end;
end;

procedure THotelConceptsLink.Charge(Client: THotelGuestHC; Posting: TConsolidatedPosting);
var packet: THCPacket;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='21';
  packet.RoomNumber:=ShortString(IntToStr(StrToIntDef(Client.RoomNumber, 0)));
  packet.FolioSequence:=ShortString(IntToStr(Client.FolioSequence));
  packet.FolioNumber:=ShortString(Client.FolioNumber);
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(IntToStr(Posting.SalesAreaNum));

  packet.Amount:=ShortString(IntToStr(Posting.Amount));
  packet.VAT1:=ShortString(IntToStr(Posting.Vat));
  packet.VATCode:='0';
  packet.PaymentMethod:=ShortString(IntToStr(Posting.PaymentMethod));

  SendReceive(@packet);

  case StrToIntDef(String(packet.Response), 0) of
    10: Client.FStatus:=hcgsConsolidated;
    11: Client.FStatus:=hcgsDetailed;
    20: Client.FStatus:=hcgsNotInHouse;
    21: Client.FStatus:=hcgsNoCredit;
    22: Client.FStatus:=hcgsOverlimit;
    else raise EHCException.Create('Unknown response code to THotelConceptsLink.Charge(Consolidated) request: '+String(packet.Response));
  end;
end;

constructor THotelConceptsLink.Create(ATerminal: String; Host: String; Port: Integer; LeadingStr: String; SaveDataProc: TSaveDataProcedure=nil);
begin
  FHost := Host;
  FPort := Port;
  FTcpClient:=nil;
  FTerminal:=ATerminal;
  FSaveDataProc:=SaveDataProc;
  FLeadingStr:=LeadingStr;
end;

destructor THotelConceptsLink.Destroy;
begin
  if assigned(FTcpClient) then
    FTcpClient.Disconnect;
  FreeAndNil(FTcpClient);
  inherited;
end;

function THotelConceptsLink.GetGuestByFolio(FolioNumber: String): THotelGuestHC;
var packet: THCPacket;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='10';
  packet.FolioNumber:=ShortString(FolioNumber);
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(FTerminal);

  SendReceive(@packet);

  result:=THotelGuestHC.Create;
  result.RoomNumber:=String(packet.RoomNumber);
  result.FolioSequence:=StrToIntDef(String(packet.FolioSequence), 0);
  result.FolioNumber:=Trim(String(packet.FolioNumber));
  result.GuestName:=String(packet.GuestName);
  case StrToIntDef(String(packet.Response), 0) of
    10: result.Status:=hcgsConsolidated;
    11: result.Status:=hcgsDetailed;
    20: result.Status:=hcgsNotInHouse;
    21: result.Status:=hcgsNoCredit;
    22: result.Status:=hcgsOverlimit;
    else raise EHCException.Create('Unknown response code to THotelConceptsLink.GetGuest request: '+String(packet.Response));
  end;
end;

function THotelConceptsLink.GetGuest(RoomNumber: String; FolioSequence: Word): THotelGuestHC;
var packet: THCPacket;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='10';
  packet.RoomNumber:=ShortString(RoomNumber);
  packet.FolioSequence:=ShortString(IntToStr(FolioSequence));
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(FTerminal);

  SendReceive(@packet);

  result:=THotelGuestHC.Create;
  result.RoomNumber:=String(packet.RoomNumber);
  result.FolioSequence:=StrToIntDef(String(packet.FolioSequence), 0);
  result.FolioNumber:=Trim(String(packet.FolioNumber));
  result.GuestName:=String(packet.GuestName);
  case StrToIntDef(String(packet.Response), 0) of
    10: result.Status:=hcgsConsolidated;
    11: result.Status:=hcgsDetailed;
    20: result.Status:=hcgsNotInHouse;
    21: result.Status:=hcgsNoCredit;
    22: result.Status:=hcgsOverlimit;
    else raise EHCException.Create('Unknown response code to THotelConceptsLink.GetGuest request: '+String(packet.Response));
  end;
end;



function CalculateBCC(p: TCommPacket): byte;
var i:integer;
begin
  result:=p[2];
  for i:=3 to 214 do result:=result xor p[i];
end;

function THotelConceptsLink.PacketToString(Packet: PHCPacket): AnsiString;
var fmt: AnsiString;
begin
  fmt := '%2.2s%-4.4s%5.5s%2.2s%10.10s%30.30s%8.8s%-30.30s%10.10s%10.10s%10.10s%10.10s%10.10s%2.2s%10.10s%2.2s%1.1s%4.4s%-8.8s%2.2s%2.2s%10.10s%10.10s%10.10s%10.10s';
  result:=format(fmt,
    [ packet^.Command,
      packet^.TerminalNumber,
      packet^.RoomNumber,
      packet^.FolioSequence,
      packet^.FolioNumber,
      packet^.GuestName, //30
      packet^.PLU, //8
      packet^.Article, // -30
      packet^.Amount, // 10
      packet^.VAT1, //10
      packet^.VAT2, //10
      packet^.VAT3, //10
      packet^.VAT4, //10
      packet^.Response, //2
      packet^.Ticket, //10
      packet^.MealPeriod, //2
      packet^.SequenceNumber, //1
      packet^.ArticleQuantity, //4
      packet^.RevenueCenter, //-8
      packet^.PaymentMethod, //2
      packet^.VATCode, //2
      packet^.AmountDiscount, //10
      packet^.AmountTip, //10
      packet^.AmountTax, //10
      packet^.AmountService]); //10
end;

procedure THotelConceptsLink.SendReceive(Packet: PHCPacket);
var data: ansistring;
    pack: TCommPacket;
    ntry,i: integer;
    msg: String;
    b: byte;
    bNotConnected: boolean;
begin

  CheckClient;

  packet.SequenceNumber:='0'; // IntToStr(FSeqNumber); - not neccessary since we use TCP/IP transport
  FixFolioNumber(packet);
  data:=PacketToString(packet);
  pack[1]:=stx;
  for i:=2 to 213 do pack[i]:=ord(data[i-1]);
  pack[214]:=etx;
  pack[215]:=CalculateBCC(pack);

  if IsEmulate then begin
    EmulateRequest(Pack);
    EmulateResponse(Packet);
    exit;
  end;

  bNotConnected := not IsIdTCPConnectionConnected(FTCPClient);

  if bNotConnected then
    try
      FTCPClient.ConnectTimeout := CONNECT_TIMEOT;
      FTCPClient.Connect();
    except
      on e: exception do
        Plugin.RaiseException('Connect to HotelConcepts failed: %s, %s', [e.Classname, e.Message]);
    end;
    
  try
    if not IsIdTCPConnectionConnected(FTcpClient) then
      raise EHCException.Create('Port is not opened');
    ntry:=0;
    repeat
      inc(ntry);
      if (ntry>MAX_SEND_TRIES) then
        raise EHCException.Create(msg);

      if bNotConnected then begin // ref. https://offsxp.office.sigma-soft.ru:8443/sigma?fileid=107557
        b:=ENQ;
        HCLog.WriteString('W: '+ByteToStr(b));
        IdWriteBuffer(FTCPClient, b, 1);
        IdReadBuffer(FTCPClient, b, 1);
        HCLog.WriteString('R: '+ByteToStr(b));
        if b = NAK then begin
          msg:='PMS could not process ENQ request';
          continue;
        end;
      end;

      HCLog.WriteString('W: '+ArrToStr(pack));
      IdWriteBuffer(FTCPClient, pack, 215);
      IdReadBuffer(FTCPClient, b, 1);
      HCLog.WriteString('R: '+ByteToStr(b));
      if b = NAK then begin
        msg:='PMS could not process input data';
        continue;
      end;

      IdReadBuffer(FTCPClient, pack, 215);
      HCLog.WriteString('R: '+ArrToStr(pack));
      if (pack[1]<>STX) or (pack[214]<>ETX) or (pack[215]<>CalculateBCC(pack)) then begin
        b:=NAK;
        HCLog.WriteString('W: '+ByteToStr(b));
        IdWriteBuffer(FTCPClient, b, 1);
        raise EHCException.Create('Wrong response packet');
      end else begin
        b:=ACK;
        HCLog.WriteString('W: '+ByteToStr(b));
        IdWriteBuffer(FTCPClient, b, 1);
      end;

      break;
    until false;

    b:=ENQ;
    HCLog.WriteString('W: '+ByteToStr(b));
    IdWriteBuffer(FTCPClient, b, 1);
    IdReadBuffer(FTCPClient, b, 1);
    HCLog.WriteString('R: '+ByteToStr(b));

  finally
    if not FKeepAlive then begin
      FTCPClient.Disconnect;
      FreeAndNil(FTCPClient);
    end;
  end;

  for i:=2 to 213 do data[i-1]:=AnsiChar(chr(pack[i]));
  StringToPacket(data, packet);
end;

procedure THotelConceptsLink.SetTerminal(const Value: String);
begin
  FTerminal := Value;
//  FTerminal:='sk-ws-11'; 
end;

procedure THotelConceptsLink.StringToPacket(data: AnsiString; Packet: PHCPacket);
begin
  packet^.Command:=copy(data, 1, 2);
  packet^.TerminalNumber:=copy(data, 3, 4);
  packet^.RoomNumber:=copy(data, 7, 5);
  packet^.FolioSequence:=copy(data, 12, 2);
  packet^.FolioNumber:=copy(data, 14, 10);
  packet^.GuestName:=Trim(copy(data, 24, 30));
  packet^.PLU:=copy(data, 54, 8);
  packet^.Article:=UTF8String(copy(data, 62, 30));
  packet^.Amount:=copy(data, 92, 10);
  packet^.VAT1:=copy(data, 102, 10);
  packet^.VAT2:=copy(data, 112, 10);
  packet^.VAT3:=copy(data, 122, 10);
  packet^.VAT4:=copy(data, 132, 10);
  packet^.Response:=copy(data, 142, 2);
  packet^.Ticket:=copy(data, 144, 10);
  packet^.MealPeriod:=copy(data, 154, 2);
  packet^.SequenceNumber:=copy(data, 156, 1);
  packet^.ArticleQuantity:=copy(data, 157, 4);
  packet^.RevenueCenter:=copy(data, 161, 8);
  packet^.PaymentMethod:=copy(data, 169, 2);
  packet^.VATCode:=copy(data, 171, 2);
  packet^.AmountDiscount:=copy(data, 173, 10);
  packet^.AmountTip:=copy(data, 183, 10);
  packet^.AmountTax:=copy(data, 193, 10);
  packet^.AmountService:=copy(data, 203, 10);
end;

procedure THotelConceptsLink.FixFolioNumber(Packet: PHCPacket);
begin
  with Packet^ do begin
    if Length(Trim(FolioNumber))<=0 then exit;

    if Length(FLeadingStr) > 0 then begin
      if LeftStr(FolioNumber, Length(FLeadingStr)) = AnsiString(FLeadingStr) then
        System.Delete(FolioNumber, 1, Length(FLeadingStr));
    end;
  end;
end;

function THotelConceptsLink.IsEmulate: Boolean;
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    result := ini.ReadInteger('common','EmulatePSP',0) <> 0;
  finally
    FreeAndNil(ini);
  end;
end;

procedure THotelConceptsLink.EmulateRequest(Pack: TCommPacket);
begin
  if assigned(FSaveDataProc) then
    FSaveDataProc('THotelConceptsLink.EmulateRequest', ArrToStr(Pack));
end;                                     

procedure THotelConceptsLink.EmulateResponse(Packet: PHCPacket);
var ini: TIniFile;
begin
  ini:=TIniFile.Create(ExtractFileDir(Application.ExeName)+'\untill.ini');
  try
    packet^.GuestName:=ShortString(ini.ReadString('common','EmulateGuestName','Unnamed guest'));
    packet^.Response:=ShortString(ini.ReadString('common','EmulateGuestStatus','10'));

  finally
    FreeAndNil(ini);
  end;
end;

procedure THotelConceptsLink.ChargeEODPaymentItem(Posting: TEODPaymentItem);
var packet: THCPacket;
    resp: integer;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='30';
  packet.RoomNumber:='00000';
  packet.FolioSequence:='';
  packet.FolioNumber:='';
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(IntToStr(Posting.SalesAreaNum));
  packet.PLU:='P'+ShortString(IntToStr(Posting.PaymentNumber));
  packet.Article:=UTF8Encode(Posting.Payment);
  packet.Amount:=ShortString(IntToStr(-Posting.Amount));
  packet.VAT1:=ShortString(IntToStr(0)); // always 0 for EOD VAT
  packet.ArticleQuantity:='0';
  packet.PaymentMethod:=ShortString(IntToStr(Posting.PaymentNumber));
  packet.VATCode:='00';

  SendReceive(@packet);

  resp:=StrToIntDef(String(packet.Response), 0);

  if (resp <> 30) then begin
    if resp = 40 then
      Plugin.RaisePosException('Error: end-of-day paymaster not checked-in')
    else
      EHCException.Create('Unknown response code to THotelConceptsLink.ChargeEODTurnoverItem request: '+String(packet.Response));
  end;
end;

procedure THotelConceptsLink.ChargeEODTurnoverItem(Posting: TEODTurnoverItem);
var packet: THCPacket;
    resp: integer;
begin
  FillChar(packet, sizeof(packet), 0);
  packet.Command:='30';
  packet.RoomNumber:='00000';
  packet.FolioSequence:='';
  packet.FolioNumber:='';
  packet.TerminalNumber:=ShortString(FTerminal);
  packet.RevenueCenter:=ShortString(IntToStr(Posting.SalesAreaNum));
  packet.PLU:=ShortString(IntToStr(Posting.DepNumber));
  packet.Article:=UTF8Encode(Posting.Department);
  packet.Amount:=ShortString(IntToStr(Posting.Amount));
  packet.VAT1:=ShortString(IntToStr(Posting.Vat));
  packet.ArticleQuantity:=ShortString(IntToStr(Posting.Quantity));
  packet.PaymentMethod:='00';
  packet.VATCode:='00';

  SendReceive(@packet);

  resp:=StrToIntDef(String(packet.Response), 0);

  if (resp <> 30) then begin
    if resp = 40 then
      Plugin.RaisePosException('Error: end-of-day paymaster not checked-in')
    else
      EHCException.Create('Unknown response code to THotelConceptsLink.ChargeEODTurnoverItem request: '+String(packet.Response));
  end;
end;

procedure HCEndOfDay(db: TUntillDB; FromSys, TillSys: TDateTime; SaveDataProc: TSaveDataProcedure; sp: IShowProgress);
var
  conf: THCDeviceConfiguration;
  hwl: THotelwareLink;
  eod: THCEndOfDay;
  eodCfg: EndOfDayConfiguration;
  includeRoom: Boolean;
  emulate: Boolean;
begin
  HCLog.WriteString('HC End of day started by '+upos.UserNameInfo.UserName + ' on "'+upos.POSComputerName+'" at '+DateTimeToStr(SystemToLocal(upos.GetPOSNow)));
  HCLog.WriteString('EOD From: '+DateTimeToStr(SystemToLocal(FromSys)));
  HCLog.WriteString('EOD Till: '+DateTimeToStr(SystemToLocal(TillSys)));
  try
  	emulate := IsEmulatingPSP;
    conf := GetCurrentPCHotelDevice(db, 0, GetUntillIniSetting('eod', 'params', ''));
    try
      hwl := CreateHotelwareDevice(db, conf);
      hwl.CheckHotelFunction(upos.UntillDB);
      eod := THCEndOfDay.Create;
      try

      	if hwl is TJavaHotelLink then begin
        	includeRoom := false;
        	if not emulate then begin
        		eodCfg := TJavaHotelLink(hwl).GetEndOfDayConfig(db, conf);
        		includeRoom := eodCfg.roomPaymentTurnoverIncluded;
          end else
          	eodCfg := nil;
          try
	        	eod.Read2(db, FromSys, TillSys, includeRoom, sp);
           	if not emulate then
		          TJavaHotelLink(hwl).HandleEndOfDay(SystemToLocal(FromSys), SystemToLocal(TillSys),  conf, eod.Turnover2, eod.Payments2, eod.Extras2, sp);
          finally
          	FreeAndNil(eodCfg);
          end;
        end else begin
	        eod.Read(db, FromSys, TillSys, sp);
	        hwl.EndOfDay(conf, eod.Turnover, eod.Payments, eod.Extras, sp);
        end;

		    if assigned(SaveDataProc) then
          SaveDataProc('HCEndOfDay export', eod.PosAlgData);

      finally
        FreeAndNil(hwl);
        FreeAndNil(eod);
      end;
    finally
      FreeAndNil(conf);
    end;
    HCLog.WriteString('HC End of day successfully finished');
  except
    on e: exception do begin
      HCLog.WriteString('HC End of day finished with error (see except.log)');
      raise
    end;
  end;
end;


function THotelConceptsLink.GetVatCode(VatPercent: Currency): String;
begin
  Case Round(VatPercent) of
    6: result := '1'; // NL before 2019
    9: result := '1';  // NL since 2019, ref: https://dev.untill.com/projects/#!511477
    21  : result := '2';
    0: result := '3';
    12: result := '4';
    19: result := '5';
    25: result := '6';
    else
      result:='0';
  end;
end;

procedure THotelConceptsLink.SetKeepAlive(const Value: Boolean);
begin
  FKeepAlive := Value;
end;

procedure THotelConceptsLink.CheckClient;
begin
  if not assigned(FTcpClient) then begin
    FTcpClient:=TIdTCPClient.Create(nil);
    FTcpClient.ReadTimeout:=READ_TIMEOT;
    FTcpClient.Host:=FHost;
    FTcpClient.Port:=FPort;
  end;
end;

{ THotelGuestHC }

procedure THotelGuestHC.SetStatus(const Value: THCGuestStatus);
begin
  FStatus := Value;
end;

{ THCLink }

procedure THCLink.Charge(Context: THotelBillPaymentContext);
var cred: THotelCredentials;
begin
  hc:=THotelConceptsLink.Create(upos.POSComputerName, Context.Config.Host, Context.Config.Port, Context.Config.LeadingString, posalg.WriteStateData);
  try
    try
      if Context.PData.Reopen then begin
        if not LoadRefundInfo(Context, cred) then exit; // exit if hotel payment info not found
      end else begin
        cred.RoomNumber := Context.Bill.HCRoomNumber;
        cred.FolioNumber := Context.Bill.HCFolioNumber;
        cred.FolioSequence := Context.Bill.HCFolioSequence;
        cred.Guestname := Context.Bill.order_name;
      end;

      assert((Trim(cred.FolioNumber)<>'') or (StrToIntDef(cred.RoomNumber, 0)>0));

      if StrToIntDef(cred.RoomNumber, 0)>0 then
        guest:=hc.GetGuest(cred.RoomNumber, StrToIntDef(cred.FolioSequence, 0))
      else
        guest:=hc.GetGuestByFolio(cred.FolioNumber);

      try

        CheckClient(); // raise exception in case of bad client status
        case guest.Status of
          hcgsConsolidated: ChargeConsolidated(Context);
          hcgsDetailed: ChargeDetailed(Context);
          else assert(false, 'unknown guest status');
        end;

        cred.RoomNumber := IntToStr(StrToIntDef(guest.RoomNumber, 0));
        cred.FolioNumber := guest.FolioNumber;
        cred.FolioSequence := IntToStr(guest.FolioSequence);
        cred.Guestname := guest.GuestName;

        if not Context.PData.Reopen then SaveDBInfo(cred, Context);
      finally
        FreeAndNil(guest);
      end;
    finally
      FreeAndNil(hc);
    end;
  except
    on E:EHCException do begin
      ExceptionLog.WriteExceptionInfo(E, 'TBLRBill.ProcessHotelPayment');
      Plugin.RaiseError(Plugin.Translate('BLRBillU','Communication error'));
    end;
    else raise;
  end;
end;

procedure THCLink.ChargeConsolidated(Context: THotelBillPaymentContext);
var mul: integer;
begin
  FillChar(cp, sizeof(cp), 0);
  if Context.PData.Reopen then mul:=-1 else mul:=1;
  cp.Amount:= mul*Round(Context.PData.Amount * 100);
  cp.Vat:=mul*Round(Context.PData.VAT * 100);
  cp.PaymentMethod:=GetPaymentNumber(upos.UntillDB, StrToInt64Def(Context.Bill.pbill_payments.id_payments.AsString, 0));
  cp.SalesAreaNum:= CacheSalesAreasRange.GetSalesAreaNumber(Context.Bill.SalesArea);
  hc.Charge(guest, cp);
  CheckClient();
end;

procedure THCLink.ChargeDetailed(Context: THotelBillPaymentContext);
var aid: int64;
    mul: integer;
    salesAreaNum: Integer;
    qty: Integer;
    idM: Int64;
    price, pp1, pp2: Currency;
    vat: double;
    psign: integer;
    sc, scvat : double;
begin
  if Context.Bill.pbill_payments.RecordCount > 1 then
    ChargeConsolidated(Context)
  else begin
    if Context.PData.Reopen then mul:=-1 else mul:=1;
    Context.Bill.pbill_items.First;
    salesAreaNum := CacheSalesAreasRange.GetSalesAreaNumber(Context.Bill.SalesArea);
    while not Context.Bill.pbill_items.Eof do begin
      aid:=StrToint64Def(Context.Bill.pbill_items.id_articles.AsString, 0);
      qty := mul * Context.Bill.pbill_items.quantity.AsInteger;
      if aid<>0 then begin

        idM := StrToInt64Def(Context.Bill.pbill_items.id_menu.AsString, 0);

        price := Context.Bill.pbill_items.price.AsCurrency * qty;
        vat := Context.Bill.pbill_items.vat.AsCurrency * qty;
        psign := 1;

        if Context.PData.Reopen then begin
          psign := -1;
          price := abs(price);
          vat := abs(vat);
        end;

        if (idM <> 0) then
          Context.Bill.items.RecalcMenuModifiersPriceBack(
            Context.Bill.ReturnedBill, price, vat, idM, pp1, pp2, sc, scvat);

        price := price * psign;
        vat := vat * psign;

        ChargeDetailedItem( Context, Context.Bill.pbill_items.pctext.AsString,
                            qty,
                            round(price * 100),
                            round(vat * 100),
                            GetPaymentNumber(upos.UntillDB, StrToInt64Def(Context.Bill.pbill_payments.id_payments.AsString, 0)),
                            salesAreaNum,
                            CacheArticleViewer.GetCachedArticle(aid).Number,
                            Context.Bill.pbill_items.vat_percent.AsInteger);
        if (idM <> 0) then
          ChargeMenuItems(Context, idM, qty, salesAreaNum);
        MainThreadAlive;
      end;
      Context.Bill.pbill_items.Next;
    end;
  end;
end;

procedure THCLink.ChargeDetailedItem(Context: THotelBillPaymentContext; Article: String; Qty, Amount,
  Vat: Integer; PaymentNumber: Byte; SalesAreaNum, PLU: Integer; VatPercent: Currency);
begin
    FillChar(dp, sizeof(dp), 0);
    dp.Article := Article;
    dp.Quantity := Qty;
    dp.Amount := Amount;
    dp.Vat := Vat;
    dp.PLU := PLU;
    dp.PaymentMethod := PaymentNumber;
    dp.VATCode := 0;
    dp.SalesAreaNum:= salesAreaNum;
    hc.Charge(guest, dp, system.abs(round(VatPercent)));
    CheckClient();
end;

procedure THCLink.EndOfDay(conf: THCDeviceConfiguration; Turnover: TList<TEODTurnoverItem>;
    Payments: TList<TEODPaymentItem>; Extras: TList<TEODExtraItem>; sp: IShowProgress);
var i: integer;
		ti: TEODTurnoverItem;
begin
  hc:=THotelConceptsLink.Create(upos.POSComputerName, conf.Host, conf.Port, conf.LeadingString, posalg.WriteStateData);
  try
    try
      hc.KeepAlive:=true;

      for i := 0 to Turnover.Count-1 do begin
        if sp<>nil then sp.CheckTerminated;
        hc.ChargeEODTurnoverItem(Turnover.Items[i]);
      end;

      for i := 0 to Payments.Count-1 do begin
        if sp<>nil then sp.CheckTerminated;
        hc.ChargeEODPaymentItem(Payments.Items[i]);
      end;

      for i:=0 to Extras.Count-1 do begin
        if sp<>nil then sp.CheckTerminated;
        With Extras[i] do
        	case ItemType of
          	EOD_EXTRA_TIP: begin
            	ti.Department := 'Tip';
              ti.DepNumber := 0;
              ti.Amount := Amount;
              ti.Vat := 0;
              ti.Quantity := 1;
              ti.SalesAreaNum := SalesAreaNum;
        			hc.ChargeEODTurnoverItem(ti);
            end;
          end;
      end;
    finally
      FreeAndNil(hc);
    end;
  except
    on E:EHCException do begin
      ExceptionLog.WriteExceptionInfo(E, 'THCLink.ChargeEODPayments');
      Plugin.RaiseError(Plugin.Translate('BLRBillU','Communication error'));
    end;
    else raise;
  end;
end;


procedure THCLink.ChargeMenuItems(Context: THotelBillPaymentContext; id_menu: Int64; parent_qty,
  SalesAreaNum: Integer);
  var bm: TMTBookmark;
      idA, idM: Int64;
begin
  bm := Context.Bill.menu_items.GetBookMark;
  try
    Context.Bill.menu_items.First;
    while not Context.Bill.menu_items.Eof do begin
      idM := StrToInt64Def(Context.Bill.menu_items.id_menu.AsString, 0);
      idA := StrToInt64Def(Context.Bill.menu_items.id_articles.AsString, 0);
      if (idM = id_menu) and (idA <> 0) then begin
        ChargeDetailedItem(Context, Context.Bill.menu_items.pctext.AsString,
          parent_qty * Context.Bill.menu_items.quantity.AsInteger,
          round(Context.Bill.menu_items.price.AsCurrency * parent_qty * Context.Bill.menu_items.quantity.AsInteger * 100),
          round(Context.Bill.menu_items.vat.AsCurrency * parent_qty * Context.Bill.menu_items.quantity.AsInteger * 100),
          GetPaymentNumber(upos.UntillDB, StrToInt64Def(Context.Bill.pbill_payments.id_payments.AsString, 0)),
          SalesAreaNum,
          CacheArticleViewer.GetCachedArticle(idA).number,
          Context.Bill.menu_items.vat_percent.AsInteger
          );
      end;
      Context.Bill.menu_items.Next;
    end;
  finally
    Context.Bill.menu_items.GoToBookmark(bm);
    Context.Bill.menu_items.FreeBookMark(bm);
  end;
end;

procedure THCLink.CheckClient();
begin
  case guest.Status of
    hcgsNotInHouse: Plugin.RaisePosTranslatedException(Plugin.TranslatePOS('BLRBillU','Guest is not in house'));
    hcgsNoCredit: Plugin.RaisePosTranslatedException(Plugin.TranslatePOS('BLRBillU','Guest has no credit'));
    hcgsOverlimit: Plugin.RaisePosTranslatedException(Plugin.TranslatePOS('BLRBillU','Guest has overlimit'));
  end;
end;

function THCLink.GetGuestInfo(Request: THotelwareGuestRequest; out NotValidMsg: WideString): TObjectList<THotelGuest>;
var
  _guest: THotelGuestHC;
begin
  result := TObjectList<THotelGuest>.Create;
  NotValidMsg := '';
  _guest:=GetHCClient(upos.UntillDB,
    Request.RoomNumber,
    Request.FolioSequence,
    Request.FolioNumber);
  case _guest.Status of
    hcgsConsolidated, hcgsDetailed: begin
      _guest.GuestName := UTF8_Decode(_guest.GuestName);
      _guest.RoomNumber := _guest.RoomNumber;
      result.Add(_guest);
    end else begin
      NotValidMsg := HCStatusToWideString(_guest.Status);
      FreeAndNil(_guest);
    end;
  end;
end;


procedure THCLink.SaveDBInfo(Cred: THotelCredentials; Context: THotelBillPaymentContext);
var q: TIBQueryU;
begin
  q:=Context.Tran.GetTempQuery;
  q.SQL.Text:='insert into pbill_hotel_payments_info (id, id_pbill_payments, '+
  'roomnumber, foliosequence, folionumber, guestname) values (:id, :id_pbill_payments, '+
  ':room, :foliosequence, :folionumber, :guestname)';

  assert(assigned(guest));
  q.ParamByName('id').AsString:=IntToStr(upos.UntillDB.GetID);
  q.ParamByName('id_pbill_payments').AsString:=Context.Bill.pbill_payments.id.AsString;

  q.ParamByName('room').AsString:=IntToStr(StrToIntDef(cred.RoomNumber, 0));
  q.ParamByName('foliosequence').AsInteger:=StrToIntDef(cred.FolioSequence, 0);
  q.ParamByName('folionumber').AsString:=cred.FolioNumber;
  q.ParamByName('guestname').AsString:=cred.GuestName;

{  q.ParamByName('room').AsString:=IntToStr(StrToIntDef(guest.RoomNumber, 0));
  q.ParamByName('foliosequence').AsInteger:=guest.FolioSequence;
  q.ParamByName('folionumber').AsString:=guest.FolioNumber;
  q.ParamByName('guestname').AsString:=guest.GuestName;}

  q.ExecSQL;
end;

function CreateHotelwareDevice(db: TUntillDB; conf: THCDeviceConfiguration): THotelwareLink;
begin
  result := nil;
  if conf.DriverKind = DRIVER_KIND_EMBEDDED then begin
    case conf.Model of
      HOTEL_HOTELCONCEPTS_1_6: result:=THCLink.Create;
      HOTEL_ITESSO_V1: result := TiTessoLink.Create;
      HOTEL_EPI_MICROS_V1: result := TEpiMicrosLink.Create;
      HOTEL_PROTEL: result := TProtelLink.Create;
      else assert(false, 'Unknown HC model: '+IntToStr(conf.model));
    end;
  end else begin // Java
    result := TJavaHotelLink.Create;
    TJavaHotelLink(result).DeviceConf := THCDeviceConfiguration(Conf.MakeCopy(TLoadDeviceContextImpl.Create(db), True));
  end;
end;

end.
