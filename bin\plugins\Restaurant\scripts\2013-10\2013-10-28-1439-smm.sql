set term !! ;
CREATE OR ALTER procedure BEVERAGE_PROCESS_ORDERITEM (
    ID_ARTICLES bigint,
    ID_ORDERS bigint,
    QUANTITY integer)
as
declare variable DATETIME timestamp;
declare variable USR bigint;
declare variable VD_NUMBER integer;
declare variable ID_GROUP bigint;
declare variable ID_LOCATION bigint;
declare variable RECEIPT_CNT bigint;
declare variable ID_RECEIPT_ARTICLE bigint;
declare variable ARTICLE_COUNT bigint;
declare variable TABLENO integer;
declare variable PCNAME varchar(50);
declare variable USE_LOCATIONS smallint;
declare variable USE_BECO_LOCATIONS integer;
declare variable DEBITCREDIT smallint;
begin 
  -- get plu and group id
  select plu_number_vandu<PERSON><PERSON>en, id_vd_group, bc_use_locations, BC_DEBITCREDIT from articles
        where id=:id_articles into :vd_number, :id_group, :use_locations, :DEBITCREDIT;

  -- get beco locations count
  select count(*) from beco_article_locations 
  where id_articles = :id_articles 
  and is_active=1
  into :use_beco_locations;

  -- get datetime and user
  select ord_datetime, id_untill_users, ord_tableno, pcname from orders
      where id=:id_orders into :datetime, :usr, :tableno, :pcname;

    -- if use locations, read computer location
    if (coalesce(:use_locations,0)=1) then begin
        select rc.id_beco_location from restaurant_computers rc, computers c 
            where rc.id_computers=c.id and upper(c.name)=upper(:pcname) and c.comp_active=1 into :id_location;
    end


    if (coalesce(:id_location,0)=0) then begin
            select first 1 beco_location_id from proc_get_bc_location(:tableno, :tableno, :pcname)
          into :id_location;
    end
      
    select count(*) from vd_receipts 
        where id_article = :id_articles 
        and is_active=1 
        and ID_RECEIPT_ARTICLE in (select id from articles where is_active=1)
        into :receipt_cnt;

  if ((coalesce(:id_group,0)<>0)
  or (receipt_cnt > 0)
  or (coalesce(:vd_number,0)<>0)
  or (coalesce(:use_locations,0)=1)
  or (use_beco_locations > 0)) then begin


    if (coalesce(:id_group,0)<>0) then begin
        if (coalesce(:DEBITCREDIT, 0)=1) then execute procedure BEVERAGE_CREDIT(:usr, :ID_ARTICLES, :QUANTITY, :ID_LOCATION);
        insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location, tableno)
        values (:datetime, :usr, null, coalesce(:id_group, -1), :quantity, 0, null, :id_location, :tableno);
    end else if (receipt_cnt > 0) then begin
        -- RECEIPE ***********
        for select id_receipt_article, article_count from vd_receipts
        where id_article=:id_articles and is_active=1 and ID_RECEIPT_ARTICLE in (select id from articles where is_active=1)
        into :id_receipt_article, :article_count do begin
            if (article_count <> 0) then begin

                -- is this article belongs to a group?
                select id_vd_group, BC_DEBITCREDIT from articles
                where id=:id_receipt_article                 
                into :id_group, :DEBITCREDIT;

                if (coalesce(:DEBITCREDIT, 0)=1) then execute procedure BEVERAGE_CREDIT(:usr, :id_receipt_article, :QUANTITY, :ID_LOCATION);

                if ((:id_group <> 0) and not (:id_group is null)) then begin
                    -- insert as group
                    insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location, tableno)
                    values (:datetime, :usr, null, :id_group, :article_count * :quantity, 0, null, :id_location, :tableno);
                end else begin
                    -- insert as article
                    insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location, tableno)
                    values (:datetime, :usr, :id_receipt_article, null, :article_count * :quantity, 0, null, :id_location, :tableno);
                end
            end
        end
    end else if ((coalesce(:vd_number,0)<>0) or (coalesce(:id_location,0)<>0))  then begin
        if (coalesce(:DEBITCREDIT, 0)=1) then execute procedure BEVERAGE_CREDIT(:usr, :ID_ARTICLES, :QUANTITY, :ID_LOCATION);
        insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location, tableno)
        values (:datetime, :usr, coalesce(:id_articles, -1), null, :quantity, 0, null, :id_location, :tableno);
    end
  end
end
!!
commit
!!
set term ; !!
