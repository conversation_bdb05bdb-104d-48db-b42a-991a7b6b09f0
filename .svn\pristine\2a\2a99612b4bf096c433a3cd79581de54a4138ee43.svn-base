unit TAEntityManager;

interface
uses
  EntityManagerU, Classes, UntillDBU, ClassManagerU, PluginU, IBSQL,
  UntillFram, SysUtils, CommonStringsU, CommonU, EntityFram, Variants,
  db, BOUntillDBU, TAReportFilterFram, DBEntityListFram, Controls, TntCompatibilityU,
  ComCtrls, Contnrs, ClassesU, Windows, Graphics, Math, UBLU;
type

  TTAEntityManager = class(TEntityManager)
  private
    BMin : Integer;
    MainCurRound : Integer;
    flistview: TEMListView;
    ffram : TTAReportFilterFrame;
    FFromDT : TDatetime;
    FTillDT : TDatetime;
    bOriginal : boolean;
    procedure SetFromDT(const Value: TDatetime);
    procedure SetTillDT(const Value: TDatetime);
    procedure DoFilterConfirmed(Sender: TObject);
    procedure DoSaveFile(Sender: TObject);
    procedure DoBeforeRefresh(Sender: TObject);
    procedure DoAfterRefreshList(Sender: TObject);
    procedure RecalcValues;
  public
    property FromDT : TDatetime read FFromDT write SetFromDT;
    property TillDT : TDatetime read FTillDT write SetTillDT;
    constructor Create(AOwner :TComponent; AUntillDB:TBOCustomUntillDB); override;
    class function GetTableName: String; override;

    function  IsQuickEditable(FieldName: String): Boolean; override;
    procedure SaveQuickEdit(FieldName: String; Data: Variant); override;
    procedure InitializeList(Sender:TEMListView);
    function GetStringRepresentation(FieldName: String; Value: Variant; Field: TField = nil): WideString; override;
    class function GetNodeName: WideString; override; // to show in Permission Management form
  end;

implementation
uses DateUtils, ThreadObserverU, UntillListDialogFrm, AccessControlU, Currencyu,
  BLWorkBreakU, UntillAppU;
  { TTAEntityManager }

constructor TTAEntityManager.Create( AOwner :TComponent; AUntillDB:TBOCustomUntillDB );
var FDateTime, ToDateTime, aFDateTime, aToDateTime : TDatetime;
    q : IIBSQL;
begin
  inherited Create( AOwner, AUntillDB );

  FFromDT := 0;
  FTillDT := 0;

  UntillApp.GetHoursOfService(AUntillDB, aFDateTime, aToDateTime);

  GetFromTillDateTime(aFDateTime, aToDateTime, Now(), FDateTime, ToDateTime);
  FFromDT := SysDateTimeToLocal(FDateTime);
  FTillDT := SysDateTimeToLocal(ToDateTime);

  BMin := 0;
  q := AUntillDB.GetPreparedIIbSql('select first 1 break_time from settings');
  q.ExecQuery;
  if not q.Eof then BMin := q.q.fields[0].asInteger;


  ListParams.TableAlias := 'a';
  SetListParams(
             ['a.ID',
              'a.ID_UNTILL_USERS',
              'a.ACTION_IN',
              'a.ACTION_TIME START_DATE',
              'a.ACTION_TIME START_TIME',
              'coalesce(ch.action_datetime, a.ACTION_TIME) END_DATE',
              'coalesce(ch.action_datetime, a.ACTION_TIME) END_TIME',
              'a.HAD_LUNCH',
              'coalesce(ch.duration,a.DURATION) DURATION',
              'a.WAS_LUNCH',
              'a.START_BREAK',
              'u.name',
              'untill_user_wages.hourly_wage hw',
              'untill_user_wages.hourly_wage total_decimal',
              'coalesce(CASE coalesce(ch.id, 0) WHEN 0 THEN null else coalesce(ch.total_cost,0.00) END, a.total_cost) total_cost',
              'a.total_cost original_total_cost',
              'WAITER_POSITIONS.name appointment',
              'u.terminal_id',
              'ug.name ugname',
              'a.ACTION_TIME original_date',
              'a.DURATION original_duration'
               ],
              ['clock_in_out a'],
              [],'ID');
  ListParams.AddJoin('join untill_users u on u.id = a.id_untill_users' );
  ListParams.AddJoin('left outer join untill_user_wages on untill_user_wages.id = a.ID_UNTILL_USER_WAGES' );
  ListParams.AddJoin('left outer join WAITER_POSITIONS on WAITER_POSITIONS.id=untill_user_wages.ID_WAITER_POSITIONS ' );
  ListParams.AddJoin('left outer join untill_group_users ug on ug.id = u.id_group_users' );
  ListParams.AddJoin('left outer join clock_in_out_change ch on ch.id_clock_in_out = a.id ' +
    'and ch.change_datetime=(select max(cc.change_datetime) from clock_in_out_change cc where cc.id_clock_in_out=a.id)' );
  BeforeRefreshList:=DoBeforeRefresh;
  AfterRefreshList:=DoAfterRefreshList;
  InitializeListProc:=InitializeList;
  DialogMode:=true;
end;

procedure TTAEntityManager.InitializeList(Sender: TEMListView);
var fr : TDBEntityListFrame;
begin
  flistview := Sender;
  MainCurRound := -GetUntillMainCurrency(UntillDB).Round;
  with Sender do begin
    AddFieldHeader(Plugin.Translate('TAEntityManager','User'),dtString,20,'name',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','User ID'),dtString,15,'terminal_id',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Wage per hour'),dtCurrency,10,'hw',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Position'),dtString,20,'appointment',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Start date'),dtDate,12,'START_DATE',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Start time'),dtTime,10,'START_TIME',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Stop date'),dtDate,12,'END_DATE',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Stop time'),dtTime,10,'END_TIME',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Total hrs:min'),dtString,15,'duration',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Total decimal'),dtFloat,15,'total_decimal',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Total costs'),dtCurrency,15,'total_cost',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Break'),dtBoolean,8,'had_lunch',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','Meal'),dtBoolean,8,'was_lunch',true);
    AddFieldHeader(Plugin.Translate('TAEntityManager','OriginalDate'),dtFloat,15,'original_date',false);
    AddFieldHeader(Plugin.Translate('TAEntityManager','OriginalDuration'),dtFloat,15,'original_duration',false);
    AddFieldHeader(Plugin.Translate('TAEntityManager','action_in'),dtInteger,15,'Action_in',false);
    AddFieldHeader('Id',dtString,0,'id',false);
    fr:=TDBEntityListFrame(ListView.Frame);

    ffram:=TTAReportFilterFrame.Create(fr);
    fr.panTop.Height:=ffram.Height;
    ffram.Parent:=fr.panTop;
    ffram.Align:=alClient;

    if (fr.Parent.Parent is TUntillListDialogForm) then begin
      TUntillListDialogForm(fr.Parent.Parent).btnOK.Default := false;
      TUntillListDialogForm(fr.Parent.Parent).Caption := Plugin.Translate('TAEntityManager','Time & Attedance report');
    end;

    if fr.Parent.Parent.Height = 300 then begin// default value
      fr.Parent.Parent.Height := 620;
    end;
    ffram.OnFilterConfirmed:=DoFilterConfirmed;
    ffram.OnSaveFile:=DoSaveFile;
    ffram.UntillDB := UntillDB;
    ffram.InitForm;
  end;
end;

class function TTAEntityManager.GetNodeName: WideString;
begin
  result := Plugin.Translate('TTAEntityManager', 'Time and attendance');
end;

function TTAEntityManager.GetStringRepresentation(
  FieldName: String; Value: Variant; Field: TField): WideString;
var fn: String;
  hh : Extended;
  strhh : string;
  strmm : string;
  ValueMin : Integer;
begin
  fn:=lowercase(FieldName);
  if fn='total_decimal' then begin
    ValueMin := Round(SecondsBetween(ClientDataset.FieldByName('duration').asCurrency,0)/60);
    if ClientDataset.FieldByName('had_lunch').asInteger=1 then
      ValueMin:= ValueMin - BMin;
    hh := 1.0 *ValueMin / 60.0;
    result:=CurrToStr(hh);
  end else if fn='total_cost' then begin
    if bOriginal then
      result:=CurrToStr(SimpleRoundTo(ClientDataset.FieldByName('original_total_cost').asCurrency, MainCurRound))
    else
      result:=CurrToStr(SimpleRoundTo(ClientDataset.FieldByName('total_cost').asCurrency, MainCurRound));
  end else if fn='duration' then begin

    ValueMin := Round(SecondsBetween(Value,0)/60);
    if ClientDataset.FieldByName('had_lunch').asInteger=1 then
      ValueMin:= ValueMin - BMin;

    hh := Int(ValueMin/60);
    strhh := IntToStr(Round(hh));
    strmm := IntToStr(Round((ValueMin/60 - int(ValueMin/60)) * 60));
    if length(strhh)<2 then strhh := '0' + strhh;
    if length(strmm)<2 then strmm := '0' + strmm;
    result:= strhh + ':' + strmm;
    if Value < 0 then result := '-' + result;

  end else
    result:=inherited GetStringRepresentation(FieldName, Value,Field);
end;

class function TTAEntityManager.GetTableName: String;
begin
result :='clock_in_out'
end;

function TTAEntityManager.IsQuickEditable(FieldName: String): Boolean;
begin
  result:=false;
  if not bOriginal then begin
    if SameText(FieldName,'Start_Date') then result:=true;
    if SameText(FieldName,'End_Date') then result:=true;
    if SameText(FieldName,'Start_Time') then result:=true;
    if SameText(FieldName,'End_Time') then result:=true;
  end;
end;

procedure TTAEntityManager.SetFromDT(const Value: TDatetime);
begin
  FFromDT := Value;
end;

procedure TTAEntityManager.SetTillDT(const Value: TDatetime);
begin
  FTillDT := Value;
end;

procedure TTAEntityManager.DoFilterConfirmed(Sender: TObject);
begin
  flistview.UnselectAll;
  flistview.RefreshList;
end;

procedure TTAEntityManager.DoSaveFile(Sender: TObject);
var FileName : string;
    strL : TTntStrings;

  procedure PutDataToList(Lines: TTntStrings);
  var   str : String;
    h,m,s,ms : Word;
    s_h, s_m, s_s : String;
    bm : TBookmark;
  begin
    assert( assigned(Lines) );

    bm := ClientDataSet.GetBookmark;
    ClientDataSet.DisableControls;
    try
      if ClientDataSet.RecordCount = 0 then exit;
      ClientDataSet.First;
      with ClientDataSet do begin
        while not eof do begin
          DecodeTime(FieldByName('Duration').asFloat,h,m,s,ms);
          s_h := IntToStr(h);
          if length(s_h) < 2 then s_h := '0' + s_h;
          s_m := IntToStr(m);
          if length(s_m) < 2 then s_m := '0' + s_m;
          s_s := IntToStr(s);
          if length(s_s) < 2 then s_s := '0' + s_s;
          str := FieldByName('name').asString + CHR(VK_TAB) +
            FieldByName('terminal_id').asString+ CHR(VK_TAB) +
            FieldByName('hw').asString+ CHR(VK_TAB) +
            DateToStr(FieldByName('Start_Date').asDateTime)+ CHR(VK_TAB)+
            TimeToStr(FieldByName('Start_Time').asDateTime)+ CHR(VK_TAB) +
            DateToStr(FieldByName('End_Date').asDateTime) + CHR(VK_TAB) +
            TimeToStr(FieldByName('End_Time').asDateTime)+ CHR(VK_TAB) +
            s_h + ':' +s_m + ':'+ s_s  + CHR(VK_TAB) +
            FieldByName('total_decimal').asString  + CHR(VK_TAB) +
            FieldByName('total_cost').asString + CHR(VK_TAB);
          if FieldByName('had_lunch').asInteger=0 then
            str := str + 'false'
          else
            str := str + 'true';
          str := str + CHR(VK_TAB);
          if FieldByName('was_lunch').asInteger=0 then
            str := str + 'false'
          else
            str := str + 'true';
          str := str + CHR(VK_TAB);
          Lines.Add(str);
          next;
        end;
      end;
    finally
      ClientDataSet.GotoBookmark(bm);
      ClientDataSet.FreeBookmark(bm);
      ClientDataSet.EnableControls;
    end;
  end;

begin
  assert(assigned(ffram));
  if ffram.dlgFileSave.Execute then begin
    FileName := ffram.dlgFileSave.FileName;
    strL := TTntStringList.Create;
    try
      PutDataToList(strL);
      strL.SaveToFile(FileName);
    finally
      FreeAndNil(strL);
    end;
  end;
end;

procedure TTAEntityManager.DoBeforeRefresh(Sender: TObject);
var I   : Integer;
    TI  : TListItem;
    ind : integer;
    strSD, strED, strUserList : String;
    ic : Integer;
begin
  assert(assigned(ffram));

  strUserList := '';
  ic := 0;
  if not ffram.rbAll.checked then begin
    for I := 0 to Pred(ffram.tvlUsers.Items.count) do begin
      TI := ffram.tvlUsers.Items[I];
      if TI.Checked and Assigned( TI.Data ) then begin
        if ic=0 then
          strUserList := IntToStr( TInt64(TI.Data).Value )
        else
          strUserList := strUserList + ',' + IntToStr( TInt64(TI.Data).Value );
        Inc(ic);
      end;
    end;
  end;
  FFromDT   := LocalDatetimeToSys(DateOf(ffram.dtFrom.Date) + TimeOf(ffram.ttFrom.Time));
  FTillDT   := LocalDatetimeToSys(DateOf(ffram.dtTo.Date) + TimeOf(ffram.ttTo.Time));
  bOriginal := ffram.rbOriginal.checked;

  strSD := IntToStr(YearOf(FFromDT)) + '/' + IntToStr(MonthOf(FFromDT)) + '/' + IntToStr(DayOf(FFromDT)) + ' ' + IntToStr(HourOf(FFromDT)) + ':' + IntToStr(MinuteOf(FFromDT));
  strED := IntToStr(YearOf(FTillDT)) + '/' + IntToStr(MonthOf(FTillDT)) + '/' + IntToStr(DayOf(FTillDT)) + ' ' + IntToStr(HourOf(FTillDT)) + ':' + IntToStr(MinuteOf(FFromDT));

  ind:=ListParams.IndexOfCondition('((action_in=1 and a.duration > 0)');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  ListParams.AddCondition('((action_in=1 and a.duration > 0) or ' +
               'not exists (select b.* from clock_in_out b where a.ID_UNTILL_USERS=b.ID_UNTILL_USERS ' +
               'and action_in=1 and b.action_time>a.ACTION_TIME and b.action_time > ''' + strSD + '''))');

  ind:=ListParams.IndexOfCondition('coalesce(ch.action_datetime, a.action_time) between ');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  ListParams.AddCondition('coalesce(ch.action_datetime, a.action_time) between ''' + strSD + ''' and ''' + strED + '''');
  ind:=ListParams.IndexOfCondition('a.id_untill_users in ');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if strUserList <> '' then
    ListParams.AddCondition('a.id_untill_users in (' + strUserList + ')');
end;

procedure TTAEntityManager.DoAfterRefreshList(Sender: TObject);
begin
  RecalcValues;
end;

procedure TTAEntityManager.RecalcValues;
var TotalMinutes : Integer;
    rec : integer;
    sd, ed : TDatetime;
    dr : double;
    oldIndexName : string;
    sumBreakDuration : Integer;
    id_user : Int64;
    secFloat : Double;
begin
  with ClientDataSet do begin
    oldIndexName := IndexName;
    IndexName := '';
    rec:=recno;
    DisableControls;
    try
      First;
      while not Eof do begin
        Edit;
        if not bOriginal then begin
          ed := SysDatetimetoLocal(DateOf(FieldByName('End_Date').AsDateTime) + TimeOf(FieldByName('End_Time').AsDateTime));
          dr := FieldByname('Duration').AsFloat;
          if FieldByName('action_in').asInteger=0 then begin
            FieldByName('end_date').Clear;
            FieldByName('end_time').Clear;
            FieldByName('duration').AsFloat := Now - ed;
            FieldByName('start_date').asDatetime := DateOf(ed - dr);
            FieldByName('start_time').asDatetime := TimeOf(ed - dr);
            ed := Now;
          end else begin
            FieldByName('end_date').asDatetime := DateOf(ed);
            FieldByName('end_time').asDatetime := TimeOf(ed);
            FieldByName('start_date').asDatetime := DateOf(ed - dr);
            FieldByName('start_time').asDatetime := TimeOf(ed - dr);
          end;
        end else begin
          ed := SysDatetimetoLocal(FieldByName('original_date').AsDateTime);
          FieldByName('start_date').asDatetime := DateOf(ed - FieldByname('original_duration').AsFloat);
          FieldByName('start_time').asDatetime := TimeOf(ed - FieldByname('original_duration').AsFloat);
          if FieldByName('action_in').asInteger=0 then begin
            FieldByName('end_date').Clear;
            FieldByName('end_time').Clear;
            FieldByName('duration').AsFloat := Now - ed;
            ed := Now;
          end else begin
            FieldByName('end_date').asDatetime := DateOf(ed);
            FieldByName('end_time').asDatetime := TimeOf(ed);
            FieldByName('duration').asFloat := FieldByname('original_duration').AsFloat;
            end;
        end;
        sd := DateOf(FieldByName('Start_Date').AsDateTime) + TimeOf(FieldByName('Start_Time').AsDateTime);
        TotalMinutes := Round(secondsbetween(sd,ed)/60);

        if sd > ed
          then  FieldByName('total_decimal').asFloat := (-1) * TotalMinutes / 60
          else  FieldByName('total_decimal').asFloat := TotalMinutes / 60;

        id_user  := StrToInt64Def(FieldByName('ID_UNTILL_USERS').asString,0);
        sumBreakDuration := GetBreakDurationSeconds( UntillDB,  id_user, LocalDatetimeToSys(sd), LocalDatetimeToSys(ed) );
        SecFloat :=  IncSecond(0, sumBreakDuration);
        FieldByname('Duration').AsFloat := FieldByname('Duration').AsFloat - secFloat;
        Post;
        Next;
      end;
      RecNo := rec;
      IndexName := oldIndexName;
      if ClientDataSet.IndexDefs.IndexOf('ffCustomSortIndex')<0 then
        ClientDataSet.AddIndex('ffCustomSortIndex', 'name;start_date;start_time',[ixCaseInsensitive]);
      ClientDataSet.IndexName:='ffCustomSortIndex';
    finally
      EnableControls;
    end;
  end;
  if bOriginal and (ListView.QuickEditField>=0) then begin
      TDBEntityListFrame(ListView.Frame).Grid.Perform(VK_ESCAPE, 0, 0);
  end;
  TDBEntityListFrame(ListView.Frame).Grid.OnDblClick:= nil;
end;

procedure TTAEntityManager.SaveQuickEdit(FieldName: String; Data: Variant);
var ed, sd : TDatetime;
    clockInOutId, userId, responsibleUserId: Int64;
    uc : TUBLConnection;
    res: String;
    startDT, endDT: Int64;
begin
 sd := DateOf(ClientDataset.FieldByName('Start_Date').asDatetime) + TimeOf(ClientDataset.FieldByName('Start_time').asDatetime);
 ed := DateOf(ClientDataset.FieldByName('End_Date').asDatetime) + TimeOf(ClientDataset.FieldByName('End_time').asDatetime);


 if SameText(FieldName,'Start_Date') then begin
    sd := DateOf(TDatetime(Data)) + TimeOf(ClientDataset.FieldByName('Start_time').asDatetime);
 end else if SameText(FieldName,'Start_Time') then begin
    sd := DateOf(ClientDataset.FieldByName('Start_date').asDatetime) + TimeOf(TDatetime(data));
 end;
 startDT := round((LocalDateTimeToSys(sd) - 25569.0) * 86400000.0);

 if ClientDataset.FieldByName('ACTION_IN').AsInteger = 1 then begin
  if SameText(FieldName,'End_Date') then
    ed := DateOf(TDatetime(Data)) + TimeOf(ClientDataset.FieldByName('End_time').asDatetime)
  else if SameText(FieldName,'End_Time') then
    ed := DateOf(ClientDataset.FieldByName('End_date').asDatetime) + TimeOf(TDatetime(data));
  endDT := round((LocalDateTimeToSys(ed) - 25569.0) * 86400000.0)
 end
  else endDT := 0;

 clockInOutId := StrToInt64Def(ClientDataset.FieldByName('id').asString,0);
 userId := StrToInt64Def(ClientDataset.FieldByName('id_untill_users').asString,0);
 responsibleUserId := untilldb.BOUserId;

 uc := getUBL(untilldb);
 res := uc.Soap.clockinout_modify(uc.SessionID, startDT, endDT, userId, clockInOutId, responsibleUserId, true);

 if res <> ''
  then plugin.RaiseException(res);

end;

initialization
  OutputDebugString('TAEntityManager.initialization');
  ClassManager.RegisterClass(TTAEntityManager);

end.
