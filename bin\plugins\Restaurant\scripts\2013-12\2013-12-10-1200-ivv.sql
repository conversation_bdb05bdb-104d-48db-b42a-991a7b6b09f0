create table bill_sc_log (
    id         u_id,
    id_sc_plan bigint,
    amount     double precision,
    id_bill    bigint,
    id_untill_users bigint,
    operation_dt timestamp, 
    sc_type    integer, 
    sc_status  integer, 
    constraint bill_sc_log_pk primary key (id),
    constraint bsl_fk1 foreign key (id_bill) references bill (id),
    constraint bsl_fk2 foreign key (id_sc_plan) references BO_SERVICE_CHARGE (id),
    constraint bsl_fk3 foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on bill_sc_log to untilluser;
commit;
execute procedure register_sync_table_ex('bill_sc_log', 'p', 1);
commit;

