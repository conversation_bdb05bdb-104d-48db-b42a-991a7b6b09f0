SET TERM !! ;

CREATE OR ALTER TRIGGER KS_BILL_ORDER_Ins_trigger FOR KS_BILL_ORDER
active after insert position 0
as
declare variable idx integer;
declare variable id_ks bigint;
declare variable cnt integer;
begin


    select id_ks, order_idx from KS_BILL_ORDER where KS_BILL_ORDER.id = new.id into :id_ks,:idx;
    select count(*) from ks_max_order_idx where id_kitchen_screen=:id_ks into cnt;
    if (cnt=0) then 
      insert into ks_max_order_idx(id_kitchen_screen, max_order_dx) values(:id_ks,:idx);
    else
      update ks_max_order_idx set max_order_dx=:idx where id_kitchen_screen=:id_ks;

end
!!
commit
!!
set term ; !!

