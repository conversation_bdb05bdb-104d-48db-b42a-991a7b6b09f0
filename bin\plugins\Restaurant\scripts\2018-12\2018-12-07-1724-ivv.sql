create table pbill_item_bookp (
    id u_id,
    id_pbill_item bigint,
    id_bookkeeping_turnover bigint,
    id_bookkeeping_vat bigint,
    constraint pbill_item_bookp_pk primary key (id),
    constraint pbill_item_bookp_fk1 foreign key (id_pbill_item) references pbill_item(id),
    constraint pbill_item_bookp_fk2 foreign key (id_bookkeeping_turnover) references BOOKKEEPING(id),
    constraint pbill_item_bookp_fk3 foreign key (id_bookkeeping_vat) references BOOKKEEPING(id)
);
commit;
grant all on pbill_item_bookp to untilluser;
commit;
execute procedure register_sync_table_ex('pbill_item_bookp', 'p', 1);
commit;
 