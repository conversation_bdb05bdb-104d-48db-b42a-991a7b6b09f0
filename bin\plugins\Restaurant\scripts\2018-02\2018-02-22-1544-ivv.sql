set term ^;

create or alter trigger sold_articles_insert for sold_articles after insert
AS
declare variable calc_gross smallint;
declare variable ord_price decimal(17,4);
declare variable single_price decimal(17,4);
declare variable ord_orig_price decimal(17,4);
declare variable negative smallint;
declare variable cntVoid int;
declare variable pcname varchar(50);
declare newprice decimal(17,4);
declare vattype integer;
declare devcnt integer;
declare dbser integer;
begin
     
  if ((deleting) or (updating)) then  exit;

  select SER_SYNC_DB from untill_db into :dbser;
  if (new.id / 5000000000<>:dbser) then exit;

  select first 1 coalesce(vat_type,0) from restaurant_vars into :vattype;
  select first(1) calc_gross from restaurant_vars into    :calc_gross;
  if (:calc_gross=0) then begin
    select count(id) from computer_devices where dev_type=7 into :devcnt; 
        if (:devcnt=0) then exit;
  end
  select pcname from pbill where id=new.id_pbill into :pcname;

  if (:vattype=0) then
    select coalesce(negative,0), 
    	(new.quantity * price * new.sa_coef
          + coalesce((select sum(new.quantity * mi.quantity * mi.price * new.sa_coef) from menu_item mi where mi.id_menu=order_item.id_menu),0))
    	  * (1.00-coalesce(bill.discount,0.00)),
        (new.quantity * original_price * new.sa_coef
          + coalesce((select sum(new.quantity * mi.quantity * mi.ORIGINAL_PRICE * new.sa_coef) from menu_item mi where mi.id_menu=order_item.id_menu),0)),
        (price 
          + coalesce((select sum(mi.quantity * mi.price) from menu_item mi where mi.id_menu=order_item.id_menu),0))
          * (1.00-coalesce(bill.discount,0.00))
    from order_item
    join orders on orders.id=order_item.id_orders
    join bill on bill.id=orders.id_bill
    where order_item.id=new.id_order_item into :negative, :ord_price,:ord_orig_price,:single_price;
  else
    select coalesce(negative,0), 
    	(new.quantity*(price + vat) * new.sa_coef
        + coalesce((select sum(new.quantity * mi.quantity * new.sa_coef * (mi.price+mi.vat)) from menu_item mi where mi.id_menu=order_item.id_menu),0))
    	* (1.00-coalesce(bill.discount,0.00)),
        (new.quantity * original_price * new.sa_coef * (1.00 + vat_percent/100)
        + coalesce((select sum(new.quantity * mi.quantity * new.sa_coef * (mi.ORIGINAL_PRICE * (1.00 + mi.vat_percent/100))) from menu_item mi where mi.id_menu=order_item.id_menu),0)),
        ((price + vat) +
        + coalesce((select sum(mi.quantity * (mi.price + mi.vat)) from menu_item mi where mi.id_menu=order_item.id_menu),0))
        * (1.00-coalesce(bill.discount,0.00))
    from order_item
    join orders on orders.id=order_item.id_orders
    join bill on bill.id=orders.id_bill
    where order_item.id=new.id_order_item into :negative, :ord_price,:ord_orig_price,:single_price;

  select count(*) from voided_items where id_order_item=new.id_order_item into :cntVoid;
  if (cntVoid>0) then
    insert into grosstotal(amount, neg_amount, pcname) values(0, :ord_price, :pcname);
  else if (:negative=1) then
    insert into grosstotal(amount, neg_amount, pcname) values(0, :ord_price, :pcname);
  else if (single_price<0) then
    insert into grosstotal(amount, neg_amount, pcname) values(0, :ord_price, :pcname);
  else
    insert into grosstotal(amount, neg_amount, pcname) values(:ord_orig_price, :ord_price-:ord_orig_price, :pcname);
end
^
commit
^

set term ;^

