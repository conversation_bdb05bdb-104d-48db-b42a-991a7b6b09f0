CREATE OR ALTER VIEW OI_SIZE_PRICE(
    ID_ORDER_ITEM,
    ART_SIZE_NAME)
AS
select order_item.id id_order_item,
    (case when coalesce(size_modifier_item.smi_name,'')='' then articles.name else size_modifier_item.smi_name || ' ' || articles.name end) art_size_name
from order_item
join articles on articles.id=order_item.id_articles
left outer join order_item_sizes oi_size on oi_size.id_order_item=order_item.id
left outer join size_modifier_item on size_modifier_item.id=oi_size.id_size_modifier_item;
commit;

CREATE OR ALTER VIEW PI_SIZE_PRICE(
    ID_PBILL_ITEM,
    ART_SIZE_NAME)
AS
select pbill_item.id id_pbill_item,
    (case when coalesce(size_modifier_item.smi_name,'')='' then articles.name else articles.name || ' ' || size_modifier_item.smi_name end) art_size_name
from pbill_item
join articles on articles.id = pbill_item.id_articles
left outer join order_item_sizes oi_size on oi_size.id_pbill_item=pbill_item.id
left outer join size_modifier_item on size_modifier_item.id=oi_size.id_size_modifier_item;
commit;

CREATE OR ALTER VIEW MI_SIZE_PRICE(
    ID_MENU_ITEM,
    ART_SIZE_NAME)
AS
select menu_item.id id_menu_item,
    (case when coalesce(size_modifier_item.smi_name,'')='' then articles.name else articles.name || ' ' || size_modifier_item.smi_name end) art_size_name
from menu_item
join articles on articles.id=menu_item.id_articles
left outer join order_item_sizes oi_size on oi_size.id_menu_item=menu_item.id
left outer join size_modifier_item on size_modifier_item.id=oi_size.id_size_modifier_item;
commit;

grant all on oi_size_price to untilluser;
commit;
grant all on pi_size_price to untilluser;
commit;
grant all on mi_size_price to untilluser;
commit;
