create table discounts (
    id u_id,

    name varchar(80) not null,

    discount_type smallint not null,
    value_type smallint not null,
    discount_value decimal(17,4) not null,

    id_discount_reasons bigint,

    free_reason_type smallint,
    free_reason_text varchar(80),

    id_periods bigint,

    check_type smallint,

    id_department bigint,
    id_courses bigint,
    id_food_group bigint,
    id_category bigint,

    ml_name BLOB SUB_TYPE 0 SEGMENT SIZE 80,

    is_active smallint,
    is_active_modified timestamp,
    is_active_modifier varchar(30),

    num smallint,
    color integer,
    font_color integer,

    constraint discounts_pk primary key (id),
	constraint discounts_fk1 foreign key (id_discount_reasons) references discount_reasons(id),
	constraint discounts_fk2 foreign key (id_periods) references periods(id),
	constraint discounts_fk3 foreign key (id_department) references department(id),
	constraint discounts_fk4 foreign key (id_courses) references courses(id),
	constraint discounts_fk5 foreign key (id_food_group) references food_group(id),
	constraint discounts_fk6 foreign key (id_category) references category(id)
);

commit;
grant all on discounts to untilluser;
commit;
execute procedure register_sync_table_ex('discounts', 'b', 1);
commit;
execute procedure register_bo_table('discounts', 'id_discount_reasons', 'discount_reasons');
commit;
execute procedure register_bo_table('discounts', 'id_periods', 'periods');
commit;
execute procedure register_bo_table('discounts', 'id_department', 'department');
commit;
execute procedure register_bo_table('discounts', 'id_courses', 'courses');
commit;
execute procedure register_bo_table('discounts', 'id_food_group', 'food_group');
commit;
execute procedure register_bo_table('discounts', 'id_category', 'category');
commit;
