unit TicketCacheU;

interface
uses Classes, Generics.Collections, UntillDBU, TicketsU, RetranslatorU, SysUtils;

type

  TCachedTicket = class
  private
    FPrintToDefaultA4Printer: Boolean;
    FName: String;
    FA4: Boolean;
    FStream: TMemoryStream;
    FCustomFont: String;
    FGuid: String;
    FReportType: TRepType;
    FExternalStream: Boolean;
    procedure SetA4(const Value: Boolean);
    procedure SetCustomFont(const Value: String);
    procedure SetGuid(const Value: String);
    procedure SetName(const Value: String);
    procedure SetPrintToDefaultA4Printer(const Value: Boolean);
    procedure SetStream(const Value: TMemoryStream);
    procedure SetReportType(const Value: TRepType);
  public
  	constructor Create; overload;
  	constructor Create(AStream: TMemoryStream); overload;
    destructor Destroy; override;
    procedure CheckExtraData;

  	property Stream: TMemoryStream read FStream write SetStream;
    property Name: String read FName write SetName;
    property A4: Boolean read FA4 write SetA4;
    property PrintToDefaultA4Printer: Boolean read FPrintToDefaultA4Printer write SetPrintToDefaultA4Printer;
    property Guid: String read FGuid write SetGuid;
    property CustomFont: String read FCustomFont write SetCustomFont;
    property ReportType: TRepType read FReportType write SetReportType;
    property ExternalStream: Boolean read FExternalStream;
  end;

  TTicketCache = class(TComponent, IDbEventListener)
  private
  	FDbEventsController: IDbEventsController;
  	Fdb: TUntillDB;
  	FItems: TDictionary<Int64, TCachedTicket>;
    function Load(const id: Int64): TCachedTicket;

    function OnDataChanged(tce: TDbDataChangeEvent): Boolean;
    function OnReset: Boolean;
    procedure OnListenerException(e: Exception);

  public
  	function Get(id: Int64): TCachedTicket;
		procedure Clear;

  	constructor Create(AOwner: TComponent; db: TUntillDB); reintroduce;
    destructor Destroy; override;
  end;

implementation
uses PluginU, ClassesU;

{ TCachedTicket }

constructor TCachedTicket.Create;
begin
	FStream := TMemoryStream.Create;
  FExternalStream := False;
end;

procedure TCachedTicket.CheckExtraData;
var
	lcl : TLabelCaptionList;
  hideInReports: byte;
begin
  if (Stream.Position < Stream.Size) then begin {ticket contains extra information appended, f.e. legal ticket loaded from file}
    lcl := TLabelCaptionList.Create;
    try
      Stream.Read(FA4, 1);
      Stream.Read(hideInReports, 1);
      if (FA4) then ReportType := rtA4 else ReportType := rtPOS;
      lcl.LoadFromStream(Stream);
      if (Stream.Position < Stream.Size) then begin // read even more (ver 126+)
        Stream.Read(FPrintToDefaultA4Printer, 1);
        ///...
      end;
    finally
      FreeAndNil(lcl);
    end;
  end;
end;

constructor TCachedTicket.Create(AStream: TMemoryStream);
begin
	FStream := AStream;
  FExternalStream := True;
  FPrintToDefaultA4Printer := False;
  FName := '';
  FA4 := False;
  FCustomFont := '';
  FGuid := '';
  FReportType := rtPOS;
end;

destructor TCachedTicket.Destroy;
begin
	if not FExternalStream then
		FreeAndNil(FStream);
  inherited;
end;

procedure TCachedTicket.SetA4(const Value: Boolean);
begin
  FA4 := Value;
end;

procedure TCachedTicket.SetCustomFont(const Value: String);
begin
  FCustomFont := Value;
end;

procedure TCachedTicket.SetGuid(const Value: String);
begin
  FGuid := Value;
end;

procedure TCachedTicket.SetName(const Value: String);
begin
  FName := Value;
end;

procedure TCachedTicket.SetPrintToDefaultA4Printer(const Value: Boolean);
begin
  FPrintToDefaultA4Printer := Value;
end;

procedure TCachedTicket.SetReportType(const Value: TRepType);
begin
  FReportType := Value;
end;

procedure TCachedTicket.SetStream(const Value: TMemoryStream);
begin
  FStream := Value;
end;

{ TTicketCache }

procedure TTicketCache.Clear;
var id: Int64;
begin
	for id in FItems.Keys do
    Fitems.Items[id].Free;
  Fitems.Clear;
end;

constructor TTicketCache.Create(AOwner: TComponent; db: TUntillDB);
begin
	inherited Create(AOwner);
	fdb := db;
	FItems:=TDictionary<Int64, TCachedTicket>.Create;
  FDbEventsController := TDbEventsController.Init(db, self, format('TDbEventWatcher[%s]', ['tickets']))
    .AddWatchingTables(['tickets'])
    .Subscribe;
end;

destructor TTicketCache.Destroy;
begin
	Clear;
  FItems.Free;
  inherited;
end;

function TTicketCache.Get(id: Int64): TCachedTicket;
begin
	if not FItems.TryGetValue(id, result) then begin
  	result := Load(id);
    Fitems.Add(id, result);
  end;
  result.Stream.Position := 0;
end;

function TTicketCache.Load(const id: Int64): TCachedTicket;
var iq: IIBSQL;
begin
	result := nil;
  iq := fdb.GetPreparedIIbSql('select content, name, a4print, guid, usedefaulta4prn, custom_font from tickets where id = :id');
  iq.q.Params[0].AsInt64 := id;
	iq.ExecQuery;
  if not iq.eof then begin
  	result := TCachedTicket.Create;
    result.Guid := iq.FieldByName('guid').asString;
    result.PrintToDefaultA4Printer := iq.FieldByName('usedefaulta4prn').asInteger <> 0;
    result.CustomFont := iq.FieldByName('custom_font').asString;
    result.A4 := iq.FieldByName('a4print').asInteger <> 0;
    if iq.FieldByName('a4print').asInteger = 0 then
      result.ReportType := rtPOS
    else
      result.ReportType := rtA4;
    result.Name := iq.Fields[1].asString;
    iq.Fields[0].SaveToStream(result.Stream);
  end else
    plugin.RaiseException('Ticket layout not found');
end;

function TTicketCache.OnDataChanged(tce: TDbDataChangeEvent): Boolean;
begin
	FItems.Clear;
  result := true;
end;

procedure TTicketCache.OnListenerException(e: Exception);
begin
  inherited;

end;

function TTicketCache.OnReset: Boolean;
begin
	FItems.Clear;
  result := true;
end;

end.
