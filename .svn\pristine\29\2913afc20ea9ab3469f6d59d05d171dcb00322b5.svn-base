[Start]
**************************************************
TimeStamp=12.09.2023 10:35
Interval=0;30.12.1899
[Class]
Name=TBLRMsgSetKNumbers
TimeStamp=15.12.2017 16:33
Interval=7.32523112674244E-5;30.12.1899 0:00:06
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=4
bill=11
deposit=1
order=1
tr=1366
[Class]
Name=TBLMessageKeyboardString
TimeStamp=12.09.2023 10:35
Interval=2.77199069387279E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRGMTestClearSales
TimeStamp=12.09.2023 10:36
Interval=5.88425900787115E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=42037.4898148148;02.02.2015 11:45:20
[Class]
Name=TBLRGMTestCombineComboVatDrivers
TimeStamp=12.09.2023 10:36
Interval=8.30092612886801E-5;30.12.1899 0:00:07
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
turnOn=True
[Class]
Name=TBLRMsgTableNo
TimeStamp=12.09.2023 10:36
Interval=9.05671331565827E-5;30.12.1899 0:00:07
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=
tableno=1
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=12.09.2023 10:36
Interval=6.94417394697666E-8;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=
tableno=1
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=12.09.2023 10:36
Interval=3.02430562442169E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=2
id=5000000121
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=12.09.2023 10:36
Interval=1.6562502423767E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=12
articleType=atNormal
id=25000077559
ID_ARTICLE_BARCODES=0
id_courses=0
ID_SIZE_MODIFIER_ITEM=0
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=12.09.2023 10:36
Interval=1.1979165719822E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000224
id_option=15000037979
[Class]
Name=TBLRMsgOption
TimeStamp=12.09.2023 10:36
Interval=1.85416647582315E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=25000077413
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=12.09.2023 10:36
Interval=3.18518505082466E-5;30.12.1899 0:00:02
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 1      <USER> <GROUP>                                                           
 1      Coca Cola Info       1,00     1,00           1,00         0,50  0,50    
             Coca Info                                                          
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=4
id_printer=0
id_ticket=0
ignoreClientQuestion=False
SendByEmail=False
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=12.09.2023 10:36
Interval=1.96527762454934E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=24
AskEmail=False
AsTips=False
AsVoucher=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
DetailText=
EFTData=
EFTParams=
Email=
EmailInvoice=False
ExternalID=
idParts=0
id_payments=15000033615
IgnoreCheckProforma=False
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
ZeroPrice=False
[Class]
Name=TBLMessageInput
TimeStamp=12.09.2023 10:36
Interval=1.334490661975E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=2
keyboard=False
s=2
[Class]
Name=TBLRMsgGetHCClient
TimeStamp=12.09.2023 10:36
Interval=7.61574483476579E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">2</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=12.09.2023 10:36
Interval=1.71759238583036E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">11</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[1]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Promo 3</articleName><articleNumber type="NS2:int">4</articleNumber><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Warme Dranken</departmentName><departmentNumber type="NS2:int">2</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">1</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">1</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">1</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">1466</transactionId><transactionNumber type="NS2:string">1366</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 1      <USER> <GROUP>              1,00     1,00           1,00         0,50  0,50    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
HotelConce  1,00        1,00        0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Start]
**************************************************
TimeStamp=12.09.2023 10:37
Interval=0.000454768516647164;30.12.1899 0:00:39
[Class]
Name=TBLRGMTestCombineComboVatDrivers
TimeStamp=12.09.2023 10:37
Interval=7.42476913728751E-5;30.12.1899 0:00:06
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
turnOn=False
[Class]
Name=TBLRMsgTableNo
TimeStamp=12.09.2023 10:37
Interval=2.00578651856631E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=
tableno=1
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=12.09.2023 10:37
Interval=5.7872966863215E-8;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=12
DelayType=odltNormal
id_bill=0
id_client=0
native_table=0
NeedOrderName=False
NotCheckTableState=False
NumberOfCovers=4
SalesArea=5000000081
TableName=
tableno=1
TableToBookClient=False
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=12.09.2023 10:37
Interval=2.69791635219008E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=2
id=5000000121
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=12.09.2023 10:37
Interval=1.93981541087851E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=12
articleType=atNormal
id=25000077559
ID_ARTICLE_BARCODES=0
id_courses=0
ID_SIZE_MODIFIER_ITEM=0
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=12.09.2023 10:37
Interval=1.28124956972897E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000224
id_option=15000037979
[Class]
Name=TBLRMsgOption
TimeStamp=12.09.2023 10:37
Interval=3.67129614460282E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=25000077413
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=12.09.2023 10:37
Interval=1.77430556504987E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------Frisdranken-------------------                               
 1      <USER> <GROUP>                                                           
 1      Coca Cola Info       1,00     1,00           1,00         0,50  0,50    
             Coca Info                                                          
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=4
id_printer=0
id_ticket=0
ignoreClientQuestion=False
SendByEmail=False
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=12.09.2023 10:37
Interval=9.44444764172658E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=24
AskEmail=False
AsTips=False
AsVoucher=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
DetailText=
EFTData=
EFTParams=
Email=
EmailInvoice=False
ExternalID=
idParts=0
id_payments=15000033615
IgnoreCheckProforma=False
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
ZeroPrice=False
[Class]
Name=TBLMessageInput
TimeStamp=12.09.2023 10:37
Interval=9.87268140306696E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=2
keyboard=False
s=3
[Class]
Name=TBLRMsgGetHCClient
TimeStamp=12.09.2023 10:37
Interval=4.82639006804675E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">3</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=12.09.2023 10:37
Interval=1.28009269246832E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">12</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[3]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Promo 3</articleName><articleNumber type="NS2:int">4</articleNumber><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Warme Dranken</departmentName><departmentNumber type="NS2:int">2</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">0</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">0</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Sherry Medium</articleName><articleNumber type="NS2:int">84</articleNumber><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Aperitieven</departmentName><departmentNumber type="NS2:int">4</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Alcohols</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">0</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">0</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Coca Cola Info</articleName><articleNumber type="NS2:int">209</articleNumber><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Frisdranken</departmentName><departmentNumber type="NS2:int">1</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">1</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">1</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">1</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">1467</transactionId><transactionNumber type="NS2:string">1367</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 1      <USER> <GROUP>              1,00     1,00           1,00         0,50  0,50    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
HotelConce  1,00        1,00        0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRGMTestClearSales
TimeStamp=12.09.2023 10:36
Interval=5.88425900787115E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=42037.4898148148;02.02.2015 11:45:20
