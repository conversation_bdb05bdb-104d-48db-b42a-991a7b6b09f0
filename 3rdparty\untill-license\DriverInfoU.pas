unit DriverInfoU;

interface

type
  TDriverInfo = record
    DriverId: string;
    DisplayName: string;
    FuncId: Integer;
    BarcodeBase: string;
    SameRenewalPrice: Boolean;
    AvailableForUntill: Boolean;
    AvailableForApostill: Boolean;
  end;

const
  DRIVER_INFOS: array[0..25] of TDriverInfo = (
    (DriverId: 'com.untill.drivers.takeawaycom.TakeawayComDriver'; DisplayName: 'Takeaway.com'; FuncId: 35; BarcodeBase: 'TAKEAWAY'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.sevenrooms.SevenroomsDriver'; DisplayName: 'Sevenrooms'; FuncId: 36; BarcodeBase: 'SEVENROOMS'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.netresto.NetrestoDriver'; DisplayName: 'Netresto'; FuncId: 37; BarcodeBase: 'NETRESTO'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.piggygiftcards.PiggyGiftcardsDriver'; DisplayName: 'Piggy Giftcards'; FuncId: 38; BarcodeBase: ''; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.piggyloyalty.PiggyLoyaltyDriver'; DisplayName: 'Piggy Loyalty'; FuncId: 38; BarcodeBase: ''; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.piggyvouchers.PiggyVouchersDriver'; DisplayName: 'Piggy Vouchers'; FuncId: 38; BarcodeBase: ''; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.clockpms.ClockPmsDriver'; DisplayName: 'Clock PMS'; FuncId: 39; BarcodeBase: 'CLOCKPMS'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.guestline.GuestlineDriver'; DisplayName: 'Guestline'; FuncId: 40; BarcodeBase: 'GUESTLINE'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.dinetime.DinetimeDriver'; DisplayName: 'Dinetime'; FuncId: 41; BarcodeBase: 'DINETIME'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.glorymoneymachine.GloryMoneyMachineDriver'; DisplayName: 'Glory Cash Machine'; FuncId: 42; BarcodeBase: 'GLORYMM'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.intersolveok.IntersolveOkDriver'; DisplayName: 'Intersolve'; FuncId: 45; BarcodeBase: 'INTERSOLVE'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.adoria.AdoriaDriver'; DisplayName: 'Adoria'; FuncId: 46; BarcodeBase: 'ADORIA'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.apaleohotel.ApaleoHotelDriver'; DisplayName: 'Apaleo'; FuncId: 47; BarcodeBase: 'APALEO'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.xafaxnetpayeft.XafaxNetpayEftDriver'; DisplayName: 'Xafax'; FuncId: 48; BarcodeBase: 'XAFAX'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.shijihotel.ShijiHotelDriver'; DisplayName: 'Shiji'; FuncId: 50; BarcodeBase: 'SHIJI'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.tflexcashmachine.TFlexCashMachineDriver'; DisplayName: 'T-Flex'; FuncId: 51; BarcodeBase: 'TFLEX'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.asahotel.AsaHotelDriver'; DisplayName: 'ASA'; FuncId: 52; BarcodeBase: 'ASA'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.loyyocards.LoyyoCardsDriver'; DisplayName: 'Loyyo'; FuncId: 53; BarcodeBase: 'LOYYO'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.sentoo.SentooDriver'; DisplayName: 'Sentoo'; FuncId: 55; BarcodeBase: 'SENTOO'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.como.ComoDriver'; DisplayName: 'Como'; FuncId: 56; BarcodeBase: 'COMO'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.zenchef.ZenchefDriver'; DisplayName: 'Zenchef'; FuncId: 57; BarcodeBase: 'ZENCHEF'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.operacloud.OperaCloudDriver'; DisplayName: 'Opera Cloud'; FuncId: 58; BarcodeBase: 'OPERACLOUD'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.worldlinetim.WorldlineTimDriver'; DisplayName: 'Worldline TIM'; FuncId: 59; BarcodeBase: 'WORLDLINETIM'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.klearlycloud.KlearlycloudDriver'; DisplayName: 'Klearly Cloud'; FuncId: 60; BarcodeBase: 'KLEARLYCLOUD'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.accountill.AccountillDriver'; DisplayName: 'Accountill'; FuncId: 61; BarcodeBase: 'ACCOUNTILL'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False),
    (DriverId: 'com.untill.drivers.unbill.UnbillDriver'; DisplayName: 'unBill'; FuncId: -1; BarcodeBase: 'UNBILL'; SameRenewalPrice: True; AvailableForUntill: True; AvailableForApostill: False)
  );

implementation

end.
