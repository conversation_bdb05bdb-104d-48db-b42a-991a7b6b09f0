set term !! ;
create or alter procedure fix_comp_dev_guids
as 
  declare variable server_no integer;
begin
  select server_no from sync_db, untill_db where sync_db.ser=untill_db.ser_sync_db into :server_no;
  if (:server_no <> 1) then exit;
  execute procedure set_logging_on;
  update computer_devices set guid=(select replace(uuid_to_char(gen_uuid()), '-', '') from rdb$database) where guid is null;
  execute procedure set_logging_off;
end;
!!
commit
!!
grant execute on procedure fix_comp_dev_guids to untilluser
!!
commit
!!
execute procedure fix_comp_dev_guids
!!
commit
!!
set term ; !!
