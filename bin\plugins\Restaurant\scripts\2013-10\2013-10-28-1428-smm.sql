set term !! ;
create or alter procedure BE<PERSON><PERSON><PERSON>_CREDIT (
    ID_UNTILL_USERS bigint,
    ID_ARTICLES bigint,
    QUANTITY integer,
    ID_BECO_LOCATIONS bigint)
as
declare variable WAITER integer;
declare variable PLU integer;
declare variable DEBITCREDIT smallint;
begin
  select first 1 coalesce(NUMBER_VANDUIJNEN, 0) from waiters where ID_UNTILL_USERS=:ID_UNTILL_USERS into :WAITER;

  select first 1 bcl.PLU_NUMBER from beco_article_locations bcl where bcl.ID_ARTICLES=:ID_ARTICLES and bcl.ID_LOCATIONS=:ID_BECO_LOCATIONS into :PLU;

  if (coalesce(PLU, 0)=0) then begin
    select a.PLU_NUMBER_VANDUIJNEN from articles a where a.ID=:ID_ARTICLES into :PLU;
  end

  if (coalesce(PLU, 0)>0) then begin
    insert into BCQ_CREDIT(plu, quantity, waiter, ID_BECO_LOCATIONS) values (:PLU, :QUANTITY, :WAITER, :ID_BECO_LOCATIONS);
  end
end
!!
commit
!!
set term ; !!
GRANT EXECUTE ON PROCEDURE BEVERAGE_CREDIT TO UNTILLUSER;
commit;
