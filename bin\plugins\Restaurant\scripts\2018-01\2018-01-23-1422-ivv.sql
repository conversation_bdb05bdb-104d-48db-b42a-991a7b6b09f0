create table ta_bill_transfers (
	id 					u_id,
	id_bill 			bigint,	
	from_timeslot_start timestamp,
	from_timeslot_end   timestamp,
	to_timeslot_start 	timestamp,
	to_timeslot_end   	timestamp,
	op_datetime			timestamp,		
	id_untill_users   	bigint,	
	pc_name   		  	varchar(50),	
    constraint ta_bill_transfers_pk primary key (id),
	constraint ta_bill_transfers_fk1 foreign key (id_bill) references bill(id),
	constraint ta_bill_transfers_fk2 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on ta_bill_transfers to untilluser;
commit;
execute procedure register_sync_table_ex('ta_bill_transfers', 'p', 1);
commit;



