create table voucher_adjustments (
    id u_id,
    id_vouchers 	bigint,
    id_untill_users 	bigint,
    adjustment_dt 	decimal(17,4),
    prev_amount 	decimal(17,4),
    new_amount  	timestamp,
    voucher_state 	integer,
    constraint vch_adj_pk primary key (id),
    constraint vch_adj_fk1 foreign key (id_vouchers) references vouchers(id),
    constraint vch_adj_fk2 foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on voucher_adjustments to untilluser;
commit;
execute procedure register_sync_table_ex('voucher_adjustments', 'p', 1);
commit;

