CREATE OR ALTER VIEW GETCPPPRICE(
    ID_ORDER_ITEM,
    PRICE)
AS
select oi.id id_order_item,
       cp.coupon_template_price / (select coalesce(sum(oi1.quantity * oi1.original_price),1)
        from order_item oi1
        join coupon_payments cp1 on cp1.id_order_item = oi1.id
        where cp1.id_coupon_items = cp.id_coupon_items and oi1.rowbeg = 0)
    from order_item oi
    left outer join coupon_payments cp on cp.id_order_item = oi.id
;
