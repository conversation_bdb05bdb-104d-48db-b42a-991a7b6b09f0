CREATE VIEW separate_bill_courses(id_bill, id_courses )
AS
select id_bill, id_courses
from order_item
join orders on orders.id=order_item.id_orders
join courses on courses.id=order_item.id_courses and courses.SEPARATE=1
where not id_courses is null
union
select id_bill, menu_item.id_courses
from menu_item
join order_item on menu_item.id_menu=order_item.id_menu
join orders on orders.id=order_item.id_orders
join courses on courses.id=menu_item.id_courses and courses.SEPARATE=1
where not menu_item.id_courses is null;
commit;

grant all on separate_bill_courses to untilluser;
commit;

