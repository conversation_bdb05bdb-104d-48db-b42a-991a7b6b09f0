[Start]
**************************************************
TimeStamp=13.12.2017 14:12
Interval=0;30.12.1899
[Class]
Name=TBLMessageKeyboardString
TimeStamp=13.12.2017 14:12
Interval=7.76620436226949E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:12
Interval=8.11342761153355E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:12
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=13.12.2017 14:12
Interval=5.74884252273478E-5;30.12.1899 0:00:04
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=42037.4898148148;02.02.2015 11:45:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:12
Interval=6.95601920597255E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:12
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLMsgResetDate
TimeStamp=13.12.2017 14:12
Interval=1.77314796019346E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
ShowException=True
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:12
Interval=1.49537008837797E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:12
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRMsgSetKNumbers
TimeStamp=13.12.2017 14:12
Interval=0.000134560184960719;30.12.1899 0:00:11
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=4
bill=1
deposit=1
order=1
tr=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:12
Interval=7.10532403900288E-5;30.12.1899 0:00:06
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:12
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=13.12.2017 14:13
Interval=1.44907389767468E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=5.37847226951271E-5;30.12.1899 0:00:04
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000193
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=2.8969909180887E-5;30.12.1899 0:00:02
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=5000000202
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 14:13
Interval=2.21412046812475E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=0
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 14:13
Interval=8.11342033557594E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=0
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 14:13
Interval=1.79629641934298E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=10000016099
id_option=5000000109
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 14:13
Interval=9.15509735932574E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=1.92476800293662E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000194
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 14:13
Interval=4.96064822073095E-5;30.12.1899 0:00:04
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>               <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
 1      Tango                2,00     2,00   0,35    1,65 21,0%         1,65    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 14:13
Interval=1.85185490408912E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>               <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
 1      Tango                2,00     2,00   0,35    1,65 21,0%         1,65    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        3,70        3,70        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:13
Interval=3.02546250168234E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:13
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=13.12.2017 14:13
Interval=6.17824116488919E-5;30.12.1899 0:00:05
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=2.23611132241786E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000193
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 14:13
Interval=3.5243050660938E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=1.33217617985792E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=5000000202
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 14:13
Interval=1.0092589945998E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000339
id_option=5000000109
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 14:13
Interval=9.6435185696464E-5;30.12.1899 0:00:08
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 14:13
Interval=1.65740784723312E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000186
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 14:13
Interval=7.5798605394084E-5;30.12.1899 0:00:06
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER> <GROUP> Cl.          3,10     3,10   0,54    2,56 21,0%         2,56    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 14:13
Interval=2.61921304627322E-5;30.12.1899 0:00:02
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=15000033615
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 14:13
Interval=1.26388913486153E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=1
[Class]
Name=TBLRMsgGetHCClient
TimeStamp=13.12.2017 14:13
Interval=1.28703686641529E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">1</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:13
Interval=1.07754676719196E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">2</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[1]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Pils 50 Cl.</articleName><articleNumber type="NS2:int">51</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Bieren</departmentName><departmentNumber type="NS2:int">3</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Alcohols</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">3.1</singlePrice><singleVatAmount type="NS2:decimal">0.538</singleVatAmount><totalPrice type="NS2:decimal">3.1</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0.538</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">1</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">102</transactionId><transactionNumber type="NS2:string">2</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Table name:    1                                                                
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER> <GROUP> Cl.          3,10     3,10   0,54    2,56 21,0%         2,56    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
HotelConce  3,10        3,10        0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 14:14
Interval=5.57523089810275E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:14
Interval=2.88541705231182E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:14
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRMsgSetHCGuestInfo
TimeStamp=13.12.2017 14:14
Interval=7.46527730370872E-5;30.12.1899 0:00:06
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=2
GuestName=John
GuestStatus=11
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:14
Interval=9.15509735932574E-6;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:14
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 14:14
Interval=6.78009237162769E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 14:14
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:15
Interval=0.000462465279269964;30.12.1899 0:00:39
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:15
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=13.12.2017 14:15
Interval=6.23611122136936E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 14:15
Interval=5.00694441143423E-5;30.12.1899 0:00:04
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:45:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:15
Interval=0.000418310184613802;30.12.1899 0:00:36
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:15
Interval=1.01967598311603E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Start]
**************************************************
TimeStamp=13.12.2017 14:51
Interval=0;30.12.1899
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 14:51
Interval=2.92013937723823E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:52
Interval=3.04861096083187E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:52
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=13.12.2017 14:52
Interval=6.32870360277593E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:52
Interval=1.88888880074956E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:52
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 14:53
Interval=0.00122236111201346;30.12.1899 0:01:45
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 14:53
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 14:53
Interval=3.14120334223844E-5;30.12.1899 0:00:02
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
    5000000193     57    Mazout    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne       1.7     0.295      1     21                           
    5000000194     58     Tango    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne         2    0.3471      1     21                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne       3.7         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:53
Interval=1.05439830804244E-5;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:54
Interval=0.00050953703612322;30.12.1899 0:00:44
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgNeedPBillList
TimeStamp=13.12.2017 14:54
Interval=8.78587961778976E-5;30.12.1899 0:00:07
Exception=
StateGUID={40226FE0-DEEB-48bc-9988-0EF394B1C7B3}
StateName=TBLRGetPBillList
FieldCount=3
CurrentUserOnly=False
PaymentKind=-1
PaymentName=
[Class]
Name=TBLRMsgGotoPBill
TimeStamp=13.12.2017 14:54
Interval=3.47106470144354E-5;30.12.1899 0:00:02
Exception=
StateGUID={40226FE0-DEEB-48bc-9988-0EF394B1C7B3}
StateName=TBLRGetPBillList
FieldCount=2
bill_number=
Direction=0
[Class]
Name=TBLRMsgReopenBill
TimeStamp=13.12.2017 14:54
Interval=4.44907418568619E-5;30.12.1899 0:00:03
Exception=
StateGUID={40226FE0-DEEB-48bc-9988-0EF394B1C7B3}
StateName=TBLRGetPBillList
FieldCount=1
bKeepWaiter=kpwPaid
[Class]
Name=TBLRMsgReopenBill
TimeStamp=13.12.2017 14:54
Interval=1.3773147657048E-5;30.12.1899 0:00:01
Exception=
StateGUID={40226FE0-DEEB-48bc-9988-0EF394B1C7B3}
StateName=TBLRGetPBillList
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">2</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[1]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Pils 50 Cl.</articleName><articleNumber type="NS2:int">51</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Bieren</departmentName><departmentNumber type="NS2:int">3</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Alcohols</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">3.1</singlePrice><singleVatAmount type="NS2:decimal">0.538</singleVatAmount><totalPrice type="NS2:decimal">3.1</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0.538</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">1</tableNumber><tablePart type="NS2:string">a</tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">102</transactionId><transactionNumber type="NS2:string">2</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">-1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=1
bKeepWaiter=kpwPaid
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:54
Interval=1.04513892438263E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:55
Interval=3.75925956177525E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:55
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 14:55
Interval=4.31481457781047E-5;30.12.1899 0:00:03
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 14:55
Interval=1.4930556062609E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>               <GROUP>,70     1,70   0,30    1,40 21,0%         1,40    
 1      Tango                2,00     2,00   0,35    1,65 21,0%         1,65    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        3,70        3,70        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:55
Interval=4.48611099272966E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 14:55
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=1
tableno=1
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 14:55
Interval=2.48726864811033E-5;30.12.1899 0:00:02
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 14:55
Interval=1.23958307085559E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table:   1 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    1                                                                
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER> <GROUP> Cl.          3,10     3,10   0,54    2,56 21,0%         2,56    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Visa        3,10        3,10        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001502
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Visa
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 14:55
Interval=0.000205393516807817;30.12.1899 0:00:17
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:55
Interval=1.06134320958517E-5;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:55
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=13.12.2017 14:55
Interval=4.14930545957759E-5;30.12.1899 0:00:03
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37661.6312717014;09.02.2003 15:09:01
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:55
Interval=2.49884251388721E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:55
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 14:55
Interval=5.05324060213752E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 14:55
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 14:55
Interval=1.46296297316439E-5;30.12.1899 0:00:01
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2003 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
    5000000186     51  Pils 50     5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne       3.1     0.538      1     21                           
    5000000193     57    Mazout    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne       1.7     0.295      1     21                           
    5000000194     58     Tango    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne         2    0.3471      1     21                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne       3.7         0
     1     2                Visa  50000000      1   Taverne       3.1         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:55
Interval=9.32870170800015E-6;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 14:56
Interval=0.000266979172010906;30.12.1899 0:00:23
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 14:56
Interval=5.87615722906776E-5;30.12.1899 0:00:05
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 14:56
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLMsgSetDate
TimeStamp=13.12.2017 15:13
Interval=0.0118395949102705;30.12.1899 0:17:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
DateTime=38020.4898148148;03.02.2004 11:45:20
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:13
Interval=0.000261724533629604;30.12.1899 0:00:22
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:13
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=13.12.2017 15:13
Interval=2.20138899749145E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 15:13
Interval=1.89004640560597E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=25000077479
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageUndo
TimeStamp=13.12.2017 15:13
Interval=1.79629641934298E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 15:13
Interval=7.99768167780712E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000077485
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:13
Interval=1.56481473823078E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000339
id_option=5000000109
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:13
Interval=2.54630140261725E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000198
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 15:13
Interval=8.61226799315773E-5;30.12.1899 0:00:07
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 15:13
Interval=1.85185490408912E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 03.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 1      <USER> <GROUP>            102,78   102,78  50,00   52,78 94,7%         52,78   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        102,78      102,78      0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 15:13
Interval=5.25115756317973E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:13
Interval=1.90046266652644E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:13
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:14
Interval=7.83564828452654E-5;30.12.1899 0:00:06
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:14
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 15:14
Interval=0.000173506938153878;30.12.1899 0:00:14
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 15:14
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38020.4905092593;03.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 15:14
Interval=1.48379622260109E-5;30.12.1899 0:00:01
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [02.02.2004 11:46:20] till [03.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
   25000077485      2   Promo 2    5000000121      2  Warme Dr    5000000091      0  Zonder A    5000000081      1   Taverne    102.78   50.0015      1      0                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne    102.78         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:14
Interval=0.000331030096276663;30.12.1899 0:00:28
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:14
Interval=2.40509252762422E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:14
Interval=2.6782407076098E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:14
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=13.12.2017 15:14
Interval=4.70370359835215E-5;30.12.1899 0:00:04
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38386.4898148148;03.02.2005 11:45:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:14
Interval=1.05902799987234E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:14
Interval=1.27314706332982E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLMsgResetDate
TimeStamp=13.12.2017 15:15
Interval=9.91898123174906E-5;30.12.1899 0:00:08
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
ShowException=True
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:15
Interval=0.000361736114427913;30.12.1899 0:00:31
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:15
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=13.12.2017 15:15
Interval=4.66319397673942E-5;30.12.1899 0:00:04
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 15:15
Interval=1.20254699140787E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000077485
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:15
Interval=7.53471977077425E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000339
id_option=5000000109
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:15
Interval=2.25694384425879E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000198
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 15:15
Interval=0.000107511572423391;30.12.1899 0:00:09
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:15
Interval=1.48379622260109E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
s=5
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:15
Interval=4.40972507931292E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
s=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 15:15
Interval=7.30324245523661E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 15:15
Interval=2.12036975426599E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 1      <USER> <GROUP>            102,78   102,78  50,00   52,78 94,7%         52,78   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        50,00       50,00       0,00                                        
Visa        52,78       52,78       0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001502
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Visa
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 15:16
Interval=7.08217630744912E-5;30.12.1899 0:00:06
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:16
Interval=8.80786683410406E-6;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:16
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=13.12.2017 15:16
Interval=7.093750173226E-5;30.12.1899 0:00:06
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:16
Interval=1.03124984889291E-5;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:16
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 15:16
Interval=6.18981430307031E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 15:16
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 15:16
Interval=2.17939814319834E-5;30.12.1899 0:00:01
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
   25000077485      2   Promo 2    5000000121      2  Warme Dr    5000000091      0  Zonder A    5000000081      1   Taverne    102.78   50.0015      1      0                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne        50         0
     1     2                Visa  50000000      1   Taverne     52.78         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:16
Interval=0.000176296300196555;30.12.1899 0:00:15
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:16
Interval=2.53819453064352E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:16
Interval=8.99421283975244E-5;30.12.1899 0:00:07
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:16
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=13.12.2017 15:16
Interval=3.0254632292781E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38385.4898148148;02.02.2005 11:45:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:16
Interval=9.03935142559931E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:16
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:16
Interval=7.74189829826355E-5;30.12.1899 0:00:06
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=13.12.2017 15:16
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=13.12.2017 15:16
Interval=1.33333305711858E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=13.12.2017 15:16
Interval=1.40277770697139E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000077485
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:16
Interval=9.5023206085898E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000339
id_option=5000000109
[Class]
Name=TBLRMsgOption
TimeStamp=13.12.2017 15:16
Interval=2.66203278442845E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000198
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=13.12.2017 15:17
Interval=8.31134311738424E-5;30.12.1899 0:00:07
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:17
Interval=1.44791629281826E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:17
Interval=5.45138755114749E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
s=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 15:17
Interval=6.7812499764841E-5;30.12.1899 0:00:05
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:17
Interval=1.39004623633809E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 0,10   Promo 2             10,00   102,78   4,86    5,14 94,7%         5,14    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        10,00       10,00       0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=13.12.2017 15:17
Interval=3.2222225854639E-5;30.12.1899 0:00:02
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=15000033615
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:17
Interval=1.25231454148889E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=13.12.2017 15:17
Interval=2.89351737592369E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=0
[Class]
Name=TBLRMsgGetHCClient
TimeStamp=13.12.2017 15:17
Interval=4.17824048781767E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">10</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=13.12.2017 15:17
Interval=1.27430612337776E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">10</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[3]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Promo 2</articleName><articleNumber type="NS2:int">2</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Warme Dranken</departmentName><departmentNumber type="NS2:int">2</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">0</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">0</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Omelet Natuur</articleName><articleNumber type="NS2:int">331</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Keuken</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Snacks</departmentName><departmentNumber type="NS2:int">22</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Warme Gerechten</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">2.5095</singlePrice><singleVatAmount type="NS2:decimal">0.2281</singleVatAmount><totalPrice type="NS2:decimal">2.5095</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0.2281</totalVatAmount><vatPercent type="NS2:decimal">10</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Puur</articleName><articleNumber type="NS2:int">910</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Diversen</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Teksten</departmentName><departmentNumber type="NS2:int">51</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Teksten</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">90.2705</singlePrice><singleVatAmount type="NS2:decimal">44.9084</singleVatAmount><totalPrice type="NS2:decimal">90.2705</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">44.9084</totalVatAmount><vatPercent type="NS2:decimal">99</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">105</transactionId><transactionNumber type="NS2:string">5</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 0,90   Promo 2             92,78   102,78  45,14   47,64 94,7%         47,64   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        10,00       10,00       0,00                                        
HotelConce  92,78       92,78       0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLSMsgNeedReports
TimeStamp=13.12.2017 15:17
Interval=4.70601808046922E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:17
Interval=2.09837962756865E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:17
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=13.12.2017 15:17
Interval=4.64814802398905E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=13.12.2017 15:17
Interval=2.33333339565434E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=13.12.2017 15:17
Interval=1.27314706332982E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=13.12.2017 15:17
Interval=5.18981469213031E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=13.12.2017 15:17
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=13.12.2017 15:17
Interval=2.44791663135402E-5;30.12.1899 0:00:02
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
   25000077485      2   Promo 2    5000000121      2  Warme Dr    5000000091      0  Zonder A    5000000081      1   Taverne        10    4.8649      1      0                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne        10         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Start]
**************************************************
TimeStamp=15.12.2017 11:41
Interval=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:42
Interval=1.9351857190486E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:42
Interval=1.46064776345156E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Start]
**************************************************
TimeStamp=15.12.2017 11:43
Interval=0.00115454861224862;30.12.1899 0:01:39
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:43
Interval=2.97916703857481E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:43
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=15.12.2017 11:43
Interval=4.41666634287685E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38385.4898148148;02.02.2005 11:45:20
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:43
Interval=3.99768541683443E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:43
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=15.12.2017 11:43
Interval=3.58217585016973E-5;30.12.1899 0:00:03
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=15.12.2017 11:43
Interval=3.29166650772095E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000120
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=15.12.2017 11:43
Interval=2.29513898375444E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=15.12.2017 11:44
Interval=1.72685176949017E-5;30.12.1899 0:00:01
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=9
articleType=atNormal
id=25000077485
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgOption
TimeStamp=15.12.2017 11:44
Interval=9.15509735932574E-6;30.12.1899
Exception=
StateGUID={B29CB48D-709E-4415-B2BC-AC4D5A9BDE18}
StateName=TBLRGetOption
FieldCount=3
free_Option=False
id_article=5000000339
id_option=5000000109
[Class]
Name=TBLRMsgOption
TimeStamp=15.12.2017 11:44
Interval=2.77777871815488E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
free_Option=False
id_article=5000000198
id_option=5000000105
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=15.12.2017 11:44
Interval=3.81365680368617E-5;30.12.1899 0:00:03
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgSplitBill
TimeStamp=15.12.2017 11:44
Interval=1.85532408067957E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
OpenType=potSimple
[Class]
Name=TBLMessageCancel
TimeStamp=15.12.2017 11:44
Interval=0.000156817135575693;30.12.1899 0:00:13
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:44
Interval=1.17013842100278E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:44
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=15.12.2017 11:44
Interval=1.27546300063841E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgSplitBillHalf
TimeStamp=15.12.2017 11:44
Interval=1.98263878701255E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=1
PartsNumber=2
[Class]
Name=TBLMessageCancel
TimeStamp=15.12.2017 11:44
Interval=5.46412047697231E-5;30.12.1899 0:00:04
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:44
Interval=7.14120687916875E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:44
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=15.12.2017 11:44
Interval=2.7164351195097E-5;30.12.1899 0:00:02
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgGetEqualPartNumber
TimeStamp=15.12.2017 11:44
Interval=9.43287159316242E-6;30.12.1899
Exception=
StateGUID={141AB2AD-3DC7-4883-B9BD-4050590D2097}
StateName=TBLSGetNumberValue
FieldCount=0
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:44
Interval=1.75925888470374E-5;30.12.1899 0:00:01
Exception=
StateGUID={141AB2AD-3DC7-4883-B9BD-4050590D2097}
StateName=TBLSGetNumberValue
FieldCount=1
s=2
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:44
Interval=7.14120687916875E-6;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=0
[Class]
Name=TBLRMsgSplitBillEqual
TimeStamp=15.12.2017 11:44
Interval=3.81944118998945E-7;30.12.1899
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=3
PartCount=2
PartsID=0
SplitType=potSimple
[Class]
Name=TBLRMsgOpenEqualPart
TimeStamp=15.12.2017 11:44
Interval=1.81018476723693E-5;30.12.1899 0:00:01
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
OpenType=potSimple
PartIndex=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:44
Interval=0.000127175924717449;30.12.1899 0:00:10
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgPayPart
TimeStamp=15.12.2017 11:44
Interval=3.62037098966539E-5;30.12.1899 0:00:03
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 0,50   Promo 2             51,39   102,78  25,00   26,39 94,7%         26,39   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        51,39       51,39       0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgOpenEqualPart
TimeStamp=15.12.2017 11:45
Interval=0.000119930555229075;30.12.1899 0:00:10
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
OpenType=potSimple
PartIndex=1
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:45
Interval=9.33564806473441E-5;30.12.1899 0:00:08
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=15000033615
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
[Class]
Name=TBLRMsgPayPart
TimeStamp=15.12.2017 11:45
Interval=2.21180525841191E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=0
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:45
Interval=1.57407412189059E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:45
Interval=1.4247685612645E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=0
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:45
Interval=4.61805757367983E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=2
[Class]
Name=TBLRMsgGetHCClient
TimeStamp=15.12.2017 11:45
Interval=5.87962858844548E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">102</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:45
Interval=1.2488424545154E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">12</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[3]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Promo 2</articleName><articleNumber type="NS2:int">2</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Warme Dranken</departmentName><departmentNumber type="NS2:int">2</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Zonder Alcohol</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">0</singlePrice><singleVatAmount type="NS2:decimal">0</singleVatAmount><totalPrice type="NS2:decimal">0</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0</totalVatAmount><vatPercent type="NS2:decimal">0</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Omelet Natuur</articleName><articleNumber type="NS2:int">331</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Keuken</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Snacks</departmentName><departmentNumber type="NS2:int">22</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Warme Gerechten</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">1.39</singlePrice><singleVatAmount type="NS2:decimal">0.1264</singleVatAmount><totalPrice type="NS2:decimal">1.39</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">0.1264</totalVatAmount><vatPercent type="NS2:decimal">10</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Puur</articleName><articleNumber type="NS2:int">910</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Diversen</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Teksten</departmentName><departmentNumber type="NS2:int">51</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Teksten</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">1</quantity><singlePrice type="NS2:decimal">50</singlePrice><singleVatAmount type="NS2:decimal">24.8744</singleVatAmount><totalPrice type="NS2:decimal">50</totalPrice><totalQuantity type="NS2:int">1</totalQuantity><totalVatAmount type="NS2:decimal">24.8744</totalVatAmount><vatPercent type="NS2:decimal">99</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">106</transactionId><transactionNumber type="NS2:string">6</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 0,50   Promo 2             51,39   102,78  25,00   26,39 94,7%         26,39   
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        51,39       51,39       0,00                                        
HotelConce  51,39       51,39       0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLSMsgNeedReports
TimeStamp=15.12.2017 11:45
Interval=0.000382303238438908;30.12.1899 0:00:33
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:45
Interval=2.85185233224183E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:45
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=15.12.2017 11:45
Interval=8.53009260026738E-5;30.12.1899 0:00:07
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=15.12.2017 11:45
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:46
Interval=2.01736111193895E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:46
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=15.12.2017 11:46
Interval=6.37499979347922E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=15.12.2017 11:46
Interval=1.01967598311603E-5;30.12.1899
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
   25000077485      2   Promo 2    5000000121      2  Warme Dr    5000000091      0  Zonder A    5000000081      1   Taverne     51.39   25.0007      1      0                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne     51.39         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:46
Interval=0.000564120375202037;30.12.1899 0:00:48
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:46
Interval=1.70370331034064E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:46
Interval=1.4722223568242E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:46
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=15.12.2017 11:47
Interval=3.70949055650271E-5;30.12.1899 0:00:03
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38385.4898148148;02.02.2005 11:45:20
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:47
Interval=7.9745368566364E-5;30.12.1899 0:00:06
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:47
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Start]
**************************************************
TimeStamp=15.12.2017 11:50
Interval=0.00104601851489861;30.12.1899 0:01:30
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=15.12.2017 11:50
Interval=2.26967604248784E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000122
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:50
Interval=1.68287078849971E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=3
[Class]
Name=TBLRMsgArticle
TimeStamp=15.12.2017 11:50
Interval=1.14814829430543E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000194
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:50
Interval=2.47337957262062E-5;30.12.1899 0:00:02
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=4
[Class]
Name=TBLRMsgArticle
TimeStamp=15.12.2017 11:50
Interval=8.66897607920691E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=9
articleType=atNormal
id=5000000203
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=15.12.2017 11:50
Interval=3.75810195691884E-5;30.12.1899 0:00:03
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Order
                             ORDER                                              
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Bar            Table name:                                                      
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 3      <USER>                <GROUP>,00     2,00   1,04    4,96 21,0%         4,96    
 4      Tripel              11,20     2,80   1,94    9,26 21,0%         9,26    
                             END OF ORDER                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgSplitBill
TimeStamp=15.12.2017 11:50
Interval=1.4120370906312E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
OpenType=potSimple
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:50
Interval=1.73263906617649E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
s=2
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=15.12.2017 11:50
Interval=7.80092523200437E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:50
Interval=1.92824081750587E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=1
s=2
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=15.12.2017 11:50
Interval=2.35185143537819E-5;30.12.1899 0:00:02
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=1
TransferType=attSimple
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:51
Interval=0.000750219907786231;30.12.1899 0:01:04
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=15000033615
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=HotelConcepts
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:51
Interval=1.42592616612092E-5;30.12.1899 0:00:01
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:51
Interval=2.66204006038606E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=0
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:51
Interval=2.31480953516439E-6;30.12.1899
Exception=
StateGUID={D21440AF-F7CF-48B2-85C1-A1D73784C932}
StateName=TBLRHCInfo
FieldCount=1
s=1
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:51
Interval=1.31018532556482E-5;30.12.1899 0:00:01
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
StateData=
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<getGuest xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema"><params><request type="NS1:GetHotelGuestRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByRoom"><room type="NS2:string">101</room><sequence type="NS2:int">0</sequence></criteria></request></params></getGuest>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
<?xml version="1.0"?>
<chargeData xmlns:NS1="http://soap.jserver.untill.eu/" xmlns:NS2="http://www.w3.org/2001/XMLSchema" xmlns:NS3="http://schemas.xmlsoap.org/soap/encoding/"><params><data type="NS1:HotelChargeRequest"><guid type="NS2:string">test-guid</guid><posId type="NS2:string">POS:test-hotel-java</posId><criteria type="NS1:GuestLookupByReservationId"><reservationId type="NS2:string">TST123</reservationId></criteria><data type="NS1:HotelChargeData"><billNumber type="NS2:string">13</billNumber><billOpenDateTime type="NS2:dateTime">2004-02-02T08:45:20.000Z</billOpenDateTime><currencyCharCode type="NS2:string">EUR</currencyCharCode><currencyDigitalCode type="NS2:string">978</currencyDigitalCode><items type="NS3:Array" NS3:arrayType="NS1:HotelChargeItem[2]"><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Tango</articleName><articleNumber type="NS2:int">58</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Bieren</departmentName><departmentNumber type="NS2:int">3</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Alcohols</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">2</quantity><singlePrice type="NS2:decimal">2</singlePrice><singleVatAmount type="NS2:decimal">0.3471</singleVatAmount><totalPrice type="NS2:decimal">4</totalPrice><totalQuantity type="NS2:int">2</totalQuantity><totalVatAmount type="NS2:decimal">0.6942</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item><item type="NS1:HotelChargeItem"><articleName type="NS2:string">Tripel</articleName><articleNumber type="NS2:int">64</articleNumber><articleThirdPartyId type="NS2:string"></articleThirdPartyId><categoryName type="NS2:string">Bar</categoryName><categoryThirdPartyId type="NS2:string"></categoryThirdPartyId><departmentName type="NS2:string">Bieren</departmentName><departmentNumber type="NS2:int">3</departmentNumber><departmentThirdPartyId type="NS2:string"></departmentThirdPartyId><discountAmount type="NS2:decimal">0</discountAmount><groupName type="NS2:string">Alcohols</groupName><groupNumber type="NS2:int">0</groupNumber><groupThirdPartyId type="NS2:string"></groupThirdPartyId><quantity type="NS2:int">2</quantity><singlePrice type="NS2:decimal">2.8</singlePrice><singleVatAmount type="NS2:decimal">0.486</singleVatAmount><totalPrice type="NS2:decimal">5.6</totalPrice><totalQuantity type="NS2:int">2</totalQuantity><totalVatAmount type="NS2:decimal">0.972</totalVatAmount><vatPercent type="NS2:decimal">21</vatPercent><vatSign type="NS2:string"></vatSign></item></items><numberOfCovers type="NS2:int">0</numberOfCovers><openDiscount type="NS2:decimal">0</openDiscount><salesAreaName type="NS2:string">Taverne</salesAreaName><salesAreaNumber type="NS2:int">1</salesAreaNumber><salesAreaThirdPartyId type="NS2:string"></salesAreaThirdPartyId><serviceCharge type="NS2:decimal">0</serviceCharge><servingTimeNumber type="NS2:int">0</servingTimeNumber><tableNumber type="NS2:int">221</tableNumber><tablePart type="NS2:string">a  </tablePart><timestamp type="NS2:dateTime">2004-02-02T08:45:20.000Z</timestamp><tips type="NS2:decimal">0</tips><transactionId type="NS2:long">107</transactionId><transactionNumber type="NS2:string">7</transactionNumber><waiterName type="NS2:string">Peter</waiterName><waiterOperatorId type="NS2:string">1</waiterOperatorId></data><requestKind type="NS2:short">1</requestKind></data></params></chargeData>

<<<TJavaHotelLink:eu.untill.sdk.drivers.hotel.hs3.Hs3HotelDriver
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 2      <USER>                <GROUP>,00     2,00   0,69    3,31 21,0%         3,31    
 2      Tripel               5,60     2,80   0,97    4,63 21,0%         4,63    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
HotelConce  9,60        9,60        0,00                                        
   Guest:   John                                                                
   Room:    12                                                                  
   F.Seq:   1                                                                   
   F. Num:  TST123                                                              
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:52
Interval=0.000362557868356816;30.12.1899 0:00:31
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=15.12.2017 11:52
Interval=7.51041734474711E-5;30.12.1899 0:00:06
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLRMsgTransferArticle
TimeStamp=15.12.2017 11:52
Interval=7.88194302003831E-6;30.12.1899
Exception=
StateGUID={844ED93B-8F90-4F53-B8F6-127FE75BB676}
StateName=TBLRSplitNote
FieldCount=2
rest_row=0
TransferType=attSimple
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:52
Interval=2.84027773886919E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
               Table name: John                                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Separate1--------------------                               
 1      <USER>                <GROUP>,00     2,00   0,35    1,65 21,0%         1,65    
 2      Tripel               5,60     2,80   0,97    4,63 21,0%         4,63    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        7,60        7,60        0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLSMsgNeedReports
TimeStamp=15.12.2017 11:52
Interval=5.89930568821728E-5;30.12.1899 0:00:05
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:52
Interval=9.28472218220122E-5;30.12.1899 0:00:08
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:52
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=15.12.2017 11:52
Interval=0.000101412035292014;30.12.1899 0:00:08
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=37296.6312717014;09.02.2002 15:09:01
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:52
Interval=7.41898111300543E-6;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:52
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=15.12.2017 11:52
Interval=4.94675914524123E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=15.12.2017 11:52
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=15.12.2017 11:52
Interval=1.61921343533322E-5;30.12.1899 0:00:01
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2002 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
    5000000194     58     Tango    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne         2    0.3471      1     21                           
    5000000203     64    Tripel    5000000122      3    Bieren    5000000090      0  Alcohols    5000000081      1   Taverne       5.6    0.9719      2     21                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne       7.6         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:53
Interval=0.000614085649431217;30.12.1899 0:00:53
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:53
Interval=1.63078657351434E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:53
Interval=3.09953684336506E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:53
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=15.12.2017 11:53
Interval=7.73032443248667E-5;30.12.1899 0:00:06
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38385.4898148148;02.02.2005 11:45:20
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:53
Interval=8.26504619908519E-5;30.12.1899 0:00:07
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLRMsgTableNo
TimeStamp=15.12.2017 11:53
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=10
id_bill=0
id_client=0
IsDelay=False
native_table=0
NeedOrderName=False
NotCheckTableState=False
SalesArea=5000000081
TableName=221
tableno=221
table_part=
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:53
Interval=1.65740784723312E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=1
[Class]
Name=TBLMessageInput
TimeStamp=15.12.2017 11:53
Interval=5.09258825331926E-6;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=0
[Class]
Name=TBLRMsgArticle
TimeStamp=15.12.2017 11:53
Interval=2.63194451690651E-5;30.12.1899 0:00:02
Exception=
StateGUID={28055024-DD33-45F3-9C75-7B134A3AE6CA}
StateName=TBLRGetPrice
FieldCount=9
articleType=atNormal
id=5000000463
NotPrint=False
PrepaidGroupId=0
PrepaidGroupInternalId=
PrepaidGroupInternalPrice=0;30.12.1899
PrepaidKind=0
PrepaidOverlimitAmount=0;30.12.1899
start_time=0;30.12.1899
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:53
Interval=1.27430539578199E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=0
[Class]
Name=TBLRMsgNeedPredefinedDiscount
TimeStamp=15.12.2017 11:54
Interval=0.000199479167349637;30.12.1899 0:00:17
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=13
Discount_apply=daMulti
Discount_subject=0
Discount_type=4
Free_reason=
FRType=frtFix
id_category=0
id_course=0
id_department=0
id_discount_group=0
id_excl_period=0
id_group=0
id_reason=10000016132
Value=10;09.01.1900
[Class]
Name=TBLRMsgNeedPayment
TimeStamp=15.12.2017 11:54
Interval=3.48842586390674E-5;30.12.1899 0:00:03
Exception=
StateGUID={E0F68320-783C-4508-935A-BC875E078D7A}
StateName=TBLRPayment
FieldCount=2
id_printer=0
id_ticket=0
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=15.12.2017 11:54
Interval=1.51620770338923E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 221 a   Waiter:   Peter 02.02.2004 11:45                                 
Table name:    221                                                              
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
--------------------Desserts---------------------                               
 10     <USER> <GROUP>        11,10     1,11   1,92    9,18 21,0%         9,18    
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        11,10       11,10       0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=19
AskEmail=False
AsTips=False
BillQty=1
DetailId=0
DetailKind=
DetailSpecified=False
EFTData=
EFTParams=
Email=
ExternalID=
idParts=0
id_payments=5000001501
IgnoreSAPart=False
IgnoreTap2=False
Invoice=False
InvoiceLayout=0
PayFromClientType=0
PayInvoice=False
paymentname=Cash
[Class]
Name=TBLSMsgNeedReports
TimeStamp=15.12.2017 11:54
Interval=3.19907412631437E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:54
Interval=2.71180542767979E-5;30.12.1899 0:00:02
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:54
Interval=1.15738657768816E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=15.12.2017 11:54
Interval=3.9525461033918E-5;30.12.1899 0:00:03
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=15.12.2017 11:54
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4905092593;02.02.2004 11:46:20
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:54
Interval=1.3090277207084E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:54
Interval=1.1574593372643E-7;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestSetEODDateTime
TimeStamp=15.12.2017 11:54
Interval=4.95023123221472E-5;30.12.1899 0:00:04
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
DateTime=36931.6312717014;09.02.2001 15:09:01
[Class]
Name=TBLRMsgHCEndOfDayExport
TimeStamp=15.12.2017 11:54
Interval=1.34375004563481E-5;30.12.1899 0:00:01
Exception=
StateGUID={B78EF766-A558-4d94-8AF8-26855A92E1D8}
StateName=TBLSShowMessage
StateData=
>>>HCEndOfDay export
**** EOD from [09.02.2001 18:09:01] till [02.02.2004 11:46:20] ****
**** Turnover2 ****
     articleId  artNo   article  departmentId  depNo   depName       groupId  group  groupNam          saId   saNo    saName     price       vat    qty   vat%   sign    smname  threedid
    5000000463    523  Banana S    5000000134     27  Desserts    5000000092      0  Koude Ge    5000000081      1   Taverne      11.1    1.9212     10     21                           
**** Payments ****
  kind  numb                name     sa_id  sa_nu   sa_name    amount       vat
     0     1                Cash  50000000      1   Taverne      11.1         0

<<<HCEndOfDay export
<<<<<EOSD
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:55
Interval=0.000384930557629559;30.12.1899 0:00:33
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLMessageOk
TimeStamp=15.12.2017 11:55
Interval=1.08911990537308E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestSetFromDateTimeOptimization
TimeStamp=15.12.2017 11:55
Interval=1.48379622260109E-5;30.12.1899 0:00:01
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
OptimizationEnabled=True
[Class]
Name=TMsgTestSetPromoAfterConfirm
TimeStamp=15.12.2017 11:55
Interval=0;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
Value=True
[Class]
Name=TBLRGMTestClearSales
TimeStamp=15.12.2017 11:55
Interval=3.08333328575827E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38385.4898148148;02.02.2005 11:45:20
