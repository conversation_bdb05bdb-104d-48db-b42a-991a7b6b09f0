create table ks_workflow_void (
    id u_id,
    id_ks_workflow_items bigint,
    quantity integer,
    id_untill_users bigint,
    op_datetime timestamp,
    transfer_identity bigint,
    constraint kswfv_pk primary key (id),
	constraint kswfv_fk_items foreign key (id_ks_workflow_items) references ks_workflow_items(id),
	constraint kswfv_fk_users foreign key (id_untill_users) references untill_users(id)
);
commit;
grant all on ks_workflow_void to untilluser;
commit;
execute procedure register_sync_table_ex('ks_workflow_void', 'p', 1);
commit;


