create table coupon_templates (
    id u_id,
    id_options     bigint,
    id_prices bigint,
    price      	   decimal(17,4),
    max_free_qty   integer,
    constraint coupon_templates_pk primary key (id),
	constraint coupon_templates_fk1 foreign key (id_options) references options(id),
	constraint coupon_templates_fk2 foreign key (id_prices) references prices(id)
);
commit;
grant all on coupon_templates to untilluser;
commit;
execute procedure register_sync_table_ex('coupon_templates', 'b', 1);
commit;
execute procedure register_bo_table_ex('coupon_templates', 'id_options', 'options',1);
commit;



