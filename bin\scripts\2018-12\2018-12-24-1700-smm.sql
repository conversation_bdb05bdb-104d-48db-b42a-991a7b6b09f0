create table pos_emails(
    id u_id,
    kind integer,
    email varchar(200),
    description varchar(200),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint pos_emails_pk primary key (id)
);
commit;
grant all on pos_emails to untilluser;
commit;
execute procedure register_sync_table_ex('pos_emails', 'b', 1);
commit;
execute procedure register_bo_table('pos_emails', '', '');
commit;

