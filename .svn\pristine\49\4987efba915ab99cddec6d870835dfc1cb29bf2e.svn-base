unit FunctionsManagerU;

interface

uses Generics.Collections, SysUtils, ClassesU, Generics.Defaults;

type

  IStateChangeHandler<T> = interface
    procedure HandleStateChange(OldState: T; NewState: T);
  end;

  TFunction<T> = class
    private
      FName: string;
      FHandlers: TList<IStateChangeHandler<T>>;
      FChilds: TObjectList<TFunction<T>>;
    public
      constructor Create(name: string);
      destructor Destroy; override;
      property name: string read FName;
      property Handlers: TList<IStateChangeHandler<T>> read FHandlers;
      property Childs: TObjectList<TFunction<T>> read FChilds;
  end;

  TFunctions<T> = class
    private
      FFuncTree: TObjectDictionary<string, TFunction<T>>;
      FFuncTreeOrdered: TList<TFunction<T>>;
      function GetByName(name: string): TFunction<T>;
      function GetFunc(name: string): TFunction<T>;
    public
      procedure RegisterFunction(name: string; ParentName: string); overload;
      procedure RegisterFunction(name: string); overload;
      property Funcs[name: string]: TFunction<T>read GetByName; default;
      property FuncTree: TObjectDictionary<string, TFunction<T>> read FFuncTree;
      property FuncTreeFirstLevelOrdered: TList<TFunction<T>> read FFuncTreeOrdered;
      constructor Create;
      destructor Destroy; override;
  end;

  TFunctionsStateManager<T> = class
    private
      FFunctions: TFunctions<T>;
      FFuncStates: TObjectDictionary<string, TState<T>>;
      FSubscribed: Integer;
      procedure SetState(name: string; NewStateValue: T);
      function GetState(name: string): T;
    public
      procedure Subscribe(name: string; Handler: IStateChangeHandler<T>);
      procedure Unsubscribe(name: string; Handler: IStateChangeHandler<T>);
      property State[name: string]: T read GetState write SetState; default;
      procedure ClearStates;
      constructor Create(Functions: TFunctions<T>);
      destructor Destroy; override;
  end;

implementation

uses AppTerminationU;

{ TFunctionsProvider }

procedure TFunctionsStateManager<T>.ClearStates;
begin
  FFuncStates.Clear;
end;

constructor TFunctionsStateManager<T>.Create(Functions: TFunctions<T>);
begin
  FFuncStates := TObjectDictionary<string, TState<T>>.Create;
  FFunctions := Functions;
  FSubscribed := 0;
end;

function TFunctions<T>.GetByName(name: string): TFunction<T>;
begin
  FFuncTree.TryGetValue(name, Result);
end;

function TFunctions<T>.GetFunc(name: string): TFunction<T>;
begin
  if not FFuncTree.TryGetValue(name, Result) then
    raise Exception.Create('Function is not registered: ' + name);
end;

destructor TFunctionsStateManager<T>.Destroy;
begin
  FreeAndNil(FFuncStates);
  assert(FSubscribed = 0);
  inherited;
end;

function TFunctionsStateManager<T>.GetState(name: string): T;
var
  State: TState<T>;
begin
  if not FFuncStates.TryGetValue(name, State) then
    raise Exception.Create(name + ' function state is not initialized');
  Result := State.State;
end;

procedure TFunctions<T>.RegisterFunction(name: string);
var
  NewFunc: TFunction<T>;
begin
  NewFunc := TFunction<T>.Create(name);
  FFuncTree.Add(name, NewFunc);
  FFuncTreeOrdered.Add(NewFunc);
end;

procedure TFunctions<T>.RegisterFunction(name, ParentName: string);
var
  ParentFunc, NewFunc: TFunction<T>;
begin
  ParentFunc := GetFunc(ParentName);
  NewFunc := TFunction<T>.Create(name);
  FFuncTree.Add(name, NewFunc);
  ParentFunc.Childs.Add(NewFunc);
end;

procedure TFunctionsStateManager<T>.Subscribe(name: string; Handler: IStateChangeHandler<T>);
var
  Func: TFunction<T>;
  State: Boolean;
begin
  Func := FFunctions[name];
  if not Assigned(Func) then
    exit;
  Func.Handlers.Add(Handler);
  if not FFuncStates.ContainsKey(name) then
    FFuncStates.Add(name, TState<T>.Create);
  Inc(FSubscribed);
end;

procedure TFunctionsStateManager<T>.Unsubscribe(name: string; Handler: IStateChangeHandler<T>);
var
  Func: TFunction<T>;
begin
  Func := FFunctions[name];
  if not Assigned(Func) then
    exit;
  Func.Handlers.Remove(Handler);
  Dec(FSubscribed);
end;

procedure TFunctionsStateManager<T>.SetState(name: string; NewStateValue: T);
var
  Func, FuncChild: TFunction<T>;
  CurrentState: TState<T>;
  OldStateValue: T;
  Handler: IStateChangeHandler<T>;
begin
  Func := FFunctions[name];
  if not Assigned(Func) then
    exit;
  for FuncChild in Func.Childs do
    SetState(FuncChild.Name, NewStateValue);
  if Func.Handlers.Count = 0 then
    exit;
  if not FFuncStates.TryGetValue(Func.Name, CurrentState) then
    exit;
  OldStateValue := CurrentState.State;
  if CurrentState.StateChanged(NewStateValue) then
    for Handler in Func.Handlers do
      Handler.HandleStateChange(OldStateValue, NewStateValue);
end;

{ TFunction }

constructor TFunction<T>.Create(name: string);
begin
  FName := name;
  FHandlers := TList<IStateChangeHandler<T>>.Create;
  FChilds := TObjectList<TFunction<T>>.Create;
end;

destructor TFunction<T>.Destroy;
begin
  FreeAndNil(FHandlers);
  FreeAndNil(FChilds);
  inherited;
end;

{ TFunctions<T> }

constructor TFunctions<T>.Create;
begin
  FFuncTree := TObjectDictionary<string, TFunction<T>>.Create;
  FFuncTreeOrdered := TList<TFunction<T>>.Create;
end;

destructor TFunctions<T>.Destroy;
begin
  FreeAndNil(FFuncTree);
  FreeAndNil(FFuncTreeOrdered);
  inherited;
end;

end.
