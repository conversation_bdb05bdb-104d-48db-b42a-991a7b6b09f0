unit AirDiscountsEntityManager;

interface
uses EntityManager2U;

type
	TAirDiscountsEntityManager = class(TPopupEntityManager)
  private
  	const F_NAME = 'name';
  	const F_NUM = 'num';
  	const F_DISCOUNT_TYPE = 'discount_type';
  	const F_VALUE_TYPE = 'value_type';
  	const F_DISCOUNT_VALUE = 'discount_value';
  	const F_ID_DISCOUNT_REASONS = 'id_discount_reasons';
  	const F_FREE_REASON_TYPE = 'free_reason_type';
  	const F_FREE_REASON_TEXT = 'free_reason_text';
  	const F_ID_PERIODS = 'id_periods';
  	const F_CHECK_TYPE = 'check_type';
  	const F_ID_DEPARTMENTS = 'id_department';
  	const F_ID_COURSES = 'id_courses';
  	const F_ID_GROUP = 'id_food_group';
  	const F_ID_CATEGORY = 'id_category';
  	const F_COLOR = 'color';
  	const F_FONT_COLOR = 'font_color';

    const S_REASON = 'reason';
		const S_CONSTRAINT = 'constraint';
		const S_APPEARANCE = 'appearance';

  protected
    class procedure DoInitForm(Form: TEntityFormDescr); override;
    procedure DoInitList(Query: TListParams2; View: TListViewDescr); override;
    class procedure DoInitNode(Node: TNodeDescr); override;
    class function GetTableDef: TManagerTableDef; override;
  end;

implementation

uses RestaurantPluginU, CommonStringsU, EntityManagerU, TreeFoldersU,
	TreeNodesU, ClassManagerU, CommonU, DataTypesU, DiscountReasonEntityManager,
  FormUnitsU, PeriodEntityManager, DepartmentEntityManager, CourseEntityManager,
  FoodGroupEntityManager, CategoryEntityManager, RestaurantCommonStringsU;

///////////////////////////////////
///
/// Dictionary
///
function STR_DISCOUNTS: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Discounts'); end;
function STR_ORDER: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Order'); end;
function STR_DISCOUNT_TYPE: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Discount Type'); end;
function STR_DISCOUNT_REASON: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Discount Reason'); end;
function STR_ALL_ITEMS: String; begin result := Plugin.Translate('AirDiscountsEntityManager','All items'); end;
function STR_SELECTED_ITEMS: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Selected items'); end;
function STR_DISCOUNT_VALUE: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Discount Value'); end;
function STR_VALUE_TYPE: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Value Type'); end;
function STR_PERCENT: String; begin result := Plugin.Translate('AirDiscountsEntityManager', 'Percentage'); end;
function STR_FIXED: String; begin result := Plugin.Translate('AirDiscountsEntityManager', 'Fixed'); end;
function STR_EXCLUDE_PERIOD: String; begin result := Plugin.Translate('AirDiscountsEntityManager', 'Exclude Period'); end;
function STR_REASON: String; begin result := Plugin.Translate('AirDiscountsEntityManager', 'Reason'); end;
function STR_FREE_REASON_TYPE: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Free Reason Type'); end;
function STR_FREE_REASON_TEXT: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Free Reason Text'); end;
function STR_FIX_TEXT: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Fixed text'); end;
function STR_ASK_REASON: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Ask Reason'); end;
function STR_CONSTRAINTS: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Constraints'); end;
function STR_APPLIED_TO: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Applied To'); end;
function STR_DEPARTMENT: String; begin result := strDep; end;
function STR_COURSE: String; begin result := strCourse; end;
function STR_FOOD_GROUP: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Group'); end;
function STR_CATEGORY: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Category'); end;
function STR_APPEARANCE: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Appearance'); end;
function STR_BACK_COLOR: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Background Color'); end;
function STR_FONT_COLOR: String; begin result := Plugin.Translate('AirDiscountsEntityManager','Font Color'); end;

///////////////////////////////////


{ TAirDiscountsEntityManager }

class procedure TAirDiscountsEntityManager.DoInitForm(Form: TEntityFormDescr);
begin

  with Form.AddSection(S_GENERAL, GeneralSectionTitle, True) do begin
    with AddTextUnit(StrNameCaption, F_NAME, True) do
      Unique := True;
    with AddNumberUnit(STR_ORDER, F_NUM) do begin
			MinValue := 0;
      MaxValue := MaxInt;
      DefaultValue := TInteger.Create(1);
    end;
    with AddStringListBox(STR_DISCOUNT_TYPE, F_DISCOUNT_TYPE) do begin
    	AddItem(STR_ALL_ITEMS, '0');
    	AddItem(STR_SELECTED_ITEMS, '1');
      DefaultValue := TString.Create('0');
    end;
    AddCurrencyUnit(STR_DISCOUNT_VALUE, F_DISCOUNT_VALUE, 0.0);
    with AddStringListBox(STR_VALUE_TYPE, F_VALUE_TYPE) do begin
    	AddItem(STR_PERCENT, '0');
    	AddItem(STR_FIXED, '1');
      DefaultValue := TString.Create('0');
    end;
  	AddClassifierUnit(STR_EXCLUDE_PERIOD, F_ID_PERIODS, TPeriodEntityManager);
	end;
  with Form.AddSection(S_REASON, STR_REASON) do begin
  	AddClassifierUnit(STR_DISCOUNT_REASON, F_ID_DISCOUNT_REASONS, TDiscountReasonEntityManager);
    with AddStringListBox(STR_FREE_REASON_TYPE, F_FREE_REASON_TYPE) do begin
    	AddItem(STR_FIX_TEXT, '0');
    	AddItem(STR_ASK_REASON, '1');
      DefaultValue := TString.Create('0');
    end;
    with AddTextUnit(STR_FREE_REASON_TEXT, F_FREE_REASON_TEXT) do begin
    	AddVisibilityCondition(TFieldEqualsCondition.Create(F_FREE_REASON_TYPE, TString.Create('0')));
    end;
  end;
  with Form.AddSection(S_CONSTRAINT, STR_CONSTRAINTS) do begin
    with AddStringListBox(STR_APPLIED_TO, F_CHECK_TYPE) do begin
    	AddItem(STR_ALL_ITEMS, '0');
    	AddItem(STR_DEPARTMENT, '1');
    	AddItem(STR_COURSE, '2');
    	AddItem(STR_FOOD_GROUP, '3');
    	AddItem(STR_CATEGORY, '4');
      DefaultValue := TString.Create('0');
    end;
    with AddClassifierUnit(STR_DEPARTMENT, F_ID_DEPARTMENTS, TDepartmentEntityManager) do
    	AddVisibilityCondition(TFieldEqualsCondition.Create(F_CHECK_TYPE, TString.Create('1')));
    with AddClassifierUnit(STR_COURSE, F_ID_COURSES, TCourseEntityManager) do
    	AddVisibilityCondition(TFieldEqualsCondition.Create(F_CHECK_TYPE, TString.Create('2')));
    with AddClassifierUnit(STR_FOOD_GROUP, F_ID_GROUP, TFoodGroupEntityManager) do
    	AddVisibilityCondition(TFieldEqualsCondition.Create(F_CHECK_TYPE, TString.Create('3')));
    with AddClassifierUnit(STR_CATEGORY, F_ID_CATEGORY, TCategoryEntityManager) do
    	AddVisibilityCondition(TFieldEqualsCondition.Create(F_CHECK_TYPE, TString.Create('4')));
  end;
  with Form.AddSection(S_APPEARANCE, STR_APPEARANCE) do begin
  	AddColorUnit(STR_FONT_COLOR, F_FONT_COLOR);
  	AddColorUnit(STR_BACK_COLOR, F_COLOR);
  end;
end;

procedure TAirDiscountsEntityManager.DoInitList(Query: TListParams2;
  View: TListViewDescr);
begin
	Query.Define([F_ID, F_NUM, F_NAME]);

  with View.Columns do begin
    Add(STR_ORDER, dtInteger, 10, F_NUM, true);
  	Add(StrNameCaption, dtWideString, 90, F_NAME, true);
  end;

  View.Operations.Add([lbtnNew, lbtnDuplicate, lbtnEdit, lbtnDelete,
  		lbtnRefresh, lbtnDefault]);
end;

class procedure TAirDiscountsEntityManager.DoInitNode(Node: TNodeDescr);
begin
	with Node do begin
    IconPath := Plugin.GetImageFileName('discounts.ico');
    HelpContext := 0;
    BaseClass := TUntillAirTreeFolder;
    PlaceInTreeType := ptLast;
    Title := STR_DISCOUNTS;
  end;
end;

class function TAirDiscountsEntityManager.GetTableDef: TManagerTableDef;
begin
	Result.TableName := 'discounts';
end;

initialization
  if GetUntillIniSetting('common', 'UntillAirBo', '0') = '1' then
    ClassManager.RegisterClass(TAirDiscountsEntityManager);
end.
