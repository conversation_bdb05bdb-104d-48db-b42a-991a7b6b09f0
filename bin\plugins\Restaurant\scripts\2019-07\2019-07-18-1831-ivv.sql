create table order_item_extra (
    id u_id,
    id_order_item bigint,
    id_menu_item bigint,
    id_pbill_item bigint,
    ID_ARTICLE_BARCODES bigint,		
    constraint oiex_pk primary key (id),
    constraint oiex_fk1 foreign key (id_order_item) references order_item(id),
    constraint oiex_fk2 foreign key (id_menu_item) references menu_item(id),
    constraint oiex_fk3 foreign key (id_pbill_item) references pbill_item(id),
    constraint oiex_fk4 foreign key (ID_ARTICLE_BARCODES) references ARTICLE_BARCODES(id)
);
commit;
grant all on order_item_extra to untilluser;
commit;
execute procedure register_sync_table_ex('order_item_extra', 'p', 1);
commit;

