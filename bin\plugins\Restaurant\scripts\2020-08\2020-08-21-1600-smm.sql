CREATE TABLE pbill_cashm_payments_info  (
    ID                    U_ID NOT NULL,
    ID_PBILL_PAYMENTS     BIGINT NOT NULL,
    misc_data blob sub_type 0 segment size 80,
    CONSTRAINT pk_pbill_cashm_paym_info PRIMARY KEY (ID),
    CONSTRAINT fk_pbill_cashm_paym_info
        FOREIGN KEY (ID_PBILL_PAYMENTS) REFERENCES PBILL_PAYMENTS (ID)
);
commit;

EXECUTE PROCEDURE register_sync_table_ex('pbill_cashm_payments_info', 'p', 1);
commit;

