set term !!;
CREATE OR ALTER trigger vanduijnen_ins_trigger for vandu<PERSON><PERSON><PERSON>
active after insert position 0
as
declare variable lcu bigint;
declare variable id_article bigint;
declare variable id_group bigint;
declare variable id_user bigint;
declare variable id_location bigint;
begin
    -- get dispencer number
   select first 1 disp.id 
   from vd_lcu_dispensers disp
   where disp.lcu_number=new.lcu_dispenser and is_active=1
   into :lcu;

   -- get user id
   select first 1 id_untill_users from waiters, untill_users
   where waiters.id_untill_users=untill_users.id
    and waiters.number_vanduijnen=new.vand_waiter
    and waiters.number_vanduijnen!=0
    and untill_users.is_active=1
   into id_user;

   -- get location
   select first 1 id_locations, id_articles
   from beco_article_locations bal, articles a, beco_locations bl
   where bal.id_articles=a.id
        and bal.id_locations=bl.id
        and plu_number=new.vand_plu
        and bal.is_active=1
        and a.is_active=1
        and bl.is_active=1
   into id_location, id_article;

    -- is location?
   if (coalesce(:id_location, 0) <> 0) then begin
         insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location)
         values (new.received, :id_user, :id_article, null, new.quantity, 1, :lcu, :id_location);
   end else begin
       -- group or article
       select first 1 id from vd_groups where number=new.vand_plu and is_active=1 into id_group;
       select first 1 id from articles where plu_number_vanduijnen=new.vand_plu and is_active=1 into id_article;
    
       if (coalesce(:id_group,0)<>0) then
         insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location)
         values (new.received, :id_user, null, coalesce(:id_group, -1), new.quantity, 1, :lcu, null);
       else
         insert into vd_turnover (posted, id_untill_users, id_articles, id_vd_group, quantity, actn, id_vd_lcu_dispensers, id_vd_location)
         values (new.received, :id_user, coalesce(:id_article, -1), null, new.quantity, 1, :lcu, null);
   end

end
!!
commit
!!
set term ;!!
