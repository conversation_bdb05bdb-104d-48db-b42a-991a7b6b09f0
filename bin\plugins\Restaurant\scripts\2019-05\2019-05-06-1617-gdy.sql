SET TERM ^ ;

create or alter procedure GETMENUITEMOSIG (
    ID_MENU_ITEM bigint)
returns (
    OSIG varchar(1024),
    OSIG_NO_SEATS varchar(1024))
as
declare variable TEXT varchar(50);
declare variable ID_ARTICLES varchar(20);
declare variable ID_COURSES varchar(20);
declare variable OSIG_TO_APPEND varchar(1024);
declare variable KIND char(1);
declare variable ROWBEG integer;
declare variable CHAIR_NUMBER integer;
declare variable IDX integer;
declare variable ID_BILL bigint;
declare variable ID_MENU bigint;
begin
  idx = 0;
  osig = '';
  osig_to_append = '';
  OSIG_NO_SEATS = '';
  SELECT first 1 o.id_bill, mi.ID_MENU, oi.CHAIR_NUMBER
    from MENU_ITEM mi
    join order_item oi on mi.id_menu = oi.id_menu
    join orders o on o.ID = oi.ID_ORDERS
    where mi.id = :ID_MENU_ITEM
    into :id_bill, :id_menu, :chair_number;

  for
  select  menu_item.rowbeg, cast(menu_item.kind as char(1)),
         cast(coalesce(menu_item.id_articles,0) as varchar(20)), menu_item.text,
         cast(coalesce(menu_item.ID_COURSES, 0) as varchar(20))
         from menu_item
    join order_item oi on menu_item.id_menu = oi.id_menu
    join orders o on o.ID = oi.ID_ORDERS
  where menu_item.ID_MENU = :id_menu and o.ID_BILL = :id_bill and menu_item.id >= :ID_MENU_ITEM
    and OI.QUANTITY > 0

  order by menu_item.id into
       :rowbeg, :kind, :id_articles, :text, :id_courses
       do begin
    if (idx>0 and rowbeg=1) then
        break;
    osig_to_append = osig_to_append || 'mi' || kind;
    if (id_articles='0') then begin
      osig_to_append = osig_to_append || '_' || text;
    end else begin
      osig_to_append = osig_to_append || '_' || id_articles;
    end
    if (id_courses <> '0') then
        osig_to_append = osig_to_append || '_' || id_courses;
    OSIG_NO_SEATS = OSIG_NO_SEATS || osig_to_append || '|';
    OSIG = OSIG || osig_to_append || '_' || chair_number || '|';
    idx = idx + 1;
  end
  suspend;
end
^

SET TERM ; ^

/* Following GRANT statements are generated automatically */

GRANT SELECT ON MENU_ITEM TO PROCEDURE GETMENUITEMOSIG;
GRANT SELECT ON ORDER_ITEM TO PROCEDURE GETMENUITEMOSIG;
GRANT SELECT ON ORDERS TO PROCEDURE GETMENUITEMOSIG;

/* Existing privileges on this procedure */

GRANT EXECUTE ON PROCEDURE GETMENUITEMOSIG TO SYSDBA;
GRANT EXECUTE ON PROCEDURE GETMENUITEMOSIG TO UNTILLUSER;


SET TERM ^ ;

create or alter procedure GETORDERITEMOSIG_AKS (
    ID_ORDER_ITEM bigint,
    ID_ORDERS bigint)
returns (
    OSIG varchar(1024),
    OSIG_NO_SEATS varchar(1024))
as
declare variable ID_COURSES varchar(20);
declare variable ID_ARTICLES varchar(20);
declare variable KIND char(1);
declare variable OSIG_TO_APPEND varchar(1024);
declare variable CHAIR_NUMBER integer;
declare variable ROWBEG integer;
declare variable TEXT varchar(20);
declare variable IDX integer;
begin
  idx = 0;
  osig = '';
  osig_no_seats = '';
  OSIG_TO_APPEND = '';
  for
  select order_item.rowbeg, cast(order_item.kind as char(1)), 
         cast(coalesce(order_item.id_articles, 0) as varchar(20)), order_item.text,
         cast(coalesce(order_item.ID_COURSES, 0) as varchar(20)), order_item.CHAIR_NUMBER
  from order_item where order_item.id_orders=:ID_ORDERS and order_item.id >= :id_order_item
  into
       :rowbeg, :kind, :id_articles, :text, :id_courses, :CHAIR_NUMBER
       do begin
    if (idx>0 and rowbeg=1) then
        break;
    if (id_articles='0') then begin
      OSIG_TO_APPEND = OSIG_TO_APPEND || '/' || kind || '/' || text;
    end else begin
      OSIG_TO_APPEND = OSIG_TO_APPEND || '/' || kind || '/' || id_articles;
    end
    if (id_courses <> '0') then
        OSIG_TO_APPEND = OSIG_TO_APPEND || '/' || id_courses;
    osig_no_seats = osig_no_seats || OSIG_TO_APPEND || '|';
    OSIG = OSIG || OSIG_TO_APPEND || '/' || chair_number || '|';
    idx = idx + 1;
  end
  suspend;
end
^

SET TERM ; ^

/* Following GRANT statements are generated automatically */

GRANT SELECT ON ORDER_ITEM TO PROCEDURE GETORDERITEMOSIG_AKS;

/* Existing privileges on this procedure */

GRANT EXECUTE ON PROCEDURE GETORDERITEMOSIG_AKS TO SYSDBA;



CREATE OR ALTER VIEW ORDERED_MI_OSIG(
    MENU_ITEM_ID,
    OSIG,
    ID_BILL)
AS
select menu_item.ID, (select osig from GETMENUITEMOSIG(menu_item.ID)), o.id_bill
from menu_item
    join order_item oi on menu_item.id_menu = oi.ID_MENU
    join orders o on o.id = oi.id_orders
    where oi.QUANTITY > 0
;

CREATE OR ALTER VIEW ORDERED_MI_OSIG_NO_SEATS(
    MENU_ITEM_ID,
    OSIG_NO_SEATS,
    ID_BILL)
AS
select menu_item.ID, (select osig_no_seats from GETMENUITEMOSIG(menu_item.ID)), o.id_bill
from menu_item
    join order_item oi on menu_item.id_menu = oi.ID_MENU
    join orders o on o.id = oi.id_orders
    where oi.QUANTITY > 0
;

CREATE OR ALTER VIEW ORDERED_OSIG_AKS(
    ID_ORDER_ITEM,
    OSIG)
AS
select order_item.ID, (select osig from GETORDERITEMOSIG_AKS(order_item.ID, order_item.ID_ORDERS))
from order_item
;

CREATE OR ALTER VIEW ORDERED_OSIG_AKS_NO_SEATS(
    ID_ORDER_ITEM,
    OSIG_NO_SEATS)
AS
select order_item.ID, (select osig_no_seats from GETORDERITEMOSIG_AKS(order_item.ID, order_item.ID_ORDERS))
from order_item
;

GRANT all on ORDERED_OSIG_AKS to untilluser;
GRANT all on ORDERED_MI_OSIG to untilluser;
GRANT all on ORDERED_OSIG_AKS_NO_SEATS to untilluser;
GRANT all on ORDERED_MI_OSIG_NO_SEATS to untilluser;

commit;