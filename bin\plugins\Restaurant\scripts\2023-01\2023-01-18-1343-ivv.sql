alter TABLE pbill_cashm_payments_info  
    add ID_VCH_PBILL_PAYMENTS BIGINT,
    add CONSTRAINT fk2_pbill_cashm_paym_info FOREIGN KEY (ID_VCH_PBILL_PAYMENTS) REFERENCES voucher_pbill_payments (ID);
commit;

SET TERM ^ ;
EXECUTE BLOCK AS BEGIN
  IF (SUBSTRING(RDB$GET_CONTEXT('SYSTEM', 'ENGINE_VERSION') FROM 1 FOR 2) = '2.') THEN
    EXECUTE STATEMENT '
update RDB$RELATION_FIELDS set
RDB$NULL_FLAG = NULL
where (RDB$FIELD_NAME = ''ID_PBILL_PAYMENTS'') and
(RDB$RELATION_NAME = ''PBILL_CASHM_PAYMENTS_INFO'');
';
  ELSE
    EXECUTE STATEMENT 'ALTER TABLE PBILL_CASHM_PAYMENTS_INFO ALTER COLUMN ID_PBILL_PAYMENTS DROP NOT NULL';
END^
SET TERM ; ^
commit;

