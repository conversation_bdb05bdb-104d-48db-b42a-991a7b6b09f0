alter table STOCK_ADJUSTMENT add conduct_datetime timestamp;
commit;
alter table STOCK_COST_CORRECTION add conduct_datetime timestamp;
commit;
alter table STOCK_INVOICE add conduct_datetime timestamp;
commit;
alter table PURCHASE_ORDER add conduct_datetime timestamp;
commit;
alter table stock_physical_count add conduct_datetime timestamp;
commit;

alter table STOCK_ADJUSTMENT add id_conduct_untill_user bigint,
	add constraint sa_uu_fk foreign key (id_conduct_untill_user) references untill_users(id);
commit;
alter table STOCK_COST_CORRECTION add id_conduct_untill_user bigint,
	add constraint sc_uu_fk foreign key (id_conduct_untill_user) references untill_users(id);
commit;
alter table STOCK_INVOICE add id_conduct_untill_user  bigint,
	add constraint si_uu_fk foreign key (id_conduct_untill_user) references untill_users(id);
commit;
alter table PURCHASE_ORDER add id_conduct_untill_user  bigint,
	add constraint po_uu_fk foreign key (id_conduct_untill_user) references untill_users(id);
commit;
alter table stock_physical_count add id_conduct_untill_user  bigint,
	add constraint spc_uu_fk foreign key (id_conduct_untill_user) references untill_users(id);
commit;


 

