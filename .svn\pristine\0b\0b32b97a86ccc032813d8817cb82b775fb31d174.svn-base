unit GraphicU;

interface
uses classes, Graphics, SysUtils, PluginU, CommonU, OrdermanEqu, UntillEquU,
  UntillPluginU, UntillDatasetsU, Generics.Collections,
  stdCtrls;

const
  CAPTION_VERSION = 3;
  IMAGELIST_VERSION = 1;
  ERR_LOAD_ELEMENT = 'element class name size';
  OPT_ORDERMAN='Options Orderman';
  OPT_PC='Options PC';
  OPT_FALCON='Options';
type
TVersion = Integer;
TVertAlign = (valgTop, valgCenter, valgBottom);

IListElement =interface
  ['{AB548DE5-F7A9-4226-8A48-064F8DCA40F0}']
  function RecCount          : Integer;
  function GetDS             : TUntillInfoDataSet;
  function GetStringDSList   : TList;
  function GetElementFont    : TFont;
  procedure ChangeDSCurPos(NewPos: Integer);
  procedure FillSelectedItemsList(const OutList: TList<Integer>);
  function IsFocusLineHidden : Boolean;
  function IsEmptyLineHidden : Boolean;
end;

ICaptionedElement = interface
['{F48929B1-82B3-4a42-AF21-3EA51C122332}']
  function GetElementCaption(LangID : String) : WideString;
  function GetElementFont : TFont;
  function HasDataSet : Boolean;
//  function WordWrap : boolean;
end;

INumberedElement = interface
['{f269e0d3-f419-4ffc-ba94-ab26c5c34aea}']
  function GetNumber() : Integer;
end;

  TUntillPen = class
  private
    FMode: TPenMode;
    FWidth: Integer;
    FColor: TColor;
    FStyle: TPenStyle;
    procedure SetColor(const Value: TColor);
    procedure SetMode(const Value: TPenMode);
    procedure SetStyle(const Value: TPenStyle);
    procedure SetWidth(const Value: Integer);
  public
    property Color : TColor read FColor write SetColor;
    property Style : TPenStyle read FStyle write SetStyle;
    property Width : Integer read FWidth write SetWidth;
    property Mode  : TPenMode read FMode write SetMode;
    procedure Assign(aPen : TUntillPen);
    procedure AssignTo(aPen : TPen);
    procedure AssignFrom(aPen : TPen);
    procedure LoadFromStream(Stream :TStream);
    procedure SaveToStream(Stream :TStream);
  end;

  TUntillBrush = class
  private
    FColor  : TColor;
    FStyle  : TBrushStyle;
    procedure SetColor(const Value: TColor);
    procedure SetStyle(const Value: TBrushStyle);
  public
    property Color : TColor read FColor write SetColor;
    property Style : TBrushStyle read FStyle write SetStyle;
    procedure Assign(aBrush : TUntillBrush);
    procedure AssignTo(aBrush : TBrush);
    procedure AssignFrom(aBrush : TBrush);
    procedure LoadFromStream(Stream :TStream);
    procedure SaveToStream(Stream :TStream);
  end;

  TElementCaption = class(TPersistent)
  private
    FOriginalFontSize : Integer;
    FVersion : TVersion;
    FFont: TFont;
    FCaptionList: TStringList;
    FAlignment: TAlignment;
    FVertAlignment: TVertAlign;
    procedure SetAlignment(const Value: TAlignment);
    procedure SetVertAlignment(const Value: TVertAlign);
  public
    property VertAlignment: TVertAlign read FVertAlignment write SetVertAlignment;
    property Alignment : TAlignment read FAlignment write SetAlignment;
    property Font  : TFont read FFont;
    property CaptionList : TStringList read FCaptionList;

    procedure Assign(Source: TPersistent); override;
    procedure SaveToStream(Stream: TStream); virtual;
    procedure LoadFromStream(Stream: TStream); virtual;
    procedure InitValues(Plugin : TUntillPlugin; InitStorage : String; ScreenType : TScreenType);
    procedure ReduceFont(Value : Extended);
    procedure AutoResize( aDX : Double );

    constructor Create;
    destructor Destroy; override;
  end;

  TElementImageList = class(TPersistent)
  private
    FVersion  : TVersion;
    FSizeList : TStringList;
    FImages   : TMemoryStream;
  public
    property SizeList : TStringList read FSizeList;
    property Images   : TMemoryStream read FImages;

    procedure SaveToStream(Stream: TStream); virtual;
    procedure LoadFromStream(Stream: TStream); virtual;

    constructor Create;
    destructor Destroy; override;
  end;

procedure SaveFontToStream(Stream :TStream; AFont : TFont);
procedure LoadFontFromStream(Stream :TStream; AFont : TFont);
procedure SaveCaptionListToStream(Stream :TStream; ACaptionList : TStringList);
procedure LoadCaptionListFromStream(Stream :TStream; ACaptionList : TStringList);
function  SaveFontToString(AFont : TFont)  :String;
procedure LoadFontFromString(strFont :String; AFont : TFont);
function  AutoResizeFont(AFont : TFont; aDX : Double) : Integer;
function ColorInSet(Color :TColor; ColorList :array of tColor) :boolean;

function VertAlign2TextLayout(Value :TVertAlign) :TTextLayout;
function TextLayout2VertAlign(Value :TTextLayout) :TVertAlign;

implementation

uses ClassesU;


function SaveFontToString(AFont : TFont)  :String;
begin
  result := AFont.Name;
  result := result  + ';' + IntToStr(AFont.Size);
  result := result  + ';' + IntToStr(AFont.Color);
  result := result  + ';' + IntToStr(Byte(AFont.Style));
end;

procedure LoadFontFromString(strFont :String; AFont : TFont);
var strL : TStringList;
begin
  assert(assigned(AFont));
  if strFont='' then exit;

  strL := TStringList.Create;
  try
    strSplitEx( strFont, strL, ';', true);
    if strL.Count < 4 then exit;
    AFont.Name := strL.Strings[0];
    AFont.Size := StrToIntDef(strL.Strings[1],8);
    AFont.Color := TColor(StrToIntDef(strL.Strings[2],0));
    AFont.Style := TFontStyles(Byte(StrToIntDef(strL.Strings[3],0)));
  finally
    FreeAndNil(strL);
  end;
end;
procedure SaveFontToStream(Stream :TStream; AFont : TFont);
var FColor : TColor;
    FCharSet : TFontCharSet;
    FHeight : Integer;
    FStyle : TFontStyles;
    FName : TFontName;
begin
  FColor := AFont.Color;
  Stream.WriteBuffer(FColor, SizeOf(FColor));
  FCharSet := AFont.CharSet;
  Stream.WriteBuffer(FCharSet, SizeOf(FCharSet));
  FHeight := AFont.Height;
  Stream.WriteBuffer(FHeight, SizeOf(FHeight));
  FStyle := AFont.Style;
  Stream.WriteBuffer(FStyle, SizeOf(FStyle));
  FName := AFont.Name;
  WriteStrToStream(Stream, AnsiString(FName));
end;

procedure LoadFontFromStream(Stream :TStream; AFont : TFont);
var FColor : TColor;
    FCharSet : TFontCharSet;
    FHeight : Integer;
    FStyle : TFontStyles;
    FName : TFontName;
    Size : Integer; 
begin
  Size := Stream.Read(FColor, SizeOf(FColor));
  if Size <> SizeOf(FColor) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['font color'],'LoadFontFromStream');
  Size := Stream.Read(FCharSet, SizeOf(FCharSet));
  if Size <> SizeOf(FCharSet) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['char set'] ,'LoadFontFromStream');
  Size := Stream.Read(FHeight, SizeOf(FHeight));
  if Size <> SizeOf(FHeight) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['font height'],'LoadFontFromStream');
  Size := Stream.Read(FStyle, SizeOf(FStyle));
  if Size <> SizeOf(FStyle) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['font style'],'LoadFontFromStream');
  FName := String(ReadStrFromStream(Stream));

  AFont.Color := FColor;
  AFont.CharSet := FCharSet;
  AFont.Height := FHeight;
  AFont.Name := FName;
  AFont.Style := FStyle;
end;

procedure SaveCaptionListToStream(Stream :TStream; ACaptionList : TStringList);
var i : Integer;
    cnt : Integer;
begin
  cnt := ACaptionList.count;
  Stream.WriteBuffer(cnt, Sizeof(cnt));
  for I := 0 to Pred(cnt) do begin
    WriteWideStrToStream(Stream, ACaptionList.strings[I]);
  end;
end;

procedure LoadCaptionListFromStream(Stream :TStream; ACaptionList : TStringList);
var I : Integer;
    cnt : Integer;
    str :WideString;
begin
  Stream.ReadBuffer(cnt, Sizeof(cnt));
  ACaptionList.Clear;
  for I := 0 to Pred(cnt) do begin
    str := ReadWideStrFromStream(Stream);
    ACaptionList.Add(str);
  end;
end;

{ TElementCaption }

procedure TElementCaption.Assign(Source: TPersistent);
var sc : TElementCaption;
begin
  if Source is (Self.ClassType) then begin
    sc := TElementCaption(Source);
    FVersion := sc.FVersion;
    FFont.Assign(sc.Font);
    FCaptionList.Assign(sc.CaptionList);
    FAlignment := sc.FAlignment;
    FVertAlignment := sc.FVertAlignment;
    FOriginalFontSize := sc.FOriginalFontSize;
  end else
    inherited;
end;

function AutoResizeFont(AFont : TFont; aDX : Double) : Integer;
begin
  Result := 0;
  if not assigned(AFont) then exit;
  Result := AFont.Size;
  if (Result < 5) and (aDX < 1) then exit;
  Result  := Round(Result * aDX * 1.1);
  if Result < 5 then Result := 5;
end;

procedure TElementCaption.AutoResize(aDX: Double);
begin
  Font.Size := AutoResizeFont(Font, aDX);
  FOriginalFontSize := Font.Size;
end;

constructor TElementCaption.Create;
begin
  inherited;
  FVersion := 0;
  FFont := TFont.Create;
  FCaptionList   := TStringList.Create;
  FAlignment     := taCenter;
  FVertAlignment := valgCenter;
end;

destructor TElementCaption.Destroy;
begin
  FreeAndNil(FFont);
  FreeAndNil(FCaptionList);
  inherited;
end;

procedure TElementCaption.InitValues(Plugin : TUntillPlugin; InitStorage : String; ScreenType : TScreenType);
var iVertAlign : Integer;
begin

  Case ScreenType of
    stPC: InitStorage := InitStorage + ' ' + OPT_PC;
    stFalcon: InitStorage := InitStorage + ' ' + OPT_FALCON;
    stOrdermanSol: InitStorage := InitStorage + ' ' + OPT_ORDERMAN;
  end;

  FFont.Name := Plugin.GetInterfaceSetting(InitStorage, 'FontName', 'Tahoma');
  FFont.Color := TColor(StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontColor', '0')));
  FFont.Size := StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontSize', '6'));
  FFont.Style :=[];
  if (StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontBold', '0')) <> 0) then FFont.Style := FFont.Style + [fsBold];
  if (StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontItalic', '0')) <> 0) then FFont.Style := FFont.Style + [fsItalic];
  if (StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontUnderline', '0')) <> 0) then FFont.Style := FFont.Style + [fsUnderline];
  iVertAlign := StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontVertCenter', '1'));
  if (iVertAlign <= 2) then
    FVertAlignment := TVertAlign(iVertAlign)
  else
    FVertAlignment := valgCenter;
  FAlignment := taLeftJustify;
  if (StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontCenter', '1')) <> 0) then FAlignment := taCenter;
  if (StrToInt(Plugin.GetInterfaceSetting(InitStorage, 'FontRight', '0')) <> 0) then FAlignment := taRightJustify;
  FOriginalFontSize := FFont.Size;

end;

procedure TElementCaption.ReduceFont(Value: Extended);
begin
  if FVersion > 0 then
    FFont.Size := Trunc(FOriginalFontSize * Value);
end;

procedure TElementCaption.LoadFromStream(Stream: TStream);
var Size : Integer;
    b    : Boolean;
begin
  Size := Stream.Read(FVersion, SizeOf(FVersion));
  if Size <> SizeOf(FVersion) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['version'],'LoadFromStream');
  LoadFontFromStream(Stream, FFont);
  FOriginalFontSize := FFont.Size;
  LoadCaptionListFromStream(Stream, FCaptionList);
  Size := Stream.Read(FAlignment, SizeOf(FAlignment));
  if Size <> SizeOf(FAlignment) then Plugin.RaiseException(ERR_LOAD_ELEMENT, ['text alignment'],'LoadFromStream');
  if FVersion < 3 then begin
    Stream.Read(b, SizeOf(b));
    if b then
      FVertAlignment := valgCenter
    else
      FVertAlignment := valgTop;
  end else begin
    Stream.Read(FVertAlignment, SizeOf(FVertAlignment))
  end;
  if FVersion<2 then
    Stream.Position := Stream.Position + 8;
end;

procedure TElementCaption.SaveToStream(Stream: TStream );
begin
  FVersion := CAPTION_VERSION;
  Stream.WriteBuffer(FVersion, SizeOf(FVersion));
  SaveFontToStream(Stream, FFont);
  SaveCaptionListToStream(Stream,FCaptionList);
  Stream.WriteBuffer(FAlignment, SizeOf(FAlignment));
  Stream.WriteBuffer(FVertAlignment, SizeOf(FVertAlignment));
end;

procedure TElementCaption.SetAlignment(const Value: TAlignment);
begin
  FAlignment := Value;
end;

procedure TElementCaption.SetVertAlignment(const Value: TVertAlign);
begin
  FVertAlignment := Value;
end;

{ TUntillBrush }

procedure TUntillBrush.Assign(aBrush: TUntillBrush);
begin
  FColor  := aBrush.Color;
  FStyle  := aBrush.Style;
end;

procedure TUntillBrush.AssignFrom(aBrush: TBrush);
begin
  FColor := aBrush.Color;
  FStyle := aBrush.Style;
end;

procedure TUntillBrush.AssignTo(aBrush: TBrush);
begin
  aBrush.Color := FColor;
  aBrush.Style := FStyle;
end;

procedure TUntillBrush.LoadFromStream(Stream: TStream);
begin
  Stream.Read(FColor, SizeOf(FColor));
  Stream.Read(FStyle, SizeOf(FStyle));
end;

procedure TUntillBrush.SaveToStream(Stream: TStream);
begin
  Stream.WriteBuffer(FColor, SizeOf(FColor));
  Stream.WriteBuffer(FStyle, SizeOf(FStyle));
end;

procedure TUntillBrush.SetColor(const Value: TColor);
begin
  FColor := Value;
  //if Fstyle = bsClear then Fstyle := bsSolid;
end;

procedure TUntillBrush.SetStyle(const Value: TBrushStyle);
begin
  FStyle := Value;
end;

{ TUntillPen }

procedure TUntillPen.Assign(aPen: TUntillPen);
begin
  FMode  := aPen.Mode;
  FWidth := aPen.Width;
  FColor := aPen.Color;
  FStyle := aPen.Style;
end;

procedure TUntillPen.AssignFrom(aPen: TPen);
begin
  FMode  := aPen.Mode;
  FWidth := aPen.Width;
  FColor := aPen.Color;
  FStyle := aPen.Style;
end;

procedure TUntillPen.AssignTo(aPen: TPen);
begin
  aPen.Mode  := FMode;
  aPen.Width := FWidth;
  aPen.Color := FColor;
  aPen.Style := FStyle;
end;

procedure TUntillPen.LoadFromStream(Stream: TStream);
begin
  Stream.Read(FWidth, SizeOf(FWidth));
  Stream.Read(FStyle, SizeOf(FStyle));
  Stream.Read(FColor, SizeOf(FColor));
  Stream.Read(FMode, SizeOf(FMode));
end;

procedure TUntillPen.SaveToStream(Stream: TStream);
begin
  Stream.WriteBuffer(FWidth, SizeOf(FWidth));
  Stream.WriteBuffer(FStyle, SizeOf(FStyle));
  Stream.WriteBuffer(FColor, SizeOf(FColor));
  Stream.WriteBuffer(FMode, SizeOf(FMode));
end;

procedure TUntillPen.SetColor(const Value: TColor);
begin
  FColor := Value;
end;

procedure TUntillPen.SetMode(const Value: TPenMode);
begin
  FMode := Value;
end;

procedure TUntillPen.SetStyle(const Value: TPenStyle);
begin
  FStyle := Value;
end;

procedure TUntillPen.SetWidth(const Value: Integer);
begin
  FWidth := Value;
end;

{ TElementImageList }

constructor TElementImageList.Create;
begin
  FVersion  := 1;
  FSizeList := TStringList.Create;
  FImages   := TMemoryStream.Create;
end;

destructor TElementImageList.Destroy;
begin
  FreeAndNil( FSizeList );
  FreeAndNil( FImages );
  inherited;
end;

procedure TElementImageList.LoadFromStream(Stream: TStream);
var listCnt : Integer;
    i       : Integer;
    size    : Integer;
begin
  FSizeList.Clear;
  Stream.Read(FVersion, SizeOf(FVersion));
  Stream.Read(listCnt, SizeOf(listCnt));
  if listCnt = 0 then exit;
  for i := 0 to Pred(listCnt) do begin
    Stream.Read(size, SizeOf(size));
    FSizeList.Add(IntToStr(size));
  end;
  FImages.Clear;
  FImages.CopyFrom(Stream, Stream.Size - Stream.Position);
end;

procedure TElementImageList.SaveToStream(Stream: TStream);
var listCnt : Integer;
    i       : Integer;
    size    : Integer;
begin
  FVersion := IMAGELIST_VERSION;
  Stream.WriteBuffer(FVersion, SizeOf(FVersion));
  listCnt := FSizeList.count;
  Stream.WriteBuffer(listCnt, SizeOf(listCnt));
  if listCnt = 0 then exit;
  for i := 0 to Pred(listCnt) do begin
    size := StrToIntDef(FSizeList[i],0);
    Stream.WriteBuffer(size, sizeof(size));
  end;
  FImages.Seek(0,0);
  FImages.SaveToStream(Stream);
end;

function ColorInSet(Color :TColor; ColorList :array of tColor) :boolean;
var Color1 :TColor;
begin
  result := false;
  For Color1 in ColorList do
    if Color = Color1 then exit(true);
end;

function VertAlign2TextLayout(Value :TVertAlign) :TTextLayout;
begin
  case Value of
    valgTop : result := tlTop;
    valgCenter :result := tlCenter;
    else result := tlBottom;
  end;
end;

function TextLayout2VertAlign(Value :TTextLayout) :TVertAlign;
begin
  case Value of
    tlTop : result := valgTop;
    tlCenter : result := valgCenter;
    else result := valgBottom;
  end;
end;


end.
