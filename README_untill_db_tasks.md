# Untill Database Tasks Binary Data Structures for ImHex

Этот набор файлов содержит структуры для разбора бинарных данных задач базы данных Untill с помощью ImHex.

## Файлы

1. **untill_db_tasks_structures.hexpat** - Основные структуры для всех типов задач
2. **untill_db_tasks_extended.hexpat** - Расширенные структуры с дополнительными возможностями
3. **README_untill_db_tasks.md** - Этот файл с документацией

## Поддерживаемые типы задач

### 1. TCompactDBTask - Задача компактирования базы данных
Обрабатывается в `TCompactDBTask.ExecuteExternal`

**Структура данных:**
```
SmallInt version        // Версия структуры (0-4)
Boolean onlyBackup      // Только резервное копирование
Boolean repair          // Выполнить восстановление
Integer days           // Количество дней (если version > 0)
Boolean dontReboot     // Не перезагружать (если version > 1)  
WideString backupPath  // Путь для резервных копий (если version > 2)
Boolean skipAggr       // Пропустить агрегаты (если version > 3)
WideString sysdbaPwd   // Пароль SYSDBA (если version > 3)
```

### 2. TExportDBTask - Задача экспорта базы данных
Обрабатывается в `TExportDBTask.ExecuteExternal`

**Структура данных:**
```
SmallInt version           // Версия структуры
AnsiString formatGuid      // GUID формата экспорта
AnsiString formatName      // Название формата
Boolean customTill         // Использовать пользовательскую дату окончания
TDateTime dtTill          // Дата окончания
Boolean customFrom        // Использовать пользовательскую дату начала (если version > 1)
TDateTime dtFrom          // Дата начала (если version > 1)
Boolean monthBreaksPeriod // Разбивать по месяцам (если version > 2)
```

### 3. TEndOfDayTask - Задача закрытия дня
Обрабатывается в `TEndOfDayTask.ExecuteExternal`

**Структура данных:**
```
SmallInt version        // Версия структуры
WideString taskParams   // Параметры задачи
```

### 4. TAggregatesRecalculationDBTask - Пересчет агрегатов
Обрабатывается в `TAggregatesRecalculationDBTask.ExecuteExternal`

**Структура данных:**
```
SmallInt version        // Версия структуры
AnsiString sysdbaPwd    // Пароль SYSDBA
```

### 5. TAggregatesRemovalDBTask - Удаление агрегатов
Обрабатывается в `TAggregatesRemovalDBTask.ExecuteExternal`

**Структура данных:**
```
SmallInt version        // Версия структуры
AnsiString sysdbaPwd    // Пароль SYSDBA
```

### 6. TFDMArchivePeriodDBTask - Архивирование периода FDM
Обрабатывается в `TFDMArchivePeriodDBTask.Execute`

**Структура данных:**
```
SmallInt version        // Версия структуры
TDateTime dtFrom        // Дата начала (double)
TDateTime dtTo          // Дата окончания (double)
Boolean isSingleDay     // Один день
```

## Типы данных Delphi/Pascal

- **SmallInt** = 16-битное знаковое целое (s16)
- **Integer** = 32-битное знаковое целое (s32)
- **Boolean** = 8-битное значение (u8), где 0=false, 1=true
- **TDateTime** = 64-битное число с плавающей точкой (double), дни с 30.12.1899
- **AnsiString** = Integer(длина) + массив char[длина]
- **WideString** = Integer(длина) + массив char16[длина]

## Использование в ImHex

### Быстрый старт
1. Откройте файл с бинарными данными задачи в ImHex
2. Загрузите файл `untill_db_tasks_structures.hexpat`
3. Раскомментируйте нужную структуру в конце файла
4. Структуры автоматически применятся к данным

### Пример для TCompactDBTask
```cpp
// В конце файла untill_db_tasks_structures.hexpat раскомментируйте:
TCompactDBTaskParams compactTask @ 0x00;
```

### Универсальный анализ
Используйте расширенный файл для автоматического определения типа:
```cpp
// В untill_db_tasks_extended.hexpat:
UniversalDBTaskParser mainData @ 0x00;
```

## Функции для работы со строками

Система использует специальные функции для сериализации строк:

### WriteStrToStream / ReadStrFromStream (AnsiString)
```pascal
procedure WriteStrToStream(Stream:TStream; Str:AnsiString);
// Записывает: Integer(длина) + массив AnsiChar[длина]

function ReadStrFromStream(Stream:TStream):AnsiString;
// Читает: Integer(длина) + массив AnsiChar[длина]
```

### WriteWideStrToStream / ReadWideStrFromStream (WideString)
```pascal
procedure WriteWideStrToStream(Stream:TStream; Str:WideString);
// Записывает: Integer(длина) + массив WideChar[длина]

function ReadWideStrFromStream(Stream:TStream):WideString;
// Читает: Integer(длина) + массив WideChar[длина]
```

## Версии структур

Каждая задача использует версионирование для обратной совместимости:
- **version 0** - базовая структура
- **version 1+** - добавляются новые поля
- Поля читаются условно в зависимости от версии

## Отладка

Для анализа неизвестных данных используйте:
```cpp
HexDump debugData @ 0x00;
```

Это покажет первые 64 байта в шестнадцатеричном формате.

## Примеры реальных данных

### Пример TCompactDBTask (версия 4)
```
04 00           // version = 4
01              // onlyBackup = true  
00              // repair = false
1E 00 00 00     // days = 30
00              // dontReboot = false
0A 00 00 00     // backupPath.length = 10
43 00 3A 00 5C 00 42 00 61 00 63 00 6B 00 75 00 70 00 73 00  // "C:\Backups"
01              // skipAggr = true
08 00 00 00     // sysdbaPwd.length = 8
70 00 61 00 73 00 73 00 77 00 6F 00 72 00 64 00              // "password"
```

### Пример TExportDBTask (версия 2)
```
02 00           // version = 2
24 00 00 00     // formatGuid.length = 36
7B 32 34 45...  // "{24E...}" GUID
0C 00 00 00     // formatName.length = 12
45 78 61 63...  // "ExactGlobe"
01              // customTill = true
00 00 00 00 00 00 F0 3F  // dtTill (TDateTime)
01              // customFrom = true
00 00 00 00 00 00 E0 3F  // dtFrom (TDateTime)
```

## Поддержка

Структуры основаны на анализе исходного кода Untill из файлов:
- `untillsrv\src\DBTasksU.pas`
- `kernel\src\CompactDBTaskParamsFram.pas`
- `kernel\src\ClassesU.pas`
- `untillsrv\src\DBManagementTasksU.pas`
