create table theme_skins (
    id u_id,
    id_themes bigint,
    id_skins bigint,
    content blob,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint theme_skins_pk primary key (id),
    constraint theme_skins_FK1 FOREIGN KEY (id_themes) REFERENCES themes (ID),
    constraint theme_skins_FK2 FOREIGN KEY (id_skins) REFERENCES skins (ID)
);
commit;
grant all on theme_skins to untilluser;
commit;
execute procedure register_sync_table_ex('theme_skins', 'b', 1);
commit;
execute procedure register_bo_table('theme_skins', 'id_themes', 'themes');
commit;
execute procedure register_bo_table('theme_skins', 'id_skins', 'skins');
commit;

