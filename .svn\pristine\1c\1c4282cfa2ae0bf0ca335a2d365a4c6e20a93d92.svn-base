unit PurchaseOrderEntityManager;

interface
uses
  EntityManagerU, Classes, UntillDBU, ClassManagerU, RestaurantPluginU, IBSQL,
  UntillFram, TreeNodesU, PurchaseOrderEntityFram, SysUtils, CommonStringsU,
  StockTreeFoldersU, UntillToolBarU, BOUntillDBU, DB, TntCompatibilityU, CommonU,
  Menus, DateUtils, ClassesU, StockManagerU, PurchaseOrderFilterFram,
  DBEntityListFram, Controls;
type
//  TStockPOState = (stpoOrdered, stpoPartial, stpoReceived, stpoProcessed);
  TPurchaseOrderEntityManager = class(TEntityManager)
    private
      tCloseButton  : TUntillToolButton;
      tCompleteButton  : TUntillToolButton;
      ffram         : TPurchaseOrderFilterFrame;
      flistview     : TEMListView;
      bNormalView   : boolean;
      Conducted     : TStockPOState;
      fSupplierId : Int64;
      bodb : TBOCustomUntillDB;
      SC   : TStockManager;
      procedure SaveClick(Sender :TObject);
      procedure CompleteClick(Sender :TObject);
      procedure BeforeRefresh(Sender: TObject);
      procedure AfterRefresh(Sender: TObject);
      procedure OnFilterConfirmed(Sender: TObject);
    public
      property SupplierId : Int64 read fSupplierID write fSupplierID;
      constructor     Create(AOwner :TComponent; AUntillDB:TBOCustomUntillDB); override;
      destructor Destroy(); override;
      class procedure GetBaseClassList(AClassList: TPlaceInTreeList); override;
      class function  GetEntityFrameClass: TEntityViewFrameClass; override;

      procedure InitializeList(Sender:TEMListView);

      procedure TranslateStrings; override;
      function SaveContent(Frame: TUntillFrame): Boolean; override;
      procedure RefreshContent(Frame: TUntillFrame); override;
      procedure CanInsert(Frame: TUntillFrame; Update: Boolean); override;
      class function GetTableName: String; override;
      class function GetNodeName: WideString; override;
      function GetStringRepresentation(FieldName: String; Value: Variant;
               Field: TField = nil): WideString; override;
      procedure OnBeforeDelete(Sender:TObject; Param:Variant);
  end;

implementation
uses DataControlsU, StockUnitsEntityManager, UntillPluginU, UntillAppU,
     StockOrderU, ThreadObserverU, UTF8U, StockInvoiceEntityFram, StockPurchaseOrderU;
{ TPurchaseOrderEntityManager }

var
  STR_PurchaseOrder :WideString;
  STR_ShowAllPO :WideString;
  STR_ShowOpenedPO :WideString;

procedure TPurchaseOrderEntityManager.AfterRefresh(Sender: TObject);
var posState : TStockPOState;
    idx : string;
begin
  ClientDataset.DisableControls;
  idx := ClientDataset.IndexName;
  ClientDataset.IndexName := '';
  ClientDataset.First;
  try
    while not ClientDataset.eof do begin
      ClientDataset.edit;
      posState := StockManagerU.GetRealPOState(Untilldb, StrToInt64Def(ClientDataset.FieldByName('id').asString,0));
      ClientDataset.fieldByName('conducted').AsInteger := Ord(posState);
      ClientDataset.post;
      ClientDataset.Next;
    end;
  finally
    ClientDataset.EnableControls;
    ClientDataset.IndexName := idx;
  end;
  ClientDataSet.Filter:='conducted=' + IntToStr(ffram.cmbStatus.ItemIndex);
  ClientDataSet.Filtered:=true;

end;

procedure TPurchaseOrderEntityManager.BeforeRefresh(Sender: TObject);
var ind     : Integer;
begin
  if not assigned(ffram) then exit;
  ind:=ListParams.IndexOfCondition('upper(b.name) like upper');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if ffram.edtName.Text <> '' then
    ListParams.AddCondition('upper(b.name) like upper(''%' + SafeSQLParam(ffram.edtName.Text) + '%'')');
  Plugin.SetInterfaceSetting('Purchase order filter','Name filter',ffram.edtName.Text);

  ind:=ListParams.IndexOfCondition('a.order_type=');
  if ind > -1 then
    ListParams.DeleteCondition(ind);
  if ffram.cmbType.ItemIndex >= 0 then
    ListParams.AddCondition('a.order_type=' + IntToStr(ffram.cmbType.ItemIndex));
  if assigned(tCloseButton) then
    tCloseButton.Enabled := ffram.cmbStatus.ItemIndex>1;
  if assigned(tCompleteButton) then
    tCompleteButton.Enabled := ffram.cmbStatus.ItemIndex=1;
end;

procedure TPurchaseOrderEntityManager.CanInsert(Frame: TUntillFrame;
  Update: Boolean);
begin
  inherited;
  if not assigned(Frame) then exit;
  with TPurchaseOrderEntityFrame(Frame) do begin
    CheckUsbOnEmpty(usbSupp, nil, Plugin.Translate('PurchaseOrderEntityManager', 'Supplier'));
  end;
end;

procedure TPurchaseOrderEntityManager.CompleteClick(Sender: TObject);
var ID : Int64;
    posState :TStockPOState;
begin
  ID := StrToInt64Def(clientDataset.fieldByName(IDFieldName).asString,0);
  posState := StockManagerU.GetRealPOState(Untilldb, ID);

  if posState <> stpoPartial then exit;

  SC.CompletePurchaseOrderByID(nil,
    TBOCustomUntillDB(Untilldb).BOUserId, LocalDatetimeToSys(Now), ID);

end;

constructor TPurchaseOrderEntityManager.Create(AOwner: TComponent;
  AUntillDB: TBOCustomUntillDB);
begin
  inherited;
  SC := TStockManager.Create(self, Untilldb,ThreadObserverU.ThreadObserver.RefreshMainThread);
  tCloseButton:= nil;
  tCompleteButton:= nil;
  Conducted   := stpoOrdered;
  bNormalView := not (AOwner is TStockInvoiceEntityFrame);
  bodb := AUntillDB;
  fSupplierID := 0;
  LoadIcon(Plugin.GetImageFileName('purchase_order.ico'));
  ListParams.TableAlias := 'a';
  BeforeRefreshList:= BeforeRefresh;
  AfterRefreshList := AfterRefresh;


  SetListParams(['a.ID',
          'a.po_number',
          'a.id_suppliers',
          'b.name supp_name',
          'a.order_type',
          'a.order_date',
          'a.reqired_date',
          'a.reference',
          'a.description',
          'a.conducted'
          ],
          ['purchase_order a'],
          [],
          'ID');

  ListParams.AddJoin('inner join suppliers b on a.id_suppliers=b.id and b.is_active=1');
  InitializeListProc:=InitializeList;
  DialogMode:=true;
  BeforeDelete := OnBeforeDelete;
end;

destructor TPurchaseOrderEntityManager.Destroy;
begin
  FreeAndNil(SC);
  inherited;
end;

procedure TPurchaseOrderEntityManager.OnBeforeDelete(Sender: TObject;
  Param: Variant);
var ObjId : TIDType;
    posState : TStockPOState;
begin
//  TStockPOState = (stpoOrdered, stpoPartial, stpoReceived, stpoProcessed);

  ObjId := Param;
  posState := StockManagerU.GetRealPOState(Untilldb, ObjId);

  if Ord(posState) >= Ord(stpoReceived) then
    Plugin.RaiseException('This document cannot be deleted - there are invoices which have already been received');
  if SQLCheckExists(['id_purchase_order'],[ObjId],'','purchase_order_item') then
    SQLDelete('id_purchase_order',ObjId,'purchase_order_item');
end;

procedure TPurchaseOrderEntityManager.OnFilterConfirmed(Sender: TObject);
begin
  flistview.UnselectAll;
  flistview.RefreshList;
end;

class procedure TPurchaseOrderEntityManager.GetBaseClassList(
  AClassList: TPlaceInTreeList);
begin
  with AClassList.AddItem do begin
    BaseClass := TStockDocumentTreeFolder;
    PlaceInTreeType := ptFirst;
  end;
end;

class function TPurchaseOrderEntityManager.GetEntityFrameClass: TEntityViewFrameClass;
begin
  Result:= TPurchaseOrderEntityFrame;
end;

class function TPurchaseOrderEntityManager.GetNodeName: WideString;
begin
  result:=STR_PurchaseOrder;
end;

function TPurchaseOrderEntityManager.GetStringRepresentation(
  FieldName: String; Value: Variant; Field: TField): WideString;
begin
   if SameText(FieldName,'order_type') then begin
     if Value = 0 then
       Result := Plugin.Translate('PurchaseOrderEntityManager','Open order')
     else
       Result := Plugin.Translate('PurchaseOrderEntityManager','Return of goods');
   end else if SameText(FieldName,'conducted') then begin
     result   := StockPurchaseOrderU.GetPOStateName(value);
   end else
     Result := inherited GetStringRepresentation(FieldName,Value,Field);
end;

class function TPurchaseOrderEntityManager.GetTableName: String;
begin
  result:='purchase_order'
end;

procedure TPurchaseOrderEntityManager.InitializeList(Sender: TEMListView);
var idx_supp_cond : Integer;
    fr:TDBEntityListFrame;
    mi: TTntMenuItem;
begin
  flistview := Sender;
  with Sender do begin
    idx_supp_cond:=ListParams.IndexOfCondition('a.id_suppliers=');
    if idx_supp_cond > -1 then
      ListParams.DeleteCondition(idx_supp_cond);
    AddFieldHeader(Plugin.Translate('PurchaseOrderEntityManager','Number'),dtInteger,10,'po_number',true);
    AddFieldHeader(Plugin.Translate('PurchaseOrderEntityManager','Supplier'),dtWideString,20,'supp_name',true);
    AddFieldHeader(Plugin.Translate('PurchaseOrderEntityManager','Date'),dtDate, 15, 'order_date', True);
    AddFieldHeader(strTypeCaption,dtWideString, 15, 'order_type', True);
    AddFieldHeader(Plugin.Translate('PurchaseOrderEntityManager','Reference'), dtWideString, 25, 'reference', True);
    AddFieldHeader(Plugin.Translate('PurchaseOrderEntityManager','Status'), dtInteger, 15, 'conducted', True);
    AddFieldHeader('ID',dtWideString,0,'id',false);
    if fSupplierId >0 then begin
    //  TStockPOState = (stpoOrdered, stpoPartial, stpoReceived, stpoProcessed);
      ListParams.AddCondition('a.id_suppliers=' + IntToStr(fSupplierId));
      ListParams.AddCondition('conducted < ' + IntToStr(Ord(stpoReceived)));
      ListParams.AddOrderByField('order_date desc');
    end;
    AddToolButton(lbtnNew);
    AddToolButton(lbtnDuplicate);
    AddToolButton(lbtnEdit);
    AddToolButton(lbtnDelete);
    AddToolButton(lbtnRefresh);

    AddToolButton(Plugin.Translate('StockAdjEntityManager','Close purchase order'),
                'Ctrl-S',ShortCut(ord('S'), [ssCtrl]),
                 TUntillPlugin(UntillApp.GetPluginByName(APP_NAME)).GetImageFileName('Conduct.ico'),
                 SaveClick, tCloseButton, mi );

    AddToolButton(Plugin.Translate('StockAdjEntityManager','Complete selected purchase order'),
                'Ctrl-K',ShortCut(ord('K'), [ssCtrl]),
                 TUntillPlugin(UntillApp.GetPluginByName(APP_NAME)).GetImageFileName('Complete.ico'),
                 CompleteClick, tCompleteButton, mi );

    fr:= TDBEntityListFrame(ListView.Frame);
    if fr.FindComponent('InvoiceItemFilterFrame') = nil then
      ffram:=TPurchaseOrderFilterFrame.Create(fr);

    ffram.Name := 'InvoiceItemFilterFrame';
    fr.panTop.Height:=ffram.Height;
    ffram.Parent:=fr.panTop;
    ffram.Align:=alClient;
    ffram.OnFilterConfirmed := OnFilterConfirmed;
  end;
end;

procedure TPurchaseOrderEntityManager.RefreshContent(Frame: TUntillFrame);
var   q   : TIBSQLU;
begin
  inherited;
  with TPurchaseOrderEntityFrame(Frame) do begin

    Conducted := stpoOrdered;
    edtNumber.Value :=1;
    if (NewEntity) or (IsDuplicating) then begin
        edtNumber.Value:=GetFirstAvailNumberDB(Self, 0,  'po_number');
        udtDate.Date:=DateOf(Now);
        udtDate.Time:=TimeOf(Now);
        udtReqired.Date:=DateOf(Now);
        udtReqTime.Time:=TimeOf(Now);
      exit;
    end;
    q:= TIBSQLU.Create(Self);
    try
      q.SQL.Text := 'select * from ' + GetTableName + ' where id='+IntToStr(ObjectId);
      UntillDB.open(q);
      if not q.eof then begin
        edtNumber.Value :=q.FieldByName('po_number').AsInteger;
        if q.FieldByName('order_type').AsInteger = 0 then
          rbOpen.checked := true
        else
          rbReturn.checked := true;
        Conducted := TStockPOState(q.FieldByName('conducted').AsInteger);
        edtRef.Text:=UTF8_Decode(q.FieldByName('reference').AsString);
        memoDesc.Text:=UTF8_Decode(q.FieldByName('description').AsString);
        udtDate.Date:=DateOf(SysDateTimeToLocal(q.FieldByName('order_date').AsDateTime));
        udtDate.Time:=TimeOf(SysDateTimeToLocal(q.FieldByName('order_date').AsDateTime));
        udtReqired.Date:=DateOf(SysDateTimeToLocal(q.FieldByName('reqired_date').AsDateTime));
        udtReqTime.Time:=TimeOf(SysDateTimeToLocal(q.FieldByName('reqired_date').AsDateTime));
        with usbSupp do begin
          Value:= StrToInt64Def(ClientDataset.FieldByName('id_suppliers').AsString,0);
          if Value<>0 then begin
            Text:=emSupp.GetWideStringById('name',Value);
            emItems.SupplierId := Value;
          end else
            Text:=''
        end;
        Caption:=STR_PurchaseOrder +': ' + IntToStr(edtNumber.value);
      end;
    finally
      FreeAndNil(q);
    end;
  end;
end;

procedure TPurchaseOrderEntityManager.SaveClick(Sender: TObject);
begin
  SC.CompleteReceivedPurchaseOrder(nil, TBOCustomUntillDB(Untilldb).BOUserId, LocalDatetimeToSys(Now));
end;

function TPurchaseOrderEntityManager.SaveContent(Frame: TUntillFrame): Boolean;
var or_type : Integer;
    d1, d2  : TDateTime;
    dts1, dts2 : TDateTimeStorage;
begin
  if Conducted = stpoProcessed then
    Plugin.RaiseException('This document cannot be changed - it is already processed');
  with  TPurchaseOrderEntityFrame(Frame) do begin
    dts1 := nil;
    dts2 := nil;
    try
      result:=false;
      if inherited SaveContent(Frame) = false then exit;
      if rbOpen.checked then or_type := 0 else or_type := 1;
      d1:=Now;
      ReplaceDate(d1, LocalDateTimeToSys(udtDate.Date));
      ReplaceTime(d1, LocalDateTimeToSys(udtDate.Date));
      d2:= LocalDateTimeToSys(DateOf(udtreqired.Date) + TimeOf(udtreqTime.Time));

      dts1 := TDateTimeStorage.Create(d1);
      dts2 := TDateTimeStorage.Create(d2);
      SaveRecord(['id','id_suppliers','order_type','order_date','reqired_date',
                'reference','description','id_untill_users','conducted',
                'po_number'],
                [ObjectId, usbSupp.value, or_type,
                dts1, dts2, edtRef.text, MemoDesc.text,
                bodb.BOUserId, Ord(conducted), edtNumber.value]);
      emItems.SaveDataset;
      // Update linked purchase order items
      if emItems.UsedPOItemList.count > 0 then begin
        StockOrderU.UpdatePOItems(Untilldb, emItems.UsedPOItemList, piPO);
      end;
      Caption:=STR_PurchaseOrder + ': ' + IntToStr(edtNumber.value);

      result:=true;
    finally
      FreeAndNil(dts1);
      FreeAndNil(dts2);
    end;
  end;
end;

procedure TPurchaseOrderEntityManager.TranslateStrings;
begin
  inherited;
  STR_PurchaseOrder := Plugin.Translate('PurchaseOrderEntityManager', 'Purchase order');
  STR_ShowOpenedPO := Plugin.Translate('PurchaseOrderEntityManager','Show only opened PO');
end;

initialization
  if not IsUntillLightMode then
    ClassManager.RegisterClass(TPurchaseOrderEntityManager);
end.
