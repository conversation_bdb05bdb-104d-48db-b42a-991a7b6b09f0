create table func_panel_tabs (
    id u_id,
    name varchar(40),
    num smallint,
    ml_name BLOB SUB_TYPE 0 SEGMENT SIZE 80,
    is_active smallint,
    is_active_modified timestamp,
    is_active_modifier varchar(30),
    constraint func_panel_tabs_pk primary key (id)
);
commit;
grant all on func_panel_tabs to untilluser;
commit;
execute procedure register_sync_table_ex('func_panel_tabs', 'b', 1);
commit;
execute procedure register_bo_table('func_panel_tabs', '', '');
commit;


create table func_panel_tab_buttons (
    id u_id,
    id_func_panel_tabs bigint,
    button_code varchar(40),
    button_text varchar(40),
    action_data varchar(80),
    ml_name BLOB SUB_TYPE 0 SEGMENT SIZE 80,
    num smallint,
    is_active smallint,
    is_active_modified timestamp,
    is_active_modifier varchar(30),
    constraint func_panel_tab_buttons_pk primary key (id),
	constraint func_panel_tab_buttons_fk1 foreign key (id_func_panel_tabs) references func_panel_tabs(id)
);
commit;
grant all on func_panel_tab_buttons to untilluser;
commit;
execute procedure register_sync_table_ex('func_panel_tab_buttons', 'b', 1);
commit;
execute procedure register_bo_table('func_panel_tab_buttons', 'id_func_panel_tabs', 'func_panel_tabs');
commit;

create table func_panel_settings (
    id u_id,
    default_tab smallint,
    constraint func_panel_settings_pk primary key (id)
);
commit;
grant all on func_panel_settings to untilluser;
commit;
execute procedure register_sync_table_ex('func_panel_settings', 'b', 1);
commit;
execute procedure register_bo_table('func_panel_settings', '', '');
commit;
