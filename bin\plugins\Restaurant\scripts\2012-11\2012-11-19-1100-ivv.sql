execute procedure set_logging_on;
update dep_button_setting set id=id where cast(id / 5000000000 as integer)=(select ser from sync_db where is_active=1 and server_no=1);
update ARTICLE_BUTTON_SETTING set id=id where cast(id / 5000000000 as integer)=(select ser from sync_db where is_active=1 and server_no=1);
commit;
execute procedure set_logging_off;
delete from dep_button_setting a 
where cast(a.id / 5000000000 as integer)<>(select ser from sync_db where is_active=1 and server_no=1)
and exists(select * from dep_button_setting b
    where  b.ID_DEPARTMENT=a.ID_DEPARTMENT and 
    cast(b.id / 5000000000 as integer)=(select ser from sync_db where is_active=1 and server_no=1));
delete from ARTICLE_BUTTON_SETTING a 
where cast(a.id / 5000000000 as integer)<>(select ser from sync_db where is_active=1 and server_no=1)
and exists(select * from ARTICLE_BUTTON_SETTING b
    where  b.id_articles=a.id_articles and 
    cast(b.id / 5000000000 as integer)=(select ser from sync_db where is_active=1 and server_no=1));
commit;

