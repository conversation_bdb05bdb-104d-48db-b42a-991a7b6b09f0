set term !! ;
create or alter procedure BCQ_TRANSFERRED_BILL (
    ID_TRANSFERRED_BILLS bigint)
as
declare variable ID_ORDERS bigint;
declare variable ID bigint;
declare variable QUANTITY integer;
declare variable ID_MENU bigint;
declare variable ID_ARTICLES bigint;
begin

  select ID_ORDERS_RESULT
  from TRANSFERRED_BILLS
  where ID = :ID_TRANSFERRED_BILLS
  into :ID_ORDERS;

  for select OI.ID, OI.QUANTITY, OI.ID_MENU, ID_ARTICLES
  from ORDER_ITEM OI
  where OI.ID_ORDERS = :ID_ORDERS
  into :ID, :QUANTITY, :ID_MENU, :ID_ARTICLES do begin
    execute procedure BEVERAGE_PREPROCESS_ORDERITEM(:id, :id_orders, -:quantity, :id_menu, :id_articles);
  end


end
!!
commit
!!
GRANT EXECUTE ON PROCEDURE BCQ_TRANSFERRED_BILL TO UNTILLUSER
!!
commit
!!
set term ; !!
