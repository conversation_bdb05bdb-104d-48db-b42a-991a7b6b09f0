unit DepartmentEntityManager;
interface
uses
  EntityManagerU, Classes, UntillDBU, ClassManagerU, RestaurantPluginU, IBSQL,
  UntillFram, TreeNodesU, SysUtils, CommonStringsU, Graphics, UntillEquU, UntillGraphicElementsU,
  DepartmentEntityFram, RestaurantTreeFoldersU, FoodGroupEntityManager, BOUntillDBU, UTF8U,
  Contnrs,ClassesU, UntillComboBoxU,ColorComboU,ButtonPropSelectFram, DB, DBRestaurantTableU,
  BOCommonEntityActionsU, DragAndDropItemsDlg;
const
  FONT_BOLD         = $01;
  FONT_ITALIC       = $02;
  FONT_UNDERLINE    = $04;
  FONT_STRIKEOUT    = $08;

type
  TDepDefFlagType = (dftColor, dftFont, dftAltFont);


  TDepartmentEntityManager = class(TEntityManager)
  private
    FbVoucher : boolean;
    Fdep_type : Integer;
    procedure ItemsOrderClick(Sender:TObject);
    procedure ItemsRmOrderClick(Sender:TObject);
    procedure BeforeRefresh(Sender: TObject);
    procedure ChangeAppearance(
              tran: IWTran; btnType: TBtnType;
              DepId: Int64; cmbGroup: TUntillCombobox;
              ChangeColor: Boolean; cmbColor: TTntColorComboBox;
              ChangeFont: Boolean; AFont: TFont;
              ChangeFontAlt: Boolean; AFontAlt: TFont;
              ADefaultColor, ADefaultFont, ADefaultAltFont: boolean);
    procedure UpdateDepDefFlag(
              ATran: IWTran; ABtnType: TBtnType;
              AFlagType: TDepDefFlagType; AId: Int64; AValue: boolean);
    procedure SetbVoucher(const Value: boolean);
    procedure UpdateDepNums(num_name: string);
  public
    constructor     Create(AOwner :TComponent; AUntillDB:TBOCustomUntillDB); override;
    class procedure GetBaseClassList(AClassList: TPlaceInTreeList); override;
    class function  GetEntityFrameClass: TEntityViewFrameClass; override;
    class function  GetMassModifyFrameClass: TMassModifyFrameClass; override;
    class function  GetTableName: String; override;
    class function  GetNodeName: WideString; override;

    property  bVoucher  : boolean read FbVoucher write SetbVoucher;
    property  dep_type  : Integer read Fdep_type write Fdep_type;
    procedure InitializeList(Sender:TEMListView);

    procedure TranslateStrings; override;
    procedure CanInsert(Frame: TUntillFrame; Update: Boolean); override;
    function  SaveContent(Frame: TUntillFrame): Boolean; override;
    procedure RefreshContent(Frame: TUntillFrame); override;
    procedure MassModifyRecords(Frame: TUntillFrame;  RecordsIds: TObjectList); override;
    procedure CanMassModify(Frame:TUntillFrame); override;
    function GetStringRepresentation(FieldName: String; Value: Variant; Field: TField = nil): WideString; override;
    procedure CanDelete(Frame: TUntillFrame; Update: Boolean; ObjectId: Int64 = 0); override;
  end;

implementation
uses DataControlsU, EntityFram, Variants, CommonU, FixedIdU, DepartmentMassModifyFram,
     ProgressFrm, EmbEntityManagerU, DepAvailEmbEntityManager, UntillAppU, LangU,
     HelpConsts, RestaurantCommonStringsU, MassModifyU, DepartmentSubEntityManager,
     Menus, UntillToolBarU, TntCompatibilityU, ViewU;

{ TDepartmentEntityManager }

var
  STR_Department  :WideString;
  STR_Name :WideString;

procedure TDepartmentEntityManager.BeforeRefresh(Sender: TObject);
var idx : Integer;
begin
  if Dep_type > -1 then begin
    idx := ListParams.IndexOfCondition('coalesce(department.dep_type,0)');
    if idx > -1 then
      ListParams.DeleteCondition(idx);
    if Dep_type  = 10 then
      ListParams.AddCondition('coalesce(department.dep_type,0) in (0,1)' )
    else
      ListParams.AddCondition('coalesce(department.dep_type,0) = ' + IntToStr(Dep_type) );
  end else begin
    idx := ListParams.IndexOfCondition('coalesce(department.dep_type,0)');
    if idx>-1 then
      ListParams.DeleteCondition(idx);
    ListParams.AddCondition('coalesce(department.dep_type,0) <> 1 ');
  end;
  idx := ListParams.IndexOfCondition('food_group.group_type');
  if idx>-1 then
    ListParams.DeleteCondition(idx);
  if ListView.Mode=Modal then begin
    if FbVoucher then
      ListParams.AddCondition('food_group.group_type=1')
    else
      ListParams.AddCondition('coalesce(food_group.group_type,0)=0')
  end;
end;

procedure TDepartmentEntityManager.CanDelete(Frame: TUntillFrame;
  Update: Boolean; ObjectId: Int64);
var iq  : IIBSQL;
begin
  inherited;

  iq := UntillDB.GetPreparedIIbSql('select count(*) from order_item '
     + ' join articles on articles.id = order_item.id_articles '
     + ' join orders on orders.id=order_item.id_orders '
     + ' join bill on bill.id=orders.id_bill '
     + ' where bill.close_datetime is null and articles.ID_DEPARTAMENT =:ID_DEPARTAMENT');
  iq.q.Params[0].AsInt64 := ObjectId;
  iq.ExecQuery;
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
     plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this department. '
      + 'Articles from this department are ordered on a table that is not closed yet') );

  iq := UntillDB.GetPreparedIIbSql('select count(*) from INVENTORY_ITEM '
     + ' join articles on articles.ID_INVENTORY_ITEM = INVENTORY_ITEM.id and INVENTORY_ITEM.is_active=1'
     + ' where articles.ID_DEPARTAMENT =:ID_DEPARTAMENT');
  iq.q.Params[0].AsInt64 := ObjectId;
  iq.ExecQuery;
  if not iq.eof then
    if iq.fields[0].asInteger>0 then
     plugin.RaiseError( Plugin.Translate('ArticleEntityManager', 'You cannot delete this department. '
      + 'Articles from this department are linked to stock ingredients') );
end;

procedure TDepartmentEntityManager.CanInsert(Frame: TUntillFrame;
  Update: Boolean);
begin
  inherited;
  if not assigned(Frame) then exit;
  with TDepartmentEntityFrame(Frame) do begin
    CheckEdtOnEmpty(edtName, nil, Str_Name);
    CheckUsbOnEmpty(usbGroup, tbGeneral, Plugin.Translate('DepartmentEntityManager', 'Group'));
    CheckSameName(edtName, STR_Department, nil, 'name', ' and coalesce(dep_type,0)=' + IntToStr(dep_type));
    CheckSameNumber(edtNumber, STR_Department, nil, 'dep_number', true, ' and coalesce(dep_type,0)=' + IntToStr(dep_type));
  end;
end;

procedure TDepartmentEntityManager.CanMassModify(Frame: TUntillFrame);
begin
  inherited;
  with TDepartmentMassModifyFrame(Frame) do begin
    if chbChangeGroup.Checked then
      CheckUsbOnEmpty(usbNewGroup, tsGeneral, Plugin.Translate('DepartmentEntityManager','Group'));
  end;
end;

procedure TDepartmentEntityManager.UpdateDepDefFlag(
  ATran: IWTran; ABtnType: TBtnType;
  AFlagType: TDepDefFlagType; AId: Int64; AValue: boolean);
var
  qr: IIBSQL;
  sql: string;
begin
  sql := 'update department set';
  case ABtnType of
    btnPC: exit;
    btnHHT:
      case AFlagType of
        dftColor:    sql := sql + ' hht_default_color = :value';
        dftFont:     sql := sql + ' hht_default_font = :value';
        dftAltFont:  sql := sql + ' hht_default_alt_font = :value';
      end;
    btnOman:
      case AFlagType of
        dftColor:   sql := sql + ' oman_default_color = :value';
        dftFont:    sql := sql + ' oman_default_font = :value';
        dftAltFont: sql := sql + ' oman_default_alt_font = :value';
      end;
  end;

  sql := sql + ' where id = :id';
  qr := ATran.GetPreparedIIbSql(sql);
  qr.q.ParamByName('id').AsInt64 := AId;
  if AValue then
    qr.q.ParamByName('value').AsInteger := 1
  else
    qr.q.ParamByName('value').AsInteger := 0;
  qr.q.ExecQuery;
end;

procedure TDepartmentEntityManager.ChangeAppearance(
          tran: IWTran; btnType: TBtnType;
          DepId: Int64; cmbGroup: TUntillCombobox;
          ChangeColor: Boolean; cmbColor: TTntColorComboBox;
          ChangeFont: Boolean; AFont: TFont;
          ChangeFontAlt: Boolean; AFontAlt: TFont;
          ADefaultColor, ADefaultFont, ADefaultAltFont: boolean);
var q, qa: TIBSQLU;
    FontAttr: SmallInt;
    prefix: string;
begin
  if (not ChangeColor) and (not ChangeFont) and (not ChangeFontAlt) then exit;

  q := tran.GetTempSql;
  q.SQL.SetText('select * from dep_button_setting where id_department = :aid and id_screen_groups = :gid');
  q.Params[0].AsInt64 := DepId;
  q.Params[1].AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
  q.ExecQuery;

  if q.Eof then begin
    ///////////////////////////////////////////////////////////////////////////////
    ///  Not exists, insert by copying info from department
    ///
    qa := tran.GetTempSql;
    case btnType of
      btnPC: prefix := 'pc';
      btnHHT: prefix := 'hht';
      else prefix := 'hht';
    end;

    qa.SQL.Text := Format('select %0:s_color, %0:s_font_name, %0:s_font_size, %0:s_font_attr, %0:s_font_color from department where id=:aid', [prefix]);
    qa.Params[0].AsInt64 := depId;
    qa.ExecQuery;
    assert(not qa.Eof, 'department not found in mass TDepartmentEntityManager.ChangeAppearance');

    q := tran.GetTempSql;
    q.SQL.SetText(
      'insert' +
      '  into dep_button_setting (' +
      '       id, id_department, id_screen_groups, color, font_name, font_size, font_attr, font_color) ' +
      'values (' +
      '       :id, :id_department, :id_screen_groups, :color, :font_name, :font_size, :font_attr, :font_color)'
    );
    q.Params[0].AsInt64 := tran.GetID;
    q.Params[1].AsInt64 := depId;
    q.Params[2].AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
    q.Params[3].AsInteger := qa.FieldByName(Format('%s_color', [prefix])).AsInteger;
    q.Params[4].AsString := qa.FieldByName(Format('%s_font_name', [prefix])).AsString;
    q.Params[5].AsInteger := qa.FieldByName(Format('%s_font_size', [prefix])).AsInteger;
    q.Params[6].AsInteger := qa.FieldByName(Format('%s_font_attr', [prefix])).AsInteger;
    q.Params[7].AsInteger := qa.FieldByName(Format('%s_font_color', [prefix])).AsInteger;
    q.ExecQuery;
  end;

  if ChangeColor then begin
    UpdateDepDefFlag(tran, btnType, dftColor, DepId, ADefaultColor);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultColor) then begin
      q := tran.GetTempSql;
      q.SQL.SetText(
        'update dep_button_setting' +
        '   set color = :color' +
        ' where id_department = :aid and id_screen_groups = :gid'
      );
      q.ParamByName('aid').AsInt64 := depId;
      q.ParamByName('gid').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      q.ParamByName('color').AsInteger := ColorToRGB(cmbColor.Selected);
      q.ExecQuery;
    end;
  end;

  if ChangeFont then begin
    UpdateDepDefFlag(tran, btnType, dftColor, DepId, ADefaultColor);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultColor) then begin
      FontAttr := 0;
      if fsBold in AFont.Style then FontAttr := FontAttr + FONT_BOLD;
      if fsItalic in AFont.Style then FontAttr := FontAttr or FONT_ITALIC;
      if fsUnderline in AFont.Style then FontAttr := FontAttr or FONT_UNDERLINE;
      if fsStrikeout in AFont.Style then FontAttr := FontAttr or FONT_STRIKEOUT;

      q := tran.GetTempSql;
      q.SQL.SetText(
        'update dep_button_setting' +
        '   set font_name = :font_name, font_size = :font_size, font_attr = :font_attr, font_color = :font_color' +
        ' where id_department = :aid and id_screen_groups = :gid'
      );
      q.ParamByName('aid').AsInt64 := depId;
      q.ParamByName('gid').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      q.ParamByName('font_name').AsString := AFont.Name;
      q.ParamByName('font_size').AsInteger := AFont.Size;
      q.ParamByName('font_attr').AsInteger := FontAttr;
      q.ParamByName('font_color').AsInteger := ColorToRGB(AFont.Color);
      q.ExecQuery;
    end;
  end;

  if ChangeFontAlt then begin
    UpdateDepDefFlag(tran, btnType, dftColor, DepId, ADefaultColor);
    if (btnType = btnPC) or ((btnType in [btnHHT, btnOman]) and not ADefaultColor) then begin
      FontAttr := 0;
      if fsBold in AFontAlt.Style then FontAttr := FontAttr + FONT_BOLD;
      if fsItalic in AFontAlt.Style then FontAttr := FontAttr or FONT_ITALIC;
      if fsUnderline in AFontAlt.Style then FontAttr := FontAttr or FONT_UNDERLINE;
      if fsStrikeout in AFontAlt.Style then FontAttr := FontAttr or FONT_STRIKEOUT;

      q := tran.GetTempSql;
      q.SQL.SetText(
        'update dep_button_setting' +
        '   set font_name_alt = :font_name, font_size_alt = :font_size, font_attr_alt = :font_attr, font_color_alt = :font_color ' +
        ' where id_department = :aid and id_screen_groups = :gid'
      );
      q.ParamByName('aid').AsInt64 := depId;
      q.ParamByName('gid').AsInt64 := TInt64(cmbGroup.Items.Objects[cmbGroup.ItemIndex]).Value;
      q.ParamByName('font_name').AsString := AFontAlt.Name;
      q.ParamByName('font_size').AsInteger := AFontAlt.Size;
      q.ParamByName('font_attr').AsInteger := FontAttr;
      q.ParamByName('font_color').AsInteger := ColorToRGB(AFontAlt.Color);
      q.ExecQuery;
    end;
  end;
end;

constructor TDepartmentEntityManager.Create(AOwner: TComponent;
  AUntillDB: TBOCustomUntillDB);
begin
  inherited Create(AOwner, AUntillDB);
  FbVoucher := false;
  FDep_type := -1;
  LoadIcon(Plugin.GetImageFileName('department.ico'));
  SetListParams(['department.ID ID', 'dep_number','department.name name','pc_fix_button',
                'rm_fix_button','id_food_group', 'food_group.name gname', 'department.dep_type',
                'category.name catname', 'coalesce(food_group.group_type,0) group_type',
                'department.sequence', 'department.rm_sequence',
                'AS0.color pc_color', 'AS0.font_color pc_font_color',
                'coalesce(AS1.color, AS0.color) rm_color',
                'coalesce(AS1.font_color,0) rm_font_color'],
                ['department'],
                [],
                'ID');
  ListParams.AddJoin('join food_group on department.id_food_group = food_group.ID and food_group.is_active=1');
  ListParams.AddJoin('join category on food_group.id_category = category.id and category.is_active=1');
  ListParams.AddJoin('left outer join DEP_BUTTON_SETTING AS0 on AS0.ID_DEPARTMENT = DEPARTMENT.id and AS0.id_screen_groups='+IntToStr(ID_SCREEN_GROUPS_PC));
  ListParams.AddJoin('left outer join DEP_BUTTON_SETTING AS1 on AS1.ID_DEPARTMENT = DEPARTMENT.id and AS1.id_screen_groups='+IntToStr(ID_SCREEN_GROUPS_FALCON));

  InitializeListProc  := InitializeList;
  Options             := Options + [emoMultipleSelection];
  BeforeRefreshList   := BeforeRefresh;
  DialogMode          := true;
  HelpContext         := HelpConsts.DEPARTMENTS_ID;
end;

class procedure TDepartmentEntityManager.GetBaseClassList(AClassList: TPlaceInTreeList);
begin
  with AClassList.AddItem do begin
    BaseClass := TProductTreeFolder;
    InsertClass := TDepartmentSubEntityManager;
    PlaceInTreeType := ptAfter;
  end;
end;

class function TDepartmentEntityManager.GetEntityFrameClass: TEntityViewFrameClass;
begin
  Result:= TDepartmentEntityFrame;
end;

class function TDepartmentEntityManager.GetMassModifyFrameClass: TMassModifyFrameClass;
begin
  result:=TDepartmentMassModifyFrame;
end;

class function TDepartmentEntityManager.GetNodeName: WideString;
begin
  result:=Plugin.Translate('DepartmentEntityManager', 'Departments');
end;

function TDepartmentEntityManager.GetStringRepresentation(FieldName: String;
  Value: Variant; Field: TField): WideString;
begin
  if Sametext(FieldName, 'dep_type') then begin
    if Field.AsInteger = 2 then
      result:=Plugin.Translate('DepartmentEntityManager','Yes')
    else
      result:= ' ';
    exit;
  end else if Sametext(FieldName, 'group_type') then begin
    if Field.AsInteger=1 then
      result:=Plugin.Translate('DepartmentEntityManager','Voucher')
    else
      result:= ' ';
    exit;
  end else
    inherited GetStringRepresentation(FieldName, Value, Field);
end;

class function TDepartmentEntityManager.GetTableName: String;
begin
  result:='department';
end;

procedure TDepartmentEntityManager.InitializeList(Sender: TEMListView);
var m: TTntMenuItem;
    b: TUntillToolButton;
begin
  with Sender do begin
    AddFieldHeader(CommonStringsU.StrNumberCaption,dtWideString,15,'dep_number',true);
    AddFieldHeader(CommonStringsU.StrNameCaption,dtWideString,30,'name',true);
    AddFieldHeader(Plugin.Translate('DepartmentEntityManager','Sub-department?'),dtWideString,18,'dep_type',true);
    AddFieldHeader(Plugin.Translate('DepartmentEntityManager','Group'),dtWideString,20,'gname',true);
    AddFieldHeader(Plugin.Translate('DepartmentEntityManager','Category'),dtWideString,20,'catname',true);
    AddFieldHeader(Plugin.Translate('DepartmentEntityManager','Sequence'),dtInteger,20,'sequence',true);
    AddFieldHeader(Plugin.Translate('DepartmentEntityManager','Rm_sequence'),dtInteger,20,'rm_sequence',true);
    AddFieldHeader(strTypeCaption,dtWideString,15,'group_type',true);
    AddFieldHeader('ID',dtWideString,0,'id',false);
    if not IsUntillLightMode then
      AddToolButton(lbtnNew);
    AddToolButton(lbtnDuplicate);
    AddToolButton(lbtnEdit);
    AddToolButton(lbtnDelete);
    AddToolButton(lbtnRefresh);
    AddToolButton(lbtnDefault);
    AddToolButton(lbtnMassModify);
    AddToolButton(Plugin.Translate('DepArticleEmbEntityManager','Departments sequence'),
                   'Ctrl-R',ShortCut(ord('R'), [ssCtrl]),Plugin.GetImageFileName('DragNDrop.ico'), ItemsOrderClick, b, m);
    AddToolButton(Plugin.Translate('DepArticleEmbEntityManager','Departments Rm-sequence'),
                   'Ctrl-M',ShortCut(ord('M'), [ssCtrl]),Plugin.GetImageFileName('DragNDrop_rm.ico'), ItemsRmOrderClick, b, m);
  end;
end;

procedure TDepartmentEntityManager.ItemsOrderClick(Sender: TObject);
var dlg   : TDragAndDropItemsDialog;
    newds : TDepartmentEntityManager;
begin
  dlg:=TDragAndDropItemsDialog.Create(Self);
  try
    dlg.NameField         := 'name';
    dlg.NumberField       := 'dep_number';
    dlg.OrderField        := 'sequence';
    dlg.PCFontColorField  := 'pc_font_color';
    dlg.PCColorField      := 'pc_color';

    newds := TDepartmentEntityManager.Create(nil, UntillDB);
    try
      newds.dep_type := 10;
      newds.GetHiddenView;
      if dlg.Execute( newds.ClientDataSet ) then begin
        Self.SetModified;
        newds.UpdateDepNums('sequence');
      end;
    finally
      FreeAndNil( newds );
    end;

  finally
    dlg.Free;
  end;
end;

procedure TDepartmentEntityManager.UpdateDepNums( num_name : string);
var q  : IIBSQL;
    wt : IWTran;
    seq : Integer;
    id : Int64;
begin
  if trim(num_name) = '' then exit;

  wt := UntillDB.getWTran;
  ClientDataSet.First;
  while not ClientDataSet.eof do begin
    id := StrToInt64Def(ClientDataSet.fieldByName('id').AsString,0);
    seq := ClientDataSet.fieldByName(num_name).AsInteger;
    q  := wt.GetPreparedIIbSql('update department set ' + num_name + '=:seq where id=:id');
    q.q.ParamByName('seq').AsInteger  := seq;
    q.q.ParamByName('id').AsInt64         := id;
    q.ExecQuery;
    ClientDataSet.Next;
  end;

  wt.commit;
end;

procedure TDepartmentEntityManager.ItemsRmOrderClick(Sender: TObject);
var dlg   : TDragAndDropItemsDialog;
    newds : TDepartmentEntityManager;
begin
  dlg:=TDragAndDropItemsDialog.Create(Self);
  try
    dlg.NameField         := 'name';
    dlg.NumberField       := 'dep_number';
    dlg.OrderField        := 'rm_sequence';
    dlg.PCFontColorField  := 'rm_font_color';
    dlg.PCColorField      := 'rm_color';

    newds := TDepartmentEntityManager.Create(nil, UntillDB);
    try
      newds.dep_type := 10;
      newds.GetHiddenView;
      if dlg.Execute( newds.ClientDataSet ) then begin
        Self.SetModified;
        newds.UpdateDepNums('rm_sequence');
      end;
    finally
      FreeAndNil( newds );
    end;
  finally
    dlg.Free;
  end;
end;

procedure TDepartmentEntityManager.MassModifyRecords(Frame: TUntillFrame;
  RecordsIds: TObjectList);
var
  i : integer;
  idCondiment, idPromo, id: int64;
  fields: array of string;
  params: array of TVarRec;
  idDep, idCour: int64;
  pf: TProgressForm;
  ws:WideString;
  tran: IWTran;

  function AddField(name:string):integer;
  begin
    SetLength(fields, Length(fields)+1);
    fields[Length(fields)-1]:=name;
    SetLength(params, Length(params)+1);
    result:=Length(params)-1;
  end;

  procedure  SaveEmbeddedMgr(id: Int64; em:TEmbeddedEntityManager;
    ControlEmClass:TEmbeddedManagerClass; addItems : TEditModeItem; id_entity_field : string);
  var em_old:TEmbeddedEntityManager;
      deleteList : TStringList;
      bCond : Boolean;
      i     : Integer;
  begin
    em.ParentId:=id;
    with em.ClientDataSet do begin
      DisableControls;
      try
        {-- initialize parent id --}
        first;
        while not eof do begin
          edit;
          FieldByName(em.idFieldName).AsString:=IntToStr(GenerateId);
          FieldByName('id_department').AsString:=IntToStr(id);
          post;
          next;
        end;

        {-- clear old records --}
        em_old:=ControlEmClass.Create(Self, Self, id);
        try
          em_old.Prepare;
          em_old.ListView.RefreshList;
          if (id_entity_field <> '') and (addItems <> emiChange) then begin
            deleteList := TStringList.Create;
            try
              First;
              while not eof do begin
                em_old.ClientDataSet.first;
                while not em_old.ClientDataSet.eof do begin
                  bCond := (em_old.ClientDataSet.FieldByName(id_entity_field).asString=FieldByName(id_entity_field).asString);
                  if bCond then
                    deleteList.Add(em_old.ClientDataSet.FieldByName(IDFieldName).asString);
                  em_old.ClientDataSet.next;
                end;
                Next;
              end;
              if deleteList.Count>0 then
                for i := 0 to Pred(deleteList.count) do
                  em_old.DeleteEntity(StrToInt64Def(deleteList[i],0));
            finally
              FreeAndNil( deleteList );
            end;
          end else
            em_old.DeleteAll;
          em_old.SaveDataset;
        finally
          FreeAndNil(em_old);
        end;

        if (addItems in [emiAdd, emiChange]) or (id_entity_field='') then
          em.SaveDataset(false);
      finally
        EnableControls;
      end;
    end;
  end;

  var editMode : TEditModeItem;
begin
  inherited;
  with TDepartmentMassModifyFrame(Frame) do begin
    pf:=TProgressForm.Create(Self);
    pf.show;
    try
      if NothingToUpdate then exit;

      {--Food group--}
      if (chbChangeGroup.Checked) then begin
        idDep:=usbNewGroup.Value;
        with params[AddField('ID_FOOD_GROUP')] do begin
          VInt64:=@idDep;
          VType:=vtInt64;
        end;
      end;
      {--Supplement--}
      if (chbChangeSupplement.Checked) then begin
        idCour:=usbNewSupplement.Value;
        with params[AddField('ID_OPTIONS_SUPPLEMENT')] do begin
          VInt64:=@idCour;
          VType:=vtInt64;
        end;
      end;
      {--Condiment--}
      if (chbChangeCondiment.Checked) then begin
        idCondiment:=usbNewCondiment.Value;
        with params[AddField('ID_OPTIONS_CONDIMENT')] do begin
          VInt64:=@idCondiment;
          VType:=vtInt64;
        end;
      end;
      {--Age group--}
      if (chbChangeAgeGroup.Checked) then begin
        idPromo:=usbNewAgeGroup.Value;
        with params[AddField('ID_AGE_GROUPS')] do begin
          VInt64:=@idPromo;
          VType:=vtInt64;
        end;
      end;

      {--HHT default settings--}
      with params[AddField('hht_default_color')] do begin
        VInteger:=Integer(chbHHTDefaultColor.checked);
        VType:=vtInteger;
      end;
      with params[AddField('hht_default_font')] do begin
        VInteger:=Integer(chbHHTDefaultFont.checked);
        VType:=vtInteger;
      end;
      with params[AddField('hht_default_alt_font')] do begin
        VInteger:=Integer(chbHHTDefaultAltFont.Checked);
        VType:=vtInteger;
      end;
      {--OMan default setting--}
      with params[AddField('oman_default_color')] do begin
        VInteger:=Integer(chbOManDefaultColor.checked);
        VType:=vtInteger;
      end;
      with params[AddField('oman_default_font')] do begin
        VInteger:=Integer(chbOManDefaultFont.checked);
        VType:=vtInteger;
      end;
      with params[AddField('oman_default_alt_font')] do begin
        VInteger:=Integer(chbOManDefaultAltFont.checked);
        VType:=vtInteger;
      end;

      ws:=Plugin.Translate('ArticleEntityManager', 'Processing record: %d of %d','Mass modifying progress');
      for i:=0 to RecordsIds.Count-1 do begin
        pf.RefreshProgress(round((i/RecordsIds.Count)*100),
          WideFormat(ws,[i, RecordsIds.Count]));

        id:=TInt64(RecordsIds.Items[i]).Value;
        {--Main fields--}
        if length(params)>0 then SQLUpdate(id, fields, params);
        {--PC Color--}
        if (chbChangePCAppearance.Checked) then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnPC,
            id, cmbPCAppGroup,
            chbChangePCColor.Checked, cmbNewPCColor,
            chbChangePCFont.Checked, NewPCFont,
            chbChangePCAltFont.Checked, NewPCAltFont,
            false, false, false
          );
          tran.commit;
        end;
        {--HHT Color--}
        if chbChangeHHTAppearance.Checked then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnHHT,
            id, cmbHHTAppGroup,
            chbChangeHHTColor.Checked, cmbHHTColor,
            chbChangeHHTFont.Checked, NewHHTFont,
            chbChangeHHTAltFont.Checked, NewHHTAltFont,
            chbHHTDefaultColor.Checked, chbHHTDefaultFont.Checked, chbHHTDefaultAltFont.Checked
          );
          tran.commit;
        end;
        {--Orderman Color--}
        if chbChangeOManAppearance.Checked then begin
          tran:=UntillDB.getWTran();
          ChangeAppearance(
            tran, btnOman,
            id, cmbOmanAppGroup,
            chbChangeOmanColor.Checked, cmbOmanColor,
            chbChangeOmanFont.Checked, NewOmanFont,
            chbChangeOmanAltFont.Checked, NewOmanAltFont,
            chbOmanDefaultColor.Checked, chbOmanDefaultFont.Checked, chbOmanDefaultAltFont.Checked
          );
          tran.commit;
        end;
        {--Available--}
        if (chbAddAvailable.Checked) or chbRemoveAvailable.Checked or chbChangeAvailable.Checked then begin
          if chbAddAvailable.Checked then
            editMode := emiAdd
          else if chbChangeAvailable.Checked then
            editMode := emiChange
          else
            editMode := emiRemove;
          SaveEmbeddedMgr(id, emDepartmentAvail, TDepAvailEmbEntityManager, editMode, 'id_sales_area');
        end;
        //Make dummy Department table update
      end;
      DBRestaurantTableU.DummyUpdateDBDepartment(UntillDB);
    finally
      FreeAndNil(pf);
    end;
  end;
end;

procedure TDepartmentEntityManager.RefreshContent(Frame: TUntillFrame);
var q : IIBSQL;
begin
  inherited;
  with TDepartmentEntityFrame(Frame) do begin
    if NewEntity or Duplicating then begin
      HHTButtonPropFram.IsDefaultColor := True;
      HHTButtonPropFram.IsDefaultFont := True;
      OManButtonPropFram.IsDefaultColor := True;
      OManButtonPropFram.IsDefaultFont := True;
    end;
    if NewEntity then begin
      PCButtonPropFram.ImBitmap.visible := false;
      PCButtonPropFram.cbPCColor.Selected := clBtnFace;
      HHTButtonPropFram.cbPCColor.Selected := clWhite;
      OManButtonPropFram.cbPCColor.Selected := clBlack;
      usSupMaxQty.value := 0;
      usConMaxQty.value := 0;
      edtName.Text:=Plugin.Translate('DepartmentEntityManager', 'New department');
      edtNameChange( edtName );
      chkFastLane.Checked   := false;
      chkExcludeArt.Checked := false;
    end;
    if Duplicating then begin
      PCButtonPropFram.cmbScreenGroups.Enabled := false;
      HHTButtonPropFram.cmbScreenGroups.Enabled := false;
      OManButtonPropFram.cmbScreenGroups.Enabled := false;
    end;

    q := GetDepartmentButtonPropQuery( UntillDB, ObjectId );
    if not q.eof then begin
      edtName.Text:=UTF8_Decode(q.FieldByName('name').AsString);
      edtName.Enabled := not IsSalesData(UntillDB, ObjectId);
      usbGroup.Enabled := edtName.Enabled;

      chkFastLane.Checked   := (q.FieldByName('fastlane').AsInteger>0);
      chkExcludeArt.Checked := (q.FieldByName('exclude_art').AsInteger>0);
      edtNumber.Value :=q.FieldByName('dep_number').AsInteger;
      edtHqId.Text := UTF8_Decode(q.FieldByName('hq_id').asString);
      edtExternalId.Text := q.FieldByName('dep_external_id').asString;
      with usbGroup do begin
        Value:=StrToInt64Def(ClientDataSet.fieldByname('id_food_group').AsString, 0);
        if Value<>0 then
          Text:=emGroup.GetWideStringById('name',Value)
        else
          Text:=''
      end;
      with usbAgeGroup do begin
        Value:=StrToInt64Def(q.q.fieldByname('id_age_groups').AsString, 0);
        if Value<>0 then
          Text:=emAgeGroup.GetWideStringById('name',Value)
        else
          Text:=''
      end;
      DisableNotVoucher(GetFoodGroupType(Untilldb, StrToInt64Def(q.q.fieldByname('ID_FOOD_GROUP').AsString,0))=0);
      Caption:=STR_Department +': ' + edtName.Text;
    end;
    PCButtonPropFram.edtPCText.Text := UTF8_Decode(q.FieldByName('pc_text').asString);
    HHTButtonPropFram.edtPCText.Text := UTF8_Decode(q.FieldByName('rm_text').asString);
    OManButtonPropFram.edtPCText.Text := UTF8_Decode(q.FieldByName('oman_text').asString);
    HHTButtonPropFram.IsDefaultColor := q.FieldByName('hht_default_color').AsInteger=1;
    HHTButtonPropFram.IsDefaultFont := q.FieldByName('hht_default_font').AsInteger=1;
    HHTButtonPropFram.IsDefaultAltFont := q.FieldByName('hht_default_alt_font').AsInteger=1;
    OManButtonPropFram.IsDefaultColor := q.FieldByName('oman_default_color').AsInteger=1;
    OManButtonPropFram.IsDefaultFont := q.FieldByName('oman_default_font').AsInteger=1;
    OManButtonPropFram.IsDefaultAltFont := q.FieldByName('oman_default_alt_font').AsInteger=1;

    BOCommonEntityActionsU.RefreshItemPCButtonpropFram( PCButtonPropFram, ObjectId, q );

    with HHTButtonPropFram do begin
      RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      pbUsedFont.Text      := pnlButton.Font.Name;
    end;
    with OManButtonPropFram do begin
      RefreshButtonProps(ObjectId, ARTICLE_TABLE_SETTINGS_NAME, ARTICLE_FIELD_SETTINGS_NAME);
      pbUsedFont.Text      := pnlButton.Font.Name;
    end;
    PCButtonPropFram.ResizeButton;
    HHTButtonPropFram.ResizeButton;
    OManButtonPropFram.ResizeButton;
    RefreshFonts;

    with usbSupplement do  begin
      Value:=StrToInt64Def(q.fieldByname('id_options_supplement').AsString, 0);
      if Value<>0 then begin
        Text:=emOption.GetWideStringById('name',Value);
        usSupMaxQty.value := q.FieldByName('max_supp_quantity').AsInteger;
        usSupMaxQty.enabled := True;
      end else begin
        Text:='';
        usSupMaxQty.value := 0;
        usSupMaxQty.enabled := False;
      end;
    end;
    with usbCondiment do begin
      Value:=StrToInt64Def(q.fieldByname('id_options_condiment').AsString, 0);
      if Value<>0 then begin
        Text:=emOption.GetWideStringById('name',Value);
        usConMaxQty.value := q.FieldByName('max_cond_quantity').AsInteger;
        usConMaxQty.enabled := True;
      end else begin
        Text:='';
        usConMaxQty.value := 0;
        usConMaxQty.enabled := False;
      end;
    end;
    chkSubDep.Checked := q.FieldByName('dep_type').AsInteger = 2;
    chkSubDep.Visible := Clientdataset.FieldByName('group_type').AsInteger = 0;
    if (NewEntity)or(Duplicating) then
      edtNumber.Value:=GetFirstAvailNumber(Self, ClientDataset.FieldByName('dep_number').AsInteger,'dep_number');
  end;
end;

function TDepartmentEntityManager.SaveContent(Frame: TUntillFrame): Boolean;
var MS : TMemoryStream;
    HHTText : String;
    artList : TStringList;
    i : Integer;
    def_name : String;
    dpt : Integer;
begin
  with TDepartmentEntityFrame(Frame) do begin
    result:=false;
    if inherited SaveContent(Frame) = false then exit;
    MS := nil;
    try
      MS := TMemoryStream.Create;

      GetImagePropStream(PCButtonPropFram, MS);

      def_name := edtname.text;
      if IsUntillLightMode then
        HHTText := edtHHTText.Text
      else
        HHTText := HHTButtonPropFram.edtPCText.text;

      if chkSubDep.checked then
        dpt := 2
      else
        dpt := 0;
      SaveRecord(
        [
          'id', 'dep_number','name','pc_fix_button',
          'rm_fix_button','id_food_group', 'pc_bitmap','pc_text',
          'rm_text','id_options_supplement', 'id_options_condiment',
          'max_supp_quantity','max_cond_quantity','hq_id','oman_text',
          'fastlane', 'id_age_groups',
          'hht_default_setting', 'hht_default_color',
          'hht_default_font',
          'oman_default_setting', 'oman_default_color',
          'oman_default_font',
          'exclude_art', 'dep_external_id', 'dep_type'
        ],
        [
          ObjectId, edtNumber.Value, def_name,0,
          0,usbGroup.Value, MS, PCButtonPropFram.edtPCText.text,
          HHTText, usbSupplement.value, usbCondiment.value,
          usSupMaxQty.value, usConMaxQty.value, edtHqId.Text,
          OManButtonPropFram.edtPCText.text,
          SmallInt(chkFastLane.Checked), usbAgeGroup.value,
          0, SmallInt(HHTButtonPropFram.IsDefaultColor),
          SmallInt(HHTButtonPropFram.IsDefaultFont),
          0, SmallInt(OManButtonPropFram.IsDefaultColor),
          SmallInt(OManButtonPropFram.IsDefaultFont),
          SmallInt(chkExcludeArt.Checked), edtExternalId.Text,
          dpt
        ]
      );
    finally
      MS.Free;
    end;
    PCButtonPropFram.SaveArtProps(ObjectId, DEP_TABLE_SETTINGS_NAME, DEP_FIELD_SETTINGS_NAME);
    HHTButtonPropFram.SaveArtProps(ObjectId, DEP_TABLE_SETTINGS_NAME, DEP_FIELD_SETTINGS_NAME);
    OManButtonPropFram.SaveArtProps(ObjectId, DEP_TABLE_SETTINGS_NAME, DEP_FIELD_SETTINGS_NAME);
    emDepAvail.SaveDataset;

    artList := TStringList.Create;
    try
      with emDepArticle.ClientDataset do begin
        first;
        while not eof do begin
          if UpdateStatus=usInserted then
            if artList.IndexOfName(FieldByName('id_articles').asString)<0 then
              artList.Values[FieldByName('id_articles').asString] := FieldByName('sequence').asString;
          next;
        end;
      end;
      emDepArticle.SaveDataset;
      emDepNormArticle.SaveDataset;
      if artList.count>0 then begin
        for I := 0 to Pred(artList.count) do begin
          InsertArticlePOS(UntillDB, StrToInt64Def(artList.Names[i],0), ObjectId, StrToIntDef(artList.Values[artList.Names[i]],0));
        end;
      end;
    finally
      FreeAndNil(artList);
    end;
    Caption:=STR_Department + ': ' + edtName.Text;

    result:=true;
  end;
end;

procedure TDepartmentEntityManager.SetbVoucher(const Value: boolean);
begin
  FbVoucher := Value;
end;

procedure TDepartmentEntityManager.TranslateStrings;
begin
  STR_Department := strDep;
  STR_Name := CommonStringsU.StrNameCaption;
  inherited;
end;

initialization
  ClassManager.RegisterClass(TDepartmentEntityManager);
end.
