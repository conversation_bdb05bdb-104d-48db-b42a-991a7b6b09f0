create table client_cards (
    id u_id,
    item_number int,
    ID_CLIENTS bigint,
    card_name 	varchar(50),
    card_number varchar(50),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint client_cards_pk primary key (id),
    constraint client_cards_fk1 foreign key (ID_CLIENTS) references CLIENTS(id)
);
commit;
grant all on client_cards to untilluser;
commit;
execute procedure register_sync_table_ex('client_cards', 'b', 1);
commit;
execute procedure register_bo_table('client_cards', 'ID_CLIENTS', 'CLIENTS');
commit;
