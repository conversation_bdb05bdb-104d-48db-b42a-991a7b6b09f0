set term !!;
create or alter procedure fix_price_numbers_20161006
as
declare variable cnt integer;
declare variable num integer;
declare variable id bigint;
begin
    SELECT count(*) from prices where not (number is null) into :cnt;
    if (cnt = 0) then begin
        num = 1;
        execute procedure set_logging_cs;
        for select id from prices order by id into :id do begin
            update prices set number=:num where id=:id;
            num = num + 1;
        end
        execute procedure set_logging_off;
    end
end
!!
commit
!!
grant execute on procedure fix_price_numbers_20161006 to untilluser
!!
commit
!!
set term ; !!

execute procedure fix_price_numbers_20161006;
commit;

drop procedure fix_price_numbers_20161006;
commit;

