unit PSP_JavaDriverU;

interface
uses PSPU, Classes, Windows, ProgressU, Generics.Collections, Messages, SysUtils, UBLProxyU,
  NamedVarsU, UBLU, PreAuthU, DevicesU, UntillDBU, KernelDriversU, DriverConstU;

const
  TRANSACTION_RESULT_DECLINED = 1;
  TRANSACTION_RESULT_CANCELLED = 2;
  TRANSACTION_RESULT_RECEIPT_RECOVERY_CODE = 3;
  EFT_READ_TIMEOUT = 90 * 1000;

  VOID_GIFT_SALE = 'GiftSale';

type

  EJavaDriverDeclined = class(EJavaDriverError);
  EJavaDriverReceiptRecovery = class(EJavaDriverError);
  TPrintHandlerProc = function(psp: TCustomPSP; msg: String): Boolean;


  TJavaDriverPSP = class(TCustomPSP)
  private
    fcancelled  : boolean;
    FDriverId: String;
    FDriverParams: IDriverParamsPersist;
    FManual: Boolean;
    FTransactionData: MapOfStringByString;
    FTips: Currency;
    FSettings: EFTSettings;
    FTransactionResult: Integer;
    FDeclinedTicketGUID: String;
    FConfigurationGuid: String;
    FEFTReceiptPrintMode: SmallInt;
    FProxyPort: Integer;
    FProxyAddress: String;
    FDisplayMessage: String;
    FCashout: Currency;
    FPrintHandler: TPrintHandlerProc;
    FTransactionId: String;
    FAllowPartialAuthorization: Boolean;

    procedure SetDriverId(const Value: String);
    procedure SetDriverParams(const Value: IDriverParamsPersist);

    procedure SetTips(const Value: Currency);
    procedure CheckSettings;
    function  CreateEmulateData(Names: array of string; Values: array of string): MapOfStringByString;
    procedure SetDeclinedTicketGUID(const Value: String);
    procedure SetConfigurationGuid(const Value: String);
    procedure SetEFTReceiptPrintMode(const Value: SmallInt);
    function  GetPspParamsMap: TStringList;
    procedure SetProxyAddress(const Value: String);
    procedure SetProxyPort(const Value: Integer);
    procedure SetDisplayMessage(const Value: String);
    procedure GiftValuesFromTransactionResponse(Response: EftResult);
    procedure SetCashout(const Value: Currency);
    procedure SetPrintHandler(const Value: TPrintHandlerProc);
    procedure SetTransactionId(const Value: String);
    procedure SetAllowPartialAuthorization(const Value: Boolean);
    function HandleNewLines(input: String): String;

//    procedure

  protected

    function    PrintMessageProc(Msg: String): Boolean;

    procedure   eftSale(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftRefund(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftVoid(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftTips(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftResetTip(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftPreAuth(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftCompletion(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftCustomCommand(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftGiftCardValidate(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress; reason: String);
    procedure   eftInputFromEFT(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);

    procedure   eftGiftGetCard(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
		{abstract}
    function    eftGiftTransaction(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress; Request: EftGiftCardTransactionRequest): EftResult;
    function    eftGiftModifyTransaction(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress; Request: EftGiftCardModifyTransactionRequest): EftResult;
		{/abstract}

    procedure   eftGiftIssueCard(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftGiftDeactivateCard(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftGiftReloadCard(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftGiftSale(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
    procedure   eftGiftCancel(uc, ucThread: TUBLConnection; params: DriverConfiguration; Drv: Driver; sp: IShowProgress; VoidWhat: String);

    procedure   EmulateResults; virtual;

    procedure   ProcessWithProgress(sp: IShowProgress);
    function    Process:Boolean; override;
    class function AvailForHandhelds: Boolean; override;

    function    EmulatePaymentData: MapOfStringByString;

  public

    function    isRequestSupported(r: TPSPRequestType): Boolean; override;

    procedure   SavePaymentData(Data: INamedVars; Options: TSaveDataOptions); override;
    procedure   LoadPaymentData(Data: INamedVars; ReopenTipsAmount: Currency); override;
    procedure   SaveTransactionData(Data: INamedVars); override;
    procedure   LoadTransactionData(PreAuth: TPreAuthTransaction); override;

    destructor  Destroy; override;
    procedure ClearTransactionData;

    function    TipsReplaced: Boolean; override;
    function    ReopenIncludingTips: Boolean; override;
    function    IsGiftCardValidationEnabled: Boolean;

    constructor Create(AOwner: TComponent); override;
    function 	ReopenPolicy: TPSPReopenPolicy; override;

    property  DriverId: String read FDriverId write SetDriverId;
    property  DriverParams: IDriverParamsPersist read FDriverParams write SetDriverParams;
    property  ConfigurationGuid: String read FConfigurationGuid write SetConfigurationGuid;
    property  DeclinedTicketGUID: String read FDeclinedTicketGUID write SetDeclinedTicketGUID;
    property  EFTReceiptPrintMode: SmallInt read FEFTReceiptPrintMode write SetEFTReceiptPrintMode;
    property  Tips: Currency read FTips write SetTips;
    property  TransactionId: String read FTransactionId write SetTransactionId;
    property  Cashout: Currency read FCashout write SetCashout;
    property  TransactionResult: Integer read FTransactionResult;

    property 	ProxyAddress: String read FProxyAddress write SetProxyAddress;
    property  ProxyPort: Integer read FProxyPort write SetProxyPort;
    property  TransactionData: MapOfStringByString read FTransactionData;
    property  DisplayMessage: String read FDisplayMessage write SetDisplayMessage;
    property  PrintHandler: TPrintHandlerProc read FPrintHandler write SetPrintHandler;

    property  AllowPartialAuthorization: Boolean read FAllowPartialAuthorization write SetAllowPartialAuthorization;

    function  IsAuthorizedPartially(out PartiallyAuthorizedAmount: Currency): Boolean;
    function  IsEftReceiptSkipped: Boolean;
    function  IsExtraReceiptLogged(out LoggedReceipt: String): Boolean;

  end;

implementation
uses PluginU, UntillAppU,
  UntillPluginU, DateUtils, CommonU, UntillPosU,
  POSAlgU, ShowProgressU, StrUtils, Forms, QualityEquipmentU, ThreadObserverU, COntrols,
  TFComPort, DeviceIntfU, UntillLogsU, XSBuiltIns, ActiveX,
  CommonStringsU, EftInputQueryPosFrm, FalconMsgFrm, OrdermanFrm, RegularExpressions;

class function TJavaDriverPSP.AvailForHandhelds: Boolean;
begin
  result := True;
end;

procedure TJavaDriverPSP.CheckSettings;
var
  uc: TUBLConnection;
begin

  if FSettings = nil then begin
    uc := GetUBL(upos.UntillDB);
    FSettings := uc.Soap.drivers_eftGetSettings(uc.SessionID, FDriverId);
    if assigned(FSettings) and (FSettings.waitTimeout=0) then
			FSettings.waitTimeout := EFT_READ_TIMEOUT;
  end;

  if not assigned(FSettings) then begin
  	FSettings := EftSettings.Create;
		FSettings.waitTimeout := EFT_READ_TIMEOUT;
  end;

end;

procedure TJavaDriverPSP.ClearTransactionData;
var e: MapEntryStringByString;
begin
	for e in FTransactionData do
  	e.Free;
  SetLength(FTransactionData, 0);
end;


constructor TJavaDriverPSP.Create(AOwner: TComponent);
begin
  inherited;
  FSettings := nil;
  FPrintHandler := nil;
  SetLength(FTransactionData, 0);
  FDriverParams := GetParamsPersist('');
end;

function TJavaDriverPSP.CreateEmulateData(Names,
  Values: array of string): MapOfStringByString;
var i: integer;
    vv: MapOfStringByString;
begin
  assert(Length(Names) = Length(Values));
  SetLength(vv, Length(Names));
  for i:=0 to Length(Names) - 1 do begin
    vv[i] := MapEntryStringByString.Create;
    vv[i].key := Names[i];
    vv[i].value := Values[i];
  end;
  result := vv;
end;

destructor TJavaDriverPSP.Destroy;
begin
	ClearTransactionData;
  FreeAndNil(FSettings);
  inherited;
end;

procedure TJavaDriverPSP.eftCompletion(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EFTCompletionRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EFTCompletionRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.data := CopyData(FTransactionData);
    request.amount := CurrencyToXSDecimal(Amount);
    if not Emulate then begin
      response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      ClearTransactionData;
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'Completion (Amt:%.f, Bill:%d, CC:%s)',
          [Amount, BillNumber, nv.ValByName[TEftFields.MASKED_CARD_NUMBER]]));

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
        ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);
    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftCustomCommand(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  response: EFTResult;
  nv: INamedVars;
  request: EftCustomCommandRequest;
  stateData: String;
begin
  request := EftCustomCommandRequest.Create;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.command := CustomCommand;
    request.waiterTerminalId := OperatorId;
    response := nil;
    if not Emulate then
      try
        response := EFTResult(ProcessInThread(
          function(ucc: TUBLConnection): DriverResult
          begin
            Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
          end,
          sp, request, uc, ucThread, PrintMessageProc));
      	ClearTransactionData;
        FTransactionData := CopyData(response.data);
        FTransactionResult := response.transactionResult;
        FDisplayMessage := HandleNewLines(response.displayMessage);
      finally
        FreeAndNil(response);
      end
    else begin // Emulate
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then begin
      	stateData := Format('CustomCommand (Cmd: %s)', [CustomCommand]);
        if PspParams<>'' then
        	stateData := stateData + ' EftParams: '+PspParams;
        PosAlg.WriteStateData('TJavaDriverPSP', stateData);
      end;

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
        ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);
    end;
  finally
    FreeAndNil(request);
  end;
end;

procedure TJavaDriverPSP.eftGiftCancel(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress;
  VoidWhat: String);
var
  request: EftGiftCardCancelRequest;
  response: EFTResult;
 	e: MapEntryStringByString;
begin
  request := EftGiftCardCancelRequest.Create;
  response := nil;
  try

  	SetLength(FTransactionData, Length(FTransactionData)+1);

    e := MapEntryStringByString.Create;
    e.key := 'Void';
    e.value := VoidWhat;
    FTransactionData[Length(FTransactionData)-1] := e;

  	response := eftGiftModifyTransaction(uc, ucThread, params, Drv, sp, request);
  finally
    FreeAndNil(request);
    if assigned(response) then
	    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftGiftCardValidate(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress; reason: String);
var
  request: EftGiftCardValidateRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EftGiftCardValidateRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.cardId := Self.GiftCardId;
    request.waiterTerminalId := OperatorId;
    request.reason := reason;

    if not Emulate then begin
      ClearTransactionData;
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'eftGiftCardValidate (Card: %s)',
          [GiftCardId]));

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.CURRENT_BALANCE], ['2055']);

    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftGiftDeactivateCard(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftGiftCardDeactivateRequest;
  response: EFTResult;
begin
  request := EftGiftCardDeactivateRequest.Create;
  response := nil;
  try
  	response := eftGiftTransaction(uc, ucThread, params, Drv, sp, request);
  finally
    FreeAndNil(request);
    if assigned(response) then
	    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftGiftGetCard(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftGiftCardReadRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EftGiftCardReadRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.cardId := Self.GiftCardId;
    request.waiterTerminalId := OperatorId;

    if not Emulate then begin
      ClearTransactionData;
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'eftGiftGetCard (Card: %s)',
          [GiftCardId]));

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.CURRENT_BALANCE], ['2055']);

    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftGiftIssueCard(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftGiftCardActivateRequest;
  response: EFTResult;
begin
  request := EftGiftCardActivateRequest.Create;
  response := nil;
  try
    request.amount := CurrencyToXSDecimal(Self.Amount);
    request.currencyCode := Currency.DigCode;
    request.currencyCharCode := Currency.CharCode;
  	response := eftGiftTransaction(uc, ucThread, params, Drv, sp, request);
  finally
    FreeAndNil(request);
    if assigned(response) then
	    FreeAndNil(response);
  end;
end;

function TJavaDriverPSP.eftGiftModifyTransaction(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress;
  Request: EftGiftCardModifyTransactionRequest): EftResult;
var
  input: String;
begin

  if GiftCardId = '' then begin
    if not Emulate then begin
      if not TEftInputQueryPosForm.Execute(StrScanOrTypeCode, input) then
        abort;
    end else
      input := '12345'; // emulated cardId
    GiftCardId := input;
  end;
{
  if GiftCardId = '' then begin
      if not TEftInputQueryPosForm.Execute(StrScanOrTypeCode, input) then
        abort;
    GiftCardId := input;
  end;
}
  request.guid := GetRequestGuid(Emulate);
  request.cardId := GiftCardId;
  request.posId := upos.GetPCPosId(False);
  request.data := CopyData(FTransactionData);
  request.waiterTerminalId := OperatorId;
  request.timestamp := DateTimeToXSDateTime(upos.GetPOSNow);

  ClearTransactionData;
  if not Emulate then begin
    result := EftResult(ProcessInThread(
      function(ucc: TUBLConnection): DriverResult
      begin
        Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
      end,
      sp, request, uc, ucThread, PrintMessageProc));
  end else begin
    result := EftResult.Create;
    result.transactionResult := 0;
    result.data := CreateEmulateData(
      [TEftFields.CURRENT_BALANCE],
      [IntToStr(Round((10 + Amount) * 100))]);

    if WriteStateData then
      PosAlg.WriteStateData('TJavaDriverPSP', Format(
        '%s (Card: %s, Amt:%.2f)',
        [Request.ClassName, GiftCardId, Amount]));
  end;
	GiftValuesFromTransactionResponse(Result);
end;

procedure TJavaDriverPSP.eftGiftReloadCard(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftGiftCardReloadRequest;
  response: EFTResult;
begin
  request := EftGiftCardReloadRequest.Create;
  response := nil;
  try
    request.amount := CurrencyToXSDecimal(Self.Amount);
    request.currencyCode := Currency.DigCode;
    request.currencyCharCode := Currency.CharCode;
  	response := eftGiftTransaction(uc, ucThread, params, Drv, sp, request);
  finally
    FreeAndNil(request);
    if assigned(response) then
	    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftGiftSale(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftGiftCardPaymentRequest;
  response: EFTResult;
begin
  request := EftGiftCardPaymentRequest.Create;
  response := nil;
  try
    request.amount := CurrencyToXSDecimal(Self.Amount);
    request.currencyCode := Currency.DigCode;
    request.currencyCharCode := Currency.CharCode;
  	response := eftGiftTransaction(uc, ucThread, params, Drv, sp, request);
  finally
    FreeAndNil(request);
    if assigned(response) then
	    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftPreAuth(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EFTPreAuthRequest;
  response: EFTResult;
begin
  request := EFTPreAuthRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.billNumber := BillNumber;
    request.amount := CurrencyToXSDecimal(Amount);
    request.orderId := OrderId;
    request.transactionId := FTransactionId;
    request.waiterTerminalId := OperatorId;
    request.currencyCode := Currency.DigCode;
    request.currencyCharCode := Currency.CharCode;
    request.manualEntry := FManual;
    request.timestamp := DateTimeToXSDateTime(upos.GetPOSNow);
    if not Emulate then begin
      response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      ClearTransactionData;
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin // Emulate
      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
        ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'PreAuth (Amt:%.f, Bill:%d)',
          [Amount, BillNumber]));
    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftRefund(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EFTReturnRequest;
  response: EFTResult;
begin
  request := EFTReturnRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.billNumber := BillNumber;
    request.amount := CurrencyToXSDecimal(Amount);
    request.orderId := OrderId;
    request.transactionId := FTransactionId;
    request.waiterTerminalId := OperatorId;
    request.currencyCode := Currency.DigCode;
    request.currencyCharCode := Currency.CharCode;
    request.manualEntry := FManual;
    request.timestamp := DateTimeToXSDateTime(upos.GetPOSNow);
    ClearTransactionData;
    if not Emulate then begin
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));

      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      FTransactionData := CreateEmulateData(
        [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
        ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'Return (Amt:%.f, Bill:%d)',
          [Amount, BillNumber]));
    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftResetTip(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EFTResetTipsRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EFTResetTipsRequest.Create;
  response := nil;
  if not Emulate then
    try
      request.guid := GetRequestGuid(Emulate);
    	request.posId := upos.GetPCPosId(False);
      request.data := CopyData(FTransactionData) ;
      response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      ClearTransactionData;
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    finally
      FreeAndNil(request);
      FreeAndNil(response);
    end
  else begin // Emulate

    nv := newNv;
    SavePaymentData(nv, TSaveDataOptions.Create(False, False));

    if WriteStateData then
      PosAlg.WriteStateData('TJavaDriverPSP', Format(
        'ResetTips (Amt:%.f, Bill:%d, CC:%s)',
        [Amount, BillNumber, nv.ValByName[TEftFields.MASKED_CARD_NUMBER]]));

    ClearTransactionData;
    FTransactionData := CreateEmulateData(
      [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
      ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);
  end;
end;

procedure TJavaDriverPSP.eftSale(uc, ucThread: TUBLConnection; params: DriverConfiguration;
		Drv: Driver; sp: IShowProgress);
var
  paymentRequest: EFTPaymentRequest;
  response: EFTResult;
  stateData: String;
begin
  response := nil;
  paymentRequest := EFTPaymentRequest.Create;
  try
    paymentRequest.guid := GetRequestGuid(Emulate);
    paymentRequest.posId := upos.GetPCPosId(False);
    paymentRequest.vat := CurrencyToXSDecimal(Vat);
    paymentRequest.cashout := CurrencyToXSDecimal(Cashout);
    paymentRequest.billNumber := BillNumber;
    paymentRequest.amount := CurrencyToXSDecimal(Amount);
    paymentRequest.orderId := OrderId;
    paymentRequest.transactionId := FTransactionId;
    paymentRequest.waiterTerminalId := OperatorId;
    paymentRequest.currencyCode := Currency.DigCode;
    paymentRequest.currencyCharCode := Currency.CharCode;
    paymentRequest.manualEntry := FManual;
    paymentRequest.timestamp := DateTimeToXSDateTime(upos.GetPOSNow);
    paymentRequest.partialAuthAllowed := FAllowPartialAuthorization;
    ClearTransactionData;
    if not Emulate then begin
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, paymentRequest);
        end,
        sp, paymentRequest, uc, ucThread, PrintMessageProc));

      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin // Emulate
    	FTransactionData := EmulatePaymentData;
      FTransactionResult := 0;
      if WriteStateData then begin
      	stateData := Format('Sale (Amt:%.f, Bill:%d)', [Amount, BillNumber]);
        if PspParams<>'' then
        	stateData := stateData + ' EftParams: '+PspParams;
        PosAlg.WriteStateData('TJavaDriverPSP', stateData);
      end;
    end;
  finally
    FreeAndNil(paymentRequest);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftTips(uc, ucThread: TUBLConnection; params: DriverConfiguration;
  Drv: Driver; sp: IShowProgress);
var
  request: EFTTipsRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EFTTipsRequest.Create;
  response := nil;
  if not Emulate then
    try
      request.guid := GetRequestGuid(Emulate);
    	request.posId := upos.GetPCPosId(False);
      request.data := CopyData(FTransactionData) ;
      request.tips := CurrencyToXSDecimal(Tips);
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));

      ClearTransactionData;
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    finally
      FreeAndNil(request);
      FreeAndNil(response);
    end
  else begin // Emulate

    nv := newNv;
    SavePaymentData(nv, TSaveDataOptions.Create(False, False));

    if WriteStateData then
      PosAlg.WriteStateData('TJavaDriverPSP', Format(
        'Tips (Amt:%.f, Bill:%d, CC:%s)',
        [Amount, BillNumber, nv.ValByName[TEftFields.MASKED_CARD_NUMBER]]));

    ClearTransactionData;
    FTransactionData := CreateEmulateData(
      [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
      ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);
  end;
end;

function TJavaDriverPSP.eftGiftTransaction(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress;
  Request: EftGiftCardTransactionRequest): EftResult;
var
  input: String;
  stateData: String;
begin
  if (GiftCardId = '') and not(Request is EftGiftCardActivateRequest) then begin
    if not Emulate then begin
      if not TEftInputQueryPosForm.Execute(StrScanOrTypeCode, input) then
        abort;
    end else
      input := '12345'; // emulated cardId
    GiftCardId := input;
  end;
{
  if (GiftCardId = '') and not(Request is EftGiftCardActivateRequest) then begin
      if not TEftInputQueryPosForm.Execute(StrScanOrTypeCode, input) then
        abort;
    GiftCardId := input;
  end;
}
  request.guid := GetRequestGuid(Emulate);
  request.posId := upos.GetPCPosId(False);
  request.cardId := Self.GiftCardId;
  request.operationId := Self.OrderId;
  request.waiterTerminalId := OperatorId;
  request.timestamp := DateTimeToXSDateTime(upos.GetPOSNow);

  ClearTransactionData;
  if not Emulate then begin
    result := EftResult(ProcessInThread(
      function(ucc: TUBLConnection): DriverResult
      begin
        Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
      end,
      sp, request, uc, ucThread, PrintMessageProc));
  end else begin
    result := EftResult.Create;

    if Amount = 971.01 then  begin
      result.data := CreateEmulateData([TEftFields.CARD_NAME, TEftFields.EFT_PARTIALLY_AUTHORIZED_AMOUNT], ['Visa', '80010']);
      result.transactionResult := 0 ;
    end else if Amount = 411.03 then begin
      result.data:= CreateEmulateData([TEftFields.CARD_NAME], ['Visa']);
      result.transactionResult := 0 ;
    end else if Amount = 412.01 then begin
      result.data := CreateEmulateData([TEftFields.CARD_NAME, 'abc'], ['MC 1', 'xy1z']);
      result.transactionResult := 0;
    end else if Self.Amount < 10 then begin
      result.transactionResult := 0;
      result.data := CreateEmulateData(
        [TEftFields.CURRENT_BALANCE],
        [IntToStr(Round((10 + Amount) * 100))]);
    end else begin
      result.transactionResult := TRANSACTION_RESULT_DECLINED;
      result.data := CreateEmulateData(
        [TEftFields.CURRENT_BALANCE],
        [IntToStr(Round((10 + Amount) * 100))]);
    end;

    if WriteStateData then begin
      stateData := Format('%s (Card: %s, Amt:%.2f)', [Request.ClassName, GiftCardId, Amount]);
      if PspParams<>'' then
        stateData := stateData + ' EftParams: '+PspParams;
      PosAlg.WriteStateData('TJavaDriverPSP', stateData);
    end;
  end;

	GiftValuesFromTransactionResponse(Result);
end;

procedure TJavaDriverPSP.eftInputFromEFT(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EftReadInputRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EftReadInputRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.waiterTerminalId := OperatorId;

    if not Emulate then begin
      ClearTransactionData;
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', 'eftInputFromEFT');

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.EFT_INPUT_DATA], ['303']);

    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

procedure TJavaDriverPSP.eftVoid(uc, ucThread: TUBLConnection;
  params: DriverConfiguration; Drv: Driver; sp: IShowProgress);
var
  request: EFTVoidSaleRequest;
  response: EFTResult;
  nv: INamedVars;
begin
  request := EFTVoidSaleRequest.Create;
  response := nil;
  try
    request.guid := GetRequestGuid(Emulate);
    request.posId := upos.GetPCPosId(False);
    request.data := CopyData(FTransactionData);
    request.waiterTerminalId := OperatorId;

    if not Emulate then begin
      ClearTransactionData;
      Response := EFTResult(ProcessInThread(
        function(ucc: TUBLConnection): DriverResult
        begin
          Result := ucc.Soap.drivers_eftOperation(ucc.SessionID, drv.driverId, params, request);
        end,
        sp, request, uc, ucThread, PrintMessageProc));
      FTransactionData := CopyData(response.data);
      FTransactionResult := response.transactionResult;
      FDisplayMessage := HandleNewLines(response.displayMessage);
    end else begin
      nv := newNv;
      SavePaymentData(nv, TSaveDataOptions.Create(False, False));

      if WriteStateData then
        PosAlg.WriteStateData('TJavaDriverPSP', Format(
          'Void (Amt:%.f, Bill:%d, CC:%s)',
          [Amount, BillNumber, nv.ValByName[TEftFields.MASKED_CARD_NUMBER]]));

      ClearTransactionData;
      FTransactionData := CreateEmulateData(
        [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode'],
        ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber)]);

    end;
  finally
    FreeAndNil(request);
    FreeAndNil(response);
  end;
end;

function TJavaDriverPSP.EmulatePaymentData: MapOfStringByString;
begin
  if Amount = 971.01 then begin
    result := CreateEmulateData([TEftFields.CARD_NAME, TEftFields.EFT_PARTIALLY_AUTHORIZED_AMOUNT], ['Visa', '80010'])
  end else if Amount = 8.11 then begin
    result := CreateEmulateData([TEftFields.CARD_NAME, TEftFields.EFT_PARTIALLY_AUTHORIZED_AMOUNT], ['Visa', '700'])
  end else if Amount = 411.03 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Visa'])
	else if Amount = 411.04 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Visa One'])
	else if Amount = 411.05 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Visa Two'])
	else if Amount = 411.06 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Visa Three'])
	else if Amount = 411.06 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Visa Three'])
	else if Amount = 412.01 then
    result := CreateEmulateData([TEftFields.CARD_NAME, 'abc'], ['MC 1', 'xy1z'])
	else if Amount = 412.02 then
    result := CreateEmulateData([TEftFields.CARD_NAME, 'abc'], ['MC 1', 'xy12z'])
	else if Amount = 413.00 then
    result := CreateEmulateData([TEftFields.CARD_NAME], ['Mastercard'])
	else if Amount = 410.00 then
    result := CreateEmulateData([TEftFields.EFT_FORMATTED_JOURNAL_RECEIPT], ['This is a formatted EFT'#13#10'Receipt!'])
  else
    result := CreateEmulateData(
      [TEftFields.MASKED_CARD_NUMBER, TEftFields.EXPIRE, 'ResultCode', TEftFields.EFT_ADDED_TIPS],
      ['*** 1234', '1213', 'EmuRes:'+IntToStr(BillNumber), '345'])
end;

procedure TJavaDriverPSP.EmulateResults;
begin

end;

function TJavaDriverPSP.GetPspParamsMap: TStringList;
begin
  result := TStringList.Create;
  StrSplitEx(PspParams, result, ';');
end;

procedure TJavaDriverPSP.GiftValuesFromTransactionResponse(
  Response: EftResult);
begin
  FTransactionData := CopyData(Response.data);
  FTransactionResult := Response.transactionResult;
  FDisplayMessage := Response.displayMessage;
end;

function TJavaDriverPSP.HandleNewLines(input: String): String;
var regexp: TRegEx;
begin
  result := input;
  if Length(result) > 0 then begin
    regexp:=TRegEx.Create('(?<!\r)\n');
    result := regexp.Replace(input, #13#10)
  end;
end;

function TJavaDriverPSP.IsAuthorizedPartially(
  out PartiallyAuthorizedAmount: Currency): Boolean;
var e: MapEntryStringByString;
begin
  result := false;
  PartiallyAuthorizedAmount := 0;
  for e in FTransactionData do begin
    if e.key = TEftFields.EFT_PARTIALLY_AUTHORIZED_AMOUNT then begin
      result := true;
      PartiallyAuthorizedAmount := StrToIntDef(e.Value, 0) / 100;
    end;
  end;
end;

function TJavaDriverPSP.IsEftReceiptSkipped: Boolean;
var e: MapEntryStringByString;
begin
  result := false;
  for e in FTransactionData do begin
    if e.key = TEftFields.EFT_NO_RECEIPT then begin
      result := true;
    end;
  end;
end;

function TJavaDriverPSP.IsExtraReceiptLogged(
  out LoggedReceipt: String): Boolean;
var e: MapEntryStringByString;
begin
  result := false;
  LoggedReceipt := '';
  for e in FTransactionData do begin
    if e.key = TEftFields.EFT_FORMATTED_JOURNAL_RECEIPT then begin
      result := true;
      LoggedReceipt := e.Value;
    end;
  end;
end;

function TJavaDriverPSP.IsGiftCardValidationEnabled: Boolean;
begin
  if Emulate then
    result := true
  else begin
    CheckSettings;
    result := FSettings.eftGiftCardsValidationEnabled;
  end;
end;

function TJavaDriverPSP.isRequestSupported(r: TPSPRequestType): Boolean;
begin
  result := true;  // Unknown until there is a try to execute driver method
  if (r = pspGiftCardValidateForReload) or (r = pspGiftCardValidateForIssue) then begin
    // drivers supporting gift card vaidation
    result := SameText(self.DriverId, 'com.untill.drivers.piggygiftcards.PiggyGiftcardsDriver') or IsGiftCardValidationEnabled;
  end;
end;


procedure TJavaDriverPSP.LoadPaymentData(Data: INamedVars;
  ReopenTipsAmount: Currency);
var nvMisc: INamedVars;
    nvAllFields: INamedVars;
    key: String;
    i, j: integer;
begin
  inherited;
  nvAllFields := newnv;
  for i:=0 to Data.Count-1 do begin
    key := Data.Name[i];
    if PspIsStandardField(key) then
      nvAllFields.Add(key, Data.ValByName[key])
    else if SameText(key, TEftFields.MISC_DATA) then begin
      nvMisc := newnv;
      StringToNamedVars(AnsiString(Data.ValByName[key]), nvMisc);
      for j := 0 to nvMisc.Count-1 do
        nvAllFields.Add(nvMisc.Name[j], nvMisc.Val[j]);
    end;
  end;

  ClearTransactionData;

  SetLength(FTransactionData, nvAllFields.Count);
  for i:=0 to nvAllFields.Count-1 do begin
    FTransactionData[i] := MapEntryStringByString.Create;
    FTransactionData[i].key := nvAllFields.Name[i];
    FTransactionData[i].value := nvAllFields.Val[i];
  end;

end;

procedure TJavaDriverPSP.LoadTransactionData(PreAuth: TPreAuthTransaction);
begin
  inherited;
  LoadPaymentData(PreAuth.CardInfo, 0);
end;

function TJavaDriverPSP.PrintMessageProc(Msg: String): Boolean;
begin
  if assigned(FPrintHandler) then
    result := FPrintHandler(Self, Msg)
  else
    result := False;
end;

function TJavaDriverPSP.Process: Boolean;
var
  params: TStringList;
begin
  fCancelled := True;
//  assert((RunMode = armNormal) or (RunMode = armPos));
  params := GetPspParamsMap();
  try
    if params.IndexOfName('manual') > -1 then
      FManual := True
    else
      FManual := False;
  finally
    FreeAndNil(params);
  end;

  if (Self.Amount < 0) and (RequestType = pspPayment) then begin
    Self.Amount := Abs(Self.Amount);
    FRequestType := pspRefund;
  end;

  if (Emulate)and(TracePSPRequest) then begin
    BillNumber:=Round(Amount); // For test purposes
  end;

  CheckSettings;
  CancelableProcWithProgress(ProcessWithProgress, FSettings.cancellingByWaiterSupported, StrPleaseWait);
  result:=not fcancelled;
end;

procedure TJavaDriverPSP.ProcessWithProgress(sp: IShowProgress);
var uc, ucThread: TUBLConnection;
    d: Driver;
    params: DriverConfiguration;
    Extra: TStringList;
    declinedMsg: String;
begin
  FTransactionResult := 0;
  FDisplayMessage := '';

  d := Driver.Create;

  Extra := GetPspParamsMap();
  try
	  params := FDriverParams.AsConfiguration(upos.UntillDB, FConfigurationGuid, Extra);
  finally
  	FreeAndNil(Extra);
  end;

  if ProxyAddress<>'' then begin
    uc := CreateUBL(upos.UntillDB, ProxyAddress, ProxyPort);
    ucThread := CreateUBL(upos.UntillDB, ProxyAddress, ProxyPort);
  end else begin
    uc := GetUBL(upos.UntillDB);
    ucThread := CreateUBL(upos.UntillDB);
  end;
	ucThread.SetReadTimeout(FSettings.waitTimeout);

  try
    d.driverKind := DRIVER_KIND_JAVA;
    d.driverId := FDriverId;

    try
      case FRequestType of
        pspPayment: eftSale(uc, ucThread, params, d, sp);
        pspRefund: eftRefund(uc, ucThread, params, d, sp);
        pspTipHandle: eftTips(uc, ucThread, params, d, sp);
        pspVoid: eftVoid(uc, ucThread, params, d, sp);
        pspResetTip: eftResetTip(uc, ucThread, params, d, sp);
        pspPreAuth: eftPreAuth(uc, ucThread, params, d, sp);
        pspCompletion: eftCompletion(uc, ucThread, params, d, sp);
        pspCustomCommand: eftCustomCommand(uc, ucThread, params, d, sp);
        pspGiftBalance: eftGiftGetCard(uc, ucThread, params, d, sp);
        pspGiftIssue: eftGiftIssueCard(uc, ucThread, params, d, sp);
        pspGiftDeactivate: eftGiftDeactivateCard(uc, ucThread, params, d, sp);
        pspGiftReload: eftGiftReloadCard(uc, ucThread, params, d, sp);
        pspGiftSale: eftGiftSale(uc, ucThread, params, d, sp);
        pspGiftVoidIssue: eftGiftCancel(uc, ucThread, params, d, sp, 'GiftActivate');
        pspGiftVoidReload: eftGiftCancel(uc, ucThread, params, d, sp, 'GiftReload');
        pspGiftVoidDeactivate: eftGiftCancel(uc, ucThread, params, d, sp, 'GiftDeactivate');
        pspGiftVoidSale: eftGiftCancel(uc, ucThread, params, d, sp, VOID_GIFT_SALE);
        pspGiftCardValidateForIssue: eftGiftCardValidate(uc, ucThread, params, d, sp, 'issue');
        pspGiftCardValidateForReload: eftGiftCardValidate(uc, ucThread, params, d, sp, 'reload');
        pspInputFromEft: eftInputFromEFT(uc, ucThread, params, d, sp);
  {      pspMainMenu: ;
        pspUnknown: ;
        pspDeposit: ;
        pspRegisterLoyalty: ;
        pspGiftGetStripe: ;
        pspGiftSale: ;
        pspGiftVoidSale: ;
        pspGiftReturn: ;
        pspGiftTipHandle: ;    }
        else
          Plugin.RaiseExceptionNoTranslate('Request not supported by TJavaDriverPSP');
      end;

      if FTransactionResult = TRANSACTION_RESULT_CANCELLED then
        abort;

      if FTransactionResult = TRANSACTION_RESULT_DECLINED then begin
        declinedMsg := 'Declined';
        if FDisplayMessage <> '' then
          declinedMsg := FDisplayMessage;
        raise EJavaDriverDeclined.Create(declinedMsg);
      end;

      if FTransactionResult = TRANSACTION_RESULT_RECEIPT_RECOVERY_CODE then
        raise EJavaDriverReceiptRecovery.Create('Recovery');

      fcancelled := false;
    except
      on e: EAbort do begin
        SetLastError(Plugin.TranslatePos('Common', 'Operation cancelled'));
      end;
      on e0: EJavaCallFailed do begin
        SetLastError(e0.GetDisplayError);
      end;
      on e: Exception do begin
        if e.Message <> '' then
          SetLastError(e.Message)
        else
          SetLastError(e.Classname)
      end;
    end;
  finally
  	if ProxyAddress<>'' then
      FreeAndNil(uc);
    FreeAndNil(ucThread);
    FreeAndNil(d);
    FreeAndNil(params);
  end;
end;

function TJavaDriverPSP.ReopenIncludingTips: Boolean;
begin
  CheckSettings;
  result := FSettings.tipsIncludedInReturn;
end;


function TJavaDriverPSP.ReopenPolicy: TPSPReopenPolicy;
var Policy: String;
begin
  CheckSettings;
  Policy := Trim(FSettings.reopenPolicy);
  if SameText(Policy, 'return-funds') then
  	result := ReturnFunds
  else if SameText(Policy, 'void-sale') then
  	result := VoidSale
  else if SameText(Policy, 'void-within-same-day') then
  	result := VoidSameDay
  else if FSettings.voidWhenSameDay then
  	result := VoidSameDay
  else
  	result := ReturnFunds;
end;

procedure TJavaDriverPSP.SavePaymentData(Data: INamedVars;
  Options: TSaveDataOptions);
var
  map: MapOfStringByString;
  pair: MapEntryStringByString;
  nvMisc: INamedVars;
  i: integer;
begin
  inherited;
  nvMisc := newNv;
  map := FTransactionData;

  for pair in map do begin
    if PspIsStandardField(pair.key) then begin
      if Data.GetIndexOf(pair.key)<0 then
        Data.Add(pair.key, pair.value)
    end else
      nvMisc.Add(pair.Key, pair.value);

    if SameText(pair.Key, TEftFields.EFT_ADDED_TIPS) then
      Self.EftAddedTips := StrToIntDef(pair.value, 0) / 100;
  end;

  if Self.GiftCardId<>'' then
    nvMisc.ValByName[TEftFields.GIFT_CARD_NUMBER] := Self.GiftCardId;

  if nvMisc.Count>0 then begin
  	if Options.CombineMiscData then
      Data.Add(TEftFields.MISC_DATA, NamedVarsToString(nvMisc))
    else
    	for i:=0 to nvMisc.Count-1 do
	    	Data.Add(nvMisc.Name[i], nvMisc.Val[i]);
  end;
end;


procedure TJavaDriverPSP.SaveTransactionData(Data: INamedVars);
begin
  inherited;
  SavePaymentData(Data, TSaveDataOptions.Create(False, False));
end;

procedure TJavaDriverPSP.SetAllowPartialAuthorization(const Value: Boolean);
begin
  FAllowPartialAuthorization := Value;
end;

procedure TJavaDriverPSP.SetCashout(const Value: Currency);
begin
  FCashout := Value;
end;

procedure TJavaDriverPSP.SetConfigurationGuid(const Value: String);
begin
  FConfigurationGuid := Value;
end;

procedure TJavaDriverPSP.SetDeclinedTicketGUID(const Value: String);
begin
  FDeclinedTicketGUID := Value;
end;

procedure TJavaDriverPSP.SetDisplayMessage(const Value: String);
begin
  FDisplayMessage := Value;
end;

procedure TJavaDriverPSP.SetDriverId(const Value: String);
begin
  FDriverId := Value;
end;

procedure TJavaDriverPSP.SetDriverParams(const Value: IDriverParamsPersist);
begin
  FDriverParams := Value;
end;

procedure TJavaDriverPSP.SetEFTReceiptPrintMode(const Value: SmallInt);
begin
  FEFTReceiptPrintMode := Value;
end;

procedure TJavaDriverPSP.SetPrintHandler(const Value: TPrintHandlerProc);
begin
  FPrintHandler := Value;
end;

procedure TJavaDriverPSP.SetProxyAddress(const Value: String);
begin
  FProxyAddress := Value;
end;

procedure TJavaDriverPSP.SetProxyPort(const Value: Integer);
begin
  FProxyPort := Value;
end;

procedure TJavaDriverPSP.SetTips(const Value: Currency);
begin
  FTips := Value;
end;

procedure TJavaDriverPSP.SetTransactionId(const Value: String);
begin
  FTransactionId := Value;
end;

function TJavaDriverPSP.TipsReplaced: Boolean;
begin
  CheckSettings;
  result := FSettings.tipsReplaced;
end;

initialization

end.
