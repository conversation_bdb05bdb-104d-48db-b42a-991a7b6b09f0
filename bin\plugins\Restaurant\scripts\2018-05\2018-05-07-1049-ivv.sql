create table rent_periods_articles (
    id u_id,
    id_articles bigint,
    price decimal(17,4),
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint rent_pp_pk primary key (id),
	constraint rent_pp_fk2 foreign key (id_articles) references articles(id)
);
commit;
grant all on rent_periods_articles to untilluser;
commit;
execute procedure register_sync_table_ex('rent_periods_articles', 'b', 1);
commit;
execute procedure register_bo_table('rent_periods_articles', 'id_articles', 'articles');
commit;




