create table articles_ks_wf_item (
    id u_id,
    id_articles   bigint,   
    id_ks_wf_template_item  bigint,   
    prep_time_min integer,
    prep_time_sec integer,
    warning_min   integer,
    is_active 		   smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint akswfi_pk  primary key (id),
    constraint akswfi_fk1 foreign key (id_articles) references articles(id),
    constraint akswfi_fk2 foreign key (id_ks_wf_template_item) references ks_wf_template_item(id)
);
commit;
grant all on articles_ks_wf_item to untilluser;
commit;
execute procedure register_sync_table_ex('articles_ks_wf_item', 'b', 1);
commit;
execute procedure register_bo_table('articles_ks_wf_item', 'id_articles', 'articles');
commit;
execute procedure register_bo_table('articles_ks_wf_item', 'id_ks_wf_template_item', 'ks_wf_template_item');
commit;                                                                                            


