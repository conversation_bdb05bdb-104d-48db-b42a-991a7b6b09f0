set term !! ;
create or alter procedure BEVE<PERSON>GE_PREPROCESS_ORDERITEM (
    ID_ORDER_ITEM bigint,
    ID_ORDERS bigint,
    QUANTITY integer,
    ID_MENU bigint,
    ID_ARTICLES bigint)
as
declare variable FUTURE_ORDER integer;
declare variable TRANSFER_FROM integer;
declare variable MA bigint;
declare variable MQ bigint;
begin
  if (not(:ID_ORDER_ITEM is null)) then
  begin
    -- skip if future order
    select count(*)
    from DELAY_BILLS DB, BILL, ORDERS
    where DB.ID_BILL = BILL.ID and
          BILL.ID = ORDERS.ID_BILL and
          ORDERS.ID = :ID_ORDERS
    into :FUTURE_ORDER;

    select count(*)
    from NEG_ORDERS NOR
    where NOR.ID_ORDERS = :ID_ORDERS and
          NOR.REASON = 2
    into :TRANSFER_FROM;

    if ((:FUTURE_ORDER = 0) and
        (:TRANSFER_FROM = 0)) then
    begin
      execute procedure BEVERAGE_PROCESS_ORDERITEM(:ID_ARTICLES, :ID_ORDERS, :QUANTITY);
      if (:ID_MENU > 0) then
      begin
        for select MI.ID_ARTICLES, MI.QUANTITY * :QUANTITY
            from MENU_ITEM MI
            where MI.ID_MENU = :ID_MENU
            into :MA, :MQ
        do
        begin
          execute procedure BEVERAGE_PROCESS_ORDERITEM(:MA, :ID_ORDERS, :MQ);
        end
      end
    end
  end
end
!!
commit
!!
GRANT EXECUTE ON PROCEDURE BEVERAGE_PREPROCESS_ORDERITEM TO UNTILLUSER
!!
commit
!!
set term ; !!
