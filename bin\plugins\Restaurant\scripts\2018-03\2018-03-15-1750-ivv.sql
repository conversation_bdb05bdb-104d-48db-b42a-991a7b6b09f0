SET TERM !! ;

create or alter procedure GET_TPAPI_TRANSACTION_ORDERS (
    aID_BILL bigint,
    aDATE_FROM timestamp,
    aDATE_TO timestamp,
    aid_sales_area bigint,
    apaid smallint)
returns (
    id_orders bigint,
    ord_datetime timestamp, 
    pcname varchar(50),
    number varchar(50),
    id_untill_users bigint,
    id_sales_area bigint)
as
declare variable sql_str varchar(1024);
begin

  sql_str = ' select orders.id, orders.ord_datetime, orders.pcname, orders.number || coalesce(orders.suffix,''''), orders.id_untill_users, orders.id_sales_area';
  sql_str = sql_str || ' from orders';
  if (apaid=1) then begin
    sql_str = sql_str || ' join bill on bill.id=orders.id_bill';
    sql_str = sql_str || ' where bill.close_datetime between :d1 and :d2 and orders.id_bill=:id_bill';
  end else
    sql_str = sql_str || ' where orders.ord_datetime between :d1 and :d2 and orders.id_bill=:id_bill';
  sql_str = sql_str || ' union all   ';
  sql_str = sql_str || ' select pbill.id, pbill.pdatetime, pbill.pcname, pbill.number || coalesce(pbill.suffix,''''), pbill.id_untill_users, pbill.id_sales_area';
  sql_str = sql_str || ' from pbill';
  sql_str = sql_str || ' join PBILL_RETURN on PBILL_RETURN.id_pbill=pbill.id';
  sql_str = sql_str || ' where pbill.pdatetime between :d1 and :d2 and pbill.id_bill=:id_bill';
  for
    execute statement (sql_str) (id_bill:=aID_BILL, d1:=aDATE_FROM, d2:=aDATE_TO)
        into :id_orders, :ord_datetime, :pcname, :number, :id_untill_users, :id_sales_area
  do
    suspend;
end
!!
commit
!!

grant execute on procedure GET_TPAPI_TRANSACTION_ORDERS to untilluser!!

SET TERM ; !!
