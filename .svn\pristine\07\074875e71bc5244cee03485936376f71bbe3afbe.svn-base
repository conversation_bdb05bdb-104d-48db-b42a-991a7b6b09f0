[Start]
**************************************************
TimeStamp=02.04.2010 16:13
Interval=0.000735856483515818;30.12.1899 0:01:03
[Class]
Name=TBLMessageKeyboardString
TimeStamp=02.04.2010 16:13
Interval=1.06597217381932E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=1
s=1
[Class]
Name=TBLRMsgTableNo
TimeStamp=02.04.2010 16:13
Interval=2.27893469855189E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=4
IsDelay=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLRMsgTableNo
TimeStamp=02.04.2010 16:13
Interval=0;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=4
IsDelay=False
SalesArea=5000000081
TableName=
tableno=222
[Class]
Name=TBLMessageInput
TimeStamp=02.04.2010 16:13
Interval=1.71874999068677E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
s=2
[Class]
Name=TBLRMsgDepartmentMessage
TimeStamp=02.04.2010 16:13
Interval=1.48263934534043E-5;30.12.1899 0:00:01
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=1
id_department=5000000121
[Class]
Name=TBLRMsgArticle
TimeStamp=02.04.2010 16:13
Interval=1.1030089808628E-5;30.12.1899
Exception=
StateGUID={D93FCC82-9C23-46D9-968A-98C881665D03}
StateName=TBLREditOrder
FieldCount=3
articleType=atNormal
id=25000077496
start_time=0;30.12.1899
[Class]
Name=TBLRMsgNeedReturn
TimeStamp=02.04.2010 16:13
Interval=1.6817131836433E-5;30.12.1899 0:00:01
Exception=
StateGUID={8B979186-7648-4631-B7D3-0EFB30ADBE5D}
StateName=TBLRRefund
FieldCount=1
NeedAllowReturn=False
[Class]
Name=TBLRMsgPaymentMode
TimeStamp=02.04.2010 16:13
Interval=1.03124984889291E-5;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
StateData=
>>>Pos ticket
Printer=Bar Printer: Ticket=Bill
                             BILL                                               
Table: 222 a   Waiter:   Peter 02.02.2004 11:45                                 
Count   Article           Price    Single VAT     N.VAT   VAT%  Purch   Profi   
-------------------------------------------------                               
 -2     New promo          -23,00   -11,50  -3,17  -19,83 16,0%         -19,83  
---------Payment----------                                                      
Mode        Amoun       Cust amoun  Return amo                                  
Cash        -23,00      -23,00      0,00                                        
                              END OF BILL                                       

<<<Pos ticket
<<<<<EOSD
FieldCount=2
id_payments=5000001501
paymentname=Cash
[Class]
Name=TBLSMsgNeedReports
TimeStamp=02.04.2010 16:13
Interval=8.85416375240311E-6;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=0
[Class]
Name=TBLSMsgNewFromDateTime
TimeStamp=02.04.2010 16:13
Interval=3.47222230629995E-5;30.12.1899 0:00:03
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewFromDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgNewTillDateTime
TimeStamp=02.04.2010 16:13
Interval=0;30.12.1899
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
FieldCount=1
NewTillDateTime=38019.4898148148;02.02.2004 11:45:20
[Class]
Name=TBLSMsgReport
TimeStamp=02.04.2010 16:13
Interval=2.47800926445052E-5;30.12.1899 0:00:02
Exception=
StateGUID={8FA79B08-3475-417D-8404-BBA9C483D7E1}
StateName=TBLSGetReportDestinationPrinter
FieldCount=2
Preview=False
ReportId=15000038423
[Class]
Name=TBLSMsgPrinter
TimeStamp=02.04.2010 16:13
Interval=2.07986158784479E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
StateData=
>>>Pos report
Printer=Bar Printer: Ticket=Sold_Articles_SA_PC
                       REPORT : ALL PAID ARTICLES WITH SALES AREA AND PC NAME                       
------------------------------------------------                                                    
For user:      All users                                                                            
From:          02.02.2004  11:45:20                                                                 
Till:          02.02.2004  11:45:20                                                                 
------------------------------------------------                                                    
Printed by:    Peter        02.02.2004 11:45                                                        
User:      Peter                                                                                    
  Department: Fastrunners                                                                           
    Category:   Bar                                                                                 
 Article       Date/Time        Quan     Price     PrName    VAT     Sales area    PC name          
 Vodka         02.02.2004 11:45 -2       -13,00    Normaal   -2,26   Taverne       test             
 Vodka Options 02.02.2004 11:45 -2       -10,00    Normaal   -0,91   Taverne       test             
  Department: Warme Dranken                                                                         
    Category:   Bar                                                                                 
 Article       Date/Time        Quan     Price     PrName    VAT     Sales area    PC name          
 New promo     02.02.2004 11:45 -2                 Normaal           Taverne       test             
-------------------------------------------------------------------------------------------------   
 Total:                                 -23,00             -3,17                                    
 Total in USD: -29,90                                                                               
=================End of report==================                                                    

<<<Pos report
<<<<<EOSD
FieldCount=1
PrinterId=5000000060
[Class]
Name=TBLSMsgReport
TimeStamp=02.04.2010 16:13
Interval=4.51388768851757E-6;30.12.1899
Exception=
StateGUID={8FA79B08-3475-417D-8404-BBA9C483D7E1}
StateName=TBLSGetReportDestinationPrinter
FieldCount=2
Preview=False
ReportId=15000038541
[Class]
Name=TBLSMsgPrinter
TimeStamp=02.04.2010 16:13
Interval=1.98958296095952E-5;30.12.1899 0:00:01
Exception=
StateGUID={CECB0C71-D4F9-4E9C-B163-6D222A18AB09}
StateName=TBLSGenerateReport
StateData=
>>>Pos report
Printer=Bar Printer: Ticket=Returned articles
Returned article 2      
------------------------------------------------
For user:      All users                        
From:          02.02.2004  11:45:20             
Till:          02.02.2004  11:45:20             
------------------------------------------------
Printed by:    Peter                            
               02.02.2004 11:45:20              
================Start of report=================
Article            Quantity         Price       
New promo          2                            
Vodka              2                13,00       
Vodka Opti         2                10,00       
    Total:                        23,00         
=================End of report==================

<<<Pos report
<<<<<EOSD
FieldCount=1
PrinterId=5000000060
[Class]
Name=TBLMessageOk
TimeStamp=02.04.2010 16:13
Interval=6.87500141793862E-6;30.12.1899
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=0
[Class]
Name=TBLRGMTestClearSales
TimeStamp=02.04.2010 16:13
Interval=2.69444426521659E-5;30.12.1899 0:00:02
Exception=
StateGUID={812EC369-5812-4EA8-9BF0-2B0147EFB375}
StateName=TBLRRestaurant
FieldCount=2
Slower=False
tillDate=38019.4898148148;02.02.2004 11:45:20
