CREATE TABLE user_updates  (
	ID U_ID,
    id_untill_users bigint,
    id_update_user bigint,
    update_dt timestamp,
    is_active smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint user_updates_pk primary key (id),
    constraint uupd_FK1 FOREIGN KEY (id_untill_users) REFERENCES untill_users (ID),
    constraint uupd_FK2 FOREIGN KEY (id_update_user) REFERENCES untill_users (ID)
);
commit;

execute procedure register_sync_table_ex('user_updates', 'b', 1);
execute procedure register_bo_table('user_updates', 'id_untill_users', 'untill_users');
commit;

