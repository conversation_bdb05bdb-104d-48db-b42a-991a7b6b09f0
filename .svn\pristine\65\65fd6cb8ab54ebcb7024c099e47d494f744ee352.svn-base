unit NeedPaymentIDFram;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, ActionParamsFram, StdCtrls, TntCompatibilityU, TicketEntityManager,
  ExtCtrls, UntillPanelU, DialogsU, UntillComboBoxU, IBSQL,
  UntillDBU, UntillCheckBoxU, ColorComboU, ComCtrls, ImgList,
  UntillScreenU, UntillGraphicElementsU, UntillSCRU, UntillSelectBoxU,
  PaymentEntityManager, CommonU, BOUntillDBU, UTF8U, UntillSpinEditU,
  UntillRadioButtonU;

type
  TNeedPaymentIDFrame = class(TActionParamsFrame)
    UntillPanel1: TUntillPanel;
    TntLabel3: TTntLabel;
    usbPayment: TUntillSelectBox;
    lblBill: TTntLabel;
    edtBill: TUntillSpinEdit;
    lblTimes: TTntLabel;
    pnlInvoice: TPanel;
    chkInvoice: TUntillCheckBox;
    lblLayout: TTntLabel;
    usbLayout: TUntillSelectBox;
    chkPayInvoice: TUntillCheckBox;
    pnlTips: TPanel;
    pnlEFTParams: TPanel;
    lblEFTParams: TTntLabel;
    edtEFTParams: TEdit;
    chkConsider: TUntillCheckBox;
    rbTips: TUntillRadioButton;
    rbVoucher: TUntillRadioButton;
    rbPrint: TUntillRadioButton;
    rbEmail: TUntillRadioButton;
    procedure usbPaymentButtonClick(Sender: TObject);
    procedure usbLayoutButtonClick(Sender: TObject);
    procedure chkInvoiceClick(Sender: TObject);
    procedure chkConsiderClick(Sender: TObject);
  private
    { Private declarations }
    CNumber : Integer;
    emTicket  : TTicketEntityManager;
    function GetPaymentIdByNumber(FieldName : String; PaymentNumber: Integer) : variant;
    procedure CheckInvoiceVisible;
    procedure CheckTipsVisible;
    procedure CheckDoAfter;
  public
    emPayment : TPaymentEntityManager;
    procedure GetParameters(var Parameters: array of Variant); override;
    procedure SetParameters(Parameters: array of Variant); override;
    procedure TranslateStrings; override;

    constructor Create(AOwner: TComponent; AActionObject:TObject; ANameManager:TObject; AUntillDB:TBOCustomUntillDB); override;
    destructor Destroy; override;
    { Public declarations }
  end;

var
  NeedPaymentIDFrame: TNeedPaymentIDFrame;

implementation
uses NameManagerU, InterpretatorU, RestaurantPluginU, EntityManagerU, PaymentsU, DataControlsU, TicketProc2U;
{$R *.dfm}

{ TTestActionFrame }

procedure TNeedPaymentIDFrame.chkConsiderClick(Sender: TObject);
begin
  inherited;
  rbTips.enabled    := chkConsider.checked;
  rbVoucher.enabled := chkConsider.checked;
end;

procedure TNeedPaymentIDFrame.chkInvoiceClick(Sender: TObject);
begin
  inherited;
  CheckInvoiceVisible;
  CheckTipsVisible;
  CheckDoAfter;
end;

procedure TNeedPaymentIDFrame.CheckDoAfter;
begin
  rbPrint.Enabled := chkInvoice.checked;
  rbEmail.Enabled := chkInvoice.checked;
  if not chkInvoice.checked then begin
    rbPrint.checked := false;
    rbEmail.checked := false;
  end else begin
    if not rbPrint.checked and not rbEmail.checked then
      rbPrint.checked := true;
  end;
  usbLayout.Enabled := chkInvoice.checked;
end;

constructor TNeedPaymentIDFrame.Create(AOwner: TComponent; AActionObject:TObject;
  ANameManager: TObject; AUntillDB: TBOCustomUntillDB);
begin
  inherited;
  emPayment := TPaymentEntityManager.Create(Self,UntillDB);
  emPayment.ListParams.AddCondition('kind <> ' + IntToStr(PAYMENT_HASH) + ' and kind <> ' + IntToStr(PAYMENT_SC));
  emTicket:=TTicketEntityManager.Create(Self, UntillDB);
end;

procedure TNeedPaymentIDFrame.GetParameters(var Parameters: array of Variant);
begin
  inherited;
  if CNumber <> -1 then
    Parameters[0] := IntToStr(CNumber)
  else
    Parameters[0] := '';
  if chkInvoice.Checked then begin
    if rbPrint.checked then
      Parameters[1] := 1
    else
      Parameters[1] := 2;
    Parameters[2] :=usbLayout.Text;
  end else begin
    Parameters[1] := 0;
    Parameters[2] :='';
  end;
  if chkPayInvoice.checked then
    Parameters[3] :=1
  else
    Parameters[3] :=0;
  if chkConsider.checked then begin
    if rbTips.checked then
      Parameters[4] :=1
    else
      Parameters[4] :=2;
  end else
    Parameters[4] :=0;
  Parameters[5] := edtBill.value;
  Parameters[6] := trim(edtEFTParams.Text);
end;

function TNeedPaymentIDFrame.GetPaymentIdByNumber(FieldName : String; PaymentNumber : Integer) : variant;
var
  q:IIBSQL;
begin
  q := UntillDB.GetIIbSQL;
  q.SetText('select '+FieldName+' from Payments where is_active=1 and number = '+IntToStr(Paymentnumber));
  q.ExecQuery;
  if not q.Eof then
    result:=q.q.Fields[0].AsVariant
  else
    result:=0;
end;

procedure TNeedPaymentIDFrame.SetParameters(Parameters: array of Variant);
begin
  inherited;
  chkInvoice.Checked := false;
  CheckDoAfter;
  with usbPayment do begin
    Value:=StrToIntDef(Parameters[0], -1);
    if Value<> -1 then begin
      Text:=UTF8_Decode(GetPaymentIdByNumber('name',Value));
      cnumber := StrToIntDef(GetPaymentIdByNumber('number',Value),0);
      CheckInvoiceVisible;
      CheckTipsVisible;
    end else begin
      Text:='';
      cnumber := -1;
    end;
    if Length(Parameters) >1 then begin
      chkInvoice.Checked := (StrToIntDef(Parameters[1], 0)>0);
      if StrToIntDef(Parameters[1], 0) = 1 then
        rbPrint.Checked := true
      else if StrToIntDef(Parameters[1], 0) = 2 then
        rbEmail.Checked := true;
    end;
    if Length(Parameters) >2 then
      FillUsb(usbLayout, GetTicketId(UntillDB, UTF8_Encode(Parameters[2]), False), emTicket, 'name');
    if Length(Parameters) >3 then
      chkPayInvoice.Checked := (StrToIntDef(Parameters[3], 0)=1);
    chkConsider.Checked := false;
    if Length(Parameters) >4 then begin
      chkConsider.Checked := (StrToIntDef(Parameters[4], 0) > 0);
      if StrToIntDef(Parameters[4], 0) = 1 then
        rbTips.Checked   := true
      else if StrToIntDef(Parameters[4], 0) = 2 then
        rbVoucher.Checked := true
    end;
    rbVoucher.enabled := chkConsider.Checked;
    rbTips.enabled    := chkConsider.Checked;

    if Length(Parameters) >5 then
      edtBill.value := StrToIntDef(Parameters[5], 1);
    if Length(Parameters) >6 then
      edtEFTParams.Text := Parameters[6];
  end;
end;

procedure TNeedPaymentIDFrame.TranslateStrings;
begin
  inherited;
  TntLabel3.Caption     := Plugin.TranslateLabel('NeedPaymentIDFram','Payment type');
  chkInvoice.Caption    := Plugin.Translate('NeedPaymentIDFram','Do after payment');
  rbPrint.Caption       := Plugin.Translate('NeedPaymentIDFram','Print invoice');
  rbEmail.Caption       := Plugin.Translate('NeedPaymentIDFram','Email invoice');
  lblLayout.Caption     := Plugin.TranslateLabel('NeedPaymentIDFram','Invoice layout');
  chkPayInvoice.Caption := Plugin.Translate('NeedPaymentIDFram','Mark invoice as paid');
  chkConsider.Caption   := Plugin.Translate('NeedPaymentIDFram','Consider return amount as');
  rbTips.Caption        := Plugin.Translate('NeedPaymentIDFram','Tips');
  rbVoucher.Caption     := Plugin.Translate('NeedPaymentIDFram','Voucher');
  lblBill.Caption       := Plugin.TranslateLabel('NeedPaymentIDFram','Print bill');
  lblTimes.Caption      := Plugin.Translate('NeedPaymentIDFram','times');
  lblEFTParams.Caption  := Plugin.TranslateLabel('NeedPaymentIDFram','EFT Params');
end;

procedure TNeedPaymentIDFrame.CheckTipsVisible;
var pk : Integer;
begin
  pk := PaymentsU.GetPaymentKindByName(Untilldb,UTF8_Encode(usbPayment.Text));
  if (pk=PAYMENT_CARD) or (pk=PAYMENT_GIFT_CARD) or (pk=PAYMENT_CHEQUE) or (pk=PAYMENT_ROOM) then begin
  	pnlTips.visible := true;
    rbTips.checked  := true;
  end else begin
  	pnlTips.visible := false;
  end;
  pnlEFTParams.Visible := (pk=PAYMENT_CARD);
end;

procedure TNeedPaymentIDFrame.CheckInvoiceVisible;
begin
  if PaymentsU.GetPaymentKindByName(Untilldb,UTF8_Encode(usbPayment.Text))=PAYMENT_ACCOUNT then
  	pnlInvoice.visible := true
  else begin
    chkInvoice.checked := false;
    CheckDoAfter;
    pnlInvoice.visible := false;
  end;
  if not chkInvoice.checked then
    chkPayInvoice.checked := false;
end;

procedure TNeedPaymentIDFrame.usbPaymentButtonClick(Sender: TObject);
var res : Int64;
begin
  inherited;
  with usbPayment, emPayment do begin
    res:=ShowModal(Value);
    if res >= 0 then begin
      Value:=res;
      Text := GetWideStringById('name',res);
      cnumber := GetVariantById('Number',res);
    end;
    CheckInvoiceVisible;
    CheckTipsVisible;
    SetFocus;
  end;
end;

destructor TNeedPaymentIDFrame.Destroy;
begin
  FreeAndNil(emPayment);
  FreeAndNil(emTicket);
  inherited;
end;

procedure TNeedPaymentIDFrame.usbLayoutButtonClick(Sender: TObject);
begin
  inherited;
  UsbButton(usbLayout, emTicket, 'name');
end;

end.
