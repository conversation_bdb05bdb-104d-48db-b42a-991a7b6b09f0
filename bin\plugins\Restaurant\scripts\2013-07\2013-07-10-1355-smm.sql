create table t2o_groups	(
    id u_id,
    name varchar(100),
    id_clients u_id,
    id_users_created u_id,
    id_users_closed bigint,

	constraint t2o_groups_pk primary key (id),
	constraint t2o_groups_fk1 foreign key (id_clients) references clients(id),
	constraint t2o_groups_fk2 foreign key (id_users_created) references untill_users(id),
	constraint t2o_groups_fk3 foreign key (id_users_closed) references untill_users(id)
);
commit;
grant all on t2o_groups to untilluser;
commit;
execute procedure register_sync_table_ex('t2o_groups', 'b', 1);
commit;


create table t2o_members (
    id u_id,
    id_t2o_groups u_id,
    id_clients u_id,
    id_users_join u_id,
    id_users_quit bigint,
    join_time timestamp,
    quit_time timestamp,

	constraint t2o_members_pk primary key (id),
	constraint t2o_members_fk1 foreign key (id_clients) references clients(id),
	constraint t2o_members_fk2 foreign key (id_users_join) references untill_users(id),
	constraint t2o_members_fk3 foreign key (id_users_quit) references untill_users(id)
);
commit;
grant all on t2o_members to untilluser;
commit;
execute procedure register_sync_table_ex('t2o_members', 'b', 1);
commit;


create table t2o_order_queue (
    ser integer,
    id_clients bigint,
    id_t2o_groups bigint,
    id_articles u_id,
    quantity integer,
    attempts integer,
    is_active smallint,

	constraint t2o_order_queue_fk1 foreign key (id_clients) references clients(id),
	constraint t2o_order_queue_fk2 foreign key (id_t2o_groups) references t2o_groups(id),
	constraint t2o_order_queue_fk3 foreign key (id_articles) references articles(id)
);
commit;
grant all on t2o_order_queue to untilluser;
commit;
