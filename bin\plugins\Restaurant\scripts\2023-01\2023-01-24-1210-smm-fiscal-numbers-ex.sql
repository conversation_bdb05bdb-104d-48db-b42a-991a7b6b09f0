CREATE TABLE fiscal_numbers_ex (
	id u_id,
    ser INTEGER,
    ser_reopened INTEGER,
    datetime timestamp,
    id_pbill u_id,
    id_pbill_reopened bigint,
    misc_data blob sub_type 0 segment size 80,
    doc_id varchar(100),
    constraint fiscal_numbers_ex_pk primary key (id),
    constraint fiscal_numbers_ex_fk1 foreign key (id_pbill) references pbill (id),
    constraint fiscal_numbers_ex_fk2 foreign key (id_pbill_reopened) references pbill (id)
);
commit;
grant all on fiscal_numbers_ex to untilluser;
commit;
execute procedure register_sync_table_ex('fiscal_numbers_ex', 'p', 0);
commit;
