create table size_modifier (
    id u_id,
    sm_number integer,
    sm_name varchar(50),
	is_active 		smallint,
        IS_ACTIVE_MODIFIED 	timestamp,
        IS_ACTIVE_MODIFIER 	varchar(30),
    constraint size_modifier_pk primary key (id)
);
commit;
grant all on driver_discount to untilluser;
commit;
execute procedure register_sync_table_ex('size_modifier', 'b', 1);
commit;
execute procedure register_bo_table('size_modifier', '', '');
commit;

create table size_modifier_item (
    id u_id,
    id_size_modifier 	bigint,
    smi_number  	integer,
    smi_name		varchar(50),	
	is_active 		smallint,
        IS_ACTIVE_MODIFIED 	timestamp,
        IS_ACTIVE_MODIFIER 	varchar(30),
    constraint size_modifier_item_pk primary key (id),
    constraint size_modifier_item_fk1 foreign key (id_size_modifier) references size_modifier(id)
);
commit;
grant all on size_modifier_item to untilluser;
commit;
execute procedure register_sync_table_ex('size_modifier_item', 'b', 1);
commit;
execute procedure register_bo_table('size_modifier_item', 'id_size_modifier', 'size_modifier');
commit;


