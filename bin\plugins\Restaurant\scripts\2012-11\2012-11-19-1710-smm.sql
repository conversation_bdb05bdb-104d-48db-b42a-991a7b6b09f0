create table xz_reports (
        id U_ID,
        datetime    	timestamp,	
        id_untill_users bigint,
        number    		integer,
        time_from		timestamp,
        time_till		timestamp,
        xreport			smallint,
        pcname			varchar(50),
        origin			smallint, --0: POS, 1:BO, 2:Export Task

        constraint xz_reports_pk primary key (id),
        constraint xz_reports_fku foreign key (id_untill_users) references untill_users (id)
);
commit;
grant all on xz_reports to untilluser;
commit;
execute procedure register_sync_table_ex('xz_reports', 'p', 1);
commit;
