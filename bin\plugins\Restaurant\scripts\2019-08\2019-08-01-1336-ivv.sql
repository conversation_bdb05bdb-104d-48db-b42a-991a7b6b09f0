SET TERM ^ ;

CREATE OR ALTER TRIGGER ACC_PAYMENTS_INS_TRIG FOR ACCOUNTS_PAYMENTS
ACTIVE AFTER INSERT POSITION 0
as
declare id_accounts bigint;
declare common_acc integer;
declare cnt integer;
begin
  if ((deleting) or (updating)) then exit;

  select coalesce(common_accounts,0), id_accounts from clients where id = new.id_clients  into :common_acc, :id_accounts;
  if ((:common_acc<>0) and (:id_accounts>0) and (coalesce(new.account_type,0)=0) ) then begin
    select count(*) from account_balance_common where id_accounts = :id_accounts into :cnt;
    
    if (:cnt = 0) then
      insert into account_balance_common (id_accounts, amount) values (:id_accounts, -coalesce(new.total,0));
    else
      update account_balance_common set amount=amount - coalesce(new.total,0) where id_accounts = :id_accounts;
   end else begin
    select count(*) from account_balance where id_clients = new.id_clients into :cnt;

    if (:cnt = 0) then
      insert into account_balance (id_clients, amount) values (new.id_clients, -coalesce(new.total,0));
    else
      update account_balance set amount=amount - coalesce(new.total,0) where id_clients = new.id_clients;
   end
end
^

SET TERM ; ^


