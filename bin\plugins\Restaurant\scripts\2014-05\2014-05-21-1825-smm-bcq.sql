set term !! ;
CREATE OR ALTER procedure BCQ_ORDER_ITEM (
    ID_ORDER_ITEM bigint)
as
declare variable ID_ORDERS bigint;
declare variable ID_ARTICLES bigint;
declare variable ID_MENU bigint;
declare variable QUANTITY integer;
declare variable ID bigint;
begin
  select OI.ID,  OI.ID_ORDERS, OI.QUANTITY, OI.ID_MENU, ID_ARTICLES
  from ORDER_ITEM OI where oi.id=:id_order_item
  into :ID, :ID_ORDERS, :QUANTITY, :ID_MENU, :ID_ARTICLES;

  execute procedure BEVERAGE_PREPROCESS_ORDERITEM(:id, :ID_ORDERS, :QUANTITY, :ID_MENU, :ID_ARTICLES);
end
!!
commit
!!
CREATE OR ALTER TRIGGER TRANSFERRED_BILLS_INS_TRIG FOR TRANSFERRED_BILLS
ACTIVE AFTER INSERT POSITION 0
as
begin
  insert into bcq (data, data_type) values (new.id, 2);
end!!
commit
!!
set term ; !!
