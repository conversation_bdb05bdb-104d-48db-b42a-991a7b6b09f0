create table order_item_sizes (
    id u_id,
    id_order_item bigint,
    id_menu_item  bigint,
    id_pbill_item bigint,
    id_articles   bigint,
    price  	  decimal(17,4),
    original_price decimal(17,4),
    vat    	  double precision,
    constraint order_item_sizes_pk primary key (id),
    constraint order_item_sizes_fk1 foreign key (id_order_item) references order_item(id),
    constraint order_item_sizes_fk2 foreign key (id_menu_item) references menu_item(id),
    constraint order_item_sizes_fk3 foreign key (id_pbill_item) references pbill_item(id),
    constraint order_item_sizes_fk4 foreign key (id_articles) references articles(id)
);
commit;
grant all on order_item_sizes to untilluser;
commit;
execute procedure register_sync_table_ex('order_item_sizes', 'p', 0);
commit;
