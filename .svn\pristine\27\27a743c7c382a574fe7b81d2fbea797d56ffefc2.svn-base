unit RemoteTermQueueU;

interface

uses
UntillFrm,
Messages,
Generics.Collections,
windows,
UntillLogsU,
SysUtils,
Classes,
ExtCtrls;

type

EQueueOverflow = class
end;

ETooLongHHTOperaion = class (Exception)

end;

TStoredRec = record
  FMSG : TMessage;
  FName : String;
end;

TRemoteTermMsgQueue = class
    FHandle : HWND;
    FCapacity : Integer;
    FHandlingMessage: Boolean;
    FHandlingMessageName: String;
    FMessagesQueue : TQueue<TStoredRec>;

    FLog : TUntillLog;
    FHHLogLevel : Integer;
    FStartHandle : TDateTime;
    FMsgOperationTimer : TTimer;

    function Logged(LogWhat: Integer): Boolean;
    procedure TraceHH(LogWhat: Integer; Msg: String);
    procedure OnOperationTimeExceeded(Sender: TObject);
public

    constructor Create(owner : TComponent; handle: HWND; capacity : Integer; timeout : DWORD);
    destructor Destroy;  override;

    procedure PutMessageInQueue(Msg : TMessage; MsgName:String);
    procedure PollMessageFromQueue;
    function TryStartHandlingMessage(MsgName: String; Msg:TMessage) : boolean;
    procedure EndHandlingMessage;

    procedure SetLog(log: TUntillLog; lvl : Integer);
end;

const
  HHLOG_LEVEL_TRACE = 1; // debug
  HHLOG_LEVEL_MESSAGES = 2; // messages
  HHLOG_LEVEL_LOWLEVEL = 3; // In/out lolevel
  QUEUE_LOG_LEVEL = TUntillLogLevel.logInfo;


implementation

uses
  DateUtils,
  PluginU;


constructor TRemoteTermMsgQueue.Create(owner : TComponent; handle : HWND; capacity : Integer; timeout : DWORD);
begin
  FCapacity := capacity;
  FHandle := handle;
  FMessagesQueue := TQueue<TStoredRec>.Create();
  FMsgOperationTimer := TTimer.Create(owner);
  FMsgOperationTimer.Enabled := False;
  FMsgOperationTimer.Interval := timeout;
  FMsgOperationTimer.OnTimer := OnOperationTimeExceeded;
end;

destructor TRemoteTermMsgQueue.Destroy;
begin
  FreeAndNil(FMsgOperationTimer);
  FMessagesQueue.Free;
end;

procedure TRemoteTermMsgQueue.PutMessageInQueue(Msg : TMessage; MsgName:String);
var rec : TStoredRec;
begin
    if FMessagesQueue.Count < FCapacity then begin
      rec.FMSG := Msg;
      rec.FName := MsgName;
      FMessagesQueue.Enqueue(rec);
      MiscLog.Log(QUEUE_LOG_LEVEL, 'TRemoteTermMsgQueue', 'Put  ' + MsgName + ', operating: ' + FHandlingMessageName);
      TraceHH(HHLOG_LEVEL_LOWLEVEL, 'Message ' + MsgName + ' added in queue');
    end
    else begin
      if MilliSecondsBetween(Now, FStartHandle) > 10000 then
        raise ETooLongHHTOperaion.Create('');
      TraceHH(HHLOG_LEVEL_LOWLEVEL, 'Already handling message, ignoring ' + MsgName);
      ExceptionLog.WriteString('Already handling message(' +FHandlingMessageName+ '), ignoring ' + MsgName);
      raise EQueueOverflow.Create;
    end;
end;

procedure TRemoteTermMsgQueue.PollMessageFromQueue;
var
  msg : TMessage;
  rec : TStoredRec;
begin
  if FMessagesQueue.Count > 0 then begin
    TraceHH(HHLOG_LEVEL_LOWLEVEL, 'Read message from queue');
    rec := FMessagesQueue.Extract;
    msg := rec.FMSG;
    MiscLog.Log(QUEUE_LOG_LEVEL, 'TRemoteTermMsgQueue', 'Poll ' + rec.FName);
    PostMessage(FHandle, msg.Msg, msg.WParam, msg.LParam);
  end;
end;

function TRemoteTermMsgQueue.TryStartHandlingMessage(MsgName: String; Msg:TMessage) : boolean;
begin
  if(FHandlingMessage) then begin
    PutMessageInQueue(Msg, MSGNAME);
    result := false;
    exit;
  end;

  MiscLog.Log(TUntillLogLevel.logTrace, 'TRemoteTermMsgQueue', 'Starting: '+ MsgName);
  FHandlingMessage := True;
  FHandlingMessageName := MsgName;
  FMsgOperationTimer.Enabled := True;
  result := True;
  FStartHandle := Now;
end;

procedure TRemoteTermMsgQueue.EndHandlingMessage;
var
  tdif : Int64;
begin
  FMsgOperationTimer.Enabled := false;
  tdif := MilliSecondsBetween(Now, FStartHandle);
  if (tdif > 500) and Assigned(FLog)then
    FLog.Log(TUntillLogLevel.logWarn, 'TRemoteTermMsgQueue', 'Too long handler for operation : ' + FHandlingMessageName + ', ' + IntToStr(tdif) + 'ms');
  MiscLog.Log(TUntillLogLevel.logTrace, 'TRemoteTermMsgQueue', 'Ending: '+ FHandlingMessageName);
  FHandlingMessage := False;
  FHandlingMessageName := '';

  PollMessageFromQueue;
end;

procedure TRemoteTermMsgQueue.SetLog(log: TUntillLog; lvl : Integer);
begin
  FLog := log;
  FHHLogLevel := lvl;
end;

function TRemoteTermMsgQueue.Logged(LogWhat: Integer): Boolean;
begin
	result := (FHHLogLevel and (1 shl LogWhat)) > 0
end;

procedure TRemoteTermMsgQueue.TraceHH(LogWhat: Integer; Msg: String);
begin
	if Assigned(FLog) and Logged(LogWhat) then
    FLog.WriteString(Msg);
end;

procedure TRemoteTermMsgQueue.OnOperationTimeExceeded(Sender: TObject);
begin
  FMsgOperationTimer.Enabled := false;
  Plugin.RaiseCriticalException('Operation time exceeded');
end;

end.
