CREATE TABLE VAT (
    ID U_ID NOT NULL,
    IS_ACTIVE SMALLINT NOT NULL,
    NAME VARCHAR(80) NOT NULL,
    VAT DECIMAL(17,4) NOT NULL,
    VAT_SIGN VARCHAR(3) NOT NULL
);
ALTER TABLE VAT ADD CONSTRAINT PK_VAT PRIMARY KEY (ID);
commit;
grant all on vat to untilluser;
commit;


ALTER TABLE FOOD_GROUP ADD ID_VAT BIGINT;
CREATE INDEX FOOD_GROUP_IDX1 ON FOOD_GROUP (ID_VAT);
ALTER TABLE FOOD_GROUP ADD CONSTRAINT FK_FOOD_GROUP_1 FOREIGN KEY (ID_VAT) REFERENCES VAT(ID);
commit;

ALTER TABLE FOOD_GROUP ADD ID_VAT_SECONDARY BIGINT;
CREATE INDEX FOOD_GROUP_IDX2 ON FOOD_GROUP (ID_VAT_SECONDARY);
ALTER TABLE FOOD_GROUP ADD CONSTRAINT FK_FOOD_GROUP_2 FOREIGN KEY (ID_VAT_SECONDARY) REFERENCES VAT(ID);
commit;


