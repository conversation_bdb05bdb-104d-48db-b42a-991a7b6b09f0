create table ks_wf_template_item (
    id u_id,
	item_number  	   integer,   
	id_ks_wf_template  bigint,   
    id_stage_template  bigint,
    is_active 		   smallint,
    IS_ACTIVE_MODIFIED timestamp,
    IS_ACTIVE_MODIFIER varchar(30),
    constraint kswfti_pk primary key (id),
    constraint kswfti_fk1 foreign key (id_ks_wf_template) references ks_wf_template(id),
    constraint kswfti_fk2 foreign key (id_stage_template) references ks_wf_stage_template(id)
);
commit;
grant all on ks_wf_template_item to untilluser;
commit;
execute procedure register_sync_table_ex('ks_wf_template_item', 'b', 1);
commit;
execute procedure register_bo_table('ks_wf_template_item', 'id_stage_template', 'ks_wf_stage_template');
commit;
execute procedure register_bo_table('ks_wf_template_item', 'id_ks_wf_template', 'ks_wf_template');
commit;                                                                                            


