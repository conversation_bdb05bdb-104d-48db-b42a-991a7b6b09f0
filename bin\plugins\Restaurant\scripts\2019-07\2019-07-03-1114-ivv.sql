create table driver_discount (
    id u_id,
    comment varchar(50),
    constraint driver_discount_pk primary key (id)
);
commit;
grant all on driver_discount to untilluser;
commit;
execute procedure register_sync_table_ex('driver_discount', 'p', 1);
commit;

create table driver_discount_item (
    id u_id,
    id_driver_discount bigint,
    osig_md5 	varchar(50),	
    amount 	decimal(17,4), 	
    id_reason 	bigint,
    reason 	varchar(100),
    constraint driver_discount_item_pk primary key (id),
    constraint driver_discount_item_fk1 foreign key (id_driver_discount) references driver_discount(id),
    constraint driver_discount_item_fk2 foreign key (id_reason) references DISCOUNT_REASONS(id)
);
commit;
grant all on driver_discount_item to untilluser;
commit;
execute procedure register_sync_table_ex('driver_discount_item', 'p', 1);
commit;


alter table PBILL_PAYMENTS add id_driver_discount bigint, 
  add constraint PBILL_PAYMENTS_DRD_FK foreign key (id_driver_discount) references driver_discount(id);
commit;
